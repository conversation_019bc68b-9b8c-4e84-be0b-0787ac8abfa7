import mssql from '@lcs/mssql-utility'
import { KeywordFields, KeywordsTableName } from '@tess-f/sql-tables/dist/lms/keyword.js'
import { LearningContextKeywordFields, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'
import { LearningContextUserViewFields, LearningContextUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-view.js'
import { LearningObjectKeywordFields, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import { LearningObjectUserViewFields, LearningObjectUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-view.js'

export default async function (from?: Date, to?: Date, userID?: string): Promise<{ Name: string, Views: number }[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  const query = `
            SELECT TOP 10 [${KeywordsTableName}].[${KeywordFields.Name}], COALESCE(SUM([ObjectViews].[Views]), 0) + COALESCE(SUM([ContextViews].[Views]), 0) AS Views
            FROM [${KeywordsTableName}]
            FULL JOIN (
                SELECT [${LearningObjectKeywordsTableName}].[${LearningObjectKeywordFields.Keyword}] AS Name, COUNT([${LearningObjectUserViewsTableName}].[${LearningObjectUserViewFields.LearningObjectID}]) AS Views
                FROM [${LearningObjectKeywordsTableName}]
                INNER JOIN [${LearningObjectUserViewsTableName}] ON [${LearningObjectKeywordsTableName}].[${LearningObjectKeywordFields.LearningObjectID}] = [${LearningObjectUserViewsTableName}].[${LearningObjectUserViewFields.LearningObjectID}]
                ${(from && to) || userID ? 'WHERE' : ''}
                ${(from && to) ? `[${LearningObjectUserViewsTableName}].[${LearningObjectUserViewFields.CreatedOn}] BETWEEN @from AND @to` : ''}
                ${from && to && userID ? 'AND' : ''}
                ${userID ? `[${LearningObjectUserViewsTableName}].[${LearningObjectUserViewFields.UserID}] = @userID` : ''}
                GROUP BY [${LearningObjectKeywordFields.Keyword}]
            ) AS ObjectViews ON [ObjectViews].[${KeywordFields.Name}] = [${KeywordsTableName}].[${KeywordFields.Name}]
            FULL JOIN (
                SELECT [${LearningContextKeywordsTableName}].[${LearningContextKeywordFields.Keyword}] AS Name, COUNT([${LearningContextUserViewsTableName}].[${LearningContextUserViewFields.LearningContextID}]) AS Views
                FROM [${LearningContextKeywordsTableName}]
                INNER JOIN [${LearningContextUserViewsTableName}] ON [${LearningContextUserViewsTableName}].[${LearningContextUserViewFields.LearningContextID}] = [${LearningContextKeywordsTableName}].[${LearningContextKeywordFields.LearningContextID}]
                ${(from && to) || userID ? 'WHERE' : ''}
                ${(from && to) ? `[${LearningContextUserViewsTableName}].[${LearningContextUserViewFields.CreatedOn}] BETWEEN @from AND @to` : ''}
                ${from && to && userID ? 'AND' : ''}
                ${userID ? `[${LearningContextUserViewsTableName}].[${LearningContextUserViewFields.UserID}] = @userID` : ''}
                GROUP BY [${LearningContextKeywordsTableName}].[${LearningContextKeywordFields.Keyword}]
            ) AS ContextViews ON [ContextViews].[${KeywordFields.Name}] = [${KeywordsTableName}].[${KeywordFields.Name}]
            GROUP BY [${KeywordsTableName}].[${KeywordFields.Name}]
            HAVING COALESCE(SUM([ObjectViews].[Views]), 0) + COALESCE(SUM([ContextViews].[Views]), 0) > 0
            ORDER BY [Views] DESC
        `

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  if (userID) {
    request.input('userID', userID)
  }

  const results = await request.query<{ Name: string, Views: number }>(query)

  return results.recordset
}
