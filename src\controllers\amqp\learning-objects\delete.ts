import del from '../../../services/mssql/learning-objects/delete.service.js'
import logger from '@lcs/logger'
import { RpcMessage } from '@tess-f/shared-config/dist/types/rpc-message.js'
import { RpcResponse } from '@tess-f/shared-config/dist/types/rpc-response.js'
import { DeleteLearningObjectRequest } from '@tess-f/lms/dist/amqp/learning-object.js'
import { getErrorMessage, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-AMQP.delete-learning-object')

export default async function (req: RpcMessage<DeleteLearningObjectRequest>): Promise<RpcResponse<any>> {
  try {
    const { ID } = z.object({ ID: zodGUID }).parse(req.data)
    await del(ID)

    log('info', 'Successfully deleted learning object', { success: true })

    return { success: true }
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete learning object: input validation error', { errorMessage: error.message, success: false })
      return {
        success: false,
        message: zodErrorToMessage(error, 'Invalid request data: ')
      }
    }

    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to delete learning object.', { errorMessage, success: false })

    return {
      success: false,
      message: errorMessage
    }
  }
}
