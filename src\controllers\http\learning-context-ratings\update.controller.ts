import { zodErrorToMessage } from '@tess-f/backend-utils'
import LearningContextRating, { learningContextRatingSchema } from '../../../models/learning-context-rating.model.js'
import update from '../../../services/mssql/learning-context-ratings/update.service.js'
import logger from '@lcs/logger'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.update-learning-context-ratings')

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-update-learning-context-ratings' label."

  try {
    const rating = new LearningContextRating(learningContextRatingSchema.parse(req.body))
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    rating.fields.ID = id

    const result = await update(rating)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-update-learning-context-ratings' label and message indicating a successful update."
    log('info', 'Successfully updated learning context rating', { id, success: true, req })

    res.json(result.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update learning context rating: input validation error', { success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to update learning context rating.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
