import mssql, { updateRow } from '@lcs/mssql-utility'
import { SystemWidget } from '@tess-f/sql-tables/dist/lms/system-widget.js'
import SystemWidgetModel from '../../../models/system-widget.model.js'

export default async function updateSystemWidgetService (widget: SystemWidgetModel): Promise<SystemWidgetModel> {
  const pool = mssql.getPool()
  const record = await updateRow<SystemWidget>(pool.request(), widget, { ID: widget.fields.ID })
  return new SystemWidgetModel(record[0])
}
