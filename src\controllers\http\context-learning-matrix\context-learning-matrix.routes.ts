import { Request<PERSON><PERSON><PERSON>, Router } from 'express'
import reorderMatrixController from './reorder-matrix.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.patch('/reorder-matrix/:parentID/:itemID/:newOrderID/:oldOrderID', checkClaims([Claims.CREATE_CATALOG_ITEMS, Claims.MODIFY_CATALOG_ITEMS]), reorderMatrixController as RequestHandler)

export default router
