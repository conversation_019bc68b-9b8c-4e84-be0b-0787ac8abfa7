import mssql, { getRows } from '@lcs/mssql-utility'
import { LearningObjectUserBookmark, LearningObjectUserBookmarksTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-bookmark.js'
import LearningObjectBookmarkModel from '../../../models/learning-object-user-bookmark.model.js'

export default async function getUserBookmarkByObjectIDAndUserID (objectID: string, userID: string): Promise<LearningObjectBookmarkModel> {
  const pool = mssql.getPool()
  const records = await getRows<LearningObjectUserBookmark>(LearningObjectUserBookmarksTableName, pool.request(), { LearningObjectID: objectID, UserID: userID })
  return new LearningObjectBookmarkModel(records[0])
}

export async function getUserBookmarksForObject (objectID: string): Promise<LearningObjectBookmarkModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningObjectUserBookmark>(LearningObjectUserBookmarksTableName, pool.request(), { LearningObjectID: objectID })
  return records.map(record => new LearningObjectBookmarkModel(record))
}

export async function getBookmarksForUser (userID: string): Promise<LearningObjectBookmarkModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningObjectUserBookmark>(LearningObjectUserBookmarksTableName, pool.request(), { UserID: userID })
  return records.map(record => new LearningObjectBookmarkModel(record))
}
