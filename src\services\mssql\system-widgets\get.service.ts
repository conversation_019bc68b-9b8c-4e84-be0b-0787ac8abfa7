import mssql, { getRows } from '@lcs/mssql-utility'
import { SystemWidget, SystemWidgetsTableName } from '@tess-f/sql-tables/dist/lms/system-widget.js'
import SystemWidgetModel from '../../../models/system-widget.model.js'

export default async function getSystemWidgetService (systemID: string): Promise<SystemWidgetModel> {
  const pool = mssql.getPool()
  const records = await getRows<SystemWidget>(SystemWidgetsTableName, pool.request(), { ID: systemID })
  return new SystemWidgetModel(undefined, records[0])
}
