import mssql, { getRows, DB_Errors } from '@lcs/mssql-utility'
import { LearnerProgress, LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { Request } from 'mssql'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import LearningObjectModel from '../../../models/learning-object.model.js'

export default async function (userID: string): Promise<{ Progress: LearnerProgressModel, LearningObject: LearningObjectModel }> {
  const pool = mssql.getPool()
  const latestProgress = await getRecentLearnerProgress(userID, pool.request())
  if (latestProgress.length <= 0) throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)

  const completedIDs: string[] = []

  const resumeProgress = latestProgress.find(progress => {
    if (progress.fields.LessonStatusID! >= 4) {
      completedIDs.push(progress.fields.LearningObjectID!)
      return false
    } else {
      const id = completedIDs.find(completeID => {
        return completeID === progress.fields.LearningObjectID
      })
      if (!id) {
        return true
      } else {
        return false
      }
    }
  })
  if (!resumeProgress) throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  const learningObject = await getLearningObject(resumeProgress.fields.LearningObjectID!, pool.request())

  return {
    Progress: resumeProgress,
    LearningObject: learningObject
  }
}

async function getRecentLearnerProgress (userID: string, request: Request): Promise<LearnerProgressModel[]> {
  const query = `
    SELECT TOP 100 * FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @id AND [${LearnerProgressFields.LearningObjectID}] IS NOT NULL
    ORDER BY [${LearnerProgressFields.ModifiedOn}] DESC, [${LearnerProgressFields.CreatedOn}] DESC
  `

  request.input('id', userID)
  const res = await request.query<LearnerProgress>(query)

  return res.recordset.map(record => new LearnerProgressModel(record))
}

async function getLearningObject (objectID: string, request: Request): Promise<LearningObjectModel> {
  const records = await getRows<LearningObject>(LearningObjectsTableName, request, { ID: objectID })
  return new LearningObjectModel(records[0])
}
