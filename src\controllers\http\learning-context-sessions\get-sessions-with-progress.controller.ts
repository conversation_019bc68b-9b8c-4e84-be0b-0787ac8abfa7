import getSessionsWithProgressService from '../../../services/mssql/learning-context-sessions/get-sessions-with-progress.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'
const { INTERNAL_SERVER_ERROR, NOT_FOUND, BAD_REQUEST } = httpStatus

const log = logger.create('Controller-HTTP.get-user-learner-progress-for-ILT', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const sessions = await getSessionsWithProgressService(id, req.session.userId)
    log('info', 'Successfully retrieved ILT course sessions user has progress in', { count: sessions.length, contextID: id, success: true, req })
    res.json(sessions.map(prog => prog.fields))
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get ILT sessions user has progress in: input validation error', { contextID: req.params.id, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get ILT sessions user has progress in because none were found in the database', { contextID: req.params.id, success: false, req })
      res.sendStatus(NOT_FOUND)
    } else {
      log('error', 'Failed to get sessions user has progress in', { contextID: req.params.id, success: false, errorMessage, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
