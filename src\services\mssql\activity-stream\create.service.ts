import mssqlUtils, { addRow } from '@lcs/mssql-utility'
import { Request } from 'mssql'
import logger from '@lcs/logger'
import { ActivityStream, ActivityStreamFields, ActivityStreamTableName } from '@tess-f/sql-tables/dist/lms/activity-stream.js'
import ActivityStreamModel from '../../../models/activity-stream.model.js'

const log = logger.create('Service.Create-Activity-Stream')

export default async function (activity: ActivityStreamModel): Promise<ActivityStreamModel> {
  const pool = mssqlUtils.getPool()
  const created = await addRow<ActivityStream>(pool.request(), activity)
  const rows = await getRecordCountForUser(pool.request(), activity.fields.UserID!)
  if (rows > 200) {
    log('info', 'User has more than 200 activity records removing the oldest record')
    await removeOldestRecord(pool.request(), activity.fields.UserID!)
    log('info', 'Successfully removed the oldest activity record for user', { userID: created.UserID })
  }
  log('info', 'Successfully added record to activity stream', { userID: created.UserID })
  return new ActivityStreamModel(created)
}

async function getRecordCountForUser (request: Request, userID: string): Promise<number> {
  request.input('UserID', userID)

  const query = `SELECT * FROM [${ActivityStreamTableName}] WHERE [${ActivityStreamFields.UserID}] = @UserID`

  const res = await request.query(query)
  return res.recordset.length
}

async function removeOldestRecord (request: Request, userID: string): Promise<void> {
  request.input('UserID', userID)

  await request.query(`
    DELETE FROM [${ActivityStreamTableName}]
    WHERE [${ActivityStreamFields.ID}] IN (
      SELECT TOP (1) [${ActivityStreamFields.ID}]
      FROM [${ActivityStreamTableName}]
      WHERE [${ActivityStreamFields.UserID}] = @UserID
      ORDER BY [${ActivityStreamFields.CreatedOn}]
    )
  `)
}
