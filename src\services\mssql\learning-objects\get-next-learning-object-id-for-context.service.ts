import mssql from '@lcs/mssql-utility'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import { ConnectionPool, Request } from 'mssql'
import getLearningObjectsService from '../learning-objects/get-multiple.service.js'
import getChildContextsService from '../catalog/get-child-contexts.service.js'
import { LearnerProgress, LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'

export default async function (contextID: string, userID: string): Promise<{ objectID: string | null, contextID: string | null }> {
  const pool = mssql.getPool()
  let id = await findNextContextOrObjectID(pool, contextID, userID)
  if (id.found) {
    return {
      contextID: id.contextID,
      objectID: id.objectID
    }
  }
  // we did find the next id so either there is no content or the user has already completed the context
  // lets get the first id we run across
  id = await findFirstContextOrObjectID(pool, contextID)
  if (id.found) {
    return {
      contextID: id.contextID,
      objectID: id.objectID
    }
  }
  // Oh no we got this far which means there is no content in this context
  throw new Error('No content found')
}

async function findNextContextOrObjectID (pool: ConnectionPool, contextID: string, userID: string): Promise<{ contextID: string | null, objectID: string | null, found: boolean }> {
  // build the learning matrix for this context
  const matrix = await buildMatrix(contextID)
  // loop through the matrix recursively looking for the first incomplete item we come across
  for (const item of matrix) {
    if (item.type === 'object') {
      // this is a learning object let's check if it's been completed
      const learnerProgress = await getLearnerProgress(pool.request(), item.ID, userID)
      let id: string | undefined
      if (learnerProgress.length <= 0) {
        // if the length is 0 then this item has not been attempted
        id = item.ID
      } else {
        let completed = false
        for (const progress of learnerProgress) {
          if (progress.fields.LessonStatusID === LessonStatuses.passed ||
            progress.fields.LessonStatusID === LessonStatuses.completed ||
            progress.fields.LessonStatusID === LessonStatuses.fail) {
            completed = true
            break
          }
        }
        if (!completed) {
          id = item.ID
        }
      }
      if (id) {
        return {
          contextID: null,
          objectID: id,
          found: true
        }
      }
    } else if (item.type === 'context' && item.ContextTypeID === LearningContextTypes.Course && item.CourseTypeID === CourseTypes.InstructorLed) {
      // this is an ILT course let's check if it's been completed
      let id: string | undefined
      const learnerProgress = await getLearnerProgressForILT(pool.request(), item.ID, userID)
      if (learnerProgress.length <= 0) {
        // if the length is 0 then this item has not been attempted
        id = item.ID
      } else {
        let completed = false
        for (const progress of learnerProgress) {
          if (progress.fields.LessonStatusID === LessonStatuses.passed ||
            progress.fields.LessonStatusID === LessonStatuses.completed ||
            progress.fields.LessonStatusID === LessonStatuses.fail) {
            completed = true
            break
          }
        }
        if (!completed) {
          id = item.ID
        }
      }
      if (id) {
        return {
          contextID: id,
          objectID: null,
          found: true
        }
      }
    } else {
      // this is a sub context we need to recursively check for the next item
      const id = await findNextContextOrObjectID(pool, item.ID, userID)
      if (id.found) {
        return id
      }
    }
  }
  // if we got this far the whole context is complete or has no content lets return a not found
  return {
    found: false,
    contextID: null,
    objectID: null
  }
}

async function findFirstContextOrObjectID (pool: ConnectionPool, contextID: string): Promise<{ contextID: string | null, objectID: string | null, found: boolean }> {
  // first build the matrix
  const matrix = await buildMatrix(contextID)
  for (const item of matrix) {
    if (item.type === 'object') {
      return {
        objectID: item.ID,
        contextID: null,
        found: true
      }
    } else if (item.type === 'context' && item.ContextTypeID === LearningContextTypes.Course && item.CourseTypeID === CourseTypes.InstructorLed) {
      return {
        objectID: null,
        contextID: item.ID,
        found: true
      }
    } else {
      const id = await findFirstContextOrObjectID(pool, item.ID)
      if (id.found) {
        return id
      }
    }
  }
  // if we got this far this context has no content
  return {
    found: false,
    contextID: null,
    objectID: null
  }
}

async function buildMatrix (contextID: string):
Promise<{ ID: string, type: string, OrderID: number, ContextTypeID?: number, CourseTypeID?: number }[]> {
  const childContexts = await getChildContextsService(contextID)
  const childObjects = await getLearningObjectsService(contextID)
  const matrix = childContexts.map(context => {
    return {
      ID: context.fields.ID!,
      type: 'context',
      OrderID: context.fields.OrderID!,
      ContextTypeID: context.fields.ContextTypeID ?? undefined,
      CourseTypeID: context.fields.CourseTypeID ?? undefined
    }
  }).concat(childObjects.map(obj => {
    return {
      ID: obj.fields.ID!,
      type: 'object',
      OrderID: obj.fields.OrderID!,
      ContextTypeID: undefined,
      CourseTypeID: undefined
    }
  }))

  // sort the items
  matrix.sort((a, b) => a.OrderID > b.OrderID ? 1 : -1)
  return matrix
}

async function getLearnerProgress (request: Request, id: string, userID: string) {
  request.input('id', id)
  request.input('userID', userID)
  const query = `
    SELECT * FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @userID
    AND [${LearnerProgressFields.LearningObjectID}] = @id
    ORDER BY [${LearnerProgressFields.CreatedOn}] DESC
  `

  const results = await request.query<LearnerProgress>(query)
  return results.recordset.map(record => new LearnerProgressModel(record))
}

async function getLearnerProgressForILT (request: Request, id: string, userID: string) {
  request.input('courseID', id)
  request.input('userID', userID)
  const results = await request.query<LearnerProgress>(`
    SELECT * FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @userID
    AND [${LearnerProgressFields.LearningContextSessionID}] IN (
      SELECT [${LearningContextSessionFields.ID}]
      FROM [${LearningContextSessionsTableName}]
      WHERE [${LearningContextSessionFields.LearningContextID}] = @courseID
    )
    ORDER BY [${LearnerProgressFields.CreatedOn}] DESC
  `)
  return results.recordset.map(record => new LearnerProgressModel(record))
}
