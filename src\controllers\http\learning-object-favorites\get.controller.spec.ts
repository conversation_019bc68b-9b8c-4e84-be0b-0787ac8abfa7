import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningObjectUserFavoriteModel from '../../../models/learning-object-user-favorite.model'

describe('HTTP get controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-object-favorites/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(LearningObjectUserFavoriteModel))
            }

            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                objectID: uuid(),
                userID: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get.controller')

        const mocks = httpMocks.createMocks({            
            session: {
                userId: uuid()
            },
            query: {
                objectID: false,
                userID: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request parameter')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('objectID')
        expect(mocks.res._getData()).include('userID')
    })

    it('returns an error if the request has no query params', async () => {
        const controller = await esmock('./get.controller')

        const mocks = httpMocks.createMocks({            
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request parameter data:')
        expect(mocks.res._getData()).include('must provide either objectID or userID')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-object-favorites/get.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(LearningObjectUserFavoriteModel))
            }

            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                objectID: uuid(),
                userID: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })




})