const { expect } = require('chai');
const tester = require('../api/utils/test-agent.utils');
const uuidv4 = require('uuid/v4');
const settings = require('../api/config/settings');

const tmpUserID = uuidv4().toUpperCase();

describe('User Preferences', () => {

    let isolated = false;

    before( done => {
        if(tester.agent == null) {
            isolated = true;
            tester.create()
            .then( agent => {
                done();
            })
            .catch( err => {
                console.error(err);
                done();
            });
        } else {
            done();
        }
    });

    it('creates a rating', done => {

        tester.agent.post(settings.server.root + 'user-preference')
            .send({
                UserID: tmpUserID,
                GettingStarted: true
            })
            .end((err, res) => {

                expect(res.status).to.equal(200);
                expect(res.body.UserID).to.equal( tmpUserID );
                expect(res.body.GettingStarted).be.true;

                done();

            });
    });

    it('updates a rating', done => {

        tester.agent.put( settings.server.root + 'user-preference/' + tmpUserID )
            .send({
                GettingStarted: false
            })
            .end((err, res) => {

                expect(res.status).to.equal(200);
                expect(res.body.UserID).to.equal( tmpUserID );
                expect(res.body.GettingStarted).be.false;

                done();

            });
    });

    it('gets a rating', done => {

        tester.agent.get(settings.server.root + 'user-preference/' + tmpUserID )
            .end((err, res) => {

                expect(res.status).to.equal(200);
                expect(res.body.UserID).to.equal( tmpUserID );
                expect(res.body.GettingStarted).be.false;

                done();

            });
    })

    it('deletes a rating', done => {

        tester.agent.delete(settings.server.root + 'user-preference/' + tmpUserID ) 
            .end((err, res) => {

                expect(res.status).to.equal(204);
                done();

            });
    })


    after((done)=>{
        if(isolated) {
            tester.close();
        } 
        done();
    });
})