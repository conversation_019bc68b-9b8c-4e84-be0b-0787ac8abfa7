import LearnerProgress, { createLearnerProgressSchema } from '../../../models/learner-progress.model.js'
import create from '../../../services/mssql/learner-progress/create.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import gradeContexts from '../../../services/background/grade-contexts.service.js'
import issueLearningObjectCertificateController from '../../certificate/issue-learning-object-certificate.controller.js'
import { gradeForLearningObject, gradeForMultiSessionCourse } from '../../../services/mssql/assignments/update-user-assignment-completion.service.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { ZodError } from 'zod'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.create-learner-progress', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-create-learner-progress' label."
  try {
      const learnerProgress = new LearnerProgress(createLearnerProgressSchema.parse(req.body))
      learnerProgress.fields.CreatedOn = new Date()
    const created = await create(learnerProgress)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-create-learner-progress' label and message indicating successful creation."
    log('info', 'Successfully created learner progress', { id: created.fields.ID, success: true, req })

    res.json(created.fields)

    await grade(created)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create learner progress: input validation error', { success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    }
    else{
    log('error', 'Failed to create learner progress.', { error, req, success: false })
    res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}

async function grade (created: LearnerProgress): Promise<void> {
  if (created.fields.LessonStatusID! > LessonStatuses.browsed) {
    // only run the service on something that has an attempt to it
    // now that the user has a response lets kick off our background grading service
    gradeContexts(created).catch(() => {
      // silent failure, the service should take care of logging any issues
    })

    // check for assignment completion
    if (created.fields.LearningObjectID) {
      try {
        await gradeForLearningObject(created.fields.LearningObjectID, created.fields.UserID!)
      } catch (error) {
        log('warn', 'Failed to update users assignment completions', { error, success: false })
      }
    } else if (created.fields.LearningContextSessionID) {
      try {
        await gradeForMultiSessionCourse(created.fields.LearningContextSessionID, created.fields.UserID!)
      } catch (error) {
        log('warn', 'Failed to update users assignment completions', { error, success: false })
      }
    }
  }

  if (created.fields.LessonStatusID! > LessonStatuses.fail &&
      created.fields.LearningObjectID) {
    // if the user passed let's issue a certificate (if we this object issues them)
    issueLearningObjectCertificateController(created).catch(() => {
      // silent failure
    })
  }
}
