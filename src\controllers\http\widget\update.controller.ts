import logger from '@lcs/logger'
import updateWidgetService from '../../../services/mssql/widgets/update.service.js'
import { Request, Response } from 'express'
import Widget, { updateWidgetSchema } from '../../../models/widget.model.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z, ZodError } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.update-widget', httpLogTransformer)

export default async function updateWidgetController (req: Request, res: Response) {
  try {
    const widget = new Widget(updateWidgetSchema.parse(req.body))
    widget.fields.ID = z.object({ ID: zodGUID }).parse(req.body).ID

    const updated = await updateWidgetService(widget)
    log('info', 'Successfully updated widget', { success: true, req })
    res.json(updated.fields)
  } catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Failed to update widget: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to update widget', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
