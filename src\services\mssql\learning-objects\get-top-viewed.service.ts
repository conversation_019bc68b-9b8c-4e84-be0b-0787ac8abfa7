import mssql from '@lcs/mssql-utility'
import { Request } from 'mssql'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObject, LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningObjectUserViewFields, LearningObjectUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-view.js'

// Gets top viewed learning objects
export default async function (limit: number = 5, from?: Date, to?: Date): Promise<LearningObjectModel[]> {
  const pool = mssql.getPool()
  const topViewed = await getViewsMap(pool.request(), limit, from, to)
  const ids = topViewed.map(views => views.LearningObjectID)
  const learningObjects = await getLearningObjects(pool.request(), ids)

  learningObjects.forEach(obj => {
    const views = topViewed.find(view => view.LearningObjectID === obj.fields.ID)
    if (views) {
      obj.fields.Views = views.Views
    }
  })

  return learningObjects
}

async function getViewsMap (request: Request, limit: number, from?: Date, to?: Date) {
  let query = `SELECT TOP ${limit} COUNT([${LearningObjectUserViewFields.LearningObjectID}]) as Views, [${LearningObjectUserViewFields.LearningObjectID}] `
  query += `FROM [${LearningObjectUserViewsTableName}] `
  if (from && to) {
    request.input('from', from)
    request.input('to', to)
    query += `WHERE [${LearningObjectUserViewFields.CreatedOn}] BETWEEN @from AND @to `
  }
  query += `GROUP BY [${LearningObjectUserViewFields.LearningObjectID}] `
  query += 'ORDER BY Views DESC'

  const res = await request.query<{ Views: number, LearningObjectID: string }>(query)
  return res.recordset
}

async function getLearningObjects (request: Request, ids: string[]): Promise<LearningObjectModel[]> {
  if (ids.length === 0) return []

  const conds = ids.map((id, index) => {
    request.input('id_' + index, id)
    return '@id_' + index
  })

  let query = `SELECT * FROM [${LearningObjectsTableName}] `
  query += `WHERE [${LearningObjectFields.ID}] IN (${conds.join(', ')})`

  const res = await request.query<LearningObject>(query)
  return res.recordset.map(record => new LearningObjectModel(undefined, record))
}
