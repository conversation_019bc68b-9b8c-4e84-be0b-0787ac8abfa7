import mssql, { addRow, <PERSON>_Errors, getRows } from '@lcs/mssql-utility'
import ActivityStreamModel from '../../../models/activity-stream.model.js'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import getLearningObject from '../learning-objects/get.service.js'
import createActivity from '../activity-stream/create.service.js'
import getContextSession from '../learning-context-sessions/get.service.js'
import getContext from '../learning-context/get.service.js'
import updateMultiSessionAssignments from '../user-assigned-multi-session-courses/update-user-context-session-status.service.js'
import updateLearningObjectAssignments from '../user-assigned-learning-objects/update-user-learning-object-status.service.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { getAllContextsThatUseIltSessionAndHaveExamOr<PERSON>ur<PERSON>, getAllContextsThatUseObjectAndHaveExamOrSurvey, getAllContextThatUseExam } from '../learning-context/utils.service.js'
import getContextCompletion from '../../../services/mssql/learning-context/get-completion.service.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { LearnerProgress } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { GradeTypes } from '@tess-f/sql-tables/dist/lms/grade-type.js'
import { User, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { sendFormattedMessage } from '@tess-f/email/dist/amqp/send-formatted-message.js'
import settings from '../../../config/settings.js'
import sendNotification from '../../../services/amqp/notification/send-notification.service.js'
import { getSystemConfig } from '../../amqp/system/get-system-config.service.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

export default async function (learnerProgress: LearnerProgressModel): Promise<LearnerProgressModel> {
  const pool = mssql.getPool()
  // contexts for items that use the exam / learning object / ILT session
  let contexts: LearningContextModel[] = []
  // contexts for items that are incomplete before we save
  const incompleteContexts: LearningContextModel[] = []

  if (learnerProgress.fields.LearningObjectID) {
    contexts = await getAllContextsThatUseObjectAndHaveExamOrSurvey(pool.request(), learnerProgress.fields.LearningObjectID);
    (await getAllContextThatUseExam(pool.request(), learnerProgress.fields.LearningObjectID)).forEach(context => {
      if (!contexts.some(c => c.fields.ID === context.fields.ID)) {
        contexts.push(context)
      }
    })
  } else if (learnerProgress.fields.LearningContextSessionID) {
    contexts = await getAllContextsThatUseIltSessionAndHaveExamOrSurvey(pool.request(), learnerProgress.fields.LearningContextSessionID)
  }

  await Promise.all(contexts.map(async context => {
    const completion = await getContextCompletion(context.fields.ID!, learnerProgress.fields.UserID!, undefined, undefined, false)
    if (completion.completion < 100) {
      incompleteContexts.push(context)
    }
  }))

  const record = await addRow<LearnerProgress>(pool.request(), learnerProgress)
  const created = new LearnerProgressModel(record)

  // create activity stream if the status is more than not attempted and this is a learning object
  if (learnerProgress.fields.LessonStatusID! >= LessonStatuses.browsed && learnerProgress.fields.LearningObjectID) {
    const statusID = created.fields.LessonStatusID!
    let activityID = -1
    if (statusID === LessonStatuses.browsed || statusID === LessonStatuses.incomplete) {
      activityID = Activities.StartedLearningObject
    } else if (statusID === LessonStatuses.fail) {
      activityID = Activities.FailedLearningObject
    } else if (statusID === LessonStatuses.passed) {
      activityID = Activities.PassedLearningObject
    } else if (statusID === LessonStatuses.completed) {
      activityID = Activities.CompletedLearningObject
    }
    if (activityID !== -1) {
      const learningObject = await getLearningObject(learnerProgress.fields.LearningObjectID)

      const activity = new ActivityStreamModel({
        UserID: learnerProgress.fields.UserID,
        LinkID: learnerProgress.fields.LearningObjectID,
        LinkText: learningObject.fields.Title,
        ActivityID: activityID,
        CreatedOn: new Date()
      })
      await createActivity(activity)
    }

    // lets update the assignment status for this learning object
    try {
      await updateLearningObjectAssignments(learnerProgress.fields.UserID!, learnerProgress.fields.LearningObjectID, learnerProgress.fields.LessonStatusID!, created.fields.ID!)
    } catch (error) {
      // If there is no assignment for this learning object move on
      if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
        throw error
      }
    }
  } else if (learnerProgress.fields.LearningContextSessionID && learnerProgress.fields.LessonStatusID! >= LessonStatuses.passed) {
    const session = await getContextSession(learnerProgress.fields.LearningContextSessionID)
    const context = await getContext(session.fields.LearningContextID!, undefined)
    const activity = new ActivityStreamModel({
      UserID: learnerProgress.fields.UserID,
      LinkID: context.fields.ID,
      LinkText: `${context.fields.Label} Course: ${context.fields.Title}`,
      ActivityID: Activities.CompletedContext,
      CreatedOn: new Date()
    })
    await createActivity(activity)

    try {
      await updateMultiSessionAssignments(learnerProgress.fields.UserID!, context.fields.ID!,
        learnerProgress.fields.LessonStatusID!, learnerProgress.fields.LearningContextSessionID,
        created.fields.ID!)
    } catch (error) {
      // if there were no assignments move on
      if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
        throw error
      }
    }
  }

  // check status of each context and see if we need to notify the user about exam or survey
  const user = (await getRows<User>(UserTableName, pool.request(), { ID: learnerProgress.fields.UserID! }))[0]
  const config = await getSystemConfig()
  const lmsConfig = config.Apps.find(app => app.Id === 'lms')

  await Promise.all(incompleteContexts.map(async context => {
    if (context.fields.GradeTypeID === GradeTypes.Exam && context.fields.ExamID !== created.fields.LearningObjectID) {
      const completion = (await getContextCompletion(context.fields.ID!, created.fields.UserID!, undefined, undefined, false)).completion
      if (completion >= 100) {
        // this was incomplete but now the exam is unlocked
        let contextAddress = `${config.Domain}${lmsConfig?.Address ?? '/lms'}/browse/`
        if (context.fields.ContextTypeID === LearningContextTypes.Course) {
          contextAddress += `course/${context.fields.ID}`
        } else {
          contextAddress += `view-context/${context.fields.ID}`
        }
        const bodyMsg = `<p>The exam for <a href="${contextAddress}">${context.fields.Label}: ${context.fields.Title}</a> is now available.</p>`
        await sendFormattedMessage(
          settings.amqp.service_queues.email, {
            to: [user.Email!],
            body: bodyMsg,
            text: `The exam for ${context.fields.Label}: ${context.fields.Title} is now available.`,
            header: 'Exam Ready',
            subject: `${context.fields.Title} Exam`
          },
          settings.amqp.command_timeout
        )
        
        // send notification
        await sendNotification({
          Title: `${context.fields.Title} Exam`,
          Message: bodyMsg,
          PublishDate: new Date(),
          Everyone: false,
          SystemID: 'lms',
          Priority: 1,
          UserIDs: [user.ID!]
        })
      }
    }
    if (context.fields.SurveyURL) {
      const completion = (await getContextCompletion(context.fields.ID!, created.fields.UserID!, undefined, undefined, true)).completion
      if (completion >= 100) {
        // this was incomplete but now it's done and the survey is ready
        const bodyMsg = `
            <p>We want to hear from you. Please fill out the below survey and tell us about your learning experience.</p>
            <p><a href="${context.fields.SurveyURL}">Take our survey</a></p>
          `
        await sendFormattedMessage(
          settings.amqp.service_queues.email, {
            to: [user.Email!],
            body: bodyMsg,
            text: `
            We want to hear from you. Please fill out the below survey and tell us about your learning experience.\n
            ${context.fields.SurveyURL}
          `,
            header: 'We want to hear from you',
            subject: `${context.fields.Title} Survey`
          },
          settings.amqp.command_timeout
        )

        // send notification
        await sendNotification({
          Title: `${context.fields.Title} Survey`,
          Message: bodyMsg,
          PublishDate: new Date(),
          Everyone: false,
          SystemID: 'lms',
          Priority: 1,
          UserIDs: [user.ID!]
        })
      }
    }
  }))

  return created
}
