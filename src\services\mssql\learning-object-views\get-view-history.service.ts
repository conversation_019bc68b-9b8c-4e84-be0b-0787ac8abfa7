import mssql from '@lcs/mssql-utility'
import { LearningObjectUserViewFields, LearningObjectUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-view.js'

export default async function (objectID: string, from?: Date, to?: Date): Promise<{ Views: number, CreatedOn: Date }[]> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('objectID', objectID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const results = await request.query<{ Views: number, CreatedOn: Date }>(`
    SELECT COUNT([${LearningObjectUserViewFields.ID}]) AS Views, CONVERT(date, [${LearningObjectUserViewFields.CreatedOn}]) as CreatedOn
    FROM [${LearningObjectUserViewsTableName}]
    WHERE [${LearningObjectUserViewFields.LearningObjectID}] = @objectID
    ${from && to ? `AND [${LearningObjectUserViewFields.CreatedOn}] BETWEEN @from AND @to` : ''}
    GROUP BY CONVERT(date, [${LearningObjectUserViewFields.CreatedOn}])
  `)

  return results.recordset
}
