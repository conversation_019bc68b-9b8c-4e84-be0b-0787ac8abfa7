import { RequestHand<PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import updateController from './update.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.post('/context-connection', checkClaims([Claims.CREATE_CATALOG_ITEMS, Claims.MODIFY_CATALOG_ITEMS]), createController as RequestHandler)
router.put('/context-connection/:parentContextID/:connectedContextID', checkClaims([Claims.CREATE_CATALOG_ITEMS, Claims.MODIFY_CATALOG_ITEMS]), updateController as RequestHandler)
router.delete('/context-connection/:parentContextID/:connectedContextID', checkClaims([Claims.CREATE_CATALOG_ITEMS, Claims.MODIFY_CATALOG_ITEMS]), deleteController as RequestHandler)

export default router
