import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { LearningObjectKeywordFields, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import { LearningContextKeywordFields, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'
import { UserOverdueAssignmentFields, UserOverdueAssignmentsViewName } from '@tess-f/sql-tables/dist/lms/user-overdue-assignments-view.js'
import { AssignmentFields, AssignmentsTableName } from '@tess-f/sql-tables/dist/lms/assignment.js'
import { UserInProgressAssignmentsViewName, UserInProgressAssignmentFields } from '@tess-f/sql-tables/dist/lms/user-in-progress-assignmnets-view.js'
import { UserNotStartedAssignmentsViewName, UserNotStartedAssignmentFields } from '@tess-f/sql-tables/dist/lms/user-not-started-assignments-view.js'
import { AssignmentLearningObjectFields, AssignmentLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-object.js'
import { LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { AssignmentLearningContextFields, AssignmentLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-context.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'

export default async function searchUserActiveAssignments (userId: string, offset = 0, limit = 10, search?: string, keywords?: string[]): Promise<{ TotalRecords: number, Assignments:{ AssignmentID: string, Title: string, DueDate?: Date }[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('userId', userId)
  request.input('offset', offset)
  request.input('limit', limit)

  let query = `
    SELECT [UserActiveAssignments].[${AssignmentLearningObjectFields.AssignmentID}], [${AssignmentsTableName}].[${AssignmentFields.Title}], [${AssignmentsTableName}].[${AssignmentFields.DueDate}], [TotalRecords] = COUNT(*) OVER()
    FROM (
      SELECT *
      FROM [${UserOverdueAssignmentsViewName}]
      WHERE [${UserOverdueAssignmentFields.UserID}] = @userId
  
      UNION
  
      SELECT *
      FROM [${UserInProgressAssignmentsViewName}]
      WHERE [${UserInProgressAssignmentFields.UserID}] = @userId
  
      UNION
  
      SELECT *
      FROM [${UserNotStartedAssignmentsViewName}]
      WHERE [${UserNotStartedAssignmentFields.UserID}] = @userId
    ) AS [UserActiveAssignments]
    JOIN [${AssignmentsTableName}] ON [${AssignmentsTableName}].[${AssignmentFields.ID}] = [UserActiveAssignments].[${AssignmentLearningObjectFields.AssignmentID}]
    WHERE 1 = 1
  `
  if (search) {
    const whereClause = parseSearchTerms(request, search, [AssignmentFields.Title], 'any')
    query += `
      AND (
        [${AssignmentLearningObjectFields.AssignmentID}] IN (
          SELECT [${AssignmentFields.ID}]
          FROM [${AssignmentsTableName}]
          WHERE ${whereClause}
        )
        OR [${AssignmentLearningObjectFields.AssignmentID}] IN (
          SELECT [${AssignmentLearningObjectFields.AssignmentID}]
          FROM [${AssignmentLearningObjectsTableName}]
          WHERE [${AssignmentLearningObjectFields.LearningObjectID}] IN (
            SELECT [${LearningObjectFields.ID}]
            FROM [${LearningObjectsTableName}]
            WHERE ${whereClause}
          )
        )
        OR [${AssignmentLearningObjectFields.AssignmentID}] IN (
          SELECT [${AssignmentLearningContextFields.AssignmentID}]
          FROM [${AssignmentLearningContextsTableName}]
          WHERE [${AssignmentLearningContextFields.LearningContextID}] IN (
            SELECT [${LearningContextFields.ID}]
            FROM [${LearningContextTableName}]
            WHERE ${whereClause}
          )
        )
      )
    `
  }

  if (keywords && keywords.filter(k => k !== '').length > 0) {
    const conds = keywords.filter(k => k !== '').map((key, index) => {
      request.input(`key_${index}`, key)
      return `@key_${index}`
    })
    query += `
      AND (
        [${AssignmentLearningObjectFields.AssignmentID}] IN (
          SELECT [${AssignmentLearningContextFields.AssignmentID}]
          FROM [${AssignmentLearningContextsTableName}]
          WHERE [${AssignmentLearningContextFields.LearningContextID}] IN (
            SELECT [${LearningContextKeywordFields.LearningContextID}]
            FROM [${LearningContextKeywordsTableName}]
            WHERE [${LearningContextKeywordFields.Keyword}] IN (${conds.join(', ')})
          )
        ) OR [${AssignmentLearningObjectFields.AssignmentID}] IN (
          SELECT [${AssignmentLearningObjectFields.AssignmentID}]
          FROM [${AssignmentLearningObjectsTableName}]
          WHERE [${AssignmentLearningObjectFields.LearningObjectID}] IN (
            SELECT [${LearningObjectKeywordFields.LearningObjectID}]
            FROM [${LearningObjectKeywordsTableName}]
            WHERE [${LearningObjectKeywordFields.Keyword}] IN (${conds.join(', ')})
          )
        )
      )
    `
  }

  query += `
    ORDER BY [${AssignmentFields.Title}] ASC
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<{ TotalRecords: number, AssignmentID: string, Title: string, DueDate?: Date }>(query)

  return {
    TotalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    Assignments: results.recordset
  }
}
