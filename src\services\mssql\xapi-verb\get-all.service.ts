import mssql, { streamQuery } from '@lcs/mssql-utility'
import { xAPIVerb, xAPIVerbsTableName } from '@tess-f/sql-tables/dist/lms/xapi-verb.js'
import XAPIVerbModel from '../../../models/xapi-verb.model.js'

export default async function getXAPIVerbs (): Promise<XAPIVerbModel[]> {
  const request = mssql.getPool().request()
  const records = await streamQuery<xAPIVerb>(request, `SELECT * FROM [${xAPIVerbsTableName}]`)
  return records.map(record => new XAPIVerbModel(record))
}
