import logger from '@lcs/logger'
import * as deleteService from '../../../services/mssql/learning-context/delete.service.js'
import Sinon from 'sinon'
import { expect } from 'chai'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('Controller.AMQP: delete learning contexts', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns unsuccessful when the id is missing from the request', async () => {
    const controller = await esmock('./delete.js')
    const response = await controller({ command: 'test', data: { } })
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('id: Required')
  })

  it('returns unsuccessful when the request id is not a uuid', async () => {
    const controller = await esmock('./delete.js')
    const response = await controller({ command: 'test', data: { id: 'test' } })
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('id: Invalid')
  })

  it('deletes the learning context', async () => {
    const controller = await esmock('./delete.js', {
      '../../../services/mssql/learning-context/delete.service.js': {
        default: Sinon.stub().returns(Promise.resolve())
      }
    })
    const response = await controller({ command: 'test', data: { id: uuid() } })
    expect(response.success).to.be.true
  })

  it('returns unsuccessful when the service has an error', async () => {
    const controller = await esmock('./delete.js', {
      '../../../services/mssql/learning-context/delete.service.js': {
        default: Sinon.stub().returns(Promise.reject(new Error('Service Error')))
      }
    })
    const response = await controller({ command: 'test', data: { id: uuid() } })
    expect(response.success).to.be.false
    expect(response.message).to.equal('Service Error')
    expect(response.error).to.exist
  })
})
