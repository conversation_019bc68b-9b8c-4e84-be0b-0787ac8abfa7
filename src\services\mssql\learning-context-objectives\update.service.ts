import mssql, { updateRow } from '@lcs/mssql-utility'
import LearningContextObjectiveModel from '../../../models/learning-context-objective.model.js'
import { Request } from 'mssql'

export default async function updateLearningContextObjective (contextObjective: LearningContextObjectiveModel, request?: Request): Promise<LearningContextObjectiveModel> {
  if (request === undefined) {
    request = mssql.getPool().request()
  }
  const updated = await updateRow(request, contextObjective, { LearningContextId: contextObjective.fields.LearningContextId, ObjectiveId: contextObjective.fields.ObjectiveId })
  contextObjective.importFromDatabase(updated[0])
  return contextObjective
}
