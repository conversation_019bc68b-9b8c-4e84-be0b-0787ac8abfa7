import mssql, { getRows } from '@lcs/mssql-utility'
import { LearningContextUserBookmark, LearningContextUserBookmarksTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-bookmark.js'
import LearningContextBookmarkModel from '../../../models/learning-context-user-bookmark.model.js'

export default async function getUserBookmarkForContext (contextID: string, userID: string): Promise<LearningContextBookmarkModel> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextUserBookmark>(LearningContextUserBookmarksTableName, pool.request(), { LearningContextID: contextID, UserID: userID })
  return new LearningContextBookmarkModel(records[0])
}

export async function getUserBookmarksForContext (contextID: string): Promise<LearningContextBookmarkModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextUserBookmark>(LearningContextUserBookmarksTableName, pool.request(), { LearningContextID: contextID })
  return records.map(record => new LearningContextBookmarkModel(record))
}

export async function getContextBookmarksForUser (userID: string): Promise<LearningContextBookmarkModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextUserBookmark>(LearningContextUserBookmarksTableName, pool.request(), { UserID: userID })
  return records.map(record => new LearningContextBookmarkModel(record))
}
