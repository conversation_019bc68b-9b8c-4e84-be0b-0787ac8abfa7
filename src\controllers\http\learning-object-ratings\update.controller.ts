import LearningObjectRating, { learningObjectRatingSchema } from '../../../models/learning-object-rating.model.js'
import update from '../../../services/mssql/learning-object-ratings/update.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.update-learning-object-rating', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, change the rating for a learning object (that you have previously rated) within a course. Note the time. Check the LMS API log for the 'http-update-learning-object-rating' label."

  
  try {
    const rating = new LearningObjectRating(learningObjectRatingSchema.parse(req.body))
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    rating.fields.ID = id

    const result = await update(rating)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, change the rating for a learning object (that you have previously rated) within a course. Note the time. Check the LMS API log for the 'http-update-learning-object-rating' label and message indicating a successful update."
    log('info', 'Successfully updated learning object rating.', { success: true, req })

    res.json(result.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update learning object rating: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    }
    else{
    log('error', 'Failed to update learning object rating.', { error, success: false, req })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
