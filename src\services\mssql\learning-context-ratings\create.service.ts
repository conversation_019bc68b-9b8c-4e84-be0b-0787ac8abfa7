import mssql, { addRow } from '@lcs/mssql-utility'
import getContext from '../learning-context/get.service.js'
import createActivity from '../activity-stream/create.service.js'
import ActivityStream from '../../../models/activity-stream.model.js'
import LearningContextRatingModel from '../../../models/learning-context-rating.model.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { LearningContextRating } from '@tess-f/sql-tables/dist/lms/learning-context-rating.js'

export default async function (rating: LearningContextRatingModel): Promise<LearningContextRatingModel> {
  const pool = mssql.getPool()
  // create an activity stream record
  const context = await getContext(rating.fields.LearningContextID!, undefined)
  const activity = new ActivityStream({
    UserID: rating.fields.UserID,
    LinkText: context.fields.Label + ': ' + context.fields.Title,
    LinkID: rating.fields.LearningContextID,
    ActivityID: Activities.RatedContext,
    Rating: rating.fields.Rating,
    CreatedOn: new Date()
  })
  await createActivity(activity)
  const record = await addRow<LearningContextRating>(pool.request(), rating)
  rating.importFromDatabase(record)
  return rating
}
