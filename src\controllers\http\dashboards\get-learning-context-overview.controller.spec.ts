import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'
import LearningContext from '../../../models/learning-context.model.js'

describe('HTTP Controller: get learning context overview', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => sinon.restore())

  it('returns bad request when the id parameter is invalid', async () => {
    const controller = await esmock('./get-learning-context-overview.controller.js')
    const mocks = httpMocks.createMocks({ params: { id: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Invalid')
  })

  it('should get the report even when the query dates are invalid', async () => {
    const controller = await esmock('./get-learning-context-overview.controller.js', {
      '../../../services/mssql/learning-context/get.service.js': { default: sinon.stub().resolves(new LearningContext({
        ID: uuid(),
        Rating: 1
      })) },
      '../../../services/mssql/learning-context-views/get-counts.service.js': { default: sinon.stub().resolves(0) },
      '../../../services/mssql/learning-context/get-completion-counts.service.js': {
        getContextCompletionCounts: sinon.stub().resolves(0),
        getContextInProgressCount: sinon.stub().resolves(0)
      },
      '../../../services/mssql/learning-context-views/get-view-history.service.js': {
        default: sinon.stub().resolves([])
      }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() }, query: { from: 'test', to: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
    expect(data.element).to.exist
    expect(data.ViewHistory).to.exist
    expect(data.ViewHistory).to.be.an('array')
    expect(data.ViewHistory.length).to.equal(0)
  })

  it('should get the report with the given dates', async () => {
    const controller = await esmock('./get-learning-context-overview.controller.js', {
      '../../../services/mssql/learning-context/get.service.js': { default: sinon.stub().resolves(new LearningContext({
        ID: uuid(),
        Rating: 1
      })) },
      '../../../services/mssql/learning-context-views/get-counts.service.js': { default: sinon.stub().resolves(0) },
      '../../../services/mssql/learning-context/get-completion-counts.service.js': {
        getContextCompletionCounts: sinon.stub().resolves(0),
        getContextInProgressCount: sinon.stub().resolves(0)
      },
      '../../../services/mssql/learning-context-views/get-view-history.service.js': {
        default: sinon.stub().resolves([])
      }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() }, query: { from: new Date().toISOString(), to: new Date().toISOString() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
    expect(data.element).to.exist
    expect(data.ViewHistory).to.exist
    expect(data.ViewHistory).to.be.an('array')
    expect(data.ViewHistory.length).to.equal(0)
  })
})
