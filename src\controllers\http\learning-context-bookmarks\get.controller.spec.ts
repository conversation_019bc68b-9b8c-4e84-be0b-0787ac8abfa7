import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextBookmarkModel from '../../../models/learning-context-user-bookmark.model.js'


describe('HTTP Get controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-context-bookmarks/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextBookmarkModel()))
            }
        })

        const mocks = httpMocks.createMocks({
            query: {
                userID: uuid(),
                contextID: uuid(),
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
        
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-context-bookmarks/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextBookmarkModel()))
            }
        })

        const mocks = httpMocks.createMocks({
            query: {
                userID: false,
                contextID: false,
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request query parameter data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('userID')
        expect(mocks.res._getData()).include('contextID')
        
    })

    it('returns internal server error if the request is rejected', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-context-bookmarks/get.service.js': {
                default: Sinon.stub().returns(Promise.reject(new LearningContextBookmarkModel()))
            }
        })

        const mocks = httpMocks.createMocks({
            query: {
                userID: uuid(),
                contextID: uuid(),
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
        
    })
})