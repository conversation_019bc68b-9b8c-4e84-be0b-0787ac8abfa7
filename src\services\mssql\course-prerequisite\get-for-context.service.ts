import mssql, { DB_Errors, getRows } from '@lcs/mssql-utility'
import { CoursePrerequisite, CoursePrerequisitesTableName } from '@tess-f/sql-tables/dist/lms/course-prerequisite.js'
import CoursePrerequisiteModel from '../../../models/course-prerequisite.model.js'
import getAlternativesForPrerequisite from '../course-prerequisite-alternative/get-for-prerequisite.service.js'
import { getErrorMessage } from '@tess-f/backend-utils'

/**
 *
 * @param {string} contextID
 * @returns {CoursePrerequisiteModel[]}
 */
export default async function (contextID: string): Promise<CoursePrerequisiteModel[]> {
  try {
    const pool = mssql.getPool()
    const records = await getRows<CoursePrerequisite>(CoursePrerequisitesTableName, pool.request(), { CourseID: contextID })
    const prerequisites = await Promise.all(records.map(async record => {
      const prerequisite = new CoursePrerequisiteModel()
      prerequisite.importFromDatabase(record)
      prerequisite.fields.Alternatives = await getAlternativesForPrerequisite(prerequisite.fields.ID ?? '')
      return prerequisite
    }))
    return prerequisites
  } catch (error) {
    if (getErrorMessage(error) === DB_Errors.default.NOT_FOUND_IN_DB) {
      return []
    }
    throw error
  }
}
