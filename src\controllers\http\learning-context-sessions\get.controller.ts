import getSession from '../../../services/mssql/learning-context-sessions/get.service.js'
import getInstructors from '../../../services/mssql/learning-context-session-instructors/get.service.js'
import getEnrollments from '../../../services/mssql/session-enrollments/get.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import LearningContextSessionInstructor from '../../../models/learning-context-session-instructor.model.js'
import { DB_Errors } from '@lcs/mssql-utility'
import SessionEnrollment from '../../../models/session-enrollment.model.js'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus
import getMultipleUsersByIds from '../../../services/mssql/users/get-multiple-by-ids.service.js'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-learning-context-session', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const session = await getSession(id)
    let sessionInstructors: LearningContextSessionInstructor[], sessionEnrollments: SessionEnrollment[]

    // STIG V-69425 data access (success)
    log('info', 'Successfully retrieved learning context session.', { success: true, req })

    // get the related records
    try {
      sessionInstructors = await getInstructors(session.fields.ID)
    } catch (error) {
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        sessionInstructors = []
      } else {
        throw error
      }
    }
    try {
      sessionEnrollments = await getEnrollments(session.fields.ID, undefined)
    } catch (error) {
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        sessionEnrollments = []
      } else {
        throw error
      }
    }

    const instructorUserIDs = sessionInstructors.map(record => record.fields.UserID!)

    session.fields.Instructors = await getMultipleUsersByIds(instructorUserIDs)
    session.fields.Enrollments = sessionEnrollments.map(sessionEnrollment => sessionEnrollment.fields)

    // STIG V-69425 data access (success)
    log('info', 'Successfully retrieved learning context session and instructors.', { success: true, req })

    res.json(session.fields)
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning context session: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learning context sessions because they were not found in the database.', { success: false, req })
      res.json([])
    } else {
      log('error', 'Failed to get learning context session(s).', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
