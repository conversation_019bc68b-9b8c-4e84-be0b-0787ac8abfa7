import logger from '@lcs/logger'
import { ConnectionPool } from 'mssql'
import mssql, { addRow } from '@lcs/mssql-utility'
import createActivity from '../activity-stream/create.service.js'
import ActivityStreamModel from '../../../models/activity-stream.model.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObject } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import LearningObjectKeywordModel from '../../../models/learning-object-keyword.model.js'
import { LearningObjectKeyword } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import createLearningObjectObjective from '../learning-object-objectives/create.service.js'
import LearningObjectObjectiveModel from '../../../models/learning-object-objective.model.js'
import { LearningObjectJson } from '@tess-f/lms/dist/common/learning-object.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { createEvaluationConnection } from '../../amqp/evaluation/create-connection.service.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service-MSSQL.Create-Learning-Object')

export default async function (model: LearningObjectModel): Promise<LearningObjectModel> {
  const pool = mssql.getPool()
  const createdObject = await addRow<LearningObject>(pool.request(), model)
  const learningObject = new LearningObjectModel(createdObject)
  if (model.fields.Keywords) {
    const createdKeywords = await createKeywords(pool, createdObject.ID!, model.fields.Keywords)
    learningObject.fields.Keywords = createdKeywords.map(record => record.Keyword!)
  }

  learningObject.fields.ObjectiveIds = await createObjectives(pool, model.fields)

  if (
    learningObject.fields.LearningObjectTypeID === LearningObjectTypes.EVAL_ASSESSMENT ||
    learningObject.fields.LearningObjectTypeID === LearningObjectTypes.EVAL_CHECKLIST ||
    learningObject.fields.LearningObjectTypeID === LearningObjectTypes.EVAL_SURVEY
  ) {
    try {
      // connect the learning object to the evaluation
      await createEvaluationConnection({
        EvaluationId: learningObject.fields.ContentID ?? '',
        ReferenceId: learningObject.fields.ID ?? '',
        ReferenceType: 'Learning Object',
        ReferenceLink: `/browse/view-content/${learningObject.fields.ID}`,
        ReferenceText: learningObject.fields.Title ?? '',
        SystemId: 'lms'
      })
    } catch (e) {
      log('error', 'Failed to connect learning object to evaluation.', { errorMessage: getErrorMessage(e), success: false })
    }
  }

  // create activity stream record
  const activity = new ActivityStreamModel({
    UserID: learningObject.fields.CreatedBy,
    LinkText: learningObject.fields.Title,
    LinkID: learningObject.fields.ID,
    ActivityID: Activities.CreatedLearningObject,
    CreatedOn: new Date()
  })
  await createActivity(activity)
  return learningObject
}

async function createKeywords (pool: ConnectionPool, objectID: string, keywords: string[]): Promise<LearningObjectKeyword[]> {
  const output: LearningObjectKeyword[] = []

  for (const word of keywords) {
    const keyword = new LearningObjectKeywordModel({
      Keyword: word,
      LearningObjectID: objectID
    })

    output.push(await addRow<LearningObjectKeyword>(pool.request(), keyword))
  }

  return output
}

async function createObjectives (pool: ConnectionPool, obj: LearningObjectJson): Promise<string[] | undefined> {
  if (obj.ObjectiveIds === undefined) return undefined

  const output: string[] = []

  for (let i = 0; i < obj.ObjectiveIds.length; i++) {
    const created = await createLearningObjectObjective(
      new LearningObjectObjectiveModel({ LearningObjectId: obj.ID, ObjectiveId: obj.ObjectiveIds[i], OrderId: i + 1 }),
      obj.Title ?? 'Learning Content',
      obj.LearningObjectTypeID ?? -1,
      pool.request()
    )

    output.push(created.fields.ObjectiveId!)
  }

  return output
}
