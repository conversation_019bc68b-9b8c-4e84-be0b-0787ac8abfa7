import del from '../../../services/mssql/locations/delete.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.delete-location', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, un-bookmark a course. Note the time. Check the LMS API log for the 'http-delete-learning-context-bookmark' label."
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const numRowsAffected = await del(id)

    if (numRowsAffected > 0) {
      // STIG V-69427 changing data (success)
      log('info', 'Successfully deleted location', { id, success: true, req })

      res.sendStatus(httpStatus.NO_CONTENT)
    } else {
      // STIG V-69427 changing data (failure)
      log('info', 'Failed to delete location because it was not found in the database.', { id: req.params.id, success: false, req })

      res.sendStatus(httpStatus.NOT_FOUND)
    }
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete location: input validation error', { success: false, req, error })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter, '))
    } else {
      log('error', 'Failed to delete location.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
