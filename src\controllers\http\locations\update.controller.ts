import { Request, Response } from 'express'
import Location, { updateLocationSchema } from '../../../models/location.model.js'
import updateLocation from '../../../services/mssql/locations/update.service.js'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'
const { INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.update-location', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, while creating a new course, enter a Course Title and Course Description and then click in another field. Note the time. Check the LMS API log for the 'http-update-learning-context' label."

  try {
    const location = new Location(updateLocationSchema.parse(req.body))
    const { ID } = z.object({ ID: zodGUID }).parse(req.body) // FIXME: this should come from the request params and not the body
    location.fields.ID = ID
    location.fields.ModifiedBy = req.session.userId
    location.fields.ModifiedOn = new Date()

    const updatedLocation = await updateLocation(location)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, while creating a new course, enter a Course Title and Course Description and then click in another field. Note the time. Check the LMS API log for the 'http-update-learning-context' label and message indicating a successful update."
    log('info', 'Successfully updated location', { locationId: updatedLocation.fields.ID, success: true, req })

    res.json(updatedLocation.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update location: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to update location.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
