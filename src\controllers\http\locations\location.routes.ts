import { RequestHandler, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getPaginatedController from './get-paginated.controller.js'
import getController from './get.controller.js'
import updateController from './update.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.post('/location', checkClaims([Claims.CREATE_COURSE, Claims.MODIFY_COURSE]), createController as RequestHandler)
router.put('/location', checkClaims([Claims.MODIFY_COURSE, Claims.CREATE_COURSE]), updateController as RequestHandler)
router.get('/locations', getPaginatedController as RequestHandler)
router.get('/location/:id', getController as RequestHandler)
router.delete('/location/:id', checkClaims([Claims.CREATE_COURSE, Claims.MODIFY_COURSE]), deleteController as RequestHandler)

export default router

//TODO Finish adding claim checks
