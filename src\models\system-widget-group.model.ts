import { Table } from '@lcs/mssql-utility'
import { SystemWidgetGroup, SystemWidgetGroupFields, SystemWidgetGroupsTableName } from '@tess-f/sql-tables/dist/lms/system-widget-group.js'

export default class SystemWidgetGroupModel extends Table<SystemWidgetGroup, SystemWidgetGroup> {
  public fields: SystemWidgetGroup

  constructor (fields?: SystemWidgetGroup) {
    super(SystemWidgetGroupsTableName, [
      SystemWidgetGroupFields.SystemWidgetID,
      SystemWidgetGroupFields.GroupID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: SystemWidgetGroup): void {
    this.fields = record
  }

  public exportJsonToDatabase (): SystemWidgetGroup {
    return this.fields
  }
}
