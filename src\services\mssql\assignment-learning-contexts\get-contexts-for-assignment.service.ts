import mssql from '@lcs/mssql-utility'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { AssignmentLearningContextFields, AssignmentLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-context.js'

export default async function getLearningContextsForAssignment (assignmentId: string): Promise<LearningContextModel[]> {
  const request = mssql.getPool().request()
  request.input('assignmentId', assignmentId)
  const results = await request.query<LearningContext>(`
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ID}] IN (
      SELECT [${AssignmentLearningContextFields.LearningContextID}]
      FROM [${AssignmentLearningContextsTableName}]
      WHERE [${AssignmentLearningContextFields.AssignmentID}] = @assignmentId
    )
  `)
  return results.recordset.map(record => new LearningContextModel(undefined, record))
}
