import mssql from '@lcs/mssql-utility'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { FlatLearningContextTreeFields, FlatLearningContextTreeViewName } from '@tess-f/sql-tables/dist/lms/flat-learning-context-view.js'

export default async function (contextID?: string): Promise<{ ID: string, nodePath: string}[]> {
  const pool = mssql.getPool()
  const request = pool.request()
  if (contextID) {
    request.input('contextID', contextID)
  }
  let query = `
    WITH Context_Hierarchy AS (
      SELECT [${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeFields.Label}], [${FlatLearningContextTreeFields.Title}], [${FlatLearningContextTreeFields.ParentContextID}],
        CAST([${FlatLearningContextTreeFields.Label}] + ': ' + [${FlatLearningContextTreeFields.Title}] AS nvarchar(MAX)) AS nodePath
      FROM [${FlatLearningContextTreeViewName}]
      WHERE [${FlatLearningContextTreeFields.ParentContextID}] IS NULL
      AND (
        [${FlatLearningContextTreeFields.ContextTypeID}] = ${LearningContextTypes.Section}
        OR [${FlatLearningContextTreeFields.ContextTypeID}] = ${LearningContextTypes.ElectiveOptional}
      )
  
      UNION ALL
  
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.Label}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.Title}],
        [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}],
        CAST([Context_Hierarchy].[nodePath] + CAST(' -> ' + [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.Label}] + ': ' + [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.Title}] AS nvarchar(MAX)) AS nvarchar(MAX)) AS nodePath
      FROM [${FlatLearningContextTreeViewName}], [Context_Hierarchy]
      WHERE [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}] = [Context_Hierarchy].[${FlatLearningContextTreeFields.ID}]
      AND (
        [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ContextTypeID}] = ${LearningContextTypes.Section}
        OR [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ContextTypeID}] = ${LearningContextTypes.ElectiveOptional}
      )
    )
  `

  if (contextID) {
    query += `
      , Child_Nodes AS (
        SELECT [${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeFields.Label}], [${FlatLearningContextTreeFields.Title}], [${FlatLearningContextTreeFields.ParentContextID}]
        FROM [${FlatLearningContextTreeViewName}]
        WHERE [${FlatLearningContextTreeFields.ID}] = @contextID
        
        UNION ALL
        
        SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.Label}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.Title}],
          [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}]
        FROM [${FlatLearningContextTreeViewName}]
          INNER JOIN [Child_Nodes] ON [Child_Nodes].[${FlatLearningContextTreeFields.ID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}]
        WHERE [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ContextTypeID}] = ${LearningContextTypes.Section} OR [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ContextTypeID}] = ${LearningContextTypes.ElectiveOptional}
      )
    `
  }

  query += `
    SELECT [${FlatLearningContextTreeFields.ID}], [nodePath], [${FlatLearningContextTreeFields.Label}], [${FlatLearningContextTreeFields.Title}]
    FROM [Context_Hierarchy]
  `

  if (contextID) {
    query += `
      WHERE [${FlatLearningContextTreeFields.ID}] NOT IN (
        SELECT [${FlatLearningContextTreeFields.ID}]
        FROM [Child_Nodes]
      )
    `
  }

  query += 'ORDER BY [nodePath]'

  const results = await request.query<{ ID: string, nodePath: string }>(query)
  return results.recordset
}
