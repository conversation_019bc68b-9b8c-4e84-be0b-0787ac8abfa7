import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import getMultiple from './get-multiple.service.js'
import deleteLearningContext from '../learning-context/delete.service.js'
import deleteAssignment from '../assignments/delete.service.js'
import { Assignment } from '@tess-f/sql-tables/dist/lms/assignment.js'
import AssignmentModel from '../../../models/assignment.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { v4 as uuidv4 } from 'uuid'
import { AssignmentLearningContext, AssignmentLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-context.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import AssignmentLearningContextsModel from '../../../models/assignment-learning-context.model.js'
import { LearningContext } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

let assignment: Assignment
let learningContext: LearningContext
let assignmentLearningContext: AssignmentLearningContext

describe('MSSQL Assignment Learning Contexts', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    assignment = await addRow<Assignment>(pool.request(), new AssignmentModel({
      Title: 'Testing assignment learning contexts',
      TypeID: 1,
      Everyone: true,
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    }))
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test Assignment Learning Context',
      Description: `Running Assignment Learning Context on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Collection
    }))
    assignmentLearningContext = await addRow<AssignmentLearningContext>(pool.request(), new AssignmentLearningContextsModel({
      AssignmentID: assignment.ID,
      LearningContextID: learningContext.ID,
      OrderID: 1,
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    }))
  })

  it('gets multiple assignment learning context records by assignment ID', async () => {
    const contexts = await getMultiple(assignment.ID!)
    expect(contexts.length).to.be.gte(1)
  })

  it('should fail to get multiple assignment learning context records with fake ID', async () => {
    try {
      const contexts = await getMultiple(uuidv4())
      expect(contexts.length).to.be.eq(0)
    } catch (error) {
      expect(error).to.exist
    }
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<AssignmentLearningContext>(
      pool.request(),
      AssignmentLearningContextsTableName,
      {
        AssignmentID: assignmentLearningContext.AssignmentID,
        LearningContextID: assignmentLearningContext.LearningContextID
      }
    )
    await deleteLearningContext(learningContext.ID!)
    await deleteAssignment(assignment.ID!)
  })
})
