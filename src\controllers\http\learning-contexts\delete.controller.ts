import del from '../../../services/mssql/learning-context/delete.service.js'
import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import get from '../../../services/mssql/learning-context/get.service.js'
import { Request, Response } from 'express'
import { byConnectedContextID, ContextConnection } from '../../../services/mssql/learning-context-connections/get.service.js'
import removeContextConnection from '../../../services/mssql/learning-context-connections/delete.service.js'
import resetParentService from '../../../services/mssql/learning-context/reset-parent.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { DeleteFiles } from '@tess-f/fds/dist/amqp/delete.js'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const { INTERNAL_SERVER_ERROR, NO_CONTENT, BAD_REQUEST } = httpStatus
const log = logger.create('Controller-HTTP.delete-learning-contexts', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, delete a course via Course Management. Note the time. Check the LMS API log for the 'http-delete-learning-contexts' label."
  try {
    const { parentId } = z.object({ parentId: zodGUID.optional() }).parse(req.query)
    const { id } = z.object({ id: zodGUID }).parse(req.params)

    let connectedContexts: ContextConnection[]
    try {
      connectedContexts = (await byConnectedContextID(id)).filter(c => c.ParentContextID !== parentId)
    } catch (error) {
      if (getErrorMessage(error) !== dbErrors.default.NOT_FOUND_IN_DB) {
        throw error
      }
      connectedContexts = []
    }

    const learningContext = await get(id, req.session.userId)

    // if this context is used in another context let's just re parent it
    if (connectedContexts.length > 0 && parentId) {
      log('verbose', 'This context is in use by other contexts, removing the connection', { useCount: connectedContexts.length, req })
      // this context is in use by other contexts
      // let's remove it from the given parent
      if (learningContext.fields.ParentContextID === parentId) {
        // we need to change the parent of this context
        await resetParentService(learningContext, connectedContexts[0].ParentContextID, connectedContexts[0].OrderID)
        log('info', 'Reset the parent of the context', { newParentID: connectedContexts[0].ParentContextID, newOrderID: connectedContexts[0].OrderID, success: true, req })
        res.sendStatus(NO_CONTENT)
      } else {
        // we just need to remove the connection to this parent
        await removeContextConnection(parentId, id)
        log('info', 'Successfully removed the parent connection to the context', { success: true, req })
        res.sendStatus(NO_CONTENT)
      }
    } else {
      if (learningContext.fields.Image) {
        try {
          await DeleteFiles(settings.amqp.service_queues.fds, {
            fileIds: [learningContext.fields.Image]
          }, settings.amqp.rpc_timeout)
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          log('error', 'Error executing RPC call', { errorMessage, success: false, req })
        }
      }

      await del(id)

      // STIG V-69427 changing data (success)
      // STIGTEST "In the LMS application, delete a course via Course Management. Note the time. Check the LMS API log for the 'http-delete-learning-contexts' label and message indicating successful deletion."
      log('info', 'Successfully deleted learning context', { learningContextId: id, success: true, req })

      res.sendStatus(NO_CONTENT)
    }
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete learning context: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else {
      log('error', 'Failed to delete learning context', { learningContextId: req.params.id, error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
