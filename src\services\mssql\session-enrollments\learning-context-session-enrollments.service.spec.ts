import { expect } from 'chai'
import mssqlConnection from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import get from './get.service.js'
import create from './create.service.js'
import createContext from '../learning-context/create.service.js'
import del from './delete.service.js'
import update from './update.service.js'
import SessionEnrollment from '../../../models/session-enrollment.model.js'
import createSession from '../learning-context-sessions/create.service.js'
import deleteSession from '../learning-context-sessions/delete.service.js'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'

let learningContext: LearningContextModel

let session: LearningContextSessionModel
let sessionEnrollment: SessionEnrollment

describe('MSSQL Learning Context Session Enrollments', () => {
  before('Connect to SQL', async () => {
    await mssqlConnection.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    // create course.
    learningContext = (await createContext(new LearningContextModel({
      Title: 'Test learning context session enrollments',
      Description: 'Running learning context session enrollments unit test',
      ContextTypeID: LearningContextTypes.Course,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false
    })))
    session = await createSession(new LearningContextSessionModel({
      SessionID: 'Service-Test-1',
      DisplayEnrollmentsOnHomePage: false,
      SessionStatusID: 1,
      CreatedOn: new Date(),
      CreatedBy: AdminUserId,
      LearningContextID: learningContext.fields.ID,
      Timezone: 'MST',
      StartDate: new Date(),
      EndDate: new Date()
    }))
    sessionEnrollment = new SessionEnrollment({
      SessionID: session.fields.ID,
      UserID: AdminUserId,
      CreatedOn: new Date(),
      CreatedBy: AdminUserId,
      Waitlisted: true
    })
  })

  it('creates a session enrollment', async () => {
    const created = await create(sessionEnrollment)

    expect(created.fields.SessionID!.toLowerCase()).to.equal(session.fields.ID!.toLowerCase())
    expect(created.fields.UserID!.toLowerCase()).to.equal(AdminUserId.toLowerCase())
    sessionEnrollment = created
  })

  it('gets a list of enrollments for a session', async () => {
    const enrollments = await get(session.fields.ID!, undefined)
    expect(enrollments.length).to.be.gte(1)
    for (let i = 0; i < enrollments.length; i++) {
      expect(enrollments[i].fields.SessionID!.toLowerCase()).to.equal(session.fields.ID!.toLowerCase())
    }
  })

  it('gets a list of enrollments for a user', async () => {
    const enrollments = await get(undefined, AdminUserId)
    expect(enrollments.length).to.be.gte(1)
    for (let i = 0; i < enrollments.length; i++) {
      expect(enrollments[i].fields.UserID!.toLowerCase()).to.equal(AdminUserId.toLowerCase())
    }
  })

  it('get user enrollment for a session', async () => {
    const enrollments = await get(session.fields.ID, AdminUserId)
    expect(enrollments.length).to.equal(1)
    expect(enrollments[0].fields.UserID!.toLowerCase()).to.equal(AdminUserId.toLowerCase())
    expect(enrollments[0].fields.SessionID!.toLowerCase()).to.equal(session.fields.ID!.toLowerCase())
  })

  it('should fail creating the same session/user combo', async () => {
    try {
      await create(sessionEnrollment)
      throw new Error('Created a duplicate user enrollment')
    } catch (error) {};
  })

  it('should update a session enrollment', async () => {
    sessionEnrollment.fields.Waitlisted = false
    const updated = await update(sessionEnrollment)
    expect(updated.fields.Waitlisted).to.be.false
  })

  it('deletes the user learning object bookmark', async () => {
    await del(session.fields.ID!, AdminUserId)
  })

  after(async () => {
    await deleteSession(session.fields.ID!)
  })
})
