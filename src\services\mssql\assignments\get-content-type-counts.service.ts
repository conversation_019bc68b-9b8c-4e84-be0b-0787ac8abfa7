import mssql from '@lcs/mssql-utility'
import { AssignmentLearningContextFields, AssignmentLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-context.js'
import { AssignmentLearningObjectFields, AssignmentLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-object.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'

/**
 * @param {Date} from
 * @param {Date} to
 * @returns {Array<{
 *  LearningObjectTypeID: number,
 *  ContextTypeID: number,
 *  UseCount: number
 * }>}
 */
export default async function (from?: Date, to?: Date): Promise<{ LearningObjectTypeID: number | null, ContextTypeID: number | null, UseCount: number }[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  const query = `
  SELECT [${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}], [${LearningContextFields.ContextTypeID}] = NULL, COUNT([${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}]) AS UseCount
  FROM [${AssignmentLearningObjectsTableName}]
  INNER JOIN [${LearningObjectsTableName}] ON [${LearningObjectsTableName}].[${LearningObjectFields.ID}] = [${AssignmentLearningObjectsTableName}].[${AssignmentLearningObjectFields.LearningObjectID}]
  ${from && to ? `WHERE [${AssignmentLearningObjectsTableName}].[${AssignmentLearningObjectFields.CreatedOn}] BETWEEN @from AND @to` : ''}
  GROUP BY [${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}]
  UNION ALL
  SELECT [${LearningObjectFields.LearningObjectTypeID}] = NULL, [${LearningContextTableName}].[${LearningContextFields.ContextTypeID}], COUNT([${LearningContextTableName}].[${LearningContextFields.ContextTypeID}]) AS UseCount
  FROM [${AssignmentLearningContextsTableName}]
  INNER JOIN [${LearningContextTableName}] ON [${LearningContextTableName}].[${LearningContextFields.ID}] = [${AssignmentLearningContextsTableName}].[${AssignmentLearningContextFields.LearningContextID}]
  ${from && to ? `WHERE [${AssignmentLearningContextsTableName}].[${AssignmentLearningContextFields.CreatedOn}] BETWEEN @from AND @to` : ''}
  GROUP BY [${LearningContextTableName}].[${LearningContextFields.ContextTypeID}]
 `

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const results = await request.query<{ LearningObjectTypeID: number | null, ContextTypeID: number | null, UseCount: number }>(query)

  return results.recordset
}
