import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextSessionModel from '../../../models/learning-context-session.model'

describe('HTTP delete controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./delete.controller', {
            '../../../services/mssql/session-enrollments/delete.service.js': {
                default: Sinon.stub().returns(Promise.resolve(1))
            },
            '../../../services/mssql/learning-context-sessions/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionModel({ ID: uuid() })))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../../../services/mssql/learning-context-sessions/get.service.js': {
                default: Sinon.stub().resolves(new LearningContextSessionModel())
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid(),
                sessionID: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.NO_CONTENT)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./delete.controller', {
            '../../../services/mssql/session-enrollments/delete.service.js': {
                default: Sinon.stub().returns(Promise.resolve(1))
            },
            '../../../services/mssql/learning-context-sessions/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionModel({ ID: uuid() })))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../../../services/mssql/learning-context-sessions/get.service.js': {
                default: Sinon.stub().resolves(new LearningContextSessionModel())
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: false,
                sessionID: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).to.include('Invalid request parameters')
        expect(data).to.include('userID')
        expect(data).to.include('sessionID')
        expect(data).to.include('Expected string, received boolean')
    })

    
    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./delete.controller', {
            '../../../services/mssql/session-enrollments/delete.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(1))
            },
            '../../../services/mssql/learning-context-sessions/update.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new LearningContextSessionModel({ ID: uuid() })))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().rejects([])
            },
            '../../../services/mssql/learning-context-sessions/get.service.js': {
                default: Sinon.stub().rejects(new LearningContextSessionModel())
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid(),
                sessionID: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

})