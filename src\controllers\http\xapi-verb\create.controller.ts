import { Request, Response } from 'express'
import logger from '@lcs/logger'
import create from '../../../services/mssql/xapi-verb/create.service.js'
import XAPIVerbModel, { xapiVerbSchema } from '../../../models/xapi-verb.model.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { ZodError } from 'zod'

const log = logger.create('Controller-HTTP.create-xapi-verb', httpLogTransformer)

export default async function (req: Request, res: Response): Promise<void> {
  try {
    const verb = new XAPIVerbModel(xapiVerbSchema.parse(req.body))
    const created = await create(verb)
    log('info', 'Successfully created xapi verb', { verbID: created.fields.Id, success: true, req })
    res.json(created.fields)
  } catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Failed to create xapi verb: validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to create xapi verb', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
