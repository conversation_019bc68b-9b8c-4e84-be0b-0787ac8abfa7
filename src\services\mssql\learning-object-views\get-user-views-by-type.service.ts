import mssql from '@lcs/mssql-utility'
import { LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningObjectUserViewFields, LearningObjectUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-view.js'
import { UserLearningObjectViewsByTypeViewFields, UserLearningObjectViewsByTypeViewName } from '@tess-f/sql-tables/dist/lms/user-learning-object-views-by-type-view.js'
import { Request } from 'mssql'

export default async function (id: string, from?: Date, to?: Date) {
  const pool = mssql.getPool()
  return await getUserViewsByType(pool.request(), id, from, to)
}

async function getUserViewsByType (request: Request, id: string, from?: Date, to?: Date): Promise<{ Views: number, UserID: string, LearningObjectTypeID: number }[]> {
  request.input('id', id)

  let query = ''
  if (from && to) {
    request.input('from', from)
    request.input('to', to)
    query = `
      SELECT COUNT([${LearningObjectUserViewsTableName}].[${LearningObjectUserViewFields.LearningObjectID}]) AS Views,
      [${LearningObjectUserViewsTableName}].[${LearningObjectUserViewFields.UserID}],
      [${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}]
      FROM [${LearningObjectUserViewsTableName}] INNER JOIN
        [${LearningObjectsTableName}] ON [${LearningObjectsTableName}].[${LearningObjectFields.ID}] = [${LearningObjectUserViewsTableName}].[${LearningObjectUserViewFields.LearningObjectID}]
      WHERE [${LearningObjectUserViewsTableName}].[${LearningObjectUserViewFields.CreatedOn}] BETWEEN @from AND @to
      AND [${LearningObjectUserViewsTableName}].[${LearningObjectUserViewFields.UserID}] = @id
      GROUP BY [${LearningObjectUserViewsTableName}].[${LearningObjectUserViewFields.UserID}], [${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}]
    `
  } else {
    query = `SELECT * FROM [${UserLearningObjectViewsByTypeViewName}] WHERE [${UserLearningObjectViewsByTypeViewFields.UserID}] = @id`
  }

  const result = await request.query<{ Views: number, UserID: string, LearningObjectTypeID: number }>(query)
  return result.recordset
}
