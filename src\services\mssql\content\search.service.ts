import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { LearningElement, LearningElementsViewFields, LearningElementsViewName } from '@tess-f/sql-tables/dist/lms/learning-elements-view.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import LearningElementModel from '../../../models/learning-elements-view.model.js'
import { isValidString, isSortDirectionValid } from '@tess-f/backend-utils/validators'
import getCompletedContextsService from '../my-learning/get-completed-contexts.service.js'
import getCompletedObjectsService from '../my-learning/get-completed-objects.service.js'
import getInprogressContextService from '../my-learning/get-inprogress-context.service.js'
import getInprogressObjectsService from '../my-learning/get-inprogress-objects.service.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextKeywordFields, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'
import { LearningObjectKeywordFields, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { SessionEnrollmentFields, SessionEnrollmentsTableName } from '@tess-f/sql-tables/dist/lms/session-enrollment.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningContextUserFavoriteFields, LearningContextUserFavoritesTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-favorite.js'
import { LearningObjectUserFavoriteFields, LearningObjectUserFavoritesTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-favorite.js'
import { LearningContextUserBookmarkFields, LearningContextUserBookmarksTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-bookmark.js'
import { LearningObjectUserBookmarkFields, LearningObjectUserBookmarksTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-bookmark.js'
import { UserAssignedMultiSessionCoursesFields, UserAssignedMultiSessionCoursesTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-multi-session-course.js'
import { UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'

/**
 * Searches learning elements
 *      Only returns published contexts
 *      Only returns browsable learning objects
 * @param {number} offset - the number of rows to offset the query by
 * @param {number} limit - the number of records to limit the query to
 * @param {string} search - the terms to search for ( title, description )
 * @param {{
 *   contextTypeIds: number[],
 *   objectTypeIds: number[],
 *   keywords: string[],
 *   sortColumn: string,
 *   sortDirection: string
 * }} filters - query filters
 * @returns {{ totalRecords: number, elements: LearningElementModel[] }}
 */
export default async function (offset: number = 0, limit: number = 10, search?: string, filters?: { labels?: string[], objectTypeIds?: number[], keywords?: string[], myLearning?: string, userID?: string }, allContent?: boolean, sortColumn?: string, sortDirection?: 'ASC' | 'DESC'): Promise<{ totalRecords: number, elements: LearningElementModel[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  // build query
  let query = `SELECT *, TotalRecords = COUNT(*) OVER() FROM [${LearningElementsViewName}] WHERE 1 = 1 `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [LearningElementsViewFields.Title, LearningElementsViewFields.Description], 'any')}) `
  }

  if (filters) {
    if (filters.labels && filters.labels.length > 0 &&
            filters.objectTypeIds && filters.objectTypeIds.length > 0) {
      // filter the query to learning objects and learning contexts of the given type
      // this will require an OR inside an AND statement to capture both
      const conds = filters.labels.map((label, index) => {
        request.input('label_' + index, label)
        return '@label_' + index
      })
      query += `
                AND (
                    [${LearningElementsViewFields.LearningObjectTypeID}] IN (${filters.objectTypeIds.join(', ')}) OR
                    [${LearningElementsViewFields.ID}] IN (
                      SELECT [${LearningContextFields.ID}]
                      FROM [${LearningContextTableName}]
                      WHERE [${LearningContextFields.Label}] IN (
                        ${conds.join(', ')}
                      )
                    )
                )
            `
    } else if (filters.labels && filters.labels.length > 0) {
      const conds = filters.labels.map((label, index) => {
        request.input('label_' + index, label)
        return '@label_' + index
      })
      query += `AND ([${LearningElementsViewFields.ID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [${LearningContextTableName}]
        WHERE [${LearningContextFields.Label}] IN (
          ${conds.join(', ')}
        )
      )) `
    } else if (filters.objectTypeIds && filters.objectTypeIds.length > 0) {
      query += `AND ([${LearningElementsViewFields.LearningObjectTypeID}] IN (${filters.objectTypeIds.join(', ')})) `
    }

    if (filters.keywords && filters.keywords.length > 0) {
      const conds = filters.keywords.map((key, index) => {
        request.input('key_' + index, key)
        return '@key_' + index
      })
      query += `
                AND ([${LearningElementsViewFields.ID}] IN (
                    SELECT [${LearningContextKeywordFields.LearningContextID}]
                    FROM [${LearningContextKeywordsTableName}]
                    WHERE [${LearningContextKeywordFields.Keyword}] IN (
                      ${conds.join(', ')}
                    )
                ) OR [${LearningElementsViewFields.ID}] IN (
                    SELECT [${LearningObjectKeywordFields.LearningObjectID}]
                    FROM [${LearningObjectKeywordsTableName}]
                    WHERE [${LearningObjectKeywordFields.Keyword}] IN (
                      ${conds.join(', ')}
                    )
                ))
            `
    }
  }

  if (filters?.myLearning && filters.userID) {
    // now we need to add the my learning data to the query
    request.input('userID', filters.userID)
    switch (filters.myLearning.toLowerCase()) {
      case 'enrolled':
        // get courses that I am enrolled in past, future, and present
        // that I have not finished (an instructor has not marked me as complete and or I have not taken the exam yet)
        query += `
        AND ([${LearningElementsViewFields.ID}] IN (
          SELECT [${LearningContextSessionFields.LearningContextID}]
          FROM [${LearningContextSessionsTableName}]
          WHERE [${LearningContextSessionFields.ID}] IN (
            SELECT [${SessionEnrollmentFields.SessionID}]
            FROM [${SessionEnrollmentsTableName}]
            WHERE [${SessionEnrollmentFields.UserID}] = @userID
          ) AND [${LearningContextSessionFields.ID}] NOT IN (
            SELECT [${LearnerProgressFields.LearningContextSessionID}]
            FROM [${LearnerProgressTableName}]
            WHERE [${LearnerProgressFields.UserID}] = @userID
            AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.fail}
            AND [${LearnerProgressFields.LearningContextSessionID}] IS NOT NULL
          )
        ))
      `
        break
      case 'favorites':
        query += `
        AND ([${LearningElementsViewFields.ID}] IN (
          SELECT [${LearningContextUserFavoriteFields.LearningContextID}]
          FROM [${LearningContextUserFavoritesTableName}]
          WHERE [${LearningContextUserFavoriteFields.UserID}] = @userID
        ) OR [${LearningElementsViewFields.ID}] IN (
          SELECT [${LearningObjectUserFavoriteFields.LearningObjectID}]
          FROM [${LearningObjectUserFavoritesTableName}]
          WHERE [${LearningObjectUserFavoriteFields.UserID}] = @userID
        ))
      `
        break
      case 'my plan':
        query += `
        AND ([${LearningElementsViewFields.ID}] IN (
          SELECT [${LearningContextUserBookmarkFields.LearningContextID}]
          FROM [${LearningContextUserBookmarksTableName}]
          WHERE [${LearningContextUserBookmarkFields.UserID}] = @userID
        ) OR [${LearningElementsViewFields.ID}] IN (
          SELECT [${LearningObjectUserBookmarkFields.LearningObjectID}]
          FROM [${LearningObjectUserBookmarksTableName}]
          WHERE [${LearningObjectUserBookmarkFields.UserID}] = @userID
        ))
      `
        break
      case 'in progress': {
        const ids = (await getInprogressContextService(filters.userID)).map(context => context.fields.ID!).concat(
          (await getInprogressObjectsService(filters.userID)).map(obj => obj.fields.ID!)
        ).map((id, index) => {
          request.input(`in_progress_${index}`, id)
          return `@in_progress_${index}`
        })
        if (ids.length <= 0) {
          // if the user is filtering for in progress an none are found return empty
          return {
            totalRecords: 0,
            elements: []
          }
        }
        query += `
          AND ([${LearningElementsViewFields.ID}] IN (${ids.join(', ')}))
        `
        break
      }
      case 'assigned':
        query += `
        AND ([${LearningElementsViewFields.ID}] IN (
          SELECT [${UserAssignedMultiSessionCoursesFields.ForContextExamID}]
          FROM [${UserAssignedMultiSessionCoursesTableName}]
          WHERE [${UserAssignedMultiSessionCoursesFields.UserID}] = @userID
          AND [${UserAssignedMultiSessionCoursesFields.LessonStatusID}] < ${LessonStatuses.fail}
          AND [${UserAssignedMultiSessionCoursesFields.Deleted}] = 0
          AND [${UserAssignedMultiSessionCoursesFields.Completed}] = 0
          AND [${UserAssignedMultiSessionCoursesFields.ForContextID}] IS NOT NULL
        ) OR [${LearningElementsViewFields.ID}] IN (
          SELECT [${UserAssignedMultiSessionCoursesFields.LearningContextID}]
          FROM [${UserAssignedMultiSessionCoursesTableName}]
          WHERE [${UserAssignedMultiSessionCoursesFields.UserID}] = @userID
          AND [${UserAssignedMultiSessionCoursesFields.LessonStatusID}] < ${LessonStatuses.fail}
          AND [${UserAssignedMultiSessionCoursesFields.Deleted}] = 0
          AND [${UserAssignedMultiSessionCoursesFields.Completed}] = 0
          AND [${UserAssignedMultiSessionCoursesFields.ForContextID}] IS NULL
        ) OR [${LearningElementsViewFields.ID}] IN (
          SELECT [${UserAssignedLearningObjectFields.LearningObjectID}]
          FROM [${UserAssignedLearningObjectsTableName}]
          WHERE [${UserAssignedLearningObjectFields.UserID}] = @userID
          AND [${UserAssignedLearningObjectFields.LessonStatusID}] < ${LessonStatuses.fail}
          AND [${UserAssignedLearningObjectFields.ForContextID}] IS NULL
          AND [${UserAssignedLearningObjectFields.Deleted}] = 0
          AND [${UserAssignedLearningObjectFields.Completed}] = 0
        ) OR [${LearningElementsViewFields.ID}] IN (
          SELECT [${UserAssignedLearningObjectFields.ForContextID}]
          FROM [${UserAssignedLearningObjectsTableName}]
          WHERE [${UserAssignedLearningObjectFields.UserID}] = @userID
          AND [${UserAssignedLearningObjectFields.LessonStatusID}] < ${LessonStatuses.fail}
          AND [${UserAssignedLearningObjectFields.ForContextID}] IS NOT NULL
          AND [${UserAssignedLearningObjectFields.Deleted}] = 0
          AND [${UserAssignedLearningObjectFields.Completed}] = 0
        ))
      `
        break
      case 'completed': {
        const ids = (await getCompletedContextsService(filters.userID)).map(context => context.fields.ID!).concat(
          (await getCompletedObjectsService(filters.userID)).map(obj => obj.fields.ID!)
        ).map((id, index) => {
          request.input(`completed_${index}`, id)
          return `@completed_${index}`
        })
        if (ids.length <= 0) {
          // if the user filtering for completed and none are found return empty
          return {
            totalRecords: 0,
            elements: []
          }
        }
        query += `
          AND ([${LearningElementsViewFields.ID}] IN (${ids.join(', ')}))
        `
        break
      }
    }
  } else if (!allContent) {
    // we only want browseable content
    query += `AND ([${LearningElementsViewFields.VisibilityID}] = ${Visibilities.Browsable} OR [${LearningElementsViewFields.VisibilityID}] = ${Visibilities.Obsolete}) `
  }

  // add offset and limit to query
  request.input('offset', offset)
  request.input('limit', limit)

  const isSortColumnValid = sortColumn !== undefined &&
    isValidString(sortColumn, [LearningElementsViewFields.CreatedOn, LearningElementsViewFields.Rating, LearningElementsViewFields.views])

  const isDirectionValid = sortDirection !== undefined && isSortDirectionValid(sortDirection)

  if (isSortColumnValid && isDirectionValid) {
    query += `ORDER BY [${sortColumn}] ${sortDirection} `
  } else {
    query += `ORDER BY [${LearningElementsViewFields.CreatedOn}] DESC, [${LearningElementsViewFields.Title}] ASC `
  }

  query += `
        OFFSET @offset ROWS
        FETCH FIRST @limit ROWS ONLY
    `

  const results = await request.query<LearningElement & { TotalRecords: number }>(query)

  // return the results
  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    elements: results.recordset.map(record => new LearningElementModel(record))
  }
}
