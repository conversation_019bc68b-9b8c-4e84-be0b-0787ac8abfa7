import mssql, { DB_Errors } from '@lcs/mssql-utility'
import { LearningContextRatingFields, LearningContextRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-rating.js'

export default async function (contextID: string): Promise<{average: number, count: number}> {
  const pool = mssql.getPool()
  const request = pool.request()

  const query = `
            SELECT AVG([${LearningContextRatingFields.Rating}]) as average, Count([${LearningContextRatingFields.ID}]) as count
            FROM [${LearningContextRatingsTableName}]
            WHERE [${LearningContextRatingFields.LearningContextID}] = @id
        `

  request.input('id', contextID)

  const results = await request.query<{average: number, count: number}>(query)

  if (results.recordset.length <= 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }

  return results.recordset[0]
}
