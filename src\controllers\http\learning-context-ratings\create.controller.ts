import LearningContextRating, { learningContextRatingSchema } from '../../../models/learning-context-rating.model.js'
import create from '../../../services/mssql/learning-context-ratings/create.service.js'
import logger from '@lcs/logger'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { ZodError } from 'zod'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.create-learning-context-ratings', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, go to the Browse screen, click on a course and then rate the course. Note the time. Check the LMS API log for the 'http-create-learning-context-ratings' label."

  try {
    const rating = new LearningContextRating(learningContextRatingSchema.parse(req.body))

    const result = await create(rating)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, go to the Browse screen, click on a course and then rate the course. Note the time. Check the LMS API log for the 'http-create-learning-context-ratings' label and message indicating successful creation."
    log('info', 'Successfully created learning context ratings.', { success: true, req })

    res.json(result.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create rating for learning context: input validation error', { errorMessage, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else if (errorMessage.startsWith('Violation of UNIQUE KEY constraint')) {
      log('warn', 'Failed to create rating for learning context because a rating already exists in the database.', { contextId: req.body.LearningContextID, success: false, req })
      res.status(BAD_REQUEST).send('User learning context rating already exists')
    } else {
      log('error', 'Failed to create learning context rating.', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
