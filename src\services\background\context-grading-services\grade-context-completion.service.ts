import mssql from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import LearningContext from '../../../models/learning-context.model.js'
import { getLearnerProgressForILT, getLearnerProgressForObject } from './utils.js'
import getLearningObjectsService from '../../mssql/learning-objects/get-multiple.service.js'
import { getContextPassFail } from './grade-context-pass-fail.service.js'
import { getContextPercentage } from './grade-percentage.service.js'
import { getContextExamScore } from './grade-exam.service.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { GradeTypes } from '@tess-f/sql-tables/dist/lms/grade-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import { UserCompletedLearningContext, UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { LearningObjectSubTypes } from '@tess-f/sql-tables/dist/lms/learning-object-sub-type.js'

const completeStatusIDs = [LessonStatuses.passed, LessonStatuses.completed, LessonStatuses.fail]
const cmi5CompleteSatisfiedIDs = [LessonStatuses.passed, LessonStatuses.completed]
const log = logger.create('Service-Background.grade-context-completion')

export default async function gradeContextCompletion (context: LearningContext, userID: string, minProgressAge: Date | undefined, maxProgressAge: Date): Promise<{ LessonStatusID: number, RawScore: number, CompletionDate: Date | null }> {
  const pool = mssql.getPool()

  if (context.fields.ContextTypeID === LearningContextTypes.Section || context.fields.ContextTypeID === LearningContextTypes.CMI5Block || context.fields.ContextTypeID === LearningContextTypes.CMI5Course) {
    // set up the completion ids
    const completionIds = context.fields.ContextTypeID === LearningContextTypes.Section ? completeStatusIDs : cmi5CompleteSatisfiedIDs
    // let's get the content for this context
    const learningObjects = await getLearningObjectsService(context.fields.ID)
    if (!context.fields.Contexts) {
      context.fields.Contexts = []
    }

    if (learningObjects.length <= 0 && context.fields.Contexts.length <= 0) {
      return {
        LessonStatusID: LessonStatuses.completed,
        RawScore: 100,
        CompletionDate: null
      } // no content so the user has passed
    }

    let completed = true // we will assume the user has completed unless we can prove they haven't
    let completedDate: Date | undefined
    let numberOfItemsAttempted = 0
    for (const learningObject of learningObjects) {
      let objectCompleted = false
      let objectCompletedDate: Date | undefined
      const progress = await getLearnerProgressForObject(pool.request(), learningObject.fields.ID!, userID, minProgressAge, maxProgressAge)
      if (progress.length <= 0) {
        // the user has not attempted this, the context is not complete
        completed = false
        continue
      }
      progress.forEach(p => {
        // this is only complete if the learning object:
        // is a cmi5 AU and completion date and completion id are valid
        // not a cmi5 AU and completion is is valid
        if (
          completionIds.includes(p.fields.LessonStatusID!) &&
          (
            (learningObject.fields.LearningObjectTypeID === LearningObjectTypes.CBT && learningObject.fields.LearningObjectSubTypeID === LearningObjectSubTypes.CMI5AU && p.fields.CompletedDate) ||
            (learningObject.fields.LearningObjectSubTypeID !== LearningObjectSubTypes.CMI5AU)
          )
        ) {
          // this is complete
          objectCompleted = true
          // check the completion date
          if (!objectCompletedDate || objectCompletedDate > (p.fields.CompletedDate || p.fields.CreatedOn!)) {
            objectCompletedDate = p.fields.CompletedDate ?? p.fields.CreatedOn!
          }
        }
      })
      // is this object completed?
      if (objectCompleted) {
        if (objectCompletedDate && (!completedDate || objectCompletedDate > completedDate)) {
          completedDate = objectCompletedDate
        }
        numberOfItemsAttempted++
      } else {
        completed = false
      }
    }
    // now we go through the nested contexts
    for (const nested of context.fields.Contexts) {
      // check the grade type and get the completion
      let grade!: { LessonStatusID: number, RawScore: number, CompletionDate: Date | null }
      const child = new LearningContext(nested)
      if (!child.fields.GradeTypeID) {
        grade = await getContextCompletion(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.CompleteIncomplete || child.fields.GradeTypeID === GradeTypes.PassFail) {
        grade = await getContextPassFail(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.Percentage) {
        grade = await getContextPercentage(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.Exam) {
        grade = await getContextExamScore(child, userID, maxProgressAge)
      }

      if (completionIds.includes(grade.LessonStatusID)) {
        numberOfItemsAttempted++
        if (grade.CompletionDate && (!completedDate || grade.CompletionDate > completedDate)) {
          completedDate = grade.CompletionDate
        }
      } else {
        completed = false
      }
    }

    // we have looked through all the content, is this context complete?
    return {
      LessonStatusID: completed ? LessonStatuses.completed : LessonStatuses.incomplete,
      RawScore: completed ? 100 : numberOfItemsAttempted / (learningObjects.length + context.fields.Contexts.length),
      CompletionDate: completedDate ?? new Date()
    }
  } else if (context.fields.ContextTypeID === LearningContextTypes.Course && context.fields.CourseTypeID === CourseTypes.InstructorLed) {
    // get the learner progress records and check if this is complete
    const progress = await getLearnerProgressForILT(pool.request(), context.fields.ID!, userID, minProgressAge, maxProgressAge)
    if (progress.length <= 0) {
      // the user has not attempted this
      return {
        LessonStatusID: LessonStatuses.notAttempted,
        RawScore: 0,
        CompletionDate: null
      }
    }

    for (const prog of progress) {
      // the progress is in order oldest to newest
      // the first one we find that satisfies our condition will be the
      // oldest record and thus our completion date
      if (completeStatusIDs.includes(prog.fields.LessonStatusID!)) {
        return {
          LessonStatusID: LessonStatuses.completed,
          RawScore: 100,
          CompletionDate: prog.fields.CompletedDate ? prog.fields.CompletedDate : prog.fields.CreatedOn!
        }
      }
    }

    // if we got this far we didn't complete the ILT
    return {
      LessonStatusID: LessonStatuses.browsed,
      RawScore: 0,
      CompletionDate: null
    }
  } else if (context.fields.ContextTypeID === LearningContextTypes.ElectiveOptional) {
    if (!context.fields.RequiredContentCount) {
      // if the user isn't required to complete any of the content give them full credit
      return {
        LessonStatusID: LessonStatuses.completed,
        RawScore: 100,
        CompletionDate: minProgressAge ?? maxProgressAge
      }
    }

    const learningObjects = await getLearningObjectsService(context.fields.ID)
    if (!context.fields.Contexts) {
      context.fields.Contexts = []
    }

    if (learningObjects.length <= 0 && context.fields.Contexts.length <= 0) {
      // no content give the user credit
      return {
        LessonStatusID: LessonStatuses.completed,
        RawScore: 100,
        CompletionDate: minProgressAge ?? maxProgressAge
      }
    }

    let numberOfCompleted = 0
    let completedDate: Date | undefined

    for (const learningObject of learningObjects) {
      let objectCompleted = false
      let objectCompletedDate: Date | undefined
      const progress = await getLearnerProgressForObject(pool.request(), learningObject.fields.ID!, userID, minProgressAge, maxProgressAge)
      for (const p of progress) {
        if (completeStatusIDs.includes(p.fields.LessonStatusID!)) {
          objectCompleted = true
          // check the completion date
          if (!objectCompletedDate || objectCompletedDate > (p.fields.CompletedDate || p.fields.CreatedOn!)) {
            objectCompletedDate = p.fields.CompletedDate ?? p.fields.CreatedOn!
          }
        }
      }
      if (objectCompleted) {
        if (objectCompletedDate && (!completedDate || objectCompletedDate > completedDate)) {
          completedDate = objectCompletedDate
        }
        numberOfCompleted++
      }
    }

    // now we go through the contexts
    for (const nested of context.fields.Contexts) {
      let grade!: { LessonStatusID: number, RawScore: number, CompletionDate: Date | null }
      const child = new LearningContext(nested)
      if (!child.fields.GradeTypeID) {
        grade = await getContextCompletion(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.CompleteIncomplete || child.fields.GradeTypeID === GradeTypes.PassFail) {
        grade = await getContextPassFail(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.Percentage) {
        grade = await getContextPercentage(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.Exam) {
        grade = await getContextExamScore(child, userID, maxProgressAge)
      }

      if (completeStatusIDs.includes(grade.LessonStatusID)) {
        numberOfCompleted++
        if (grade.CompletionDate && (!completedDate || grade.CompletionDate > completedDate)) {
          completedDate = grade.CompletionDate
        }
      }
    }

    // now we need to calculate the completion
    if (numberOfCompleted >= context.fields.RequiredContentCount) {
      return {
        LessonStatusID: LessonStatuses.completed,
        RawScore: 100,
        CompletionDate: completedDate ?? minProgressAge ?? maxProgressAge
      }
    } else {
      return {
        LessonStatusID: numberOfCompleted === 0 ? LessonStatuses.notAttempted : LessonStatuses.incomplete,
        RawScore: numberOfCompleted / context.fields.RequiredContentCount,
        CompletionDate: null
      }
    }
  } else {
    return {
      LessonStatusID: -1,
      RawScore: 0,
      CompletionDate: null
    }
  }
}

export async function getContextCompletion (context: LearningContext, userID: string, minProgressAge: Date | undefined, maxProgressAge: Date): Promise<{ LessonStatusID: number, RawScore: number, CompletionDate: Date | null }> {
  try {
    // find the latest score
    const request = mssql.getPool().request()
    request.input('contextID', context.fields.ID)
    request.input('userID', userID)
    request.input('to', maxProgressAge)

    const res = await request.query<UserCompletedLearningContext>(`
      SELECT TOP(1) *
      FROM [${UserCompletedLearningContextsTableName}]
      WHERE [${UserCompletedLearningContextFields.LearningContextID}] = @contextID
      AND [${UserCompletedLearningContextFields.UserID}] = @userID
      AND [${UserCompletedLearningContextFields.LessonStatusID}] >= ${LessonStatuses.passed}
      AND [${UserCompletedLearningContextFields.CompletedOn}] <= @to
      ORDER BY [${UserCompletedLearningContextFields.RawScore}] DESC, [${UserCompletedLearningContextFields.CompletedOn}] DESC
    `)

    if (res.recordset.length > 0) {
      return {
        LessonStatusID: res.recordset[0].LessonStatusID!,
        RawScore: res.recordset[0].RawScore!,
        CompletionDate: res.recordset[0].CompletedOn!
      }
    } else {
      // we have not completed this yet, let's grade it now
      return gradeContextCompletion(context, userID, minProgressAge, maxProgressAge)
    }
  } catch (error) {
    log('error', 'Unknown database error', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}
