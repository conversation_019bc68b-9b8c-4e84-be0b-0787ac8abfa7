import { Table } from '@lcs/mssql-utility'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { LearningContextUserFavorite, LearningContextUserFavoriteFields, LearningContextUserFavoritesTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-favorite.js'
import { z } from 'zod'

export const createLearningContextFavoriteSchema = z.object({
  [LearningContextUserFavoriteFields.UserID]: zodGUID,
  [LearningContextUserFavoriteFields.LearningContextID]: zodGUID
})

export default class LearningContextUserFavoriteModel extends Table<LearningContextUserFavorite, LearningContextUserFavorite> {
  public fields: LearningContextUserFavorite

  constructor (fields?: LearningContextUserFavorite) {
    super(LearningContextUserFavoritesTableName, [
      LearningContextUserFavoriteFields.UserID,
      LearningContextUserFavoriteFields.LearningContextID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearningContextUserFavorite): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningContextUserFavorite {
    return this.fields
  }
}
