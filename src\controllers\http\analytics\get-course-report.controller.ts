import getCompletionReport from '../../../services/mssql/user-completed-learning-contexts/get-for-report.service.js'
import logger from '@lcs/logger'
import { Parser } from '@json2csv/plainjs'
import { Request, Response } from 'express'
import { httpLogTransformer } from '@tess-f/backend-utils'
import httpStatus from 'http-status'

const log = logger.create('Controller-HTTP.get-course-completion-report', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const report = await getCompletionReport()

    const fields = [
      { label: 'Catalog Title', value: 'Title' },
      { label: 'Catalog Label', value: 'Label' },
      { label: 'User ID', value: 'UserID' },
      { label: 'Status', value: 'Status' },
      { label: 'Completed On', value: 'CompletedOn' },
      { label: 'Score', value: 'RawScore' },
      { label: 'Grade Type', value: 'GradeType' }
    ]

    const parser = new Parser({ fields })
    const csv = parser.parse(report)

    res.header('Content-Type', 'text/csv')
    const today = new Date()
    res.attachment(`course-completion-report-${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}.csv`)
    res.send(csv)
  } catch (error) {
    log('error', 'Failed to get course completion report', { error, success: false, req })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
