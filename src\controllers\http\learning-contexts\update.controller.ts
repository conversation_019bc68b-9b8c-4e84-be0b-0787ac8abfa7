import LearningContextModel, { updateLearningContextSchema } from '../../../models/learning-context.model.js'
import logger from '@lcs/logger'
import update from '../../../services/mssql/learning-context/update.service.js'
import { Request, Response } from 'express'
import { byConnectedContextID as getConnectedContexts, ContextConnection } from '../../../services/mssql/learning-context-connections/get.service.js'
import removeContextConnection from '../../../services/mssql/learning-context-connections/delete.service.js'
import createContextConnection from '../../../services/mssql/learning-context-connections/create.service.js'
import createContextConnectionInPosition from '../../../services/mssql/learning-context-connections/create-in-position.service.js'
import getContextContentCount from '../../../services/mssql/learning-context/get-content-count.service.js'
import resetParentService from '../../../services/mssql/learning-context/reset-parent.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import moveContextToRootService from '../../../services/mssql/learning-context/move-context-to-root.service.js'
import getChildContextsService from '../../../services/mssql/catalog/get-child-contexts.service.js'
import getLearningObjectsService from '../../../services/mssql/learning-objects/get-multiple.service.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import httpStatus from 'http-status'
import LearningContextConnectionModel from '../../../models/learning-context-connection.model.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.update-learning-context', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, while creating a new course, enter a Course Title and Course Description and then click in another field. Note the time. Check the LMS API log for the 'http-update-learning-context' label."

  try {
    const learningContext = new LearningContextModel(updateLearningContextSchema.parse(req.body))

    // Set the modified by fields
    learningContext.fields.ModifiedBy = req.session.userId
    learningContext.fields.ModifiedOn = new Date()

    // Insure the updating context id is the one requested by the params
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    learningContext.fields.ID = id

    // delete the image field we don't want http calls to update it only RPC
    learningContext.fields.Image = undefined

    // update prerequisites if they exist
    if (learningContext.fields.Prerequisites && learningContext.fields.Prerequisites.length > 0) {
      for (const prereq of learningContext.fields.Prerequisites) {
        prereq.CreatedBy = req.session.userId
        prereq.CreatedOn = new Date()
      }
    }
    
    const { ParentContextIds } = z.object({ ParentContextIds: z.array(zodGUID).optional() }).parse(req.body)

    let result = await update(learningContext)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, while creating a new course, enter a Course Title and Course Description and then click in another field. Note the time. Check the LMS API log for the 'http-update-learning-context' label and message indicating a successful update."
    log('info', 'Successfully updated learning context', {  id: result.fields.ID, success: true, req })
    
    // We need to check if there is a list of parent ID's in the body
    if (ParentContextIds) {
      // we have a list of parent contexts
      // we need to check for parents that need to be removed and added
      let originalContextConnections!: ContextConnection[]
      try {
        originalContextConnections = await getConnectedContexts(result.fields.ID!)
      } catch (error) {
        if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
          originalContextConnections = []
        } else {
          throw error
        }
      }
      // if the ids is the original id list but not in the one the user sent we need to remove the connection
      const connectionsToRemove = originalContextConnections.filter(connection => !ParentContextIds.includes(connection.ParentContextID))
      // if the id is in the user list but not the original list we need to add the connection
      const connectionsToAdd = ParentContextIds.filter(id => !originalContextConnections.find(connection => connection.ParentContextID === id) && id !== result.fields.ParentContextID)

      // remove the connections that we no longer want
      for (const connection of connectionsToRemove) {
        await removeContextConnection(connection.ParentContextID, connection.ConnectedContextID)
      }

      // add the new connections
      const addedConnections = await Promise.all(connectionsToAdd.map(async id => {
        const connection = new LearningContextConnectionModel()
        connection.fields.ConnectedContextID = result.fields.ID
        connection.fields.CreatedBy = req.session.userId
        connection.fields.CreatedOn = new Date()
        connection.fields.ParentContextID = id
        // if this is an elective container it get's added at the bottom
        if (result.fields.ContextTypeID === LearningContextTypes.ElectiveOptional) {
          connection.fields.OrderID = (await getContextContentCount(id)) + 1
          return await createContextConnection(connection)
        } else {
          // we need to determine where to add this content above elective but bellow all other content
          const objects = await getLearningObjectsService(id)
          const contexts = await getChildContextsService(id)
          connection.fields.OrderID = objects.length + contexts.filter(context => context.fields.ContextTypeID !== LearningContextTypes.ElectiveOptional).length + 1

          return await createContextConnectionInPosition(connection)
        }
      }))

      log('info', 'Successfully updated context connections.', { removed: connectionsToRemove.length, added: addedConnections.length, success: true, req })

      // lastly we need to check if we need to update the parent of this context
      if (result.fields.ParentContextID && !ParentContextIds.includes(result.fields.ParentContextID)) {
        log('info', 'Contexts original parent has been removed, updating connection now.')
        // this parent is no longer a parent
        if (originalContextConnections.filter(connection => ParentContextIds.includes(connection.ParentContextID)).length > 0) {
          // lets add one of our existing connections as the parent
          const existingConnection = originalContextConnections.filter(connection => ParentContextIds.includes(connection.ParentContextID))[0]
          await resetParentService(result, existingConnection.ParentContextID, existingConnection.OrderID)
          log('info', 'Successfully re-parented context to an existing connection', { newParentID: result.fields.ParentContextID, success: true, contextID: result.fields.ID, req })
        } else if (addedConnections.length > 0) {
          // lets add one of our newly added connections as the parent
          await resetParentService(result, addedConnections[0].fields.ParentContextID!, addedConnections[0].fields.OrderID!)
          log('info', 'Successfully re-parented context to a new connection', { newParentID: result.fields.ParentContextID, success: true, contextID: result.fields.ID, req })
        } else {
          result = await moveContextToRootService(result)
          log('info', 'Context no longer has a parent. Successfully moved context to the root of the catalog.', { contextID: result.fields.ID, success: true, req })
        }
      } else if (!result.fields.ParentContextID && addedConnections.length > 0) {
        // we had no parent and now we do
        result.fields.OrderID = addedConnections[0].fields.OrderID
        result.fields.ParentContextID = addedConnections[0].fields.ParentContextID
        result = await update(result)
        await removeContextConnection(addedConnections[0].fields.ParentContextID!, addedConnections[0].fields.ConnectedContextID!, false)
        log('info', 'Context previously had no parent now it does.', { parentID: result.fields.ParentContextID, success: true, contextID: result.fields.ID, req })
      }
    }

    res.json(result.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update learning context: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to update learning context.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
