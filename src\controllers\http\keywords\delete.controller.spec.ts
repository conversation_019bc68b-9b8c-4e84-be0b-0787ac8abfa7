import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'

describe('HTTP Delete controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./delete.controller', {
            '../../../services/mssql/keywords/delete.service.js': {
                default: Sinon.stub().returns(1)
            }
        })

        const mocks = httpMocks.createMocks({
            params: {
                name: 'test'
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.NO_CONTENT)
    })


    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./delete.controller', {
            '../../../services/mssql/keywords/delete.service.js': {
                default: Sinon.stub().returns(1)
            }
        })

        const mocks = httpMocks.createMocks({
            params: {
                name: 123
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const message = mocks.res._getData()
        expect(message).eq('Invalid keyword parameter: name: Expected string, received number')
    })

  
})