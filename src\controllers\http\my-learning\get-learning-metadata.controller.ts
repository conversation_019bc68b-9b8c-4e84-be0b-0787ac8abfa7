import { Request, Response } from 'express'
import getMetadataService from '../../../services/mssql/my-learning/get-metadata.service.js'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.get-learning-metadata', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-get-learning-metadata' label."

  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const result = await getMetadataService(id)

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-get-learning-metadata' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved learning metadata.', { req })

    res.json(result)
  } catch (error) {
    // STIG V-69425 data access (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning metadata: input validation error', { req, error })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter, '))
    } else {
      log('error', 'Failed to get learning metadata.', { error, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
