import logger from '@lcs/logger'
import AssignmentsModel from '../../models/assignment.model.js'
import getUserById from '../mssql/users/get-by-id.service.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { User } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { SystemConfig } from '@tess-f/system-config/dist/http/system-config.js'
import { getSystemConfig } from '../amqp/system/get-system-config.service.js'
import moment from 'moment-timezone'
const { tz } = moment
import getLearningContextsForAssignment from '../mssql/assignment-learning-contexts/get-contexts-for-assignment.service.js'
import getLearningObjectsForAssignment from '../mssql/assignment-learning-objects/get-objects-for-assignment.service.js'
import { getLearningContextType, getLearningObjectType } from '../../utils/statics-to-names.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import getMultipleUsersByIds from '../mssql/users/get-multiple-by-ids.service.js'
import { sendLearnerAssignmentMessage } from '@tess-f/email/dist/amqp/send-learner-assignment.js'
import settings from '../../config/settings.js'
import sendNotification from '../../services/amqp/notification/send-notification.service.js'

const log = logger.create('Service-Email.send-assignment-notification')

export default async function sendAssignmentNotification (assignment: AssignmentsModel, usersToNotify: string[]): Promise<void> {
  // get the assigners profile
  let assigner: User
  try {
    assigner = await getUserById(assignment.fields.CreatedBy!)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get the assignment creators profile: not found', { assignmentId: assignment.fields.ID, createdBy: assignment.fields.CreatedBy, success: false })
    } else {
      log('error', 'Failed to get the assignment creators profile', { errorMessage, success: false })
    }
    return
  }

  const learners = await getMultipleUsersByIds(usersToNotify)
  const learnerEmails = learners.filter(learner => learner.Email !== undefined).map(learner => learner.Email!)
  const learnerIds = learners.map(learner => learner.ID!)

  let systemConfig: SystemConfig
  try {
    systemConfig = await getSystemConfig()
  } catch (error) {
    log('warn', 'Failed to get system config', { errorMessage: getErrorMessage(error), success: false })
    return
  }

  const lmsConfig = systemConfig.Apps.find(app => app.Id === 'lms')

  let dueDate: string = ''
  if (assignment.fields.DueDate) {
    const d = tz(assignment.fields.DueDate.toUTCString(), 'UTC')
    dueDate = d.format('MM/DD/YYYY')
  }

  // get the contexts for the assignment
  const contexts = await getLearningContextsForAssignment(assignment.fields.ID!)
  // get the learning objects for the assignment
  const objects = await getLearningObjectsForAssignment(assignment.fields.ID!)

  const assignments: Array<{ Title: string, Image?: string, TypeName: string, Link: string }> = []
  // map contexts to assignment info
  contexts.forEach(context => {
    let Link: string = `${systemConfig.Domain}${lmsConfig?.Address ?? '/lms'}/browse/`
    if (context.fields.ContextTypeID === LearningContextTypes.Course) {
      Link += `course/${context.fields.ID}`
    } else {
      Link = `view-context/${context.fields.ID}`
    }
    assignments.push({
      Title: context.fields.Title!,
      Link,
      TypeName: context.fields.Label ?? getLearningContextType(context.fields.ContextTypeID!)
    })
  })

  // map objects to assignment info
  objects.forEach(obj => {
    assignments.push({
      Title: obj.fields.Title!,
      TypeName: getLearningObjectType(obj.fields.LearningObjectTypeID!),
      Link: `${systemConfig.Domain}${lmsConfig?.Address ?? '/lms'}/browse/view-content/${obj.fields.ID}`
    })
  })

  if (learnerEmails.length > 0) {
    await sendLearnerAssignmentMessage(
      settings.amqp.service_queues.email, {
        to: learnerEmails,
        subject: 'New Assignment',
        header: 'New Assignment',
        message: {
          assignerName: `${assigner.FirstName} ${assigner.LastName}`,
          optionalMessage: assignment.fields.EmailMessage!,
          assignmentDueDate: dueDate,
          assignments
        }
      },
      settings.amqp.command_timeout
    )
  } else {
    log('warn', 'None of the learners have email addresses', { success: false })
  }

  // notify user of assignments 
  let message = `<p>${assigner.FirstName} ${assigner.LastName} has just assigned something to you.</p>`
  if (assignment.fields.EmailMessage !== undefined) {
    message += `<p>${assignment.fields.EmailMessage}</p>`
  }

  if (dueDate !== '') {
    message += `<p>Complete the assignment(s) below no later than ${dueDate}</p>`
  } else {
    message += `<p>Complete the assignment(s) below. (No assigned due date)</p>`
  }

  assignments.forEach(assign => {
    message += `
      <p>
        <strong>${assign.TypeName}</strong><br>
        <a href="${assign.Link}>${assign.Title}</a>
      </p>
    `
  })

  await Promise.all(learnerIds.map(async id => {
    await sendNotification({
      Title: 'New Assignment',
      Message: message,
      PublishDate: new Date(),
      Everyone: false,
      SystemID: 'lms',
      Priority: 1,
      UserIDs: [id]
    })
  }))
  log('info', 'Successfully sent assignment emails for assignment.', { assignmentId: assignment.fields.ID, success: true })
}
