import logger from '@lcs/logger'
import { DB_Errors } from '@lcs/mssql-utility'
import getAssignments from '../../../services/mssql/assignments-view/get-multiple.service.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import getUsersAndGroupsForAssignment from '../../../services/mssql/assignment-users/get-users-and-groups-for-assignment.service.js'
import { httpLogTransformer, getErrorMessage, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodDates, zodLimit, zodOffset, zodString } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-assignments', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { search, offset, limit, dueDate, sortColumn, sortDirection, status, missingSessions } = z.object({
      search: zodString.optional(),
      offset: zodOffset,
      limit: zodLimit,
      dueDate: zodDates.optional(),
      sortDirection: z.literal('asc').or(z.literal('desc')).or(z.literal('ASC')).or(z.literal('DESC')).optional(),
      sortColumn: z.string().optional(),
      status: z.array(z.number()).optional(),
      missingSessions: z.boolean().optional()
    }).parse(req.body)

    // get the assignments
    const results = await getAssignments(offset, limit, search, dueDate, status, missingSessions, sortColumn, sortDirection)

    // loop through the assignments async and map out the extra data
    results.assignments = await Promise.all(results.assignments.map(async assignment => {
      const { users, groups } = await getUsersAndGroupsForAssignment(assignment)
      assignment.fields.Users = users
      assignment.fields.Groups = groups
      return assignment
    }))

    log('info', 'Successfully retrieved assignments.', { count: results.assignments.length, totalRecords: results.totalRecords, success: true })

    res.json({
      totalRecords: results.totalRecords,
      assignments: results.assignments.map(assignment => assignment.fields)
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to retrieve assignments: validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      const errorMessage = getErrorMessage(error)
      if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
        log('warn', 'Failed to retrieve assignments because none were found in the database.', { success: false, req })
        res.json({ totalRecords: 0, assignments: [] })
      } else {
        log('error', 'Failed to retrieve assignments.', { errorMessage, success: false, req })
        res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
      }
    }
  }
}
