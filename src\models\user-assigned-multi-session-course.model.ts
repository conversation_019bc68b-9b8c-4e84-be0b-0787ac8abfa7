import { Table } from '@lcs/mssql-utility'
import { UserAssignedMultiSessionCourses, UserAssignedMultiSessionCoursesFields, UserAssignedMultiSessionCoursesTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-multi-session-course.js'

export default class UserAssignedMultiSessionCoursesModel extends Table<UserAssignedMultiSessionCourses, UserAssignedMultiSessionCourses> {
  public fields: UserAssignedMultiSessionCourses

  constructor (fields?: UserAssignedMultiSessionCourses) {
    super(UserAssignedMultiSessionCoursesTableName, [
      UserAssignedMultiSessionCoursesFields.AssignmentID,
      UserAssignedMultiSessionCoursesFields.UserID,
      UserAssignedMultiSessionCoursesFields.LearningContextID,
      UserAssignedMultiSessionCoursesFields.LessonStatusID,
      UserAssignedMultiSessionCoursesFields.CreatedOn,
      UserAssignedMultiSessionCoursesFields.Deleted
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: UserAssignedMultiSessionCourses): void {
    this.fields = record
  }

  public exportJsonToDatabase (): UserAssignedMultiSessionCourses {
    return this.fields
  }
}
