import mssql, { updateRow } from '@lcs/mssql-utility'
import { xAPIVerb } from '@tess-f/sql-tables/dist/lms/xapi-verb.js'
import XAPIVerbModel from '../../../models/xapi-verb.model.js'

export default async function updateVerb (verb: XAPIVerbModel): Promise<XAPIVerbModel> {
  const pool = mssql.getPool()
  const records = await updateRow<xAPIVerb>(pool.request(), verb, { Id: verb.fields.Id })
  verb.importFromDatabase(records[0])
  return verb
}
