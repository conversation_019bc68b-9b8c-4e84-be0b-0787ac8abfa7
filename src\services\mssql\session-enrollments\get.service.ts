import mssql, { getRows } from '@lcs/mssql-utility'
import SessionEnrollmentModel from '../../../models/session-enrollment.model.js'
import { SessionEnrollment, SessionEnrollmentsTableName } from '@tess-f/sql-tables/dist/lms/session-enrollment.js'

export default async function (SessionID: string | undefined, UserID: string | undefined): Promise<SessionEnrollmentModel[]> {
  const pool = mssql.getPool()
  let records: SessionEnrollment[]
  if (SessionID) {
    records = await getRows<SessionEnrollment>(SessionEnrollmentsTableName, pool.request(), { SessionID })
  } else if (UserID) {
    records = await getRows<SessionEnrollment>(SessionEnrollmentsTableName, pool.request(), { UserID })
  } else {
    records = await getRows<SessionEnrollment>(SessionEnrollmentsTableName, pool.request())
  }

  return records.map(record => new SessionEnrollmentModel(record))
}
