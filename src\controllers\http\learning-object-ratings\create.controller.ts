import LearningObjectRating, { learningObjectRatingSchema } from '../../../models/learning-object-rating.model.js'
import create from '../../../services/mssql/learning-object-ratings/create.service.js'
import logger from '@lcs/logger'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { ZodError } from 'zod'

const log = logger.create('Controller-HTTP.create-learning-object-rating', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, rate a learning object within a course. Note the time. Check the LMS API log for the 'http-create-learning-object-rating' label."

  try {
    const rating = new LearningObjectRating(learningObjectRatingSchema.parse(req.body))
    const result = await create(rating)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, rate a learning object within a course. Note the time. Check the LMS API log for the 'http-create-learning-object-rating' label and message indicating successful creation."
    log('info', 'Successfully created learning object rating.', { success: true, req })
    res.json(result.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create learning object rating because of invalid input.', { errorMessage, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else if (errorMessage.startsWith('Violation of UNIQUE KEY constraint')) {
      log('warn', 'Failed to create learning object rating because it already exists in the database.', { errorMessage, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send('User rating already exists')
    } else {
      log('error', 'Failed to create learning object rating.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
