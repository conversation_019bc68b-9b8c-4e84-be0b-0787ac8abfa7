import logger from '@lcs/logger'
import { Request, Response } from 'express'
import updateSystemWidgetService from '../../../services/mssql/system-widgets/update.service.js'
// Widget CRUD
import createWidgetService from '../../../services/mssql/widgets/create.service.js'
import deleteWidget from '../../../services/mssql/widgets/delete.service.js'
import updateWidgetService from '../../../services/mssql/widgets/update.service.js'
import getWidgetsForCreator from '../../../services/mssql/widgets/get-for-creator.service.js'
// system widget group CRUD
import createSystemWidgetGroup from '../../../services/mssql/system-widget-groups/create.service.js'
import getGroupsForSystemWidgetsService from '../../../services/mssql/system-widget-groups/get-multiple.service.js'
import deleteSystemWidgetGroup from '../../../services/mssql/system-widget-groups/delete.service.js'
import SystemWidget, { updateSystemWidgetSchema } from '../../../models/system-widget.model.js'
import SystemWidgetGroup from '../../../models/system-widget-group.model.js'
import { DB_Errors } from '@lcs/mssql-utility'
import Widget from '../../../models/widget.model.js'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { ZodError } from 'zod'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const log = logger.create('Controller-HTTP.update-system-widget', httpLogTransformer)

export default async function updateSystemWidgetController (req: Request, res: Response) {
  try {
    const requestData = updateSystemWidgetSchema.parse(req.body)
    let systemWidget = new SystemWidget()
    if (req.claims.includes(Claims.MODIFY_SYSTEM_WIDGETS) || req.claims.includes(Claims.CREATE_SYSTEM_WIDGETS)) {
      systemWidget = new SystemWidget(requestData)
    }
    if (req.claims.includes(Claims.PUBLISH_SYSTEM_WIDGETS) || req.claims.includes(Claims.CREATE_SYSTEM_WIDGETS)) {
      systemWidget.fields.ID = requestData.ID
      systemWidget.fields.Published = requestData.Published
    } else {
      systemWidget.fields.Published = undefined
    }

    const updated = await updateSystemWidgetService(systemWidget)

    // update groups
    let originalGroups: SystemWidgetGroup[] = []

    try {
      originalGroups = await getGroupsForSystemWidgetsService(updated.fields.ID!)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      if (errorMessage !== DB_Errors.default.NOT_FOUND_IN_DB) {
        log('error', 'Failed to get the original groups for the system widget', { error, success: false, req })
        throw error
      }
    }

    if (req.claims.includes(Claims.CREATE_SYSTEM_WIDGETS) || req.claims.includes(Claims.MODIFY_SYSTEM_WIDGETS)) {
      // compare the groups and see which ones we need to add and which ones we need to remove
      const groupsToAdd = systemWidget.fields.Groups?.filter(group => !originalGroups.some(oGroup => oGroup.fields.GroupID === group.ID))
      const groupsToRemove = originalGroups.filter(oGroup => !systemWidget.fields.Groups?.some(group => group.ID === oGroup.fields.GroupID))

      // add the new groups
      if (groupsToAdd && groupsToAdd.length > 0) {
        await Promise.all(groupsToAdd.map(async group => {
          await createSystemWidgetGroup(new SystemWidgetGroup({
            SystemWidgetID: updated.fields.ID,
            GroupID: group.ID
          }))
        }))
      }

      // remove old groups
      if (groupsToRemove.length > 0) {
        await Promise.all(groupsToRemove.map(async group => {
          await deleteSystemWidgetGroup(updated.fields.ID!, group.fields.GroupID!)
        }))
      }
    }

    updated.fields.Groups = systemWidget.fields.Groups ?? requestData.Groups

    // update the widgets
    let originalWidgets: Widget[] = []
    try {
      originalWidgets = await getWidgetsForCreator(updated.fields.ID!)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      if (errorMessage !== DB_Errors.default.NOT_FOUND_IN_DB) {
        log('error', 'Failed to get the original widgets for the system widget', { errorMessage, success: false, req })
        throw error
      }
    }

    if (req.claims.includes(Claims.MODIFY_SYSTEM_WIDGETS) || req.claims.includes(Claims.CREATE_SYSTEM_WIDGETS)) {
      const widgetsToAdd = systemWidget.fields.Widgets?.filter(widget => !originalWidgets.some(oWidget => oWidget.fields.ID === widget.ID))
      const widgetsToRemove = originalWidgets.filter(oWidget => !systemWidget.fields.Widgets?.some(widget => widget.ID === oWidget.fields.ID))
      const widgetsToUpdate = systemWidget.fields.Widgets?.filter(widget => originalWidgets.some(oWidget => oWidget.fields.ID === widget.ID))

      updated.fields.Widgets = []
      if (widgetsToRemove.length > 0) {
        await Promise.all(widgetsToRemove.map(async widget => {
          await deleteWidget(widget.fields.ID!)
        }))
      }

      if (widgetsToAdd && widgetsToAdd?.length > 0) {
        await Promise.all(widgetsToAdd.map(async widget => {
          widget.CreatedBy = updated.fields.ID
          const added = await createWidgetService(new Widget(widget))
          updated.fields.Widgets?.push(added.fields)
        }))
      }

      if (widgetsToUpdate && widgetsToUpdate.length > 0) {
        await Promise.all(widgetsToUpdate.map(async widget => {
          const updatedWidget = await updateWidgetService(new Widget(widget))
          updated.fields.Widgets?.push(updatedWidget.fields)
        }))
      }
    }

    log('info', 'Successfully updated system widget', { id: updated.fields.ID, success: true, req })

    res.json(updated.fields)
  } catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Failed to update system widget: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else {
      log('error', 'Failed to update system widget', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
