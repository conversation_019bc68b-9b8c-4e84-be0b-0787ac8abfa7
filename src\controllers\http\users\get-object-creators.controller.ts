import getObjectCreators from '../../../services/mssql/learning-objects/get-creator-ids.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer } from '@tess-f/backend-utils'
import { Request, Response } from 'express'
import httpStatus from 'http-status'

const log = logger.create('Controller-HTTP.get-learning-object-creators', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const creators = await getObjectCreators()

    if (creators.length > 0) {
      log('info', 'Successfully retrieved learning object creators.', { count: creators.length, success: true, req })
      res.json(creators)
    } else {
      log('info', 'No learning object creators found.', { success: true, req })
      res.json([])
    }
  } catch (error) {
    log('error', 'Failed to get learning object creators.', { error, success: false, req })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
