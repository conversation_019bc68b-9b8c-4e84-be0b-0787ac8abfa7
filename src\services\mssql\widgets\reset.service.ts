import mssql, { <PERSON>_Errors, deleteRow, getRows, updateRow } from '@lcs/mssql-utility'
import UserPreferenceModel from '../../../models/user-preference.model.js'
import { Widget, WidgetsTableName } from '@tess-f/sql-tables/dist/lms/widget.js'
import { WidgetKeywordsTableName } from '@tess-f/sql-tables/dist/lms/widget-keyword.js'
import { WidgetLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/widget-learning-context.js'
import { WidgetLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/widget-learning-object.js'
import { UserHiddenWidgetsTableName } from '@tess-f/sql-tables/dist/lms/user-hidden-widget.js'
import { UserWidgetOrderOverridesTableName } from '@tess-f/sql-tables/dist/lms/user-widget-order-override.js'
import { UserPreferences } from '@tess-f/sql-tables/dist/lms/user-preference.js'

export default async function (userID: string) {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })
    // delete all of the user created widgets
    let widgets: Widget[] = []
    try {
      widgets = await getRows<Widget>(WidgetsTableName, transaction.request(), { CreatedBy: userID })
    } catch (error) {
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        widgets = []
      } else {
        throw error
      }
    }
    for (const widget of widgets) {
      // delete the widget keywords
      await deleteRow(transaction.request(), WidgetKeywordsTableName, { WidgetID: widget.ID })
      // delete the widget learning contexts
      await deleteRow(transaction.request(), WidgetLearningContextsTableName, { WidgetID: widget.ID })
      // delete the widget learning objects
      await deleteRow(transaction.request(), WidgetLearningObjectsTableName, { WidgetID: widget.ID })
      // delete the widget
      await deleteRow(transaction.request(), WidgetsTableName, { ID: widget.ID })
    }
    // delete all of the users hidden widgets
    await deleteRow(transaction.request(), UserHiddenWidgetsTableName, { UserID: userID })
    // delete all of the widget order overrides
    await deleteRow(transaction.request(), UserWidgetOrderOverridesTableName, { UserID: userID })
    // reset the users home page layout
    await updateRow<UserPreferences>(transaction.request(), new UserPreferenceModel({
      UserID: userID,
      HomeLayout: 'col-12'
    }), { UserID: userID })

    await transaction.commit()
  } catch (error) {
    if (!rolledBack) transaction.rollback()
    throw error
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}
