import mssql, { updateRow } from '@lcs/mssql-utility'
import { LearnerProgress } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import LearnerProgressModel from '../../../models/learner-progress.model.js'

export default async function (learnerProgress: LearnerProgressModel): Promise<LearnerProgressModel> {
  const pool = mssql.getPool()
  const records = await updateRow<LearnerProgress>(pool.request(), learnerProgress, { ID: learnerProgress.fields.ID })
  return new LearnerProgressModel(records[0])
}
