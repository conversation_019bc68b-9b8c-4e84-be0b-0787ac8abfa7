import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { FlatLearningContextTreeFields, FlatLearningContextTreeViewName } from '@tess-f/sql-tables/dist/lms/flat-learning-context-view.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'

export default async function getCmi5CourseContextForAu (auId: string): Promise<LearningContextModel> {
  const request = mssql.getPool().request()
  request.input('auId', auId)

  const result = await request.query<LearningContext>(`
    WITH Parent_Nodes AS (
      SELECT [${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeFields.ParentContextID}]
      FROM [${FlatLearningContextTreeViewName}]
      WHERE [${FlatLearningContextTreeFields.ID}] IN (
        SELECT [${LearningObjectContextFields.LearningContextID}]
        FROM [${LearningObjectContextsTableName}]
        WHERE [${LearningObjectContextFields.LearningObjectID}] = @auId
      )

      UNION ALL

      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Parent_Nodes] ON [Parent_Nodes].[${FlatLearningContextTreeFields.ParentContextID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}]
    )
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ID}] IN (
      SELECT [${FlatLearningContextTreeFields.ID}]
      FROM [Parent_Nodes]
    )
    AND [${LearningContextFields.ContextTypeID}] = ${LearningContextTypes.CMI5Course}
  `)

  if (result.recordset.length > 0) {
    return new LearningContextModel(undefined, result.recordset[0])
  }

  throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
}
