import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import LearningContextConnectionModel from '../../../models/learning-context-connection.model.js'
import create from './create.service.js'
import createInPosition from './create-in-position.service.js'
import { byConnectedContextID } from './get.service.js'
import update from './update.service.js'
import remove from './delete.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { v4 as uuidv4 } from 'uuid'

let connectedContext: LearningContext
let parentContext: LearningContext
let connectedContextInPosition: LearningContext
let parentContextInPosition: LearningContext
let badContextConnection: LearningContextConnectionModel
let contextConnection: LearningContextConnectionModel

describe('MSSQL Learning Context Connections Service', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    parentContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test Parent Context',
      Description: `Running learning-context-connections on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Collection
    }))
    connectedContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test Child Context',
      Description: `Running learning-context-connections on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Collection
    }))
    parentContextInPosition = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test Parent Context',
      Description: `Running learning-context-connections on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Collection
    }))
    connectedContextInPosition = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test Child Context',
      Description: `Running learning-context-connections on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Collection
    }))
    badContextConnection = new LearningContextConnectionModel({
      ParentContextID: uuidv4(),
      ConnectedContextID: connectedContext.ID,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      OrderID: 2
    })
  })

  it('creates a learning context Connection', async () => {
    contextConnection = await create(new LearningContextConnectionModel({
      ParentContextID: parentContext.ID,
      ConnectedContextID: connectedContext.ID,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      OrderID: 1
    }))

    expect(contextConnection.fields.ParentContextID).to.equal(parentContext.ID)
    expect(contextConnection.fields.ConnectedContextID).to.equal(connectedContext.ID)
    expect(contextConnection.fields.CreatedBy).to.equal(AdminUserId)
    expect(contextConnection.fields.OrderID).to.equal(1)
  })

  it('creates in position a learning context Connection', async () => {
    contextConnection = await createInPosition(new LearningContextConnectionModel({
      ParentContextID: parentContextInPosition.ID,
      ConnectedContextID: connectedContextInPosition.ID,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      OrderID: 1
    }))

    expect(contextConnection.fields.ParentContextID).to.equal(parentContextInPosition.ID)
    expect(contextConnection.fields.ConnectedContextID).to.equal(connectedContextInPosition.ID)
    expect(contextConnection.fields.CreatedBy).to.equal(AdminUserId)
    expect(contextConnection.fields.OrderID).to.equal(1)
  })

  it('gets contexts by connected context id', async () => {
    try {
      const _contextConnections = await byConnectedContextID(connectedContext.ID!)

      for (const connection of _contextConnections) {
        expect(connection.ConnectedContextID).to.equal(connectedContext.ID)
      }
    } catch (error) {
      expect(error).to.exist
    }
  })

  it('updates a context connection', async () => {
    contextConnection.fields.OrderID = 3
    const updated = await update(contextConnection)
    expect(updated.fields.OrderID).to.equal(3)
  })

  it('deletes the new context connection', async () => {
    await remove(parentContext.ID!, connectedContext.ID!)
  })

  it('fails updating an unknown context connection', async () => {
    try {
      await update(badContextConnection)
    } catch (error) {
      expect(error).to.exist
    }
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { CreatedBy: AdminUserId })
  })
})
