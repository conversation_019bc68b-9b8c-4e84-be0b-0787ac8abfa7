import logger from '@lcs/logger'
import getPaginatedService from '../../../services/mssql/certificate/get-paginated.service.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodDates, zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'
import { z } from 'zod'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'

const log = logger.create('Controller-HTTP.get-paginated-certificates', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    let userID: string
    const forUserId = zodGUID.safeParse(req.query.for)
    if (forUserId.success) {
      userID = forUserId.data
    } else {
      userID = req.session.userId
    }

    const { offset, limit, search, labels, objectTypeIds, keywords, from, to } = z.object({
      offset: zodOffset,
      limit: zodLimit,
      search: z.string().optional(),
      labels: z.array(z.string()).optional(),
      objectTypeIds: z.array(z.nativeEnum(LearningObjectTypes)).optional(),
      keywords: z.array(z.string()).optional(),
      from: zodDates.optional(),
      to: zodDates.optional()
    }).parse(req.body)

    const certificates = await getPaginatedService(userID, offset, limit, search, labels, objectTypeIds, keywords, from, to)

    log('info', 'Successfully retrieved paginated certificates for user', { userID, count: certificates.items.length, totalRecords: certificates.TotalRecords, success: true, req })

    res.json({
      totalRecords: certificates.TotalRecords,
      items: certificates.items
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get paginated certificates: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to get paginated certificates', { error, req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
