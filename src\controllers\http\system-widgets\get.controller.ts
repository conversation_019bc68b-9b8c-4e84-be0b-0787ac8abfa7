import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getSystemWidgetService from '../../../services/mssql/system-widgets/get.service.js'
import getWidgetsForCreator from '../../../services/mssql/widgets/get-for-creator.service.js'
import getGroupsForSystemWidgetsService from '../../../services/mssql/system-widget-groups/get-multiple.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import getMultipleGroupsByIds from '../../../services/mssql/groups/get-multiple-by-ids.service.js'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'
import getWidgetSpecifiedContentService from '../../../services/mssql/content/get-widget-specified-content.service.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import getWidgetKeywordsService from '../../../services/mssql/widgets/get-keywords.service.js'

const log = logger.create('Controller-HTTP.get-system-widget', httpLogTransformer)

export default async function getSystemWidgetController (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const systemWidget = await getSystemWidgetService(id)
    systemWidget.fields.Widgets = (await getWidgetsForCreator(id)).map(widget => widget.fields)
    if (!systemWidget.fields.Everyone) {
      const systemGroups = await getGroupsForSystemWidgetsService(id)
      systemWidget.fields.Groups = await getMultipleGroupsByIds(systemGroups.map(group => group.fields.GroupID!))
    }

    for (const widget of systemWidget.fields.Widgets) {
      if (!widget.Filter) {
        try {
          const content = await getWidgetSpecifiedContentService(widget.ID!)
          widget.LearningContexts = content.filter(item => item.fields.EntityType === 'Learning Context').map(item => {
            const context = new LearningContextModel({ ...item.fields, Prerequisites: [] })
            return context.fields
          })
          widget.LearningObjects = content.filter(item => item.fields.EntityType === 'Learning Object').map(item => item.fields)
        } catch (error) {
          log('error', 'Failed to get the specified content for the widget', { widgetID: widget.ID, error, success: false, req })
        }
      } else {
        try {
          widget.Keywords = await getWidgetKeywordsService(widget.ID!)
        } catch (error) {
          log('error', 'Failed to get the keywords for the widget', { widgetID: widget.ID, error, success: false, req })
        }
      }
    }

    log('info', 'Successfully retrieved system widget', { systemWidgetID: systemWidget.fields.ID, success: true, req })

    res.json(systemWidget.fields)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get system widget: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    }
    else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get system widget because the record was not found in the DB', { success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      log('error', 'Failed to get system widget', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
