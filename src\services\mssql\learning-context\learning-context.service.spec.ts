import { expect } from 'chai'
import mssql, { addRow, DB_Errors, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import create from './create.service.js'
import update from './update.service.js'
import get from './get.service.js'
import getAvailableCoursePreReqs from './get-available-course-prereqs.service.js'
import { getContextCompletionCounts, getContextInProgressCount } from './get-completion-counts.service.js'
import { getCompletedUserIDs, getInProgressUserIDs } from './get-completion-user-ids.service.js'
import getCompletion from './get-completion.service.js'
import getContentCount from './get-content-count.service.js'
import getContextCreators from './get-context-creators.service.js'
import getContextModifiers from './get-context-modifiers.service.js'
import getContextsThatUseObject from './get-contexts-that-use-object.service.js'
import getDistinctLabels from './get-distinct-labels.service.js'
import getDuration from './get-duration.service.js'
import getILTContextsWithoutSessionsForAssignment from './get-ILT-contexts-without-sessions-for-assignment.service.js'
import getILTContextWithoutUpcomingSessions from './get-ILT-contexts-without-upcoming-sessions.service.js'
import getNestedContexts from './get-nested-contexts.service.js'
import getPaginated from './get-paginated.service.js'
import getParentContextIds from './get-parent-context-ids.service.js'
import getParentContextsForContext from './get-parent-contexts-for-context.service.js'
import { getUserCompletedStartedDate, getUserInProgressDates } from './get-user-completion-dates.service.js'
import isCourseIdUnique from './is-course-id-unique.service.js'
import moveContextToRoot from './move-context-to-root.service.js'
import resetParent from './reset-parent.service.js'
import remove from './delete.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import LearningObjectContextModel from '../../../models/learning-object-context.model.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { LearningObjectContext, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { ConnectionPool } from 'mssql'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { v4 as uuidV4 } from 'uuid'
import { fail } from 'assert'

let learningContext: LearningContextModel
let learningObject: LearningObject
let pool: ConnectionPool

describe('MSSQL Learning Contexts Service', () => {
  let context2: LearningContextModel
  let context3: LearningContextModel

  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    pool = mssql.getPool()
    learningObject = await addRow<LearningObject>(pool.request(), new LearningObjectModel({
      Title: 'Test learning context',
      Description: 'Running learning context unit test',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      LearningObjectTypeID: LearningObjectTypes.Audio,
      ContentID: uuidV4(),
      MinutesToComplete: 100
    }))
  })

  it('creates a learning context', async () => {
    learningContext = await create(new LearningContextModel({
      Title: 'Test Parent Context',
      Description: 'Running learning context unit test',
      ModifiedBy: AdminUserId,
      ModifiedOn: new Date(),
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Collection,
      Label: 'Learning Context Service Tests',
      Image: uuidV4(),
      CourseID: 'TestCourse-101'
    }))

    await addRow<LearningObjectContext>(pool.request(), new LearningObjectContextModel({
      LearningObjectID: learningObject.ID,
      LearningContextID: learningContext.fields.ID,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      OrderID: 1
    }))

    expect(learningContext.fields.ModifiedBy).to.equal(AdminUserId)
    expect(learningContext.fields.ModifiedOn).to.exist
    expect(learningContext.fields.CreatedBy).to.equal(AdminUserId)
    expect(learningContext.fields.CreatedOn).to.exist
    expect(learningContext.fields.ContextTypeID).to.equal(LearningContextTypes.Collection)
  })

  it('creates a second learning context', async () => {
    context2 = await create(new LearningContextModel({
      Title: 'Test Sub Context',
      Description: 'Test Context for learning context service test',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      ContextTypeID: LearningContextTypes.ElectiveOptional
    }))
  })

  it('creates a third learning context', async () => {
    context3 = await create(new LearningContextModel({
      Title: 'Test Sub Context',
      Description: 'Test Context for learning context service test',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      ContextTypeID: LearningContextTypes.ElectiveOptional
    }))
  })

  it('gets learning context object', async () => {
    try {
      const result = await get(learningContext.fields.ID!, AdminUserId)
      expect(result.fields.ModifiedBy).to.equal(AdminUserId)
      expect(result.fields.ModifiedOn).to.exist
      expect(result.fields.CreatedBy).to.equal(AdminUserId)
      expect(result.fields.CreatedOn).to.exist
      expect(result.fields.ContextTypeID).to.equal(4)
    } catch (error: any) {
      expect(error).to.exist
    }
  })

  it('get available course prereqs', async () => {
    const result = await getAvailableCoursePreReqs(learningContext.fields.ID!)
    expect(result.totalRecords).to.be.gte(0)
  })

  it('gets context completion counts', async () => {
    const result = await getContextCompletionCounts(learningContext.fields.ID!)
    expect(result).to.be.gte(0)
  })

  it('gets context in progress count', async () => {
    const result = await getContextInProgressCount(learningContext.fields.ID!)
    expect(result).to.be.gte(0)
  })

  it('gets completed user ids', async () => {
    const result = await getCompletedUserIDs(undefined, undefined, learningContext.fields.ID!)
    expect(result.totalRecords).to.be.gte(0)
  })

  it('gets in progress user ids', async () => {
    const result = await getInProgressUserIDs(undefined, undefined, learningContext.fields.ID!)
    expect(result.totalRecords).to.be.gte(0)
  })

  it('gets completions', async () => {
    const result = await getCompletion(learningContext.fields.ID!, AdminUserId)
    expect(result.numberOfObjects).to.be.gte(0)
  })

  it('gets content count', async () => {
    const result = await getContentCount(learningContext.fields.ID!)
    expect(result).to.be.gte(0)
  })

  it('gets context creators', async () => {
    const result = await getContextCreators(learningContext.fields.ContextTypeID!)
    expect(result.length).to.be.gte(1)
  })

  it('gets context modifiers', async () => {
    const result = await getContextModifiers(learningContext.fields.ContextTypeID!)
    expect(result.length).to.be.gte(1)
  })

  it('gets context that use object', async () => {
    const result = await getContextsThatUseObject(learningObject.ID!)
    expect(result.length).to.be.gte(1)
  })
  it('gets distinct labels', async () => {
    const result = await getDistinctLabels(true)
    expect(result.length).to.be.gte(1)
  })

  it('gets duration', async () => {
    const result = await getDuration(learningContext.fields.ID!)
    expect(result).to.equal(learningObject.MinutesToComplete)
  })

  it('gets ILT context without sessions for assignment', async () => {
    const result = await getILTContextsWithoutSessionsForAssignment(learningContext.fields.ID!)
    expect(result.length).to.be.gte(0)
  })

  it('gets ILT context without upcoming sessions', async () => {
    try {
      await getILTContextWithoutUpcomingSessions(learningContext.fields.ID!)
    } catch (error: any) {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.equal(DB_Errors.default.NOT_FOUND_IN_DB)
    }
  })

  it('gets nested contexts', async () => {
    try {
      await getNestedContexts(learningContext.fields.ID!)
    } catch (error: any) {
      expect(error).to.exist
    }
  })

  it('get paginated', async () => {
    const result = await getPaginated(1, 150)
    expect(result.totalRecords).to.be.gte(1)
    expect(result.contexts.length).to.be.gte(1)
    expect(result.contexts.length).to.be.lte(150)
  })

  it('gets parent context ids', async () => {
    const result = await getParentContextIds(learningContext.fields.ID!)
    expect(result.length).to.be.gte(0)
  })

  it('gets parent contexts for context', async () => {
    const result = await getParentContextsForContext(learningContext.fields.ID!)
    expect(result.length).to.be.gte(0)
  })

  it('gets user completed start date', async () => {
    try {
      const result = await getUserCompletedStartedDate(learningContext.fields.ID!, AdminUserId, new Date())
      expect(result).to.exist
    } catch (error: any) {
      expect(error).to.exist
    }
  })

  it('gets user in progress dates', async () => {
    const result = await getUserInProgressDates(learningContext.fields.ID!, AdminUserId)
    if (result.LastAccessed === undefined || result.startedOn === undefined) {
      expect(result.LastAccessed).to.eq(undefined)
      expect(result.startedOn).to.eq(undefined)
    } else {
      expect(result.LastAccessed).to.exist
      expect(result.startedOn).to.exist
    }
  })

  it('returns boolean if course id is unique', async () => {
    let result = await isCourseIdUnique(learningContext.fields.CourseID!)
    expect(result).to.be.false
    result = await isCourseIdUnique(learningContext.fields.CourseID!, learningContext.fields.ID)
    expect(result).to.be.true
    result = await isCourseIdUnique('Test-course-id')
    expect(result).to.be.true
  })

  it('moves context to the root', async () => {
    const result = await moveContextToRoot(learningContext)
    expect(result.fields.ModifiedBy).to.equal(AdminUserId)
    expect(result.fields.ModifiedOn).to.exist
    expect(result.fields.CreatedBy).to.equal(AdminUserId)
    expect(result.fields.CreatedOn).to.exist
    expect(result.fields.ContextTypeID).to.equal(4)
  })

  it('Resets the parent', async () => {
    try {
      await resetParent(context3, context2.fields.ID!, 1)
      expect(true).to.be.true
    } catch (error: any) {
      fail('Parent should have been reset')
    }
  })

  it('updates learning context object', async () => {
    learningContext.fields.Title = `${learningContext.fields.Title} Updated`
    learningContext.fields.OrderID = 2
    const result = await update(learningContext)
    expect(result.fields.OrderID).to.be.eq(2)
    expect(result.fields.Title).to.equal(learningContext.fields.Title)
  })

  it('removes learning context from DB', async () => {
    const result = await remove(learningContext.fields.ID!)
    expect(result).to.be.eq(undefined)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningObject>(pool.request(), LearningObjectsTableName, { CreatedBy: AdminUserId })
    await deleteRow<LearningObjectContext>(pool.request(), LearningObjectContextsTableName, { CreatedBy: AdminUserId })
  })
})
