import getMulti from '../../../services/mssql/learning-objects/get-multiple.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import { DB_Errors } from '@lcs/mssql-utility'
import learningObjectExtrasMapper from '../../../mappers/learning-object-extras.mapper.js'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID, zodStringToBoolean } from '@tess-f/backend-utils/validators'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'

const log = logger.create('Controller-HTTP.get-multi-learning-objects', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, click on a single course. Note the time. Check the LMS API log for the 'http-get-multi-learning-objects' label."
  try {
    const { contextID, visibility, contextCount, rating } = z.object({
      contextID: zodGUID.optional(),
      visibility: z.nativeEnum(Visibilities).optional(),
      contextCount: zodStringToBoolean.optional(),
      rating: zodStringToBoolean.optional()
    }).parse(req.query)

    const result = await getMulti(contextID, visibility, contextCount, rating)

    await learningObjectExtrasMapper(result, req.session.userId, {
      rating: true,
      bookmark: true,
      favorite: true,
      completion: !!contextID
    })

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, click on a single course. Note the time. Check the LMS API log for the 'http-get-multi-learning-objects' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved learning objects.', { count: result.length, success: true, req })

    res.json(result.map(obj => obj.fields))
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning objects because of input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request query data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get multi learning objects because they were not found in the database.', { success: false, req })
      res.json([])
    } else {
      log('error', 'Failed to get multi learning objects.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
