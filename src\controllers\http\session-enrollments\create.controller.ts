import logger from '@lcs/logger'
import moment from 'moment-timezone'
import SessionEnrollment, { sessionEnrollmentSchema } from '../../../models/session-enrollment.model.js'

import getSession from '../../../services/mssql/learning-context-sessions/get.service.js'
import getSessionEnrollments from '../../../services/mssql/session-enrollments/get.service.js'
import getInstructorIDs from '../../../services/mssql/learning-context-session-instructors/get.service.js'
import getSessionLocation from '../../../services/mssql/locations/get.service.js'
import getLearningContext from '../../../services/mssql/learning-context/get.service.js'
import updateSession from '../../../services/mssql/learning-context-sessions/update.service.js'
import createSessionEnrollment from '../../../services/mssql/session-enrollments/create.service.js'

import { sendInstructorEnrollmentMessage } from '@tess-f/email/dist/amqp/send-instructor-enrollment.js'
import { sendLearnerEnrollmentMessage } from '@tess-f/email/dist/amqp/send-learner-enrollment.js'
import { Request, Response } from 'express'
import { DB_Errors } from '@lcs/mssql-utility'
import getMultipleUsersByIds from '../../../services/mssql/users/get-multiple-by-ids.service.js'
import getUserById from '../../../services/mssql/users/get-by-id.service.js'
import httpStatus from 'http-status'
import settings from '../../../config/settings.js'
import sendAdminSessionEnrollments from '../../../services/amqp/email/send-admin-session-enrollments.service.js'
import sendNotification from '../../../services/amqp/notification/send-notification.service.js'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { ZodError } from 'zod'

const log = logger.create('Controller-HTTP.create-learning-context-session-enrollment', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  try {
    const enrollment = sessionEnrollmentSchema.parse(req.body)
    const sessionEnrollment = new SessionEnrollment(enrollment)
    const sessionId = enrollment.SessionID
    let allEnrollmentsForSession, sessionLocation
    sessionEnrollment.fields.CreatedBy = req.session.userId
    sessionEnrollment.fields.CreatedOn = new Date()

    const createdSessionEnrollment = await createSessionEnrollment(sessionEnrollment)

    try {
      allEnrollmentsForSession = await getSessionEnrollments(sessionId, undefined)
    } catch (error) {
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        allEnrollmentsForSession = []
      } else {
        throw error
      }
    }

    const session = await getSession(sessionId)

    try {
      sessionLocation = await getSessionLocation(session.fields.LocationID!)
    } catch (error) {
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        sessionLocation = undefined
      } else {
        throw error
      }
    }

    const sessionLearningContext = await getLearningContext(session.fields.LearningContextID!, undefined)

    const sessionInstructors = await getInstructorIDs(sessionId)

    if (session.fields.MaxEnrollments && session.fields.MaxEnrollments >= allEnrollmentsForSession.length) {
      session.fields.SessionStatusID = 3
      await updateSession(session)
    }

    // get all the instructor users from id management
    const instructors = await getMultipleUsersByIds(sessionInstructors.map(instructor => instructor.fields.UserID!))
    const learner = await getUserById(createdSessionEnrollment.fields.UserID!)

    const startTime = moment.tz(session.fields.StartDate!.toISOString(), session.fields.Timezone!)
    const endTime = moment.tz(session.fields.EndDate!.toISOString(), session.fields.Timezone!)
    const now = moment.tz(Date.now(), session.fields.Timezone!)

    // only send emails if this course hasn't started yet
    if (now.isSameOrBefore(startTime)) {
      // send emails to each instructor
      await sendInstructorEnrollmentMessage(
        settings.amqp.service_queues.email, {
          to: instructors.map(record => record.Email!),
          subject: 'Enrollment Confirmation',
          header: 'Enrollment Confirmation',
          message: {
            sessionTitle: sessionLearningContext.fields.Title!,
            sessionID: session.fields.SessionID!,
            sessionLearnerNames: [{ name: learner.FirstName + ' ' + learner.LastName, email: learner.Email ?? '' }],
            sessionStartDate: startTime.format('dddd, MMMM Do'),
            sessionTime: `${startTime.format('h:mm')} - ${endTime.format('h:mm')} (${endTime.format('z')})`,
            sessionLocation: sessionLocation?.fields,
            sessionWebinarInfo:
            {
              webinarUrl: (session.fields.WebinarURL ?? undefined) as string | undefined,
              webinarID: (session.fields.WebinarID ?? undefined) as string | undefined,
              webinarDialInNumber: (session.fields.WebinarDialInNumber ?? undefined) as string | undefined
            }
          }
        },
        settings.amqp.command_timeout
      )

      const instructorIDs = instructors.map(instructor => instructor.ID!).filter(ID => ID)
      let instructorMessage = `
        <p>The following learners have registered for: ${sessionLearningContext.fields.Title} - ${session.fields.SessionID}.</p>
        <h3>User(s) Registered</h3>
        <ul>
          ${learner.Email ? '<li><a href="mailto:' + learner.Email + '">' + learner.FirstName + ' ' + learner.LastName + '</a></li>' : '<li>' + learner.FirstName + ' ' + learner.LastName + '</li>'}
        </ul>
        <h3>Date/Time Information</h3>
        <p>
          <strong>${startTime.format('dddd, MMMM Do')}</strong><br>
          ${startTime.format('h:mm')} - ${endTime.format('h:mm')} (${endTime.format('z')})
        </p>
      `

      if (sessionLocation !== undefined) {
        instructorMessage += `
          <h3>Location Information</h3>
          <p>
            <strong>${sessionLocation.fields.Title}</strong><br>
            ${sessionLocation.fields.AddressLine1}
            ${sessionLocation.fields.AddressLine2 ? sessionLocation.fields.AddressLine2 + '<br>' : ''}
            ${sessionLocation.fields.City}, ${sessionLocation.fields.State} ${sessionLocation.fields.Zip}<br>
            ${sessionLocation.fields.Country}
          </p>
        `
      }

      if (session.fields.WebinarDialInNumber !== undefined || session.fields.WebinarID !== undefined || session.fields.WebinarURL !== undefined) {
        instructorMessage += `
          <h3>Webinar Information</h3>
          <p>
            ${session.fields.WebinarURL ? 'Webinar URL: ' + session.fields.WebinarURL + '<br>' : ''}
            ${session.fields.WebinarID ? 'Webinar/conference ID: ' + session.fields.WebinarID + '<br>' : ''}
            ${session.fields.WebinarDialInNumber ? 'Dial-In Number: ' + session.fields.WebinarDialInNumber : ''}
          </p>
        `
      }

      // send notification to each instructor
      await sendNotification({
        Title: 'Enrollment Confirmation',
        Message: instructorMessage,
        PublishDate: new Date(),
        Everyone: false,
        SystemID: 'lms',
        Priority: 1,
        UserIDs: instructorIDs
      })

      // send email and notifications to administrator
      await sendAdminSessionEnrollments(sessionLearningContext, session, [learner], startTime, endTime, sessionLocation)

      // send emails to the student
      if (learner.Email) {
        await sendLearnerEnrollmentMessage(
          settings.amqp.service_queues.email, {
            to: [learner.Email],
            subject: 'Enrollment Confirmation',
            header: 'Enrollment Confirmation',
            message: {
              startDate: session.fields.StartDate!.toString(),
              timezone: session.fields.Timezone!,
              endDate: session.fields.EndDate!.toString(),
              title: sessionLearningContext.fields.Title!,
              location: sessionLocation?.fields,
              webinarInfo: session.fields.WebinarDialInNumber && session.fields.WebinarID && session.fields.WebinarURL
                ? {
                    url: (session.fields.WebinarURL ?? undefined) as string | undefined,
                    id: (session.fields.WebinarID ?? undefined) as string | undefined,
                    dialInNumber: (session.fields.WebinarDialInNumber ?? undefined) as string | undefined
                  }
                : undefined,
              instructors: instructors.filter(instructor => instructor.Email).map(record => { return { name: record.FirstName + ' ' + record.LastName, email: record.Email! } })
            }
          },
          settings.amqp.command_timeout
        )
      }

      let learnerMessage = `
        <p>Good news! You are now enrolled in ${sessionLearningContext.fields.Title}.</p>
        <h3>Date/Time Information</h3>
        <p>
          <strong>${startTime.format('dddd, MMMM Do')}</strong><br>
          ${startTime.format('h:mm')} - ${endTime.format('h:mm')} (${endTime.format('z')})
        </p>
      `
      if (sessionLocation !== undefined) {
        learnerMessage += `
          <h3>Location Information</h3>
          <p>
            <strong>${sessionLocation.fields.Title}</strong><br>
            ${sessionLocation.fields.AddressLine1}
            ${sessionLocation.fields.AddressLine2 ? sessionLocation.fields.AddressLine2 + '<br>' : ''}
            ${sessionLocation.fields.City}, ${sessionLocation.fields.State} ${sessionLocation.fields.Zip}<br>
            ${sessionLocation.fields.Country}
          </p>
        `
      }

      if (session.fields.WebinarDialInNumber !== undefined || session.fields.WebinarID !== undefined || session.fields.WebinarURL !== undefined) {
        learnerMessage += `
          <h3>Webinar Information</h3>
          <p>
            ${session.fields.WebinarURL ? 'Webinar URL: ' + session.fields.WebinarURL + '<br>' : ''}
            ${session.fields.WebinarID ? 'Webinar/conference ID: ' + session.fields.WebinarID + '<br>' : ''}
            ${session.fields.WebinarDialInNumber ? 'Dial-In Number: ' + session.fields.WebinarDialInNumber : ''}
          </p>
        `
      }

      learnerMessage += `
        <h3>Questions?</h3>
        <p>Contact the instructor(s)</p>
        <ul>${instructors.map(instructor => instructor.Email ? '<li><a href="mailto:' + instructor.Email + '">' + instructor.FirstName + ' ' + instructor.LastName + '</a></li>' : '<li>' +instructor.FirstName + ' ' + instructor.LastName + '</li>')}</ul>
      `

      // send notification to the student
      await sendNotification({
        Title: 'Enrollment Confirmation',
        Message: learnerMessage,
        PublishDate: new Date(),
        Everyone: false,
        SystemID: 'lms',
        Priority: 1,
        UserIDs: [learner.ID!]
      })

    }

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, bookmark a course. Note the time. Check the LMS API log for the 'http-create-learning-context-bookmark' label and message indicating successful creation."
    log('info', `Successfully created learning context session enrollments for session`, { sessionId, success: true, req })

    res.json(createdSessionEnrollment.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create learning context session enrollment due to invalid data in the request.', { error, req, success: false })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      const errorMessage = getErrorMessage(error)
      if (errorMessage.startsWith('Violation of UNIQUE KEY constraint')) {
        log('warn', `Failed to create learning context session enrollments for session because it already exists in the database.`, { sessionId: req.body.SessionID, error, req, success: false })
      } else {
        log('error', 'Failed to create learning context session enrollments.', { error, req, success: false })
      }
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
