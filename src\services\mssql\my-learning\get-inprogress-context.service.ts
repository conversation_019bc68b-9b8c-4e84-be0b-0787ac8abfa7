import mssql from '@lcs/mssql-utility'
import LearningContextModel from '../../../models/learning-context.model.js'
import { getAverageRatingMap } from '../learning-context/utils.service.js'
import getCompletionService from '../learning-context/get-completion.service.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { SessionEnrollmentFields, SessionEnrollmentsTableName } from '@tess-f/sql-tables/dist/lms/session-enrollment.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { FlatLearningContextTreeFields, FlatLearningContextTreeViewName } from '@tess-f/sql-tables/dist/lms/flat-learning-context-view.js'

export default async function (userID: string, from?: Date, to?: Date): Promise<LearningContextModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('userID', userID)

  // Only fetch learning contexts that the user has progress for
  const results = await request.query<LearningContext>(`
    WITH Object_Parent_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] IN (
        SELECT [${LearningObjectContextFields.LearningContextID}]
        FROM [${LearningObjectContextsTableName}]
        WHERE [${LearningObjectContextFields.LearningObjectID}] IN (
          SELECT [${LearnerProgressFields.LearningObjectID}]
          FROM [${LearnerProgressTableName}]
          WHERE [${LearnerProgressFields.UserID}] = @userID
        )
      )
      
      UNION ALL
      
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}]
      FROM [${FlatLearningContextTreeViewName}]
          INNER JOIN [Object_Parent_Nodes] ON [Object_Parent_Nodes].[${LearningContextFields.ParentContextID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}]
    ), ILT_Parent_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] IN (
        SELECT [${LearningContextSessionFields.LearningContextID}]
        FROM [${LearningContextSessionsTableName}]
        WHERE [${LearningContextSessionFields.ID}] IN (
          SELECT [${SessionEnrollmentFields.SessionID}]
          FROM [${SessionEnrollmentsTableName}]
          WHERE [${SessionEnrollmentFields.UserID}] = @userID
          AND [${SessionEnrollmentFields.SessionID}] NOT IN (
            SELECT [${LearnerProgressFields.LearningContextSessionID}]
            FROM [${LearnerProgressTableName}]
            WHERE [${LearnerProgressFields.UserID}] = @userID
            AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.fail}
            AND [${LearnerProgressFields.LearningContextSessionID}] IS NOT NULL
          )
        )
      )
      
      UNION ALL
      
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}]
      FROM [${FlatLearningContextTreeViewName}]
          INNER JOIN [ILT_Parent_Nodes] ON [ILT_Parent_Nodes].[${LearningContextFields.ParentContextID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}]
    )
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE ([${LearningContextFields.VisibilityID}] = ${Visibilities.Browsable} OR [${LearningContextFields.VisibilityID}] = ${Visibilities.Obsolete})
    AND (
      [${LearningContextFields.ID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [Object_Parent_Nodes]
      )
      OR [${LearningContextFields.ID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [ILT_Parent_Nodes]
      )
    )
  `)

  const contexts = results.recordset.map(record => new LearningContextModel(record))
  const inProgressContexts = (await Promise.all(contexts.map(async context => {
    const isComplete = await getCompletionService(context.fields.ID!, userID, from, to)
    if ((isComplete.completion > 0 && isComplete.completion < 100 && context.fields.CourseTypeID !== CourseTypes.InstructorLed) || (isComplete.completion < 100 && context.fields.CourseTypeID === CourseTypes.InstructorLed)) {
      return context
    } else {
      return null
    }
  }))).filter(context => context !== null) as LearningContextModel[]

  const ratingMap = await getAverageRatingMap(pool.request(), inProgressContexts.map(context => context.fields.ID!))
  inProgressContexts.forEach(context => {
    const ratings = ratingMap[context.fields.ID!]
    if (ratings) {
      context.fields.Rating = ratings.average
      context.fields.RatingCount = ratings.count
    }
  })

  return inProgressContexts
}
