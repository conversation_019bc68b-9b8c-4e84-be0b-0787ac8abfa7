const { expect } = require('chai')
const httpMocks = require('node-mocks-http')
const logger = require('../api/../shared_libraries/libs/logger')
const utils = require('../api/utils/test-agent.utils')

const getActiveLearnerHistory = require('../api/controllers/http/active-learners/get-history.controller')
const getActiveLearnerLive = require('../api/controllers/http/active-learners/get-live.controller')
const getActiveLearnerMulti = require('../api/controllers/http/active-learners/get-multiple.controller')
const getActiveLearner = require('../api/controllers/http/active-learners/get.controller')

const getActivityStream = require('../api/controllers/http/activity-stream/get.controller')

const getClaimsUserMulti = require('../api/controllers/http/claims/user/get-multiple.controller')
const createClaims = require('../api/controllers/http/claims/user/create.controller')
const updateClaims = require('../api/controllers/http/claims/user/update.controller')
const deleteClaims = require('../api/controllers/http/claims/user/delete.controller')

const keywordsCreate = require('../api/controllers/http/keywords/create.controller')
const keywordsDelete = require('../api/controllers/http/keywords/delete.controller')

const learnerProgressCreate = require('../api/controllers/http/learner-progress/create.controller')
const learnerProgressDelete = require('../api/controllers/http/learner-progress/delete.controller')
const learnerProgressGetMulti = require('../api/controllers/http/learner-progress/get-multiple.controller')
const learnerProgressGet = require('../api/controllers/http/learner-progress/get.controller')
const learnerProgressResume = require('../api/controllers/http/learner-progress/resume-learing.controller')
const learnerProgressUpdate = require('../api/controllers/http/learner-progress/update.controller')

const learningContextBookmarksCreate = require('../api/controllers/http/learning-context-bookmarks/create.controller')
const learningContextBookmarksDelete = require('../api/controllers/http/learning-context-bookmarks/delete.controller')
const learningContextBookmarksGet = require('../api/controllers/http/learning-context-bookmarks/get.controller')

const learningContextFavoritesCreate = require('../api/controllers/http/learning-context-favorites/create.controller')
const learningContextFavoritesDelete = require('../api/controllers/http/learning-context-favorites/delete.controller')
const learningContextFavoritesGet = require('../api/controllers/http/learning-context-favorites/get.controller')

const learningContextRatingsCreate = require('../api/controllers/http/learning-context-ratings/create.controller')
const learningContextRatingsDelete = require('../api/controllers/http/learning-context-ratings/delete.controller')
const learningContextRatingsGet = require('../api/controllers/http/learning-context-ratings/get.controller')
const learningContextRatingsUpdate = require('../api/controllers/http/learning-context-ratings/update.controller')

const learningContextsCreate = require('../api/controllers/http/learning-contexts/create.controller')
const learningContextsDelete = require('../api/controllers/http/learning-contexts/delete.controller')
const learningContextsGetCompletion = require('../api/controllers/http/learning-contexts/get-completion.controller')
const learningContextsGetDuration = require('../api/controllers/http/learning-contexts/get-duration.controller')
const learningContextsGetMulti = require('../api/controllers/http/learning-contexts/get-multiple.controller')
const learningContextsGetBookmarked = require('../api/controllers/http/learning-contexts/get-my-bookmarked.controller')
const learningContextsGetFavorites = require('../api/controllers/http/learning-contexts/get-my-favorites.controller')
const learningContextsGetPopular = require('../api/controllers/http/learning-contexts/get-popular.controller')
const learningContextsGetRecent = require('../api/controllers/http/learning-contexts/get-recent.controller')
const learningContextsGet = require('../api/controllers/http/learning-contexts/get.controller')
const learningContextsReparent = require('../api/controllers/http/learning-contexts/reparent-context.controller')
const learningContextsUpdate = require('../api/controllers/http/learning-contexts/update.controller')

const learningObjectBookmarksCreate = require('../api/controllers/http/learning-object-bookmarks/create.controller')
const learningObjectBookmarksDelete = require('../api/controllers/http/learning-object-bookmarks/delete.controller')
const learningObjectBookmarksGet = require('../api/controllers/http/learning-object-bookmarks/get.controller')

const learningObjectContextsCreate = require('../api/controllers/http/learning-object-contexts/create.controller')
const learningObjectContextsDelete = require('../api/controllers/http/learning-object-contexts/delete.controller')
const learningObjectContextsMove = require('../api/controllers/http/learning-object-contexts/move.controller')
const learningObjectContextsUpdate = require('../api/controllers/http/learning-object-contexts/update.controller')

const learningObjectFavoritesCreate = require('../api/controllers/http/learning-object-favorites/create.controller')
const learningObjectFavoritesDelete = require('../api/controllers/http/learning-object-favorites/delete.controller')
const learningObjectFavoritesGet = require('../api/controllers/http/learning-object-favorites/get.controller')

const learningObjectRatingsCreate = require('../api/controllers/http/learning-object-ratings/create.controller')
const learningObjectRatingsDelete = require('../api/controllers/http/learning-object-ratings/delete.controller')
const learningObjectRatingsGet = require('../api/controllers/http/learning-object-ratings/get.controller')
const learningObjectRatingsUpdate = require('../api/controllers/http/learning-object-ratings/update.controller')

const learningObjectsCreate = require('../api/controllers/http/learning-objects/create.controller')
const learningObjectsDelete = require('../api/controllers/http/learning-objects/delete.controller')
const learningObjectsGetBookmarked = require('../api/controllers/http/learning-objects/get-my-bookmarked.controller')
const learningObjectsGetFavorites = require('../api/controllers/http/learning-objects/get-my-favorites.controller')
const learningObjectsGetViewsKeyword = require('../api/controllers/http/learning-objects/get-user-views-by-keyword.controller')
const learningObjectsGetViewsType = require('../api/controllers/http/learning-objects/get-user-views-by-type.controller')
const learningObjectsGetViewHistory = require('../api/controllers/http/learning-objects/get-view-history.controller')
const learningObjectsGet = require('../api/controllers/http/learning-objects/get.controller')
const learningObjectsUpdate = require('../api/controllers/http/learning-objects/update.controller')

const myLearningGetCompContexts = require('../api/controllers/http/my-learning/get-completed-contexts.controller')
const myLearningGetCompObjects = require('../api/controllers/http/my-learning/get-completed-objects.controller')
const myLearningGetInProgContexts = require('../api/controllers/http/my-learning/get-completed-contexts.controller')
const myLearningGetInProgObjects = require('../api/controllers/http/my-learning/get-completed-objects.controller')
const myLearningGetLearningMeta = require('../api/controllers/http/my-learning/get-learning-metadata.controller')
const myLearningGetMyMeta = require('../api/controllers/http/my-learning/get-my-metadata.controller')

const relatedLearningGet = require('../api/controllers/http/related-learning/get.controller')

const skillLevelsCreate = require('../api/controllers/http/skill-levels/create.controller')
const skillLevelsDelete = require('../api/controllers/http/skill-levels/delete.controller')
const skillLevelsGet = require('../api/controllers/http/skill-levels/get.controller')
const skillLevelsUpdate = require('../api/controllers/http/skill-levels/update.controller')

const userPrefCreate = require('../api/controllers/http/user-preferences/create.controller')
const userPrefDelete = require('../api/controllers/http/user-preferences/delete.controller')
const userPrefGet = require('../api/controllers/http/user-preferences/get.controller')
const userPrefUpdate = require('../api/controllers/http/user-preferences/update.controller')

const usersGet = require('../api/controllers/http/users/get.controller')

const nonExistingID = '00000000-0000-0000-0000-000000000000'
const invalidID = 'not-a-guid'

describe('STIG logging for failure conditions', () => {
  before(async () => {
    await logger.init({
      name: 'test-lms-api',
      level: 'silly',
      use_console: true,
      use_elasticsearch: false
    })
  })

  // -------- active-learners --------

  it('should log failed http-get-active-learner-history', async () => {
    const mocks = httpMocks.createMocks({
    })
    utils.fillRequestHeaders(mocks.req)

    await getActiveLearnerHistory(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(500)
  })

  it('should log failed http-get-active-learner-live', async () => {
    const mocks = httpMocks.createMocks({
    })
    utils.fillRequestHeaders(mocks.req)

    await getActiveLearnerLive(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(500)
  })

  it('should log failed http-get-multi-active-learner', async () => {
    const mocks = httpMocks.createMocks({
    })
    utils.fillRequestHeaders(mocks.req)

    await getActiveLearnerMulti(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(500)
  })

  it('should log failed http-get-active-learner', async () => {
    const mocks = httpMocks.createMocks({
    })
    utils.fillRequestHeaders(mocks.req)

    await getActiveLearner(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(500)
  })

  // -------- activity-stream --------

  it('should log failed http-get-activity-stream', async () => {
    const mocks = httpMocks.createMocks({
    })
    utils.fillRequestHeaders(mocks.req)

    await getActivityStream(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(400)
  })

  // -------- claims --------

  it('should log failed http-create-user-claims', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    createClaims(req, res)
  })

  it('should log failed http-delete-user-claims', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(404)
      done()
    })

    deleteClaims(req, res)
  })

  it('should log failed http-get-multi-user-claims', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      done()
    })

    getClaimsUserMulti(req, res)
  })

  it('should log failed http-update-user-claims', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    updateClaims(req, res)
  })

  // -------- keywords --------

  it('should log failed http-create-keyword', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    keywordsCreate(req, res)
  })

  it('should log failed http-delete-keyword', done => {
    const req = utils.createRequestWithHeaders({
      params: { name: 'keyword-not-in-database' }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(404)
      done()
    })

    keywordsDelete(req, res)
  })

  // -------- learner-progress --------

  it('should log failed http-create-learner-progress', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learnerProgressCreate(req, res)
  })

  it('should log failed http-delete-learner-progress', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(404)
      done()
    })

    learnerProgressDelete(req, res)
  })

  it('should log failed http-get-multi-learner-progress', done => {
    const req = utils.createRequestWithHeaders({
      params: { userID: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(404)
      done()
    })

    learnerProgressGetMulti(req, res)
  })

  it('should log failed http-get-learner-progress', done => {
    const req = utils.createRequestWithHeaders({
      params: { userID: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      done()
    })

    learnerProgressGet(req, res)
  })

  it('should log failed http-resume-learning', done => {
    const req = utils.createRequestWithHeaders({
      params: { userID: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(404)
      done()
    })

    learnerProgressResume(req, res)
  })

  it('should log failed http-update-learner-progress', done => {
    const req = utils.createRequestWithHeaders({
      params: { userID: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learnerProgressUpdate(req, res)
  })

  // -------- learning context bookmarks --------

  it('should log failed http-create-learning-context-bookmark', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningContextBookmarksCreate(req, res)
  })

  it('should log failed http-delete-learning-context-bookmark', done => {
    const req = utils.createRequestWithHeaders({
      params: { userID: nonExistingID, contextID: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(404)
      done()
    })

    learningContextBookmarksDelete(req, res)
  })

  it('should log failed http-get-learning-context-bookmark', done => {
    const req = utils.createRequestWithHeaders({
      query: { userID: nonExistingID, contextID: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(404)
      done()
    })

    learningContextBookmarksGet(req, res)
  })

  // -------- learning context favorites --------

  it('should log failed http-create-learning-context-favorites', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningContextFavoritesCreate(req, res)
  })

  it('should log failed http-delete-learning-context-favorites', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningContextFavoritesDelete(req, res)
  })

  it('should log failed http-get-learning-context-favorites', done => {
    const req = utils.createRequestWithHeaders({
      query: { userID: nonExistingID, contextID: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(404)
      done()
    })

    learningContextFavoritesGet(req, res)
  })

  // -------- learning context ratings --------

  it('should log failed http-create-learning-context-ratings', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningContextRatingsCreate(req, res)
  })

  it('should log failed http-delete-learning-context-ratings', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningContextRatingsDelete(req, res)
  })

  it('should log failed http-get-learning-context-ratings', done => {
    const req = utils.createRequestWithHeaders({
      query: { userID: nonExistingID, contextID: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningContextRatingsGet(req, res)
  })

  it('should log failed http-update-learning-context-ratings', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningContextRatingsUpdate(req, res)
  })

  // -------- learning contexts --------

  it('should log failed http-create-learning-contexts', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningContextsCreate(req, res)
  })

  it('should log failed http-delete-learning-contexts', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningContextsDelete(req, res)
  })

  it('should log failed http-get-learning-context-completion', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(404)
      done()
    })

    learningContextsGetCompletion(req, res)
  })

  it('should log failed http-get-learning-context-duration', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: invalidID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningContextsGetDuration(req, res)
  })

  it('should log failed http-get-multi-learning-contexts', done => {
    const req = utils.createRequestWithHeaders({
      query: { type: 'invalidType' }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningContextsGetMulti(req, res)
  })

  it('should log failed http-get-my-bookmarked-learning-contexts', done => {
    const req = utils.createRequestWithHeaders({

    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningContextsGetBookmarked(req, res)
  })

  it('should log failed http-get-my-favorite-learning-contexts', done => {
    const req = utils.createRequestWithHeaders({

    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningContextsGetFavorites(req, res)
  })

  it('should log failed http-get-popular-learning-contexts', done => {
    const req = utils.createRequestWithHeaders({
      query: { typeID: 'invalidTypeID' }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningContextsGetPopular(req, res)
  })

  it('should log failed http-get-recent-learning-contexts', done => {
    const req = utils.createRequestWithHeaders({
      query: { typeID: 'invalidTypeID' }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningContextsGetRecent(req, res)
  })

  it('should log failed http-get-learning-context', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningContextsGet(req, res)
  })

  it('should log failed http-reparent-context', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningContextsReparent(req, res)
  })

  it('should log failed http-update-learning-context', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningContextsUpdate(req, res)
  })

  // -------- learning object bookmarks --------

  it('should log failed http-create-learning-object-bookmark', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectBookmarksCreate(req, res)
  })

  it('should log failed http-delete-learning-object-bookmark', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectBookmarksDelete(req, res)
  })

  it('should log failed http-get-learning-object-bookmarks', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectBookmarksGet(req, res)
  })

  // -------- learning object contexts --------

  it('should log failed http-create-learning-object-contexts', done => {
    const req = utils.createRequestWithHeaders({
      decoded_token: { id: nonExistingID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectContextsCreate(req, res)
  })

  it('should log failed http-delete-learning-object-contexts', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(404)
      done()
    })

    learningObjectContextsDelete(req, res)
  })

  it('should log failed http-move-object-context', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningObjectContextsMove(req, res)
  })

  it('should log failed http-update-learning-object-contexts', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectContextsUpdate(req, res)
  })

  // -------- learning object favorites --------

  it('should log failed http-create-learning-object-favorite', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectFavoritesCreate(req, res)
  })

  it('should log failed http-delete-learning-object-favorite', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectFavoritesDelete(req, res)
  })

  it('should log failed http-get-learning-object-favorites', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectFavoritesGet(req, res)
  })

  // -------- learning object ratings --------

  it('should log failed http-create-learning-object-rating', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectRatingsCreate(req, res)
  })

  it('should log failed http-delete-learning-object-rating', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectRatingsDelete(req, res)
  })

  it('should log failed http-get-learning-object-ratings', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectRatingsGet(req, res)
  })

  it('should log failed http-update-learning-object-rating', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectRatingsUpdate(req, res)
  })

  // -------- learning objects --------

  it('should log failed http-create-learning-object', done => {
    const req = utils.createRequestWithHeaders({
      body: { Keywords: 'notAnArray' }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectsCreate(req, res)
  })

  it('should log failed http-delete-learning-object', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectsDelete(req, res)
  })

  it('should log failed http-get-my-bookmarked-learning-objects', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningObjectsGetBookmarked(req, res)
  })

  it('should log failed http-get-my-favorite-learning-objects', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningObjectsGetFavorites(req, res)
  })

  it('should log failed http-get-learning-object-user-views-by-keyword', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectsGetViewsKeyword(req, res)
  })

  it('should log failed http-get-learning-object-user-views-by-type', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectsGetViewsType(req, res)
  })

  it('should log failed http-get-learning-object-view-history', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectsGetViewHistory(req, res)
  })

  it('should log failed http-get-learning-object', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningObjectsGet(req, res)
  })

  it('should log failed http-update-learning-object', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    learningObjectsUpdate(req, res)
  })

  // -------- my learning --------

  it('should log failed http-get-completed-learning-contexts', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    myLearningGetCompContexts(req, res)
  })

  it('should log failed http-get-completed-learning-objects', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    myLearningGetCompObjects(req, res)
  })

  it('should log failed http-get-inprogress-learning-context', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    myLearningGetInProgContexts(req, res)
  })

  it('should log failed http-get-inprogress-learning-objects', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    myLearningGetInProgObjects(req, res)
  })

  it('should log failed http-get-learning-metadata', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    myLearningGetLearningMeta(req, res)
  })

  it('should log failed http-get-my-metadata', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    myLearningGetMyMeta(req, res)
  })

  // -------- related learning --------

  it('should log failed http-get-related-learning', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    relatedLearningGet(req, res)
  })

  // -------- skill levels --------

  it('should log failed http-create-skill-level', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    skillLevelsCreate(req, res)
  })

  it('should log failed http-delete-skill-level', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(404)
      done()
    })

    skillLevelsDelete(req, res)
  })

  it('should log failed http-get-skill-level', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    skillLevelsGet(req, res)
  })

  it('should log failed http-update-skill-level', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    skillLevelsUpdate(req, res)
  })

  // -------- user preferences --------

  it('should log failed http-create-user-preferences', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    userPrefCreate(req, res)
  })

  it('should log failed http-delete-user-preferences', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    userPrefDelete(req, res)
  })

  it('should log failed http-get-user-preferences', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    userPrefGet(req, res)
  })

  it('should log failed http-update-user-preferences', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(400)
      done()
    })

    userPrefUpdate(req, res)
  })

  // -------- users --------

  it('should log failed http-get-users', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    usersGet(req, res)
  })

  after(done => {
    // rabbitMQ.close();
    logger.close()
    done()
  })
})
