import logger from '@lcs/logger'
import getLearningMetadata from '../../../services/mssql/my-learning/get-metadata.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR } = httpStatus
import getPaginatedUserIds from '../../../services/mssql/users/get-paginated-user-ids.service.js'
import { LearningMetadata } from '@tess-f/lms/dist/common/learning-metadata.js'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodDates, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-users-learning-metadata', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { search, offset, limit, from, to } = z.object({
      search: z.string().optional(),
      offset: zodOffset,
      limit: zodLimit,
      from: zodDates.optional(),
      to: zodDates.optional()
    }).parse(req.query)

    // build out the user learning metadata table
    let userLearningMetadata: Array<LearningMetadata> = []

    // get the users
    const userIds = await getPaginatedUserIds(offset, limit, search)

    // get the metadata for each user
    userLearningMetadata = await Promise.all(userIds.ids.map(async id => {
      try {
        const metadata = await getLearningMetadata(id, from, to)
        metadata.UserID = id
        return metadata
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        if (errorMessage !== DB_Errors.default.NOT_FOUND_IN_DB) {
          throw error
        } else {
          return {
            Completed: 0,
            Inprogress: 0,
            Assigned: 0,
            Overdue: 0,
            UserID: id
          }
        }
      }
    }))

    log('info', 'Successfully retrieved user learning metadata', { count: userLearningMetadata.length, success: true, req })

    res.json({
      UserLearningMetadata: userLearningMetadata,
      TotalRecords: userIds.totalRecords
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to retrieve users learning metadata: validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request query data: '))
    } else {
      log('error', 'Failed to retrieve users learning metadata.', { error, req, success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
