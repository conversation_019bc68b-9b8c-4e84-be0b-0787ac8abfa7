import { Request<PERSON><PERSON><PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getController from './get.controller.js'
import updateController from './update.controller.js'

const router = Router()

router.get('/context-rating', getController as RequestHandler)
router.post('/context-rating', createController as RequestHandler)
router.put('/context-rating/:id', updateController as RequestHandler)
router.delete('/context-rating/:id', deleteController as RequestHandler)

export default router
