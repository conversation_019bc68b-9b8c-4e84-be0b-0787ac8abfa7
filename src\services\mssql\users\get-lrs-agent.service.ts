import mssql, { DB_Errors, getRows } from '@lcs/mssql-utility'
import { AgentJson } from '../../../models/amqp/lrs/statement.model.js'
import { User, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'

export default async function getUserByLrsAgent (agent: AgentJson): Promise<User> {
  if (agent.account === undefined && agent.mbox === undefined) {
    throw new Error('Either account or mbox must be provided')
  }

  const search: Partial<User> = {}

  if (agent.account !== undefined) {
    search.Username = agent.account.name
  } else if (agent.mbox) {
    search.Email = agent.mbox
  }

  const results = await getRows<User>(UserTableName, mssql.getPool().request(), search, 1)

  if (results.length > 0) {
    return results[0]
  } else {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }
}
