import mssql from '@lcs/mssql-utility'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'

export default async function (objectID: string): Promise<LearningContextModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('objectID', objectID)
  const res = await request.query<LearningContext>(`
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ID}] IN (
      SELECT [${LearningObjectContextFields.LearningContextID}]
      FROM [${LearningObjectContextsTableName}]
      WHERE [${LearningObjectContextFields.LearningObjectID}] = @objectID
    )
  `)
  return res.recordset.map(record => new LearningContextModel(undefined, record))
}
