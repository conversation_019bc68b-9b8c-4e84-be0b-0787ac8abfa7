import logger from '@lcs/logger'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import Sinon from 'sinon'
import { v4 as uuid } from 'uuid'
import httpStatus from 'http-status'
import esmock from 'esmock'
import AssignmentsModel from '../../../models/assignment.model'

describe('HTTP Controller: get assignment', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns bad request when the id parameter is missing', async () => {
    const controller = await esmock('./get.controller.js')
    const mocks = httpMocks.createMocks()
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.equal('Invalid assignment ID in request parameters')
  })

  it('returns bad request when the id parameter is not a valid uuid', async () => {
    const controller = await esmock('./get.controller.js')
    const mocks = httpMocks.createMocks({ params: { id: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.equal('Invalid assignment ID in request parameters')
  })

  it('returns bad request when the id parameter is a number', async () => {
    const controller = await esmock('./get.controller.js')
    const mocks = httpMocks.createMocks({ params: { id: 1 } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.equal('Invalid assignment ID in request parameters')
  })

  it('returns ok when the request params are valid', async () => {
    const controller = await esmock('./get.controller.js', {
      '../../../services/mssql/assignments/get.service.js': {
        default: Sinon.stub().returns(Promise.resolve(new AssignmentsModel({ ID: uuid() })))
      },
      '../../../services/mssql/assignment-learning-objects/get-multiple.service.js': {
        default: Sinon.stub().returns(Promise.resolve([]))
      },
      '../../../services/mssql/assignment-learning-contexts/get-multiple.service.js': {
        default: Sinon.stub().returns(Promise.resolve([]))
      },
      '../../../services/mssql/assignment-users/get-users-and-groups-for-assignment.service.js': {
        default: Sinon.stub().returns(Promise.resolve({ users: [], groups: [] }))
      }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = JSON.parse(mocks.res._getData())
    expect(data).to.not.be.undefined
  })
})
