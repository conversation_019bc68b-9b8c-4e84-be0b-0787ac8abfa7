import { Request<PERSON><PERSON><PERSON>, Router } from 'express'
import getAdminOverviewController from './get-admin-overview.controller.js'
import getAssignmentsDashboardController from './get-assignments-dashboard.controller.js'
import getUserActivityDashboardController from './get-user-activity-dashboard.controller.js'
import getUsersMetadataController from './get-users-metadata.controller.js'
import getContentOverviewController from './get-content-overview.controller.js'
import getLearningContextOverviewController from './get-learning-context-overview.controller.js'
import getLearningObjectOverviewController from './get-learning-object-overview.controller.js'
import getAssignmentDetailsController from './get-assignment-details.controller.js'
import getMyTeamsLearningMetadataController from './get-my-teams-learning-metadata.controller.js'
import getMyTeamOverviewController from './get-my-team-overview.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'
import checkHasTeamMiddleware from './middlewares/check-has-team.middleware.js'

const router = Router()

router.get('/admin-overview-dashboard', checkClaims([Claims.VIEW_ADMIN_OVERVIEW]), getAdminOverviewController as RequestHandler)
router.get('/user-activity-dashboard/:id', checkClaims([Claims.VIEW_MY_ACTIVITY, Claims.VIEW_ADMIN_OVERVIEW]), getUserActivityDashboardController as RequestHandler)
router.get('/users-learning-metadata', checkClaims([Claims.VIEW_ADMIN_OVERVIEW]), getUsersMetadataController as RequestHandler)
router.get('/assignment-dashboard', checkClaims([Claims.VIEW_ASSIGNMENT_DASHBOARD]), getAssignmentsDashboardController as RequestHandler)
router.post('/content-overview', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getContentOverviewController as RequestHandler)
router.get('/learning-context-overview/:id', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getLearningContextOverviewController as RequestHandler)
router.get('/learning-object-overview/:id', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getLearningObjectOverviewController as RequestHandler)
router.post('/assignment-details-dashboard/:id', checkClaims([Claims.VIEW_ASSIGNMENT_DASHBOARD]), getAssignmentDetailsController as RequestHandler)
router.get('/my-teams-learning-metadata', checkHasTeamMiddleware, getMyTeamsLearningMetadataController as RequestHandler)
router.get('/my-teams-overview', checkHasTeamMiddleware, getMyTeamOverviewController as RequestHandler)

export default router
