import mssql from '@lcs/mssql-utility'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import { LearningContextSession, LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { SessionStatuses } from '@tess-f/sql-tables/dist/lms/session-status.js'

export default async function (contextID: string, offset = 0, limit = 10): Promise<{ totalRecords: number, sessions: LearningContextSessionModel[]}> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('contextID', contextID)
  request.input('offset', offset)
  request.input('limit', limit)

  const results = await request.query<LearningContextSession & {TotalRecords: number}>(`
    SELECT *, TotalRecords = COUNT(*) OVER()
    FROM [${LearningContextSessionsTableName}]
    WHERE [${LearningContextSessionFields.LearningContextID}] = @contextID
    AND [${LearningContextSessionFields.SessionStatusID}] != ${SessionStatuses.Hidden}
    AND [${LearningContextSessionFields.StartDate}] <= GETDATE()
    ORDER BY [${LearningContextSessionFields.StartDate}] DESC
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    sessions: results.recordset.map(record => new LearningContextSessionModel(undefined, record))
  }
}
