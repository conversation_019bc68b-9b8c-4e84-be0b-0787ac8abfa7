import mssql from '@lcs/mssql-utility'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextUserViewFields, LearningContextUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-view.js'
import { Request } from 'mssql'

export default async function (userID: string, from?: Date, to?: Date): Promise<{ Views: number, UserID: string, Label: string }[]> {
  const pool = mssql.getPool()
  return await getUserViewsByType(pool.request(), userID, from, to)
}

async function getUserViewsByType (request: Request, id: string, from?: Date, to?: Date): Promise<{ Views: number, UserID: string, Label: string }[]> {
  request.input('id', id)

  let query = `
    SELECT COUNT ([${LearningContextUserViewsTableName}].[${LearningContextUserViewFields.LearningContextID}]) AS Views, [${LearningContextUserViewsTableName}].[${LearningContextUserViewFields.UserID}], [${LearningContextTableName}].[${LearningContextFields.Label}]
    FROM [${LearningContextUserViewsTableName}]
    INNER JOIN [${LearningContextTableName}] ON [${LearningContextUserViewsTableName}].[${LearningContextUserViewFields.LearningContextID}] = [${LearningContextTableName}].[${LearningContextFields.ID}]
    WHERE [${LearningContextUserViewsTableName}].[${LearningContextUserViewFields.UserID}] = @id
  `

  if (to && from) {
    request.input('from', from)
    request.input('to', to)
    query += `AND [${LearningContextUserViewsTableName}].[${LearningContextUserViewFields.CreatedOn}] Between @from AND @to `
  }

  query += `GROUP BY [${LearningContextUserViewsTableName}].[${LearningContextUserViewFields.UserID}], [${LearningContextTableName}].[${LearningContextFields.Label}]`

  const results = await request.query<{ Views: number, UserID: string, Label: string }>(query)
  return results.recordset
}
