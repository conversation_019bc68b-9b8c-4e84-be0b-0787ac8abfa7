import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'


describe('HTTP create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            body: {
                Title: 'New Learning Context',
                ContextTypeID: 1
            },
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            body: {
                Title: false,
                ContextTypeID: false
            },
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).include('Invalid request data')
        expect(data).include('Expected string, received boolean')
        expect(data).include('Expected 1 | 2 | 3 | 4 | 5 | 6 | 7, received boolean')
        expect(data).include('Title')
        expect(data).include('ContextTypeID')

    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context/create.service.js': {
                default: Sinon.stub().returns(Promise.reject(new LearningContextSessionModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            body: {
                Title: 'New Learning Context',
                ContextTypeID: 1
            },
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })




})