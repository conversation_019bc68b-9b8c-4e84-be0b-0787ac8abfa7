import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextSessionInstructor from '../../../models/learning-context-session-instructor.model.js'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'


describe('HTTP Get upcoming controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-upcoming.controller', {
            '../../../services/mssql/learning-context-sessions/get-upcoming-for-context.service.js': {
                default: Sinon.stub().returns(Promise.resolve([new LearningContextSessionModel({ ID: uuid() })]))
            },
            '../../../services/mssql/learning-context-session-instructors/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve([new LearningContextSessionInstructor({ SessionID: uuid(), UserID: uuid()})]))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve([{ SessionEnrollment: uuid() }]))
            },
            '../../../services/mssql/users/get-multiple-by-ids.service.js': {
                 default: Sinon.stub().returns(Promise.resolve([{ User: uuid() }]))
                 }
            
        })

        const mocks = httpMocks.createMocks({
            params: {
                id: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })


    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-upcoming.controller', {
            '../../../services/mssql/learning-context-sessions/get-upcoming-for-context.service.js': {
                default: Sinon.stub().returns(Promise.resolve([new LearningContextSessionModel({ ID: uuid() })]))
            },
            '../../../services/mssql/learning-context-session-instructors/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve([new LearningContextSessionInstructor({ SessionID: uuid(), UserID: uuid()})]))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve([{ SessionEnrollment: uuid() }]))
            },
            '../../../services/mssql/users/get-multiple-by-ids.service.js': {
                 default: Sinon.stub().returns(Promise.resolve([{ User: uuid() }]))
                 }
            
        })

        const mocks = httpMocks.createMocks({
            params: {
                id: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request parameter data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('id')

    })
    
    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-upcoming.controller', {
            '../../../services/mssql/learning-context-sessions/get-upcoming-for-context.service.js': {
                default: Sinon.stub().returns(Promise.reject([new LearningContextSessionModel({ ID: uuid() })]))
            },
            '../../../services/mssql/learning-context-session-instructors/get.service.js': {
                default: Sinon.stub().returns(Promise.reject([new LearningContextSessionInstructor({ SessionID: uuid(), UserID: uuid()})]))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().returns(Promise.reject([{ SessionEnrollment: uuid() }]))
            },
            '../../../services/mssql/users/get-multiple-by-ids.service.js': {
                 default: Sinon.stub().returns(Promise.reject([{ User: uuid() }]))
                 }
            
        })

        const mocks = httpMocks.createMocks({
            params: {
                id: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })

})