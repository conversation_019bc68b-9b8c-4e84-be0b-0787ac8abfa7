import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextFavorites from '../../../models/learning-context-user-favorite.model.js'

describe('HTTP Create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context-favorites/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextFavorites({UserID: uuid(), LearningContextID: uuid()})))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                UserID: uuid(),
                LearningContextID: uuid()
            },
            session: { userId: uuid() }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context-favorites/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextFavorites({UserID: uuid(), LearningContextID: uuid()})))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                UserID: false,
                LearningContextID: false
            },
            session: { userId: uuid() }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect (data).to.include('Invalid request data')
        expect(data).to.include('UserID: Expected string, received boolean')
        expect(data).to.include('LearningContextID: Expected string, received boolean')
    })

    it('returns an internal server error if the request data is rejected', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context-favorites/create.service.js': {
                default: Sinon.stub().returns(Promise.reject(new LearningContextFavorites({UserID: uuid(), LearningContextID: uuid()})))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                UserID: uuid(),
                LearningContextID: uuid()
            },
            session: { userId: uuid() }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

    

})