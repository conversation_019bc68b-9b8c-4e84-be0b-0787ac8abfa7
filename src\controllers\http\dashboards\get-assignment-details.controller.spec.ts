import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: get assignment details', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => sinon.restore())

  it('returns bad request when the search value is a boolean', async () => {
    const controller = await esmock('./get-assignment-details.controller.js')
    const mocks = httpMocks.createMocks({ body: { search: true } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('search: Expected string, received boolean')
  })

  it('returns bad request when the search value is a number', async () => {
    const controller = await esmock('./get-assignment-details.controller.js')
    const mocks = httpMocks.createMocks({ body: { search: 1 } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('search: Expected string, received number')
  })

  it('returns bad request when the groups array contains invalid data', async () => {
    const controller = await esmock('./get-assignment-details.controller.js')
    const mocks = httpMocks.createMocks({ body: { groups: ['a', 1, true, uuid()] } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('groups.0: Invalid')
    expect(data).to.include('groups.1: Expected string, received number')
    expect(data).to.include('groups.2: Expected string, received boolean')
  })

  it('returns bad request when the id parameter is not a valid uuid', async () => {
    const controller = await esmock('./get-assignment-details.controller.js')
    const mocks = httpMocks.createMocks({ params: { id: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Invalid')
  })

  it('returns the requested report', async() => {
    const controller = await esmock('./get-assignment-details.controller.js', {
      '../../../services/mssql/assignment-users/search-active-assignment-users.service.js': { default: sinon.stub().resolves({ TotalRecords: 0, userIDs: [] }) }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
    expect(data.totalRecords).to.be.a('number')
    expect(data.items).to.be.an('array')
    expect(data.items.length).to.equal(0)
    expect(data.totalRecords).to.equal(0)
  })

  it('gracefully handles an error', async () => {
    const controller = await esmock('./get-assignment-details.controller.js', {
      '../../../services/mssql/assignment-users/search-active-assignment-users.service.js': { default: sinon.stub().rejects(new Error('Service Error')) }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })
})
