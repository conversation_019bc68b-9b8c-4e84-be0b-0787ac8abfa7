import { Table } from '@lcs/mssql-utility'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { LearningObjectUserFavorite, LearningObjectUserFavoriteFields, LearningObjectUserFavoritesTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-favorite.js'
import { z } from 'zod'

export const createLearningObjectFavoriteSchema = z.object({
  [LearningObjectUserFavoriteFields.UserID]: zodGUID,
  [LearningObjectUserFavoriteFields.LearningObjectID]: zodGUID
})

export default class LearningObjectUserFavoriteModel extends Table<LearningObjectUserFavorite, LearningObjectUserFavorite> {
  public fields: LearningObjectUserFavorite

  constructor (fields?: LearningObjectUserFavorite) {
    super(LearningObjectUserFavoritesTableName, [
      LearningObjectUserFavoriteFields.UserID,
      LearningObjectUserFavoriteFields.LearningObjectID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearningObjectUserFavorite): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningObjectUserFavorite {
    return this.fields
  }
}
