import logger from '@lcs/logger'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import Sinon from 'sinon'
import httpStatus from 'http-status'

describe('HTTP Controller: Get Multiple Assignments', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('should return a bad request status if the request body is invalid', async () => {
    const create = await esmock('./get-multiple.controller.js')
    const req = httpMocks.createRequest({
      body: {
        dueDate: 'tomorrow',
        sortDirection: 'up',
        sortColumn: 1,
        status: 'completed',
        missingSessions: 'no'
      }
    })
    const res = httpMocks.createResponse()
    await create(req, res)
    expect(res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('dueDate: Invalid date')
    expect(data).to.include('sortDirection: Invalid input')
    expect(data).to.include('sortColumn: Expected string, received number')
    expect(data).to.include('missingSessions: Expected boolean, received string')
  })

  it('should returns ok status when the request is valid', async () => {
    const create = await esmock('./get-multiple.controller.js', {
      '../../../services/mssql/assignments-view/get-multiple.service.js': {
        default: Sinon.stub().returns(Promise.resolve({ assignments: [], totalRecords: 0 }))
      }
    })
    const req = httpMocks.createRequest({
      body: {
        dueDate: (new Date()).toISOString(),
        sortDirection: 'asc',
        sortColumn: 'Title',
        status: [1],
        missingSessions: false
      }
    })
    const res = httpMocks.createResponse()
    await create(req, res)
    expect(res._getStatusCode()).to.equal(httpStatus.OK)
    const data: { assignments: any[]; totalRecords: number } = JSON.parse(res._getData())
    expect(data.assignments).to.be.an('array')
    expect(data.totalRecords).to.be.a('number')
    expect(data.totalRecords).to.equal(0)
    expect(data.assignments.length).to.equal(0)
  })
})
