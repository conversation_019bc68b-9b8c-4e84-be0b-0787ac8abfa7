import logger from '@lcs/logger'
import httpStatus from 'http-status'
import Sinon from 'sinon'
import { expect } from 'chai'
import httpMock from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: Get Catalog Children', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('should return bad request status when the id parameter is missing', async () => {
    const controller = await esmock('./get-children.controller.js')
    const mocks = httpMock.createMocks()
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.equal('Invalid context id in request parameters')
  })

  it('should return bad request status when the id parameter is not a valid guid', async () => {
    const controller = await esmock('./get-children.controller.js')
    const mocks = httpMock.createMocks({ params: { id: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.equal('Invalid context id in request parameters')
  })

  it('should return bad request status when the id parameter is a number an not a valid guid', async () => {
    const controller = await esmock('./get-children.controller.js')
    const mocks = httpMock.createMocks({ params: { id: 123 } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.equal('Invalid context id in request parameters')
  })

  it('should return ok status when the request is valid', async () => {
    const controller = await esmock('./get-children.controller.js', {
      '../../../services/mssql/catalog/get-child-contexts.service.js': { default: Sinon.stub().returns(Promise.resolve([])) },
      '../../../services/mssql/learning-objects/get-multiple.service.js': { default: Sinon.stub().returns(Promise.resolve([])) }
    })
    const mocks = httpMock.createMocks({ params: { id: uuid() }, session: { userId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const data = JSON.parse(mocks.res._getData())
    expect(data).to.not.be.undefined
    expect(Array.isArray(data)).to.equal(true)
    expect(data.length).to.equal(0)
  })
})
