import globals from "globals";
import pluginJs from "@eslint/js";
import tseslint from "typescript-eslint";


/** @type {import('eslint').Linter.Config[]} */
export default [
  {files: ["**/*.{js,mjs,cjs,ts}"]},
  {languageOptions: { globals: globals.es2025 }},
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  {
    rules: {
      camelcase: 'off',
      'object-shorthand': 'error',
      'curly': 'error',
      'space-before-function-paren': 'error',
      eqeqeq: 'error',
      'space-infix-ops': 'error',
      'comma-spacing': 'error',
      'brace-style': ['error', '1tbs', { 'allowSingleLine': true }],
      'operator-linebreak': 'error',
      'block-spacing': 'error',
      'eol-last': 'error',
      'key-spacing': 'error',
      'no-trailing-spaces': 'error',
      'space-before-blocks': 'error'
    },
    overrides: [
      {
        files: ['**/*.spec.{js,mjs,cjs,ts}'],
        rules: {
          'no-unused-expressions': 'off'
        }
      }
    ]
  }
];
