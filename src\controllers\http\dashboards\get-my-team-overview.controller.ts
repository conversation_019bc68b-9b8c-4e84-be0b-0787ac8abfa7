import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR } = httpStatus
import getTeamsActiveAssignmentCount from '../../../services/mssql/assignments/get-teams-active-assignment-count.service.js'
import getTeamsOverdueAssignmentCont from '../../../services/mssql/assignments/get-teams-overdue-assignment-count.service.js'
import getTeamCertificateCount from '../../../services/mssql/certificate/get-team-certificate-count.service.js'
import getTeamsAverageScore from '../../../services/mssql/learner-progress/get-teams-average-score.service.js'
import { httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('Controller-HTTP.get-my-team-overview', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const Certifications = await getTeamCertificateCount(req.session.userId)
    log('info', 'Successfully retrieved teams certificate count', { count: Certifications, success: true, req })

    const averageScore = await getTeamsAverageScore(req.session.userId)
    log('info', 'Successfully retrieved teams average score', { averageScore, success: true, req })

    const OverdueAssignments = await getTeamsOverdueAssignmentCont(req.session.userId)
    log('info', 'Successfully retrieved teams overdue assignment count', { count: OverdueAssignments, success: true, req })

    const ActiveAssignments = await getTeamsActiveAssignmentCount(req.session.userId)
    log('info', 'Successfully retrieved teams active assignment count', { count: ActiveAssignments, success: true, req })

    res.json({
      Certifications,
      AverageScore: averageScore,
      OverdueAssignments,
      ActiveAssignments
    })
  } catch (error) {
    log('error', 'Failed to get my teams learning overview', { error, req, success: false })
    res.sendStatus(INTERNAL_SERVER_ERROR)
  }
}
