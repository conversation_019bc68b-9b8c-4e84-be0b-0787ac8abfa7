import get from '../../../services/mssql/learning-objects/get.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import getUserFavorite from '../../../services/mssql/learning-object-favorites/get.service.js'
import getUserBookmark from '../../../services/mssql/learning-object-bookmarks/get.service.js'
import { getObjectRatingsForUser as getUserRating } from '../../../services/mssql/learning-object-ratings/get.service.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID, zodStringToBoolean } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.get-learning-object', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, click on a learning object within the course outline when viewing a single course. Note the time. Check the LMS API log for the 'http-get-learning-object' label."

  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const { incViews, contextCount, rating, forContextID } = z.object({
      incViews: zodStringToBoolean.optional(),
      contextCount: zodStringToBoolean.optional(),
      rating: zodStringToBoolean.optional(),
      forContextID: zodGUID.optional()
    }).parse(req.query)

    const result = await get(id, req.session.userId, { incViews, contextCount, rating, forContextID })

    const learningObject = result.fields

    // try to get the learning object bookmark for this user
    try {
      const userFavorite = await getUserFavorite(id, req.session.userId)
      learningObject.UserFavorite = userFavorite.fields
    } catch (error) {
      // if not found in the database don't throw the error its ok
      if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
        log('verbose', 'Failed to get learning object favorite. User has not favorited learning object', { objectId: id, userId: req.session.userId, error, success: false, req })
        throw error
      } else {
        log('info', 'Failed to get learning object favorite for user because it was not found in the database', { objectId: id, userId: req.session.userId, success: false, req })
      }
    }

    // try to get the bookmark for this user
    try {
      const userBookmark = await getUserBookmark(id, req.session.userId)
      learningObject.UserBookmark = userBookmark.fields
    } catch (error) {
      // if not found in the database it's ok
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        log('info', 'Failed to get learning object bookmark for user because it was not found in the database', { objectId: id, userId: req.session.userId, success: false, req })
      } else {
        log('error', 'Failed to get learning object bookmark for user', { objectId: id, userId: req.session.userId, error, success: false, req })
        throw error
      }
    }

    // try to get the users rating for this object

    try {
      const userRating = await getUserRating(id, req.session.userId)
      learningObject.UserRating = userRating.fields
    } catch (error) {
      // if not found in the database it's ok
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        log('info', 'Failed to get users rating of learning object for user because it was not found in the database.', { objectId: id, userId: req.session.userId, success: false, req })
      } else {
        log('error', 'Failed to get users rating of the learning object', { objectId:id, userId: req.session.userId, error, success: false, req })
        throw error
      }
    }

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, click on a learning object within the course outline when viewing a single course. Note the time. Check the LMS API log for the 'http-get-learning-object' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved learning object', { objectId: learningObject.ID, success: true, req })

    res.json(learningObject)
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning object because of input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learning object because it was not found in the database.', { success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      log('error', 'Failed to get learning object.', { error, success: false, req })
      res.status(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
