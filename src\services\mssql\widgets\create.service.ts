import mssql, { addRow } from '@lcs/mssql-utility'
import WidgetModel from '../../../models/widget.model.js'
import WidgetKeywordModel from '../../../models/widget-keyword.model.js'
import WidgetLearningContextModel from '../../../models/widget-learning-context.model.js'
import WidgetLearningObjectModel from '../../../models/widget-learning-object.model.js'
import { Widget } from '@tess-f/sql-tables/dist/lms/widget.js'
import { WidgetKeyword } from '@tess-f/sql-tables/dist/lms/widget-keyword.js'
import { WidgetLearningContext } from '@tess-f/sql-tables/dist/lms/widget-learning-context.js'
import { WidgetLearningObject } from '@tess-f/sql-tables/dist/lms/widget-learning-object.js'

export default async function createWidgetService (widget: WidgetModel): Promise<WidgetModel> {
  const pool = mssql.getPool()
  const record = await addRow<Widget>(pool.request(), widget)
  const created = new WidgetModel(undefined, record)

  // if this widget is filtered we need to add the keywords if any are present
  if (created.fields.Filter && widget.fields.Keywords) {
    const keywords = await Promise.all(widget.fields.Keywords.map(async key => {
      const keywordDBrecord = await addRow<WidgetKeyword>(pool.request(), new WidgetKeywordModel({
        Keyword: key,
        WidgetID: created.fields.ID
      }))
      return new WidgetKeywordModel(keywordDBrecord)
    }))
    created.fields.Keywords = keywords.map(key => key.fields.Keyword!)
  }

  // if this widget is not filtered we need to add the specified content
  if (!created.fields.Filter) {
    if (widget.fields.LearningContexts && widget.fields.LearningContexts.length > 0) {
      await Promise.all(widget.fields.LearningContexts.map(async context => {
        await addRow<WidgetLearningContext>(pool.request(), new WidgetLearningContextModel({
          WidgetID: created.fields.ID,
          LearningContextID: context.ID,
          OrderID: context.OrderID!
        }))
      }))

      created.fields.LearningContexts = widget.fields.LearningContexts
    }
    if (widget.fields.LearningObjects && widget.fields.LearningObjects.length > 0) {
      await Promise.all(widget.fields.LearningObjects.map(async obj => {
        await addRow<WidgetLearningObject>(pool.request(), new WidgetLearningObjectModel({
          WidgetID: created.fields.ID,
          LearningObjectID: obj.ID,
          OrderID: obj.OrderID!
        }))
      }))

      created.fields.LearningObjects = widget.fields.LearningObjects
    }
  }

  return created
}
