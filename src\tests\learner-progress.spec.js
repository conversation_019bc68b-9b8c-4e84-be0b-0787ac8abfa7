const chai = require('chai');
const expect = chai.expect;
const tester = require('../api/utils/test-agent.utils');
const uuidv4 = require('uuid/v4');
const settings = require('../api/config/settings');

const userID = uuidv4(); // Random
const lessonContextID = '83C4BF97-BBE6-4C1A-83A2-EEC762461AFA'; // Course 1 / Module 1 / Topic 1 / Lesson 1
const learningObjectID = uuidv4(); // Random

const progress = {
    LessonStatusID: 1,
    UserID: userID,
    LearningContextID: lessonContextID,
    LearningObjectID: learningObjectID
};

describe('Learner Progress', () => {

    let isolated = false;

    before( done => {
        if(tester.agent == null) {
            isolated = true;
            tester.create()
            .then( agent => {
                done();
            })
            .catch( err => {
                console.error(err);
                done();
            });
        } else {
            done();
        }
    });

    it('should create a learner progresses', done => {

        tester.agent.post(settings.server.root + 'learner-progress')
        .send( progress )
        .end((err, res) => {

            expect(res.status).to.equal(200);
            expect(res.body.ID).to.exist;

            progress.ID = res.body.ID;

            done();
        });

    });

    it('should get a learner progress with userId learningContextID and learningObjectID', done => {
        
        tester.agent.get(settings.server.root + 'learner-progress/' + userID + '/' + lessonContextID + '/' + learningObjectID)
        .end((err, res) => {

            expect( res.body.ID ).to.equal( progress.ID );
            done();
        });

    });

    it('should update a learner progress', done => {

        tester.agent.put( settings.server.root + 'learner-progress/' + progress.ID )
        .send({ LessonLocation: 'test-updated' })
        .end((err, res) => {

            expect( res.body.LessonLocation ).to.equal('test-updated');
            done();
        });

    });

    xit('should resume learning with correct initial learning context ID', done => {
        
        tester.agent.get( settings.server.root + 'resume-learning/' + userID )
        .end((err, res) => {

            expect( res.body.CourseTitle ).to.equal( 'Course 1' );
            expect( res.body.LessonTitle ).to.equal( 'Lesson 1' );
            done();
        });

    });

    xit('should resume to next lesson when progress is updated to lessonStatus > 3', done => {
        tester.agent.put( settings.server.root + 'learner-progress/' + progress.ID )
        .send({ LessonStatusID: 5 })
        .end((err, res) => {

            tester.agent.get( settings.server.root + 'resume-learning/' + userID )
            .end((err, res) => {

                expect( res.body.CourseTitle ).to.equal( 'Course 1' );
                expect( res.body.LessonTitle ).to.equal( 'Lesson 2' );
                done();
            });

        });
    });

    it('should delete learner progresses', done => {
        tester.agent.delete(settings.server.root + 'learner-progress/' + progress.ID )
        .end((err, res) => {

            expect(res.status).to.equal(204);
            done();
        });
    });

    after((done)=>{
        if(isolated) {
            tester.close();
        } 
        done();
    });

});