import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import LearningObjectBookmarkModel from '../../../models/learning-object-user-bookmark.model.js'
import get, { getBookmarksForUser, getUserBookmarksForObject } from './get.service.js'
import create from './create.service.js'
import remove from './delete.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { v4 as uuidv4 } from 'uuid'

let learningObject: LearningObject
let learningObjectBookmark: LearningObjectBookmarkModel

describe('MSSQL Learning Object User Bookmarks', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningObject = await addRow<LearningObject>(pool.request(), new LearningObjectModel({
      Title: 'Test object user bookmarks',
      Description: `Running learning object bookmarks on ${new Date()}`,
      ContentID: uuidv4(),
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      LearningObjectTypeID: LearningObjectTypes.PDF
    }))
  })

  it('creates a learning object user bookmark', async () => {
    learningObjectBookmark = await create(new LearningObjectBookmarkModel({
      LearningObjectID: learningObject.ID,
      UserID: AdminUserId
    }))

    expect(learningObjectBookmark.fields.LearningObjectID).to.equal(learningObject.ID)
    expect(learningObjectBookmark.fields.UserID).to.equal(learningObject.CreatedBy)
  })

  it('gets a users bookmark object', async () => {
    const bookmark = await get(learningObject.ID!, AdminUserId)
    expect(bookmark.fields.UserID).to.equal(AdminUserId)
    expect(bookmark.fields.LearningObjectID).to.equal(learningObject.ID)
  })

  it('gets bookmark objects for a user', async () => {
    const bookmarks = await getBookmarksForUser(AdminUserId)

    for (let i = 0; i < bookmarks.length; i++) {
      expect(bookmarks[i].fields.UserID).to.equal(AdminUserId)
    }
  })

  it('get user bookmarks for a learning object', async () => {
    const bookmarks = await getUserBookmarksForObject(learningObject.ID!)

    for (let i = 0; i < bookmarks.length; i++) {
      expect(bookmarks[i].fields.LearningObjectID).to.equal(learningObject.ID)
    }
  })

  it('should fail creating the same object/user combo', async () => {
    try {
      await create(new LearningObjectBookmarkModel({
        LearningObjectID: learningObject.ID,
        UserID: AdminUserId
      }))
      throw new Error('Created a duplicate user learning object bookmark')
    } catch (error) {};
  })

  it('deletes the user learning object bookmark', async () => {
    await remove(learningObject.ID!, AdminUserId)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningObject>(pool.request(), LearningObjectsTableName, { ID: learningObject.ID })
  })
})
