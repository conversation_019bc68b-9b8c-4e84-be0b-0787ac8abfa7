import mssql from '@lcs/mssql-utility'
import { LearningContextConnectionsTableName, LearningContextConnectionFields } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import LearningContextModel from '../../../models/learning-context.model.js'

// gets the contexts that list the given ID as the parent from the learning context table and the learning context connections table
export default async function (parentID: string): Promise<LearningContextModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('parentID', parentID)

  const records = (await request.query<LearningContext>(`
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ParentContextID}] = @parentID
    UNION
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ID}] IN (
      SELECT [${LearningContextConnectionFields.ConnectedContextID}]
      FROM [${LearningContextConnectionsTableName}]
      WHERE [${LearningContextConnectionFields.ParentContextID}] = @parentID
    )
    ORDER BY [OrderID]
  `)).recordset

  const contexts = records.map(record => new LearningContextModel(undefined, record))

  const orderIdMap = (await request.query<{ ConnectedContextID: string, OrderID: number }>(`
      SELECT [${LearningContextConnectionFields.ConnectedContextID}], [${LearningContextConnectionFields.OrderID}]
      FROM [${LearningContextConnectionsTableName}]
      WHERE [${LearningContextConnectionFields.ParentContextID}] = @parentID
  `))

  contexts.forEach(context => {
    const connectedContext = orderIdMap.recordset.find(connected => connected.ConnectedContextID === context.fields.ID)
    if (connectedContext) {
      context.fields.OrderID = connectedContext.OrderID
    }
  })

  return contexts
}
