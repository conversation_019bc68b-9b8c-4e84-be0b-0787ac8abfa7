import logger from '@lcs/logger'
import mssql, { <PERSON>_Errors as dbErrors } from '@lcs/mssql-utility'
import { Request } from 'mssql'
import LearningContext from '../../../models/learning-context.model.js'
import UserCompletedLearningContextModel from '../../../models/user-completed-learning-context.model.js'
import createCompletionService from '../../mssql/user-completed-learning-contexts/create.service.js'
import saveCert from '../../amqp/file/save-certificate.service.js'
import createCertificate from '../../file/create-certificate.service.js'
import settings from '../../../config/settings.js'
import { LearnerProgress, LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { UserCompletedLearningContext, UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import getUserById from '../../mssql/users/get-by-id.service.js'
import { User } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { sendGenericMessage } from '@tess-f/email/dist/amqp/send-generic-message.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import { getSystemConfig } from '../../amqp/system/get-system-config.service.js'
import { CMI5Verbs } from '@tess-f/sql-tables/dist/lrs/verb.js'
import LearningContextRegistrationModel from '../../../models/learning-context-registration.model.js'
import { LearningContextRegistration, LearningContextRegistrationFields, LearningContextRegistrationsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-registration.js'
import createStatementService from '../../amqp/lrs/create-statement.service.js'
import { AgentJson } from '../../../models/amqp/lrs/statement.model.js'
import sendNotification from '../../../services/amqp/notification/send-notification.service.js'

const log = logger.create('Context-Grading-Utils')

export async function getLearnerProgressForObject (request: Request, learningObjectID: string, userID: string, from?: Date, to?: Date): Promise<LearnerProgressModel[]> {
  request.input('objectID', learningObjectID)
  request.input('userID', userID)

  let query = `
    SELECT *
    FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @userID
    AND [${LearnerProgressFields.LearningObjectID}] = @objectID
    AND [${LearnerProgressFields.LessonStatusID}] > ${LessonStatuses.notAttempted}
  `

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
    if (from < to) {
      query += `
        AND (
          [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to
          OR [${LearnerProgressFields.ModifiedOn}] BETWEEN @from AND @to
          OR [${LearnerProgressFields.CompletedDate}] BETWEEN @from AND @to
        )
      `
    } else {
      query += `
        AND (
          [${LearnerProgressFields.CreatedOn}] BETWEEN @to AND @from
          OR [${LearnerProgressFields.ModifiedOn}] BETWEEN @to AND @from
          OR [${LearnerProgressFields.CompletedDate}] BETWEEN @to AND @from
        )
      `
    }
  } else if (from && !to) {
    request.input('from', from)
    query += ` AND ( [${LearnerProgressFields.CreatedOn}] >= @from )`
  } else if (to && !from) {
    request.input('to', to)
    query += `AND [${LearnerProgressFields.CreatedOn}] <= @to `
  }

  query += `ORDER BY [${LearnerProgressFields.CreatedOn}] DESC`
  const results = await request.query<LearnerProgress>(query)
  return results.recordset.map(record => new LearnerProgressModel(record))
}

export async function getLearnerProgressForILT (request: Request, contextID: string, userID: string, from?: Date, to?: Date): Promise<LearnerProgressModel[]> {
  request.input('contextID', contextID)
  request.input('userID', userID)

  let query = `
    SELECT *
    FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @userID
    AND [${LearnerProgressFields.LessonStatusID}] > ${LessonStatuses.browsed}
    AND [${LearnerProgressFields.LearningContextSessionID}] IN (
      SELECT [${LearningContextSessionFields.ID}]
      FROM [${LearningContextSessionsTableName}]
      WHERE [${LearningContextSessionFields.LearningContextID}] = @contextID
    )
  `
  if (from && to) {
    request.input('from', from)
    request.input('to', to)
    if (from < to) {
      query += `
        AND (
          [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to
          OR [${LearnerProgressFields.ModifiedOn}] BETWEEN @from AND @to
          OR [${LearnerProgressFields.CompletedDate}] BETWEEN @from AND @to
        )
      `
    } else {
      query += `
        AND (
          [${LearnerProgressFields.CreatedOn}] BETWEEN @to AND @from
          OR [${LearnerProgressFields.ModifiedOn}] BETWEEN @to AND @from
          OR [${LearnerProgressFields.CompletedDate}] BETWEEN @to AND @from
        )
      `
    }
  } else if (from && !to) {
    request.input('from', from)
    query += ` AND [${LearnerProgressFields.CreatedOn}] >= @from `
  } else if (to && !from) {
    request.input('to', to)
    query += `AND [${LearnerProgressFields.CreatedOn}] <= @to `
  }

  query += `ORDER BY [${LearnerProgressFields.CreatedOn}] DESC`

  const results = await request.query<LearnerProgress>(query)
  return results.recordset.map(record => new LearnerProgressModel(record))
}

export async function issueCertificate (context: LearningContext, completion: UserCompletedLearningContextModel, notifyUser: boolean = true) {
  // get the user
  let user: User | undefined
  try {
    user = await getUserById(completion.fields.UserID!)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    // we had a problem getting the user
    log('error', 'Failed to get user when issuing context certificate', { errorMessage, contextID: completion.fields.LearningContextID, userID: completion.fields.UserID })
  }

  let pdf: string | undefined
  if (user) {
    pdf = await createCertificate(`${user.FirstName} ${user.LastName}`,
      (context.fields.ContextTypeID === LearningContextTypes.Section ? context.fields.Label + ' ' : 'Course: ') + context.fields.Title,
      completion.fields.CompletedOn!
    )
  }

  if (pdf && user) {
    try {
      const contentID = await saveCert(pdf, `${user.FirstName}_${user.LastName}_Completion_Certificate_${context.fields.Title?.split(' ').join('_')}`)
      // save the id
      completion.fields.Certificate = contentID
    } catch (error) {
      log('error', 'Failed to save certificate for learning context completion', { errorMessage: getErrorMessage(error) })
    }
  }

  // don't let a user fetch error stop us from saving the completion
  // if the certificate did not create or save it's oke we still want to save our completion
  const created = await createCompletionService(completion)

  if (notifyUser) {
    if (user?.Email) {
      try {
        // notify the user they have a new certificate
        await sendGenericMessage(
          settings.amqp.service_queues.email, {
            to: [user.Email],
            message: `Congratulations your certificate of completion for ${context.fields.Title} is now ready to view.`,
            header: `${context.fields.Title} Certificate of Completion`,
            subject: 'Certificate of Completion',
            ccAdmin: settings.mail.cc_certificates_to_admin
          },
          settings.amqp.command_timeout
        )
      } catch (error) {
        log('error', 'Failed to notify user of learning context completion', { errorMessage: getErrorMessage(error) })
      }
    }

    // notify the user they have a new certificate
    await sendNotification({
      Title: 'Certificate of Completion',
      Message: `Congratulations your certificate of completion for ${context.fields.Title} is now ready to view.`,
      PublishDate: new Date(),
      Everyone: false,
      SystemID: 'lms',
      Priority: 1,
      UserIDs: [user!.ID!],
      GroupIDs: []
    })
  }

  return created
}

export async function getLastCompletedDate (request: Request, contextID: string, userID: string, maxCompletionDate?: Date): Promise<Date | undefined> {
  request.input('contextID', contextID)
  request.input('userID', userID)

  let query = `
    SELECT TOP (1) *
    FROM [${UserCompletedLearningContextsTableName}]
    WHERE [${UserCompletedLearningContextFields.LearningContextID}] = @contextID
    AND [${UserCompletedLearningContextFields.UserID}] = @userID
    AND [${UserCompletedLearningContextFields.LessonStatusID}] > ${LessonStatuses.fail}
  `

  if (maxCompletionDate) {
    request.input('maxAge', maxCompletionDate)
    query += ` AND [${UserCompletedLearningContextFields.CompletedOn}] <= @maxAge `
  }

  query += `ORDER BY [${UserCompletedLearningContextFields.CompletedOn}] DESC`

  const res = await request.query<UserCompletedLearningContext>(query)

  if (res.recordset.length > 0) {
    return res.recordset[0].CompletedOn
  } else {
    return undefined
  }
}

export async function issueCmi5ContextSatisfactionStatement (userId: string, context: LearningContext, progressId: string) {
  try {
    const config = await getSystemConfig()
    const lmsConfig = config.Apps.find(app => app.Id === 'lms')
    const currentUser = await getUserById(userId)
    const registration = await getRegistrationForContext(context.fields.ID!, userId)
    const agent: AgentJson = {
      objectType: 'Agent',
      name: `${currentUser.FirstName} ${currentUser.LastName}`,
      account: {
        name: currentUser.Username!,
        homePage: config.Domain
      }
    }
    await createStatementService({
      verb: { id: CMI5Verbs.Satisfied },
      object: {
        objectType: 'Activity',
        id: `${config.Domain}${lmsConfig?.Address ?? '/lms'}/learning-context/${context.fields.ID}`,
        definition: {
          type: context.fields.ContextTypeID === LearningContextTypes.CMI5Course ? 'https://w3id.org/xapi/cmi5/activitytype/course' : 'https://w3id.org/xapi/cmi5/activitytype/block'
        }
      },
      actor: agent,
      authority: agent,
      context: {
        registration: registration.fields.Id,
        contextActivities: {
          grouping: [
            {
              id: context.fields.IRI!
            }
          ]
        },
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': progressId
        }
      },
      timestamp: (new Date()).toISOString()
    })
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to issue satisfaction statement', { errorMessage, success: false })
  }
}

export async function completeCmi5CourseRegistration (context: LearningContext, userId: string, progressId: string) {
  // first we need to mark that this context is satisfied in the LRS
  await issueCmi5ContextSatisfactionStatement(userId, context, progressId)
  // next we need to complete out the registration
  const request = mssql.getPool().request()
  request.input('contextId', context.fields.ID)
  request.input('userId', userId)
  await request.query(`
    UPDATE [${LearningContextRegistrationsTableName}]
    SET [${LearningContextRegistrationFields.CompletedOn}] = GETDATE()
    WHERE [${LearningContextRegistrationFields.LearningContextId}] = @contextId
    AND [${LearningContextRegistrationFields.UserId}] = @userId
    AND [${LearningContextRegistrationFields.CompletedOn}] IS NULL
  `)
}

async function getRegistrationForContext (contextId: string, userId: string): Promise<LearningContextRegistrationModel> {
  const request = mssql.getPool().request()
  request.input('contextId', contextId)
  request.input('userId', userId)

  const result = await request.query<LearningContextRegistration>(`
    SELECT TOP(1) *
    FROM [${LearningContextRegistrationsTableName}]
    WHERE [${LearningContextRegistrationFields.LearningContextId}] = @contextId
    AND [${LearningContextRegistrationFields.UserId}] = @userId
    ORDER BY [${LearningContextRegistrationFields.CreatedOn}] DESC
  `)

  if (result.recordset.length > 0) {
    return new LearningContextRegistrationModel(result.recordset[0])
  } else {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  }
}
