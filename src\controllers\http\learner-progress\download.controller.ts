import { Request, Response } from 'express'
import getAll from '../../../services/mssql/learner-progress/get-all-for-download.service.js'
import logger from '@lcs/logger'
import { Parser } from '@json2csv/plainjs'
import { getLearningProgressName } from '../../../utils/statics-to-names.js'
import httpStatus from 'http-status'
import { httpLogTransformer } from '@tess-f/backend-utils'
const { INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.download-learner-progress', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const progresses = await getAll()

    log('info', 'Successfully retrieved learner progress records', { count: progresses.length, success: true, req })

    const fields = [
      { label: 'User ID', value: 'UserID' },
      { label: 'Date Started', value: 'StartedDate' },
      { label: 'Date Completed', value: 'CompletedDate' },
      { label: 'Number of Attempts', value: 'Attempts' },
      { label: 'File Type', value: 'FileType' },
      { label: 'Status', value: (row: any) => getLearningProgressName(Number(row.LessonStatusID)) },
      { label: 'Grade', value: 'Grade' },
      { label: 'Score', value: 'RawScore' },
      { label: 'Rating', value: 'Rating' },
      { label: 'Total Time', value: 'TotalTime' },
      { label: 'Estimated Time To Complete', value: 'TimeToComplete' },
      { label: 'File Title', value: 'Title' },
      { label: 'Instructor(s)', value: 'Instructors' }
    ]

    const today = new Date()
    const json2csv = new Parser({ fields })
    const csv = json2csv.parse(progresses)

    res.header('Content-Type', 'text/csv')
    res.attachment(`learner-progress-${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}.csv`)
    res.send(csv)
  } catch (error) {
    log('error', 'Failed to generate learner progress csv.', { error, req, success: false })
    res.sendStatus(INTERNAL_SERVER_ERROR)
  }
}
