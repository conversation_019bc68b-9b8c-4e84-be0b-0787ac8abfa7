import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import SessionEnrollmentModel from '../../../models/session-enrollment.model.js'


describe('HTTP Get-past controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-past.controller', {
            '../../../services/mssql/learning-context-sessions/get-past-for-context.service.js': {
                default: Sinon.stub().returns(Promise.resolve({ sessions: [new LearningContextSessionModel({ ID: uuid() })], totalRecords: 1 }))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve([new SessionEnrollmentModel({ UserID: uuid(), SessionID: uuid() })]))
            }
        })

        const mocks = httpMocks.createMocks({
            query: {
                offset: 0,
                limit: 10,
            },
            params: {
                id: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-past.controller', {
            '../../../services/mssql/learning-context-sessions/get-past-for-context.service.js': {
                default: Sinon.stub().returns(Promise.resolve({ sessions: [new LearningContextSessionModel({ ID: uuid() })], totalRecords: 1 }))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve([new SessionEnrollmentModel({ UserID: uuid(), SessionID: uuid() })]))
            }
        })

        const mocks = httpMocks.createMocks({
            query: {
                offset: false,
                limit: false,
            },
            params: {
                id: false
            }
        })
        await controller(mocks.req, mocks.res)
        //expect(mocks.res.statusCode).equal(httpStatus.OK)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('id')

    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-past.controller', {
            '../../../services/mssql/learning-context-sessions/get-past-for-context.service.js': {
                default: Sinon.stub().returns(Promise.reject({ sessions: [new LearningContextSessionModel({ ID: uuid() })], totalRecords: 1 }))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().returns(Promise.reject([new SessionEnrollmentModel({ UserID: uuid(), SessionID: uuid() })]))
            }
        })

        const mocks = httpMocks.createMocks({
            query: {
                offset: 0,
                limit: 10,
            },
            params: {
                id: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

})