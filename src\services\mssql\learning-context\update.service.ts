import mssql, { addRow, deleteRow, updateRow } from '@lcs/mssql-utility'
import CoursePrerequisiteModel from '../../../models/course-prerequisite.model.js'
import LearningContextKeywordModel from '../../../models/learning-context-keyword.model.js'
import createActivity from '../activity-stream/create.service.js'
import ActivityStream from '../../../models/activity-stream.model.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import errors from '../../../config/errors.js'
import { Transaction } from 'mssql'
import { LearningContext } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { CoursePrerequisite, CoursePrerequisitesTableName } from '@tess-f/sql-tables/dist/lms/course-prerequisite.js'
import { LearningContextKeyword, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'
import getObjectiveIdsForLearningContext from '../learning-context-objectives/get-multi.service.js'
import createLearningContextObjective from '../learning-context-objectives/create.service.js'
import LearningContextObjectiveModel from '../../../models/learning-context-objective.model.js'
import removeLearningContextObjective from '../learning-context-objectives/remove.service.js'
import updateLearningContextObjective from '../learning-context-objectives/update.service.js'
import { CoursePrerequisiteJson } from '@tess-f/lms/dist/common/course-prerequisite.js'
import { CoursePrerequisiteAlternativeModel } from '../../../models/course-prerequisite-alternative.model.js'
import { CoursePrerequisiteAlternative } from '@tess-f/sql-tables/dist/lms/course-prerequisite-alternative.js'

/*
    ID must be set in the learning context
*/
export default async function (learningContext: LearningContextModel): Promise<LearningContextModel> {
  if (!learningContext.fields.ID) {
    throw new Error(errors.INVALID_ARGS)
  }
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    const updatedRecord = await updateRow<LearningContext>(transaction.request(), learningContext, { ID: learningContext.fields.ID })
    const updated = new LearningContextModel(undefined, updatedRecord[0])
    updated.fields.Prerequisites = await replacePrerequisites(transaction, updated.fields.ID!, learningContext.fields.Prerequisites)
    updated.fields.Keywords = await replaceKeywords(transaction, updated.fields.ID!, learningContext.fields.Keywords)
    updated.fields.ObjectiveIds = await updateObjectives(transaction, updated.fields, learningContext.fields.ObjectiveIds)

    await transaction.commit()

    // create an activity stream record
    const activity = new ActivityStream({
      UserID: updated.fields.ModifiedBy,
      LinkText: updated.fields.Label + ': ' + updated.fields.Title,
      LinkID: updated.fields.ID,
      ActivityID: Activities.ModifiedContext,
      CreatedOn: new Date()
    })
    createActivity(activity)

    return updated
  } catch (err) {
    if (!rolledBack) await transaction.rollback()
    throw err
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}

async function replacePrerequisites (transaction: Transaction, contextID: string, prerequisites?: CoursePrerequisiteJson[]): Promise<CoursePrerequisiteJson[] | undefined> {
  // First delete all
  await deleteRow(transaction.request(), CoursePrerequisitesTableName, { CourseID: contextID })

  if (prerequisites) {
    const prereqs: CoursePrerequisiteJson[] = []
    for (const prerequisite of prerequisites) {
      const prereq = new CoursePrerequisiteModel(prerequisite)
      prereq.fields.CourseID = contextID
      prereq.fields.ID = undefined
      const record = await addRow<CoursePrerequisite>(transaction.request(), prereq)
      prereq.importFromDatabase(record)

      // if the prerequisite is enforced and has alternatives, add those as well
      if (prereq.fields.Enforce && prerequisite.Alternatives.length > 0) {
        for (const alternative of prerequisite.Alternatives) {
          const alt = new CoursePrerequisiteAlternativeModel(alternative)
          alt.fields.ForPrerequisiteId = prereq.fields.ID
          const newAlt = await addRow<CoursePrerequisiteAlternative>(transaction.request(), alt)
          prereq.fields.Alternatives.push(newAlt)
        }
      }
      prereqs.push(prereq.fields)
    }
    return prereqs
  } else {
    return undefined
  }
}

async function replaceKeywords (transaction: Transaction, contextID: string, keywords?: string[]): Promise<string[] | undefined> {
  // delete keywords first
  await deleteRow(transaction.request(), LearningContextKeywordsTableName, { LearningContextID: contextID })

  if (keywords) {
    // create new keywords
    const output: string[] = []

    for (const word of keywords) {
      const keyword = new LearningContextKeywordModel({
        Keyword: word,
        LearningContextID: contextID
      })
      const record = await addRow<LearningContextKeyword>(transaction.request(), keyword)
      output.push(record.Keyword!)
    }

    return output
  } else {
    return undefined
  }
}

async function updateObjectives (transaction: Transaction, context: LearningContext, objectiveIds?: string[]): Promise<string[]> {
  // get the original list of objectives
  const original = await getObjectiveIdsForLearningContext(context.ID ?? '')

  if (objectiveIds === undefined) {
    // if no list was passed in, send back the original
    return original
  }

  // find the objectives to remove, add, update
  const toAdd = objectiveIds.filter(newObjective => !original.includes(newObjective))
  const toRemove = original.filter(originalObjective => !objectiveIds.includes(originalObjective))
  const toUpdate = objectiveIds.filter((id, index) => original.includes(id) && original.indexOf(id) !== index)

  await Promise.all(toAdd.map(async id => {
    await createLearningContextObjective(
      new LearningContextObjectiveModel({ LearningContextId: context.ID, ObjectiveId: id, OrderId: objectiveIds.indexOf(id) + 1 }),
      context.Title ?? 'Course',
      context.Label ?? 'Course',
      context.ContextTypeID ?? -1,
      transaction.request()
    )
  }))

  await Promise.all(toRemove.map(async id => {
    await removeLearningContextObjective(context.ID ?? '', id, transaction.request())
  }))

  await Promise.all(toUpdate.map(async id => {
    await updateLearningContextObjective(new LearningContextObjectiveModel({ LearningContextId: context.ID, ObjectiveId: id, OrderId: objectiveIds.indexOf(id) + 1 }), transaction.request())
  }))

  return objectiveIds
}
