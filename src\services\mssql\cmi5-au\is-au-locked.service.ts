/**
 * This service takes the id of an AU (learning object) and finds the parent cmi5 course
 * Then it loops though the content ensuring that the user has satisfied all of the prior AU's
 * @returns {boolean} true if the AU is locked and the user has NOT satisfied all prior AU's, false if the AU is unlocked and the user can view it's content
 */
import { getLearningContextContentMatrix } from '../learning-context/utils.service.js'
import isAuSatisfied from './is-au-satisfied.service.js'

export default async function isAuLockedService (contextId: string, auId: string, userId: string): Promise<boolean> {
  // loop through the context looking to see if the prior au's have been satisfied
  const result = await getStatus(contextId, auId, userId)
  return result ?? false // if we didn't get an explicit answer assume the au is unlocked
}

async function getStatus (contextId: string, auId: string, userId: string): Promise<boolean | undefined> {
  // build the learning matrix for this context
  const matrix = await getLearningContextContentMatrix(contextId)
  // loop through recursively until we find the au in question or
  // we find that the au is locked
  for (const item of matrix) {
    if (item.type === 'object') {
      // this is an AU, if it is the au we are checking return false
      if (item.Id === auId) {
        return false
      }
      // not the au we are looking for
      /// let's check that it has been satisfied
      if (!(await isAuSatisfied(item.Id, userId))) {
        // we can stop looping this au is not satisfied
        // the au we are looking at is locked
        return true
      }
    } else {
      // this is a block we need to recursively check the next item in this block
      const result = await getStatus(item.Id, auId, userId)
      if (result !== undefined) return result // only return if we got an explicit answer otherwise keep looping
    }
  }
}
