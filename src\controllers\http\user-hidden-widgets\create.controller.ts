import logger from '@lcs/logger'
import UserHiddenWidget from '../../../models/user-hidden-widget.model.js'
import { Request, Response } from 'express'
import createUserHiddenWidgetService from '../../../services/mssql/user-hidden-widgets/create.service.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'
const { INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.create-user-hidden-widget', httpLogTransformer)

export default async function createUserHiddenWidgetController (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const hiddenWidget = new UserHiddenWidget({
      WidgetID: id,
      UserID: req.session.userId
    })
    await createUserHiddenWidgetService(hiddenWidget)
    log('info', 'Successfully created user hidden widget', { widgetId: id, userId: req.session.userId, success: true, req })
    res.json(hiddenWidget)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to create user hidden widget: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter, '))
    } else {
      log('error', 'Failed to create user hidden widget', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
