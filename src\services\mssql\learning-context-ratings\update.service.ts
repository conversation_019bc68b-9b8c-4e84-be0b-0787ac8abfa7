import mssql, { updateRow } from '@lcs/mssql-utility'
import getContext from '../learning-context/get.service.js'
import createActivity from '../activity-stream/create.service.js'
import ActivityStream from '../../../models/activity-stream.model.js'
import LearningContextRatingModel from '../../../models/learning-context-rating.model.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { LearningContextRating } from '@tess-f/sql-tables/dist/lms/learning-context-rating.js'

export default async function (updateModel: LearningContextRatingModel): Promise<LearningContextRatingModel> {
  const pool = mssql.getPool()
  // create activity stream record
  const context = await getContext(updateModel.fields.LearningContextID!, undefined)
  const activity = new ActivityStream({
    UserID: updateModel.fields.UserID,
    LinkText: context.fields.Label + ': ' + context.fields.Title,
    LinkID: updateModel.fields.LearningContextID,
    ActivityID: Activities.RatedContext,
    Rating: updateModel.fields.Rating,
    CreatedOn: new Date()
  })
  await createActivity(activity)
  const records = await updateRow<LearningContextRating>(pool.request(), updateModel, { ID: updateModel.fields.ID })
  return new LearningContextRatingModel(records[0])
}
