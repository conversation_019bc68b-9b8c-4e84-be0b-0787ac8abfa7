import mssql from '@lcs/mssql-utility'
import { UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { FlatLearningContextTreeFields, FlatLearningContextTreeViewName } from '@tess-f/sql-tables/dist/lms/flat-learning-context-view.js'

export async function getContextInProgressCount (contextID: string, from?: Date, to?: Date): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('contextID', contextID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const query = `
    WITH Child_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}], [${LearningContextFields.VisibilityID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] = @contextID
      
      UNION ALL
      
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Child_Nodes] ON [Child_Nodes].[${LearningContextFields.ID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}] AND [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}] != ${Visibilities.Hidden}
    )
    SELECT COUNT([${UserFields.ID}]) as InProgress
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] IN (
      SELECT DISTINCT [${LearnerProgressFields.UserID}]
      FROM [${LearnerProgressTableName}]
      WHERE ([${LearnerProgressFields.LearningObjectID}] IN (
        SELECT [${LearningObjectContextFields.LearningObjectID}]
        FROM [${LearningObjectContextsTableName}]
        WHERE [${LearningObjectContextFields.LearningContextID}] IN (
          SELECT [${LearningContextFields.ID}]
          FROM [Child_Nodes]
        )
      ) OR [${LearnerProgressFields.LearningContextSessionID}] IN (
        SELECT [${LearningContextSessionFields.ID}]
        FROM [${LearningContextSessionsTableName}]
        WHERE [${LearningContextSessionFields.LearningContextID}] IN (
          SELECT [${LearningContextFields.ID}]
          FROM [Child_Nodes]
        )
      ))
      ${from && to ? `AND [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to` : ''}
    ) AND [${UserFields.ID}] NOT IN (
      SELECT [${UserCompletedLearningContextFields.UserID}]
      FROM [${UserCompletedLearningContextsTableName}]
      WHERE [${UserCompletedLearningContextFields.LearningContextID}] = @contextID
      AND [${UserCompletedLearningContextFields.LessonStatusID}] >= ${LessonStatuses.fail}
      ${from && to ? `AND [${UserCompletedLearningContextFields.CompletedOn}] BETWEEN @from AND @to` : ''}
    )
  `

  const results = await request.query<{InProgress: number}>(query)
  return results.recordset[0].InProgress
}

export async function getContextCompletionCounts (contextID: string, from?: Date, to?: Date): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('contextID', contextID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const query = `
    SELECT COUNT(DISTINCT [${UserCompletedLearningContextFields.UserID}]) AS Completed
    FROM [${UserCompletedLearningContextsTableName}]
    WHERE [${UserCompletedLearningContextFields.LearningContextID}] = @contextID
    ${from && to ? `AND [${UserCompletedLearningContextFields.CompletedOn}] BETWEEN @from AND @to` : ''}
  `

  const results = await request.query<{Completed: number}>(query)
  return results.recordset[0].Completed
}
