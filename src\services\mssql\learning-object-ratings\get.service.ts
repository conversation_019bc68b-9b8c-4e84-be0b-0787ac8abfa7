import mssql, { getRows } from '@lcs/mssql-utility'
import LearningObjectRatingModel from '../../../models/learning-object-rating.model.js'
import { LearningObjectRating, LearningObjectRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-rating.js'

export default async function getByID (guid: string): Promise<LearningObjectRatingModel> {
  const pool = mssql.getPool()
  const records = await getRows<LearningObjectRating>(LearningObjectRatingsTableName, pool.request(), { ID: guid })
  return new LearningObjectRatingModel(records[0])
}

export async function getObjectRatingsForUser (objectID: string, userID: string): Promise<LearningObjectRatingModel> {
  const pool = mssql.getPool()
  const records = await getRows<LearningObjectRating>(LearningObjectRatingsTableName, pool.request(), { LearningObjectID: objectID, UserID: userID })
  return new LearningObjectRatingModel(records[0])
}

export async function getAllObjectRatingsForUser (userID: string): Promise<LearningObjectRatingModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningObjectRating>(LearningObjectRatingsTableName, pool.request(), { UserID: userID })
  return records.map(record => new LearningObjectRatingModel(record))
}

export async function getAllObjectRatings (objectID: string): Promise<LearningObjectRatingModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningObjectRating>(LearningObjectRatingsTableName, pool.request(), { LearningObjectID: objectID })
  return records.map(record => new LearningObjectRatingModel(record))
}
