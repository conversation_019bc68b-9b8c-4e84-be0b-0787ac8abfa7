import rabbitmq from '@lcs/rabbitmq'
import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import Statement from '../../../models/amqp/lrs/statement.model.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service.AMQP.Create-LRS-Statement')

export default async function (statement: Statement): Promise<string> {
  let response!: {
    success: boolean,
    id?: string
    message?: string
  }

  try {
    response = await rabbitmq.executeRPC(
      settings.amqp.service_queues.lrs, {
        command: 'create',
        statement
      },
      settings.amqp.rpc_timeout
    )
  } catch (error) {
    log('error', 'Error executing rpc call', { errorMessage: getErrorMessage(error), success: false })
  }

  if (response?.success && response?.id) {
    return response.id
  } else if (response?.message) {
    throw new Error(response.message)
  } else {
    throw new Error('Unknown Error')
  }
}
