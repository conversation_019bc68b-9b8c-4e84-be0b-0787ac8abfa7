import mssql, { getRows } from '@lcs/mssql-utility'
import { AssignmentUser, AssignmentUsersTableName } from '@tess-f/sql-tables/dist/lms/assignment-user.js'
import AssignmentUserModel from '../../../models/assignment-users.model.js'
import logger from '@lcs/logger'
import { getErrorMessage } from '@tess-f/backend-utils'
const log = logger.create('Service.Get-Multiple-Assignment-Users')

export default async function (assignmentID: string): Promise<AssignmentUserModel[]> {
  try {
    const pool = mssql.getPool()
    const records = await getRows<AssignmentUser>(AssignmentUsersTableName, pool.request(), { AssignmentID: assignmentID })
    return records.map(record => new AssignmentUserModel(record))
  } catch (error) {
    log('error', 'Unexpected error', { errorMessage: getErrorMessage(error), success: false })
    // Unexpected error
    throw error
  }
}
