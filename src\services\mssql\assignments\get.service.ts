import mssql, { getRows } from '@lcs/mssql-utility'
import { Assignment, AssignmentsTableName } from '@tess-f/sql-tables/dist/lms/assignment.js'
import AssignmentModel from '../../../models/assignment.model.js'

export default async function (id: string): Promise<AssignmentModel> {
  const pool = mssql.getPool()
  const records = await getRows<Assignment>(AssignmentsTableName, pool.request(), { ID: id })
  return new AssignmentModel(records[0])
}
