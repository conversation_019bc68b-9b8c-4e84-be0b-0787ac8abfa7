import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearnerProgressModel from '../../../models/learner-progress.model'
import LearningObjectModel from '../../../models/learning-object.model.js'

describe('HTTP Resume Learning controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./resume-learning.controller', {
            '../../../services/mssql/learner-progress/resume-learning.service.js': {
                default: Sinon.stub().returns(Promise.resolve({ Progress: new LearnerProgressModel({  }), LearningObject: new LearningObjectModel({  }) }))
            }
        })

        const mocks = httpMocks.createMocks({
           
            params: {
                userID: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
        
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./resume-learning.controller', {
            '../../../services/mssql/learner-progress/resume-learning.service.js': {
                //default: Sinon.stub().returns(Promise.resolve({  }))
                default: Sinon.stub().returns(Promise.resolve({ Progress: new LearnerProgressModel({  }), LearningObject: new LearningObjectModel({  }) }))
            }
        })

        const mocks = httpMocks.createMocks({
           
            params: {
                userID: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request parameter data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('userID')
        
    })


    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./resume-learning.controller', {
            '../../../services/mssql/learner-progress/resume-learning.service.js': {
                default: Sinon.stub().rejects(Promise.resolve({ Progress: new LearnerProgressModel({  }), LearningObject: new LearningObjectModel({  }) }))
            }
        })

        const mocks = httpMocks.createMocks({
           
            params: {
                userID: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
        
    })



})