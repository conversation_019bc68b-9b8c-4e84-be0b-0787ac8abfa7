import logger from '@lcs/logger'
import httpStatus from 'http-status'
import Sinon from 'sinon'
import { expect } from 'chai'
import httpMock from 'node-mocks-http'
import esmock from 'esmock'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContextJson } from '@tess-f/lms/dist/common/learning-context.js'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: Get Available Parent Nodes', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('should return bad request when the id parameter is missing', async () => {
    const controller = await esmock('./get-available-parent-nodes.controller.js')
    const mocks = httpMock.createMocks()
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.equal('Invalid context ID in request parameters')
  })

  it('should return bad request when the id parameter is not a valid uuid', async () => {
    const controller = await esmock('./get-available-parent-nodes.controller.js')
    const mocks = httpMock.createMocks({ params: { id: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.equal('Invalid context ID in request parameters')
  })

  it('should return bad request when the id parameter is a number and not a valid uuid', async () => {
    const controller = await esmock('./get-available-parent-nodes.controller.js')
    const mocks = httpMock.createMocks({ params: { id: 123456 } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.equal('Invalid context ID in request parameters')
  })

  it('should return ok status when the id parameter is valid', async () => {
    const controller = await esmock('./get-available-parent-nodes.controller.js', {
      '../../../services/mssql/catalog/get-available-parent-nodes.service.js': {
        default: Sinon.stub().returns(Promise.resolve([]))
      }
    })
    const mocks = httpMock.createMocks({ params: { id: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const data = JSON.parse(mocks.res._getData())
    expect(Array.isArray(data)).to.be.true
    expect(data.length).to.equal(0)
  })
})
