import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import SystemWidgetGroupModel from '../../../models/system-widget-group.model'
import SystemWidgetModel from '../../../models/system-widget.model'

describe('HTTP update controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/system-widgets/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SystemWidgetModel({ID: uuid()})))
            },
            '../../../services/mssql/system-widget-groups/get-multiple.service.js': {
                default: Sinon.stub().returns(Promise.resolve([new SystemWidgetGroupModel()]))
            },
            '../../../services/mssql/system-widget-groups/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve({}))
            },
            '../../../services/mssql/system-widget-groups/delete.service.js': {
                default: Sinon.stub().returns(Promise.resolve(1))
            },
            '../../../services/mssql/widgets/get-for-creator.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                ID: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/system-widgets/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SystemWidgetModel({ID: uuid()})))
            },
            '../../../services/mssql/system-widget-groups/get-multiple.service.js': {
                default: Sinon.stub().returns(Promise.resolve([new SystemWidgetGroupModel()]))
            },
            '../../../services/mssql/system-widget-groups/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve({}))
            },
            '../../../services/mssql/system-widget-groups/delete.service.js': {
                default: Sinon.stub().returns(Promise.resolve(1))
            },
            '../../../services/mssql/widgets/get-for-creator.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                ID: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).to.include('Invalid request parameter data')
        expect(mocks.res._getData()).to.include('ID')
        expect(mocks.res._getData()).to.include('Expected string, received boolean')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/system-widgets/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SystemWidgetModel({ID: uuid()})))
            },
            '../../../services/mssql/system-widget-groups/get-multiple.service.js': {
                default: Sinon.stub().rejects(Promise.resolve([new SystemWidgetGroupModel()]))
            },
            '../../../services/mssql/system-widget-groups/create.service.js': {
                default: Sinon.stub().rejects(Promise.resolve({}))
            },
            '../../../services/mssql/system-widget-groups/delete.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(1))
            },
            '../../../services/mssql/widgets/get-for-creator.service.js': {
                default: Sinon.stub().rejects(Promise.resolve([]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                ID: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })
   

})
