import mssql from '@lcs/mssql-utility'
import { Request } from 'mssql'
import GroupClaimModel from '../../../../models/group-claim.model.js'
import { GroupClaim, GroupClaimFields, GroupClaimsTableName } from '@tess-f/sql-tables/dist/lms/group-claim.js'

/**
 * Get claims for a group or list of groups by ID.
 *
 * @param { String || Array[String] } groupIDs
 * @returns { Array[GroupClaimModel] }
 */
export default async function (groupIDs: string | string[]): Promise<GroupClaimModel[]> {
  // If only 1 group ID was passed in as a string, convert it to an array.
  let ids: string[]
  if (groupIDs && typeof groupIDs === 'string') {
    ids = [groupIDs]
  } else if (Array.isArray(groupIDs)) {
    ids = groupIDs
  } else {
    ids = []
  }

  const pool = mssql.getPool()
  return await getMultipleGroupClaims(pool.request(), ids)
};

// The generic MSSQL utils getRows(...) function does not support querying by
// multiple values of the same search field, so do it here instead.
async function getMultipleGroupClaims (request: Request, groupIDs: string[]): Promise<GroupClaimModel[]> {
  const keyIDs = []
  for (let i = 0; i < groupIDs.length; i++) {
    keyIDs.push('@key_' + i)
    request.input('key_' + i, groupIDs[i])
  }
  const query = `SELECT * FROM [${GroupClaimsTableName}] WHERE [${GroupClaimFields.GroupID}] IN (${keyIDs.join(', ')})`
  const result = await request.query<GroupClaim>(query)
  return result.recordset.map(record => new GroupClaimModel(record))
}
