import { Table } from '@lcs/mssql-utility'
import { LearningObject, LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningObjectJson } from '@tess-f/lms/dist/common/learning-object.js'
import z from 'zod'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { LearningObjectSubTypes } from '@tess-f/sql-tables/dist/lms/learning-object-sub-type.js'
import { zodGUID } from '@tess-f/backend-utils/validators'

export const createLearningObjectSchema = z.object({
  Title: z.string().max(500),
  Description: z.string(),
  LearningObjectTypeID: z.nativeEnum(LearningObjectTypes),
  MinutesToComplete: z.coerce.number().optional(),
  VisibilityID: z.nativeEnum(Visibilities).optional(),
  EnableCertificates: z.boolean().optional(),
  URL: z.string().max(2048).optional(),
  LearningObjectSubTypeID: z.nativeEnum(LearningObjectSubTypes).nullable().optional(),
  SystemId: z.string().max(50).optional(),
  Keywords: z.array(z.string()).optional(),
  ObjectiveIds: z.array(zodGUID).optional(),
  [LearningObjectFields.ContentID]: zodGUID.nullish()
})

export const updateLearningObjectSchema = createLearningObjectSchema.partial()

export default class LearningObjectModel extends Table<LearningObjectJson, LearningObject> {
  public fields: LearningObjectJson

  constructor (fields?: LearningObjectJson, record?: LearningObject) {
    super(LearningObjectsTableName, [
      LearningObjectFields.Title,
      LearningObjectFields.Description,
      LearningObjectFields.LearningObjectTypeID,
      LearningObjectFields.CreatedBy,
      LearningObjectFields.CreatedOn
    ])
    if (fields) this.fields = fields
    else this.fields = {}

    if (record) this.importFromDatabase(record)
  }

  public importFromDatabase (record: LearningObject & { Keywords?: string }): void {
    this.fields.ContentID = record.ContentID
    this.fields.CreatedBy = record.CreatedBy
    this.fields.CreatedOn = record.CreatedOn
    this.fields.Description = record.Description
    this.fields.EnableCertificates = record.EnableCertificates
    this.fields.Image = record.Image
    this.fields.URL = record.URL
    this.fields.ID = record.ID
    this.fields.Keywords = record.Keywords ? record.Keywords.split(',') : undefined
    this.fields.LaunchData = record.LaunchData
    this.fields.LearningObjectTypeID = record.LearningObjectTypeID
    this.fields.MasteryScore = record.MasteryScore
    this.fields.MinutesToComplete = record.MinutesToComplete
    this.fields.ModifiedBy = record.ModifiedBy
    this.fields.ModifiedOn = record.ModifiedOn
    this.fields.Title = record.Title
    this.fields.VisibilityID = record.VisibilityID
    this.fields.LearningObjectSubTypeID = record.LearningObjectSubTypeID
    this.fields.ActivityType = record.ActivityType
    this.fields.CMI5CourseNodeID = record.CMI5CourseNodeID
    this.fields.EntitlementKey = record.EntitlementKey
    this.fields.IRI = record.IRI
    this.fields.LaunchMethod = record.LaunchMethod
    this.fields.MoveOn = record.MoveOn
    this.fields.SystemId = record.SystemId
  }

  public exportJsonToDatabase (): LearningObject {
    return {
      ContentID: this.fields.ContentID,
      CreatedBy: this.fields.CreatedBy,
      CreatedOn: this.fields.CreatedOn,
      Description: this.fields.Description,
      Image: this.fields.Image,
      URL: this.fields.URL,
      ID: this.fields.ID,
      LearningObjectTypeID: this.fields.LearningObjectTypeID,
      MinutesToComplete: this.fields.MinutesToComplete,
      ModifiedBy: this.fields.ModifiedBy,
      ModifiedOn: this.fields.ModifiedOn,
      Title: this.fields.Title,
      VisibilityID: this.fields.VisibilityID,
      EnableCertificates: this.fields.EnableCertificates,
      LaunchData: this.fields.LaunchData,
      MasteryScore: this.fields.MasteryScore,
      LearningObjectSubTypeID: this.fields.LearningObjectSubTypeID,
      IRI: this.fields.IRI,
      MoveOn: this.fields.MoveOn,
      SystemId: this.fields.SystemId
    }
  }
}
