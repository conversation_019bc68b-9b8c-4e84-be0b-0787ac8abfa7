import { expect } from 'chai'
import mssql, { addRow, deleteRow, getRows } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import create from './create.service.js'
import get from './get.service.js'
import getAllForDownload from './get-all-for-download.service.js'
import getAllForUsersILTCourse from './get-all-for-users-ILT-course.service.js'
import getMultiple from './get-multiple.service.js'
import getTeamsAverageScore from './get-teams-average-score.service.js'
import getUserCompletionCountForObject from './get-user-completion-count-for-object.service.js'
import resumeLearning from './resume-learning.service.js'
import searchUserCompletedObject from './search-user-completed-object.service.js'
import updateRecordOnly from './update-record-only.service.js'
import update from './update.service.js'
import remove from './delete.service.js'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { AdminUserId, User, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { LearningContextSession, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import { v4 as uuidv4 } from 'uuid'

let learningContext: LearningContext
let learningObject: LearningObject
let learningContextSession: LearningContextSession
let learnerProgress: LearnerProgressModel
let adminUser: User

xdescribe('MSSQL Learner Progress', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    adminUser = (await getRows<User>(UserTableName, pool.request(), { ID: AdminUserId }))[0]
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test learning progress context',
      Description: `Running learning progress unit test on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Collection
    }))
    learningObject = await addRow<LearningObject>(pool.request(), new LearningObjectModel({
      Title: 'Test learning progress object',
      Description: `Running learning progress unit test on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      LearningObjectTypeID: LearningObjectTypes.Audio,
      ContentID: uuidv4()
    }))
    learningContextSession = await addRow<LearningContextSession>(pool.request(), new LearningContextSessionModel({
      SessionID: 'Service-Test-1',
      DisplayEnrollmentsOnHomePage: false,
      SessionStatusID: 1,
      CreatedOn: new Date(),
      CreatedBy: AdminUserId,
      LearningContextID: learningContext.ID,
      Timezone: 'MST',
      StartDate: new Date(),
      EndDate: new Date()
    }))
  })

  it('creates a new learner progress object', async () => {
    learnerProgress = await create(new LearnerProgressModel({
      LessonStatusID: LessonStatuses.passed,
      UserID: AdminUserId,
      LearningObjectID: learningObject.ID,
      CreatedOn: new Date(),
      LearningContextSessionID: learningContextSession.ID
    }))
    expect(learnerProgress.fields.UserID).to.be.eq(AdminUserId)
    expect(learnerProgress.fields.LessonStatusID).to.be.eq(LessonStatuses.passed)
    expect(learnerProgress.fields.LearningObjectID).to.be.eq(learningObject.ID)
    expect(learnerProgress.fields.LearningContextSessionID).to.be.eq(learningContextSession.ID)
  })

  it('gets the learner progress from DB', async () => {
    const result = await get(AdminUserId, learningObject.ID!)
    expect(result.fields.UserID).to.be.eq(AdminUserId)
    expect(result.fields.LessonStatusID).to.be.eq(LessonStatuses.passed)
    expect(result.fields.LearningObjectID).to.be.eq(learningObject.ID)
    expect(result.fields.LearningContextSessionID).to.be.eq(learningContextSession.ID)
  })

  it('gets all for download', async () => {
    const result = await getAllForDownload()
    expect(result.length).to.be.gte(0)
  })

  it('gets all for users ILT course', async () => {
    const result = await getAllForUsersILTCourse(learningContext.ID!, AdminUserId)
    expect(result.length).to.be.gte(0)
  })

  it('gets multiple', async () => {
    const result = await getMultiple(AdminUserId, learningObject.ID!)
    expect(result.length).to.be.gte(0)
  })

  it('gets teams average score', async () => {
    const result = await getTeamsAverageScore(adminUser.ManagerID!)
    expect(result).to.be.gte(0)
  })

  it('gets user completion count for object', async () => {
    const result = await getUserCompletionCountForObject(AdminUserId, learningObject.ID!)
    expect(result).to.be.gte(0)
  })

  it('gets resume learning', async () => {
    try {
      const result = await resumeLearning(AdminUserId)
      expect(result.LearningObject.fields.ID).to.be.eq(learningObject.ID)
    } catch (error) {
      expect(error).to.exist
    }
  })

  it('gets search user completed object', async () => {
    try {
      const result = await searchUserCompletedObject(AdminUserId, learningObject.ID!)
      expect(result.fields.UserID).to.be.eq(AdminUserId)
    } catch (error) {
      expect(error).to.exist
    }
  })

  it('update record only', async () => {
    learnerProgress.fields.LessonStatusID = LessonStatuses.browsed
    const result = await updateRecordOnly(learnerProgress)
    expect(result.fields.LessonStatusID).to.be.eq(LessonStatuses.browsed)
  })

  it('updates record', async () => {
    learnerProgress.fields.LessonStatusID = LessonStatuses.fail
    const result = await update(learnerProgress)
    expect(result.fields.LessonStatusID).to.be.eq(LessonStatuses.fail)
  })

  it('remove the newly created learner progress', async () => {
    const result = await remove(learnerProgress.fields.ID!)
    expect(result).to.be.gte(0)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { CreatedBy: AdminUserId })
    await deleteRow<LearningObject>(pool.request(), LearningObjectsTableName, { CreatedBy: AdminUserId })
    await deleteRow<LearningContextSession>(pool.request(), LearningContextSessionsTableName, { CreatedBy: AdminUserId })
  })
})
