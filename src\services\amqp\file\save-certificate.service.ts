import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import { CreateFile } from '@tess-f/fds/dist/amqp/create.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service.AMQP.Save-Certificate')

export default async function (base64Cert: string, fileTitle: string): Promise<string> {
  try {
    const response = await CreateFile(
      settings.amqp.service_queues.fds, {
        createdById: 'Not Telling',
        filename: `${fileTitle}.pdf`,
        mimeType: 'application/pdf',
        owner: 'lms',
        isPublic: false,
        isPackage: false,
        createThumbnail: false,
        fileBase64: base64Cert
      },
      settings.amqp.rpc_timeout
    )

    if (response.success && response.data) {
      return response.data.id
    } else if (response.success && !response.data) {
      throw new Error('RPC returned success status but no data')
    } else {
      throw new Error(response.message ?? 'Unknown RPC error')
    }
  } catch (error) {
    log('error', 'Error executing rpc call', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}
