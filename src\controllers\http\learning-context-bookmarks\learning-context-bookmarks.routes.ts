import { Request<PERSON><PERSON><PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getController from './get.controller.js'

const router = Router()

router.post('/context-bookmark', createController as RequestHandler)
router.get('/context-bookmark', getController as RequestHandler)
router.delete('/context-bookmark/:userID/:contextID', deleteController as RequestHandler)

export default router
