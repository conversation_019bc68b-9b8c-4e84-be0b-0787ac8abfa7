import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { GhostUserId, User, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'

export default async function getPaginatedUserIds (offset: number = 0, limit: number = 10, search?: string): Promise<{ totalRecords: number, ids: string[] }> {
  const request = mssql.getPool().request()

  let query = `
    SELECT [${UserFields.ID}], TotalRecords = COUNT(*) OVER()
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] != '${GhostUserId}'
  `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [UserFields.FirstName, UserFields.LastName, UserFields.Username], 'any')}) `
  }

  request.input('offset', offset)
  request.input('limit', limit)

  query += `
    ORDER BY [${UserFields.FirstName}], [${UserFields.LastName}]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<User & { TotalRecords: number }>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    ids: results.recordset.map(record => record.ID!)
  }
}
