import mssql from '@lcs/mssql-utility'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import { Request } from 'mssql'
import getContextService from './get.service.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import getLearningObjectsService from '../learning-objects/get-multiple.service.js'
import { LearnerProgress, LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import { GradeTypes } from '@tess-f/sql-tables/dist/lms/grade-type.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { LearningObjectSubTypes } from '@tess-f/sql-tables/dist/lms/learning-object-sub-type.js'

/**
 * @param contextID The ID of the context to check completion progress for
 * @param userID The ID of the user to check status on this context for
 * @param from Date to start the progress search from
 * @param to Date to end progress search
 * @param includeExam When false will not include the exam for this context in it's completion calculation. This is useful in determining if all content except the exam has been completed.
 */
export default async function (contextID: string, userID: string, from?: Date, to?: Date, includeExam = true) {
  // get the context with it's nested contexts
  const context = await getContextService(contextID, undefined, { nestedContexts: true })

  const status = await calculateCompletion(context, userID, from, to, includeExam)

  return {
    completion: status.completion,
    numberOfObjects: status.numberOfItems,
    numberOfCompletedObjects: status.numberCompleted
  }
}

async function calculateCompletion (context: LearningContextModel, userID: string, from?: Date, to?: Date, includeExam: boolean = true): Promise<{ completion: number, numberOfItems: number, numberCompleted: number }> {
  const pool = mssql.getPool()

  let completion = 0
  let numberOfCompleted = 0
  let completionProgress = 0
  let totalItems = 0

  if (context.fields.ContextTypeID === LearningContextTypes.Section || context.fields.ContextTypeID === LearningContextTypes.CMI5Block || context.fields.ContextTypeID === LearningContextTypes.CMI5Course) {
    // lets get the content for this context
    const learningObjects = await getLearningObjectsService(context.fields.ID)
    if (!context.fields.Contexts) {
      context.fields.Contexts = []
    }

    if (learningObjects.length <= 0 && context.fields.Contexts.length <= 0) {
      completion = 100
    }

    for (const obj of learningObjects) {
      let objectCompleted = false
      let objectCompletion = -1
      const progress = await getLearnerProgress(pool.request(), [obj.fields.ID!], userID, from, to)
      const completionIds = obj.fields.LearningObjectTypeID === LearningObjectTypes.CBT && obj.fields.LearningObjectSubTypeID === LearningObjectSubTypes.CMI5AU
        ? [LessonStatuses.completed, LessonStatuses.passed]
        : [LessonStatuses.completed, LessonStatuses.passed, LessonStatuses.fail]
      for (const p of progress) {
        if (
          completionIds.includes(p.fields.LessonStatusID!) &&
          (
            (obj.fields.LearningObjectTypeID === LearningObjectTypes.CBT && obj.fields.LearningObjectSubTypeID === LearningObjectSubTypes.CMI5AU && p.fields.CompletedDate) ||
            obj.fields.LearningObjectSubTypeID !== LearningObjectSubTypes.CMI5AU
          )
        ) {
          objectCompleted = true
          break
        } else if ((obj.fields.LearningObjectTypeID === LearningObjectTypes.Video || obj.fields.LearningObjectTypeID === LearningObjectTypes.Audio) &&
          objectCompletion <= 0 && p.fields.RawScore && !isNaN(p.fields.RawScore)) {
          // lets add the progress on this towards completion
          objectCompletion = Number(p.fields.RawScore) / 100
          break
        }
      }
      if (objectCompleted) {
        numberOfCompleted++
        completionProgress += 1
      } else if (objectCompletion > 0) {
        completionProgress += objectCompletion
      }
      totalItems++
    }

    // now we need to go through the nested contexts
    for (const lc of context.fields.Contexts) {
      // when looking at the completion of a sub context always include the exam
      const status = await calculateCompletion(new LearningContextModel(lc), userID, from, to, true)
      totalItems += status.numberOfItems
      numberOfCompleted += status.numberCompleted
      completionProgress += status.numberCompleted
    }

    if (totalItems > 0) {
      completion = (completionProgress / totalItems) * 100
    }

    // now we look at the exam
    if (context.fields.GradeTypeID === GradeTypes.Exam && includeExam) {
      // this is graded by exam
      // add one item for the exam
      totalItems++
      if (completion >= 100) {
        // the exam is unlocked we can look at the status of it
        const examProgress = await getLearnerProgress(pool.request(), [context.fields.ExamID!], userID, from, to)
        let examCompleted = false
        for (const p of examProgress) {
          if (p.fields.LessonStatusID === 4 || p.fields.LessonStatusID === 5 || p.fields.LessonStatusID === 6) {
            examCompleted = true
            break
          }
        }
        if (examCompleted) {
          numberOfCompleted++
          completionProgress += 1
        }
        completion = (completionProgress / totalItems) * 100
      } else {
        completion = (completionProgress / totalItems) * 100
      }
    }
    return {
      completion,
      numberOfItems: totalItems,
      numberCompleted: numberOfCompleted
    }
  } else if (context.fields.ContextTypeID === LearningContextTypes.Course && context.fields.CourseTypeID === CourseTypes.InstructorLed) {
    // ILT
    totalItems++
    const progress = await getLearnerProgressForILT(pool.request(), context.fields.ID!, userID, from, to)
    for (const prog of progress) {
      if (prog.fields.LessonStatusID! >= LessonStatuses.fail) {
        completionProgress += 1
        numberOfCompleted += 1
        break
      }
    }
    if (totalItems > 0) {
      completion = (completionProgress / totalItems) * 100
    }

    // WJB: Removed the check for the exam file for ILT courses, that will be marked on the overall completion of the session
    // No need to look at the exam score

    return {
      completion,
      numberOfItems: totalItems,
      numberCompleted: numberOfCompleted
    }
  } else if (context.fields.ContextTypeID === LearningContextTypes.ElectiveOptional) {
    if (!context.fields.RequiredContentCount) {
      // if the user isn't required to complete any of the content give them credit
      return {
        completion: 100,
        numberCompleted: 0,
        numberOfItems: 0
      }
    }

    // this can have learning objects and ILT courses
    const learningObjects = await getLearningObjectsService(context.fields.ID)
    if (!context.fields.Contexts) {
      context.fields.Contexts = []
    }

    if (learningObjects.length <= 0 && context.fields.Contexts.length <= 0) {
      numberOfCompleted = context.fields.RequiredContentCount
    }

    for (const obj of learningObjects) {
      let objectCompleted = false
      const progress = await getLearnerProgress(pool.request(), [obj.fields.ID!], userID, from, to)
      for (const p of progress) {
        if (p.fields.LessonStatusID === LessonStatuses.passed || p.fields.LessonStatusID === LessonStatuses.fail || p.fields.LessonStatusID === LessonStatuses.completed) {
          objectCompleted = true
          break
        }
      }
      if (objectCompleted) {
        numberOfCompleted++
      }
    }

    // now we need to go through the contexts
    for (const lc of context.fields.Contexts) {
      // when looking at the completion of a context always include the exam status
      const status = await calculateCompletion(new LearningContextModel(lc), userID, from, to, true)
      numberOfCompleted += status.completion >= 100 ? 1 : 0 // we are not worried about counting the exam for the ILT
    }

    // now we need to calculate the completion
    if (numberOfCompleted >= context.fields.RequiredContentCount) {
      return {
        completion: 100,
        numberOfItems: numberOfCompleted,
        numberCompleted: numberOfCompleted
      }
    } else {
      return {
        completion: numberOfCompleted / context.fields.RequiredContentCount,
        numberCompleted: numberOfCompleted,
        numberOfItems: context.fields.RequiredContentCount
      }
    }
  }

  // if we got this far we don't know how to calculate this
  return {
    completion,
    numberCompleted: numberOfCompleted,
    numberOfItems: totalItems
  }
}

async function getLearnerProgress (request: Request, ids: string[], userID: string, from?: Date, to?: Date) {
  if (ids.length === 0) return []

  const conditions = ids.map((id, index) => {
    request.input('id_' + index, id)
    let condition = '@id_' + index

    if (index < ids.length - 1) {
      condition += ', '
    }
    return condition
  })
  request.input('userID', userID)
  let query = `
    SELECT *
    FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @userID
    AND [${LearnerProgressFields.LearningObjectID}] IN (${conditions.join('')})
  `

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
    query += ` AND [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to `
  }
  query += `ORDER BY [${LearnerProgressFields.CreatedOn}] DESC`
  const results = await request.query<LearnerProgress>(query)
  return results.recordset.map(record => new LearnerProgressModel(record))
}

async function getLearnerProgressForILT (request: Request, id: string, userID: string, from?: Date, to?: Date) {
  request.input('courseID', id)
  request.input('userID', userID)
  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }
  const results = await request.query<LearnerProgress>(`
    SELECT * FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @userID
    AND [${LearnerProgressFields.LearningContextSessionID}] IN (
      SELECT [${LearningContextSessionFields.ID}]
      FROM [${LearningContextSessionsTableName}]
      WHERE [${LearningContextSessionFields.LearningContextID}] = @courseID
    )
    ${from && to ? `AND [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to` : ''}
    ORDER BY [${LearnerProgressFields.CreatedOn}] DESC
  `)
  return results.recordset.map(record => new LearnerProgressModel(record))
}
