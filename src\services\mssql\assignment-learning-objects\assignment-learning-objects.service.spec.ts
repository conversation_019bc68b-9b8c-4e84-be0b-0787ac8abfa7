import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import getMultiple from './get-multiple.service.js'
import deleteLearningObject from '../learning-objects/delete.service.js'
import deleteAssignment from '../assignments/delete.service.js'
import { Assignment } from '@tess-f/sql-tables/dist/lms/assignment.js'
import AssignmentModel from '../../../models/assignment.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningObject } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { v4 as uuidv4 } from 'uuid'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { AssignmentLearningObject, AssignmentLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-object.js'
import AssignmentLearningObjectModel from '../../../models/assignment-learning-object.model.js'

let assignment: Assignment
let learningObject: LearningObject
let assignmentLearningObject: AssignmentLearningObject

describe('MSSQL Assignment Learning Objects', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    assignment = await addRow<Assignment>(pool.request(), new AssignmentModel({
      Title: 'Testing assignment learning objects',
      TypeID: 1,
      Everyone: true,
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    }))
    learningObject = await addRow<LearningObject>(pool.request(), new LearningObjectModel({
      Title: 'Test assignment learning objects',
      Description: 'Running assignment learning objects',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      LearningObjectTypeID: LearningObjectTypes.Audio,
      ContentID: uuidv4()
    }))
    assignmentLearningObject = await addRow<AssignmentLearningObject>(pool.request(), new AssignmentLearningObjectModel({
      AssignmentID: assignment.ID,
      LearningObjectID: learningObject.ID,
      OrderID: 1,
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    }))
  })

  it('gets multiple assignment learning object records by assignment ID', async () => {
    const objs = await getMultiple(assignment.ID!)
    expect(objs.length).to.be.gte(1)
  })

  it('should fail to get assignment learning object records with fake ID', async () => {
    try {
      const objs = await getMultiple(uuidv4())
      expect(objs.length).to.be.gte(0)
    } catch (error) {
      expect(error).to.exist
    }
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<AssignmentLearningObject>(
      pool.request(),
      AssignmentLearningObjectsTableName,
      {
        AssignmentID: assignmentLearningObject.AssignmentID,
        LearningObjectID: assignmentLearningObject.LearningObjectID
      }
    )
    await deleteLearningObject(learningObject.ID!)
    await deleteAssignment(assignment.ID!)
  })
})
