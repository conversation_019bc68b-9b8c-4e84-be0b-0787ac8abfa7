import { Request, Response, NextFunction } from 'express'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import sessionAuthority from '@lcs/session-authority'

const log = logger.create('Middleware.HTTP-Request')

export default function (req: Request, res: Response, next: NextFunction) {
  if (settings.server.log_all_requests === true) {
    log('info', `(${sessionAuthority.getRequestIp(req)}) -> ${req.originalUrl}`)
  };
  next()
}
