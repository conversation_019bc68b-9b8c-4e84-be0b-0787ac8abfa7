import logger from '@lcs/logger'
import { DB_Errors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR } = httpStatus
import getMulti from '../../../services/mssql/keywords/get-multiple.service.js'
import { httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('Controller-HTTP.get-multi-keywords', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, begin creating a new course. Note the time. Check the LMS API log for the 'http-get-multi-keywords' label."

  try {
    const keywords = await getMulti()

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, begin creating a new course. Note the time. Check the LMS API log for the 'http-get-multi-keywords' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved keywords.', { count: keywords.length, success: true, req })

    res.json(keywords.map(keyword => keyword.fields))
  } catch (error) {
    // STIG V-69425 data access (failure)

    if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to retrieve keywords because they were not found in the database.', { success: false, req })
      res.json([])
    } else {
      log('error', 'Failed to retrieve keywords.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
