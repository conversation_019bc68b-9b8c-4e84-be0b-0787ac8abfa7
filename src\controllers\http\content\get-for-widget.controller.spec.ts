import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import WidgetModel from '../../../models/widget.model.js'
import { WidgetTypes } from '@tess-f/sql-tables/dist/lms/widget-type'

describe('HTTP Controller: get content for widget', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => sinon.restore())

  it('returns bad request when the id request param is not a valid uuid', async () => {
    const controller = await esmock('./get-for-widget.controller.js')
    const mocks = httpMocks.createMocks({ params: { id: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request parameters,')
    expect(data).to.include('id: Invalid')
  })

  it('returns not found when the widget is not found', async () => {
    const controller = await esmock('./get-for-widget.controller.js', {
      '../../../services/mssql/widgets/get.service.js': {
        default: sinon.stub().rejects(new Error(dbErrors.default.NOT_FOUND_IN_DB))
      }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.NOT_FOUND)
  })

  it('returns the content for the widget', async () => {
    const controller = await esmock('./get-for-widget.controller.js', {
      '../../../services/mssql/widgets/get.service.js': {
        default: sinon.stub().resolves(new WidgetModel({ ID: uuid(), TypeID: WidgetTypes.SmallCard, Filter: false }))
      }, '../../../services/mssql/content/get-widget-specified-content.service.js': {
        default: sinon.stub().resolves([])
      }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.be.an('array')
    expect(data.length).to.equal(0)
  })

  it('should gracefully handle an error', async () => {
    const controller = await esmock('./get-for-widget.controller.js', {
      '../../../services/mssql/widgets/get.service.js': {
        default: sinon.stub().rejects(new Error('unknown error'))
      }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })
})
