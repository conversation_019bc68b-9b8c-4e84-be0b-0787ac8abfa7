import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextRating from '../../../models/learning-context-rating.model.js'


describe('HTTP Update controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context-ratings/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextRating({})))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                UserID: uuid(),
                LearningContextID: uuid(),
                Rating: 1
            },
            params: {
                id: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request body is invalid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context-ratings/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextRating({})))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                UserID: false,
                LearningContextID: false,
                Rating: false
            },
            params: {
                id: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Rating: Expected number, received boolean')
        expect(mocks.res._getData()).include('UserID: Expected string, received boolean')
        expect(mocks.res._getData()).include('LearningContextID: Expected string, received boolean')
    })


    it('returns an error if the request params are invalid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context-ratings/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextRating({})))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                UserID: uuid(),
                LearningContextID: uuid(),
                Rating: 1
            },
            params: {
                id: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('id: Expected string, received boolean')
    })
    
    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context-ratings/update.service.js': {
                default: Sinon.stub().returns(Promise.reject(new LearningContextRating({})))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                UserID: uuid(),
                LearningContextID: uuid(),
                Rating: 1
            },
            params: {
                id: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

    

})