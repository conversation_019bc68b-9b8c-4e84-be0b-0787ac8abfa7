import LearningContextFavorites, { createLearningContextFavoriteSchema } from '../../../models/learning-context-user-favorite.model.js'
import create from '../../../services/mssql/learning-context-favorites/create.service.js'
import logger from '@lcs/logger'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { ZodError } from 'zod'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.create-learning-context-favorites', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, click to favorite a course. Note the time. Check the LMS API log for the 'http-create-learning-context-favorites' label."

  try {
    const favorites = new LearningContextFavorites(createLearningContextFavoriteSchema.parse(req.body))

    const result = await create(favorites)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, click to favorite a course. Note the time. Check the LMS API log for the 'http-create-learning-context-favorites' label and message indicating successful creation."
    log('info', 'Successfully created learning context favorite for context', { contextId: req.body.LearningContextID, success: true, req })

    res.json(result.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create favorite for learning context: input validation error', { errorMessage, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    }
    else {
    if (errorMessage.startsWith('Violation of UNIQUE KEY constraint')) {
      log('warn', 'Failed to create favorite for learning context because it already exists in the database.', { contextId: req.body.LearningContextID, success: false, req })
      res.status(BAD_REQUEST).send('User learning context favorite already exists')
    } else {
      log('error', 'Failed to create favorite for learning context.', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
}
