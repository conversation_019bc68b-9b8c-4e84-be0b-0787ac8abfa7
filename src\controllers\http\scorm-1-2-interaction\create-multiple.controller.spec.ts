import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import SCORM1_2InteractionModel from '../../../models/scorm-1-2-interaction.model.js'

describe('HTTP create-multiple controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create-multiple.controller', {
            '../../../services/mssql/scorm-1-2-interaction/delete-for-user-session.service.js': {
                default: Sinon.stub().returns(Promise.resolve(1))
            },
            '../../../services/mssql/scorm-1-2-interaction/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SCORM1_2InteractionModel()))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: [{
                ID: uuid(),
                Time: '00:00:00',
                Type: 'true-false',
                Weighting: '1',
                StudentResponse: 'true',
                Result: 'correct',
                Latency: '00:00:00',
                LearnerProgressID: uuid(),
                CorrectResponses: 'true',
                UserID: uuid()
            }]

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create-multiple.controller', {
            '../../../services/mssql/scorm-1-2-interaction/delete-for-user-session.service.js': {
                default: Sinon.stub().returns(Promise.resolve(1))
            },
            '../../../services/mssql/scorm-1-2-interaction/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SCORM1_2InteractionModel()))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: [{
              
            }]

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).to.include('Invalid request data:')
        expect(mocks.res._getData()).to.include('Required')
        expect(mocks.res._getData()).to.include('LearnerProgressID')
        expect(mocks.res._getData()).to.include('UserID')

    })


    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./create-multiple.controller', {
            '../../../services/mssql/scorm-1-2-interaction/delete-for-user-session.service.js': {
                default: Sinon.stub().returns(Promise.resolve(1))
            },
            '../../../services/mssql/scorm-1-2-interaction/create.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new SCORM1_2InteractionModel()))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: [{
                ID: uuid(),
                Time: '00:00:00',
                Type: 'true-false',
                Weighting: '1',
                StudentResponse: 'true',
                Result: 'correct',
                Latency: '00:00:00',
                LearnerProgressID: uuid(),
                CorrectResponses: 'true',
                UserID: uuid()
            }]

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })


})
