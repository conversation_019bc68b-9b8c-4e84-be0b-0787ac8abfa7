import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import isValid from '../../../services/mssql/learning-context/is-course-id-unique.service.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.is-course-id-unique', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { CourseID, ContextID } = z.object({
      CourseID: z.string(),
      ContextID: zodGUID.optional()
    }).parse(req.body)

    const result = await isValid(CourseID, ContextID)
    log('info', `Successfully determined that course ${result ? 'is' : 'is not'} unique`, { id: CourseID, success: true, req })
    res.json(result)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to determine if course id is unique: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to determine if course id is unique.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
