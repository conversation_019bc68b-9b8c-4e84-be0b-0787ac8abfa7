const chai = require('chai');
const expect = chai.expect;
const uuidv4 = require('uuid/v4');
const tester = require('../api/utils/test-agent.utils');
const settings = require('../api/config/settings');

let isolated = false;
let userID;


describe("Users", () => {

    before( done => {
        if(tester.agent == null) {
            isolated = true;
            tester.create()
            .then( agent => {
                done();
            })
            .catch( err => {
                console.error(err);
                done();
            });
        } else {
            done();
        }
    });


    it("can get all users", done => {
        tester.agent
        .get(settings.server.root + 'users')
        .end((err, res) => {
            expect(res.statusCode).to.equal(200);
            expect(res.body.length).to.be.gt(1);
            userID = res.body[0].id;
            done();
        });
    });

    it("can get a user", done => {
        tester.agent
        .get(settings.server.root + 'user/' + userID)
        .end((err, res) => {
            expect(res.body).to.exist;
            expect(res.body.id).to.equal(userID);
            done();
        });
    });

    after((done)=>{
        if(isolated) {
            tester.close();
        } 
        done();
    });

})