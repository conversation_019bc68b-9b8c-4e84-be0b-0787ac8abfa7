import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { Certificate } from '@tess-f/lms/dist/common/certificate.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextKeywordFields, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'
import { LearningObjectKeywordFields, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import { UserContentCertificateFields, UserContentCertificatesViewName } from '@tess-f/sql-tables/dist/lms/user-content-certificate.js'

export default async function getTeamsCertificatesService (managerID: string, offset: number = 0, limit: number = 10, search?: string, labels?: string[], objectTypeIds?: number[], keywords?: string[], from?: Date, to?: Date): Promise<{ TotalRecords: number, items: Certificate[] }> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('managerID', managerID)

  let query = `
    SELECT *, TotalRecords = COUNT(*) OVER() FROM [${UserContentCertificatesViewName}]
    WHERE [${UserContentCertificateFields.UserID}] IN (
      SELECT [${UserFields.ID}]
      FROM [${UserTableName}]
      WHERE [${UserFields.ManagerID}] = @managerID
    )
  `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [UserContentCertificateFields.Title], 'any')}) `
  }

  if (labels && labels.length > 0 && objectTypeIds && objectTypeIds.length > 0) {
    const conditions = labels.map((label, index) => {
      request.input(`label_${index}`, label)
      return `@label_${index}`
    })
    query += `
      AND (
        [${UserContentCertificateFields.LearningObjectTypeID}] IN (${objectTypeIds.join(', ')}) OR
        [${UserContentCertificateFields.ID}] IN (
          SELECT [${LearningContextFields.ID}]
          FROM [${LearningContextTableName}]
          WHERE [${LearningContextFields.Label}] IN (
            ${conditions.join(', ')}
          )
        )
      )
    `
  } else if (labels && labels.length > 0) {
    const conditions = labels.map((label, index) => {
      request.input(`label_${index}`, label)
      return `@label_${index}`
    })
    query += `
      AND (
        [${UserContentCertificateFields.ID}] IN (
          SELECT [${LearningContextFields.ID}]
          FROM [${LearningContextTableName}]
          WHERE [${LearningContextFields.Label}] IN (
            ${conditions.join(', ')}
          )
        )
      )
    `
  } else if (objectTypeIds && objectTypeIds.length > 0) {
    query += `AND ([${UserContentCertificateFields.LearningObjectTypeID}] IN (${objectTypeIds.join(', ')})) `
  }

  if (keywords && keywords.length > 0) {
    const conditions = keywords.map((key, index) => {
      request.input(`key_${index}`, key)
      return `@key_${index}`
    })
    query += `
      AND (
        [${UserContentCertificateFields.ID}] IN (
          SELECT [${LearningContextKeywordFields.LearningContextID}]
          FROM [${LearningContextKeywordsTableName}]
          WHERE [${LearningContextKeywordFields.Keyword}] IN (
            ${conditions.join(', ')}
          )
        ) OR [${UserContentCertificateFields.ID}] IN (
          SELECT [${LearningObjectKeywordFields.LearningObjectID}]
          FROM [${LearningObjectKeywordsTableName}]
          WHERE [${LearningObjectKeywordFields.Keyword}] IN (
            ${conditions.join(', ')}
          )
        )
      )
    `
  }

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
    query += `AND ([${UserContentCertificateFields.CompletedDate}] BETWEEN @from AND @to) `
  }

  request.input('offset', offset)
  request.input('limit', limit)

  query += `
    ORDER BY [${UserContentCertificateFields.CompletedDate}] DESC, [${UserContentCertificateFields.Title}] ASC
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const records = await request.query<Certificate & { TotalRecords: number }>(query)

  return {
    TotalRecords: records.recordset.length > 0 ? records.recordset[0].TotalRecords : 0,
    items: records.recordset
  }
}
