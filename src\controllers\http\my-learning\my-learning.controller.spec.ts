import mssql from '@lcs/mssql-utility'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import { fillRequestHeaders } from '../../../utils/test-agent.utils.js'
import { startServices, shutdownServices } from '../../../utils/testing.utils.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { v4 as uuidv4 } from 'uuid'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import createLearningObject from '../../../services/mssql/learning-objects/create.service.js'
import deleteLearningObject from '../../../services/mssql/learning-objects/delete.service.js'
import createLearnerProgress from '../../../services/mssql/learner-progress/create.service.js'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import createAssignment from '../../../services/mssql/assignments/create.service.js'
import deleteAssignment from '../../../services/mssql/assignments/delete.service.js'
import AssignmentsModel from '../../../models/assignment.model.js'

import getLearningMetadata from './get-learning-metadata.controller.js'
import AssignmentUserModel from '../../../models/assignment-users.model.js'
import AssignmentLearningObjectModel from '../../../models/assignment-learning-object.model.js'
import { ActivityStreamTableName } from '@tess-f/sql-tables/dist/lms/activity-stream.js'
import { LearningMetadata } from '@tess-f/lms/dist/common/learning-metadata.js'

describe('HTTP: My Learning Controller', () => {
  let learningObject1: LearningObjectModel
  let learningObject2: LearningObjectModel
  let assignment: AssignmentsModel

  before(async () => {
    await startServices()
    learningObject1 = await createLearningObject(new LearningObjectModel({
      Title: 'Test Object',
      Description: 'Test Object for My Learning controller tests',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      LearningObjectTypeID: LearningObjectTypes.Text,
      ContentID: uuidv4()
    }))
    learningObject2 = await createLearningObject(new LearningObjectModel({
      Title: 'Test Object',
      Description: 'Test Object for My Learning Controller Tests',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      LearningObjectTypeID: LearningObjectTypes.Text,
      ContentID: uuidv4()
    }))
    await createLearnerProgress(new LearnerProgressModel({
      LearningObjectID: learningObject1.fields.ID,
      UserID: AdminUserId,
      LessonStatusID: LessonStatuses.passed
    }))
    await createLearnerProgress(new LearnerProgressModel({
      LearningObjectID: learningObject2.fields.ID,
      UserID: AdminUserId,
      LessonStatusID: LessonStatuses.browsed
    }))
    assignment = (await createAssignment(new AssignmentsModel({
      Title: 'Test for my learning controller',
      TypeID: 1,
      Everyone: false,
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    }), [new AssignmentUserModel({
      UserID: AdminUserId
    })], [], [new AssignmentLearningObjectModel({
      LearningObjectID: learningObject2.fields.ID,
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    })], [])).assignment
  })

  it('gets learning metadata for a given user', async () => {
    const mocks = httpMocks.createMocks({
      params: { id: AdminUserId }
    })
    fillRequestHeaders(mocks.req)

    await getLearningMetadata(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(200)
    const response: LearningMetadata = JSON.parse(mocks.res._getData())
    expect(response.Completed).to.be.gt(0)
    expect(response.InProgress).to.be.gt(0)
  })

  after(async () => {
    await deleteAssignment(assignment.fields.ID!)
    await deleteLearningObject(learningObject1.fields.ID!)
    await deleteLearningObject(learningObject2.fields.ID!)
    await mssql.getPool().request().query(`
      DELETE FROM [${ActivityStreamTableName}]
    `)
    await shutdownServices()
  })
})
