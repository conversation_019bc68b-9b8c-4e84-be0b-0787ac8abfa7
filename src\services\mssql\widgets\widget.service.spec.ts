import { expect } from 'chai'
import mssqlConnection from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import WidgetModel from '../../../models/widget.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { v4 as uuidV4 } from 'uuid'
import KeywordModel from '../../../models/keyword.model.js'
import createKeyword from '../keywords/create.service.js'
import deleteKeyword from '../keywords/delete.service.js'

// services to test
import create from './create.service.js'
// TODO import getWidgetsForGroups from './get-group-widgets.service.js'
import get from './get.service.js'
import getWidgetForCreator from './get-for-creator.service.js'
// TODO import getWidgetsForUser from './get-for-user.service.js'
import getWidgetForKeywords from './get-keywords.service.js'
import update from './update.service.js'
import remove from './delete.service.js'

describe('MSSQL Widget Services', () => {
  let widget: WidgetModel
  const keyword: string = uuidV4()

  before('Connect to SQL', async () => {
    await mssqlConnection.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    await createKeyword(new KeywordModel({ Name: keyword }))
  })

  it('Unit test to create new widget', async () => {
    widget = await create(new WidgetModel({
      Title: 'Running widget MSSQL unit test',
      Carousel: true,
      Filter: true,
      IsAdmin: true,
      CreatedBy: AdminUserId,
      OrderID: 1150,
      TypeID: 1,
      Keywords: [keyword]
    }))
    expect(widget.fields.ID).not.to.be.undefined
  })

  it('Unit test to get widget', async () => {
    const results = await get(widget.fields.ID!)
    expect(results).to.not.be.eq(undefined)
  })

  it('Unit test to get creator widget', async () => {
    const results = await getWidgetForCreator(widget.fields.CreatedBy!)
    expect(results.length).to.be.gte(1)
  })

  // it('Unit test to get user widget', async () => {
  //   const results = await getWidgetForUser(widget.fields.ID!)
  //   expect(results).to.not.be.eq(undefined)
  // })

  // it('Unit test to get group widget', async () => {
  //   const results = await getWidgetsForGroups(widget.fields.ID!)
  //   expect(results).to.not.be.eq(undefined)
  // })

  it('Unit test to get keywords widget', async () => {
    const results = await getWidgetForKeywords(widget.fields.ID!)
    expect(results.length).to.be.gte(1)
  })

  it('Unit test to update widget', async () => {
    widget.fields.Title = 'Testing Updating Title'
    const results = await update(widget)
    expect(results.fields.Title).to.be.eq('Testing Updating Title')
  })

  it('Unit test to remove widget', async () => {
    const results = await remove(widget.fields.ID!)
    expect(results).to.be.eq(undefined)
  })

  after(async () => {
    await deleteKeyword(keyword)
  })
})
