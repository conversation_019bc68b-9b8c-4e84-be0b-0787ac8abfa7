import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'
import { UserAssignedMultiSessionCoursesFields, UserAssignedMultiSessionCoursesTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-multi-session-course.js'

/**
 * Returns the soonest due date for a given piece of content
 * @param contextID ID of the learning context to get a due date for
 * @param userID User who the content is assigned to
 */
export default async function getLearningContextDueDate (contextID: string, userID: string): Promise<Date> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('userID', userID)
  request.input('contextID', contextID)

  const query = `
    WITH DueDates AS (
      SELECT [${UserAssignedLearningObjectFields.DueDate}]
      FROM [${UserAssignedLearningObjectsTableName}]
      WHERE [${UserAssignedLearningObjectFields.ForContextID}] = @contextID
      AND [${UserAssignedLearningObjectFields.UserID}] = @userID
      AND [${UserAssignedLearningObjectFields.DueDate}] IS NOT NULL

      UNION

      SELECT [${UserAssignedMultiSessionCoursesFields.DueDate}]
      FROM [${UserAssignedMultiSessionCoursesTableName}]
      WHERE [${UserAssignedMultiSessionCoursesFields.DueDate}] IS NOT NULL
      AND [${UserAssignedMultiSessionCoursesFields.UserID}] = @userID
      AND (
        [${UserAssignedMultiSessionCoursesFields.LearningContextID}] = @contextID
        OR [${UserAssignedMultiSessionCoursesFields.ForContextID}] = @contextID
      )
    )
    SELECT TOP (1) [${UserAssignedLearningObjectFields.DueDate}]
    FROM [DueDates]
    ORDER BY [${UserAssignedLearningObjectFields.DueDate}] ASC
  `

  const results = await request.query<{ DueDate: Date }>(query)

  if (results.recordset.length <= 0) {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  } else {
    return results.recordset[0].DueDate
  }
}
