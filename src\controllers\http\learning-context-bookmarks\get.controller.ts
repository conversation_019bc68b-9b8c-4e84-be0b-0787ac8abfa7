import get, { getContextBookmarksForUser, getUserBookmarksForContext } from '../../../services/mssql/learning-context-bookmarks/get.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-learning-context-bookmark', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { contextID, userID } = z.object({
      contextID: zodGUID.optional(),
      userID: zodGUID.optional()
    }).superRefine(({ contextID, userID }, ctx) => {
      if (!contextID && !userID) {
        ctx.addIssue({ code: 'custom', message: 'must provide either contextID or userID' })
      }
    }).parse(req.query)

    if (contextID && userID) {
      const result = await get(contextID, userID)
      log('info', 'Successfully retrieved learning context bookmark(s).', { success: true, req })
      res.json(result.fields)
    } else if (contextID) {
      const results = await getUserBookmarksForContext(contextID)
      log('info', 'Successfully retrieved learning context bookmark(s).', { success: true, req })
      res.json(results.map(bookmark => bookmark.fields))
    } else if (userID) {
      const results = await getContextBookmarksForUser(userID)
      log('info', 'Successfully retrieved learning context bookmark(s).', { success: true, req })
      res.json(results.map(bookmark => bookmark.fields))
    }
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning context bookmark(s): input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request query parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learning context bookmarks because they were not found in the database.', { success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      log('error', 'Failed to get learning context bookmark(s).', { errorMessage, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
