import mssql, { deleteRow } from '@lcs/mssql-utility'
import { LearningContextUserFavoritesTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-favorite.js'

export default async function (contextID: string, userID: string): Promise<number> {
  const pool = mssql.getPool()
  return deleteRow(pool.request(), LearningContextUserFavoritesTableName, { LearningContextID: contextID, UserID: userID })
}
