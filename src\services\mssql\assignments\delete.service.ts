import mssql, { deleteRow } from '@lcs/mssql-utility'
import { AssignmentsTableName } from '@tess-f/sql-tables/dist/lms/assignment.js'

export default async function (assignmentID: string): Promise<void> {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    // delete row in assignments table
    await deleteRow(transaction.request(), AssignmentsTableName, { ID: assignmentID })

    // update the
    await transaction.commit()
  } catch (err) {
    if (!rolledBack) await transaction.rollback()
    throw err
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}
