import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import create from './create.service.js'
import update from './update.service.js'
import getUseCount from './get-object-use-count.service.js'
import remove from './delete.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import LearningObjectContextModel from '../../../models/learning-object-context.model.js'
import { v4 as uuidv4 } from 'uuid'

let learningContext: LearningContext
let learningObject: LearningObject
let learningObjectContext: LearningObjectContextModel
let badObjectContext: LearningObjectContextModel

describe('MSSQL Learning Object Contexts Service', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test Parent Context',
      Description: `Running object-contexts on ${new Date()}`,
      ModifiedBy: AdminUserId,
      ModifiedOn: new Date(),
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Collection
    }))
    learningObject = await addRow<LearningObject>(pool.request(), new LearningObjectModel({
      Title: 'Test object contexts',
      Description: `Running object-contexts on ${new Date()}`,
      ContentID: uuidv4(),
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      LearningObjectTypeID: LearningObjectTypes.PDF
    }))
    badObjectContext = new LearningObjectContextModel({
      LearningObjectID: uuidv4(),
      LearningContextID: uuidv4(),
      ModifiedBy: AdminUserId,
      ModifiedOn: new Date(),
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    })
  })

  it('creates a learning object context', async () => {
    learningObjectContext = await create(new LearningObjectContextModel({
      LearningObjectID: learningObject.ID,
      LearningContextID: learningContext.ID,
      ModifiedBy: AdminUserId,
      ModifiedOn: new Date(),
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    }))

    expect(learningObjectContext.fields.LearningObjectID).to.equal(learningObject.ID)
    expect(learningObjectContext.fields.LearningContextID).to.equal(learningContext.ID)
    expect(learningObjectContext.fields.ModifiedBy).to.equal(learningContext.ModifiedBy)
    expect(learningObjectContext.fields.ModifiedOn).to.exist
    expect(learningObjectContext.fields.CreatedBy).to.equal(learningContext.CreatedBy)
    expect(learningObjectContext.fields.CreatedOn).to.exist
    expect(learningObjectContext.fields.OrderID).to.equal(1)
  })

  it('gets the count of how many contexts use a given learning object', async () => {
    const count = await getUseCount(learningObject.ID!)
    expect(count).to.be.gte(1)
  })

  it('updates a object context', async () => {
    learningObjectContext.fields.OrderID = 2
    const updated = await update(learningObjectContext)
    expect(updated.fields.OrderID).to.equal(2)
  })

  it('deletes the new learning object context', async () => {
    await remove(learningContext.ID!, learningObject.ID!)
  })

  it('fails updating an unknown object context', async () => {
    try {
      await update(badObjectContext)
    } catch (error: any) {
      expect(error).to.exist
    }
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { ID: learningContext.ID })
    await deleteRow<LearningObject>(pool.request(), LearningObjectsTableName, { ID: learningObject.ID })
  })
})
