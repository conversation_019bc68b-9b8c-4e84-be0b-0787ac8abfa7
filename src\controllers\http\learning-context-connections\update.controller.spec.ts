import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextConnection from '../../../models/learning-context-connection.model.js'

describe('HTTP Update controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context-connections/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextConnection({ParentContextID: uuid(), ConnectedContextID: uuid(), OrderID: 1, CreatedBy: uuid(), CreatedOn: new Date()})))
            }
        })


        const mocks = httpMocks.createMocks({
            params: {
                parentContextID: uuid(),
                connectedContextID: uuid()
            },
            body: {
                OrderID: 1,
            },
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request params are invalid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context-connections/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextConnection({ParentContextID: uuid(), ConnectedContextID: uuid(), OrderID: 1, CreatedBy: uuid(), CreatedOn: new Date()})))
            }
        })


        const mocks = httpMocks.createMocks({
            params: {
                parentContextID: false,
                connectedContextID: false
            },
            body: {
                OrderID: 1,
            },
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).include('Invalid request data')
        expect(data).include('parentContextID: Expected string, received boolean')
        expect(data).include('connectedContextID: Expected string, received boolean')
    })

    it('returns an error if the request body is invalid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context-connections/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextConnection({ParentContextID: uuid(), ConnectedContextID: uuid(), OrderID: 1, CreatedBy: uuid(), CreatedOn: new Date()})))
            }
        })


        const mocks = httpMocks.createMocks({
            params: {
                parentContextID: uuid(),
                connectedContextID: uuid()
            },
            body: {
                OrderID: false,
            },
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).include('Invalid request data')
        expect(data).include('OrderID: Expected number, received boolean')
    })

    it('returns an internal server error if the request data is rejected', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context-connections/update.service.js': {
                default: Sinon.stub().returns(Promise.reject(new LearningContextConnection({ParentContextID: uuid(), ConnectedContextID: uuid(), OrderID: 1, CreatedBy: uuid(), CreatedOn: new Date()})))
            }
        })


        const mocks = httpMocks.createMocks({
            params: {
                parentContextID: uuid(),
                connectedContextID: uuid()
            },
            body: {
                OrderID: 1,
            },
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

})