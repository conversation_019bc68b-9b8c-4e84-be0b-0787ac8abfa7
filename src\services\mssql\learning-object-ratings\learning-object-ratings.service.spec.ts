import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import { getAllObjectRatingsForUser, getObjectRatingsForUser, getAllObjectRatings } from './get.service.js'
import create from './create.service.js'
import getAverageForObject from './get-average-for-object.service.js'
import update from './update.service.js'
import remove from './delete.service.js'
import LearningObjectRatingModel from '../../../models/learning-object-rating.model.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { v4 as uuidv4 } from 'uuid'

let learningObject: LearningObject
let learningObjectRating: LearningObjectRatingModel

describe('MSSQL Learning Object Ratings', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningObject = await addRow<LearningObject>(pool.request(), new LearningObjectModel({
      Title: 'Testing object ratings',
      Description: `Testing object ratings on ${new Date()}`,
      LearningObjectTypeID: LearningObjectTypes.Audio,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      ContentID: uuidv4()
    }))
  })

  it('Should create a new learning object rating object', async () => {
    learningObjectRating = await create(new LearningObjectRatingModel({
      UserID: AdminUserId,
      Rating: 1,
      LearningObjectID: learningObject.ID
    }))
  })

  it('Should get all object rating', async () => {
    const results = await getAllObjectRatings(learningObject.ID!)
    expect(results.length).to.be.gte(0)
  })

  it('Should get all object rating', async () => {
    const results = await getObjectRatingsForUser(learningObject.ID!, AdminUserId)
    expect(results.fields.LearningObjectID).to.be.eq(learningObject.ID)
    expect(results.fields.Rating).to.be.eq(1)
    expect(results.fields.UserID).to.be.eq(AdminUserId)
  })

  it('Should get all object rating', async () => {
    const results = await getAllObjectRatingsForUser(AdminUserId)
    expect(results.length).to.be.gte(0)
  })

  it('Should get all object rating', async () => {
    const results = await getAverageForObject(learningObject.ID!)
    expect(results.count).to.be.gte(0)
  })

  it('Should get all object rating', async () => {
    learningObjectRating.fields.Rating = 2
    const results = await update(learningObjectRating)
    expect(results.fields.Rating).to.be.eq(2)
  })

  it('Should get all object rating', async () => {
    const results = await remove(learningObject.ID!)
    expect(results).to.be.eq(undefined)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningObject>(pool.request(), LearningObjectsTableName, { ID: learningObject.ID })
  })
})
