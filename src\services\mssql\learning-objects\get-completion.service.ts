import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import LearnerProgress from '../../../models/learner-progress.model.js'
import getLearningProgress from '../learner-progress/get-multiple.service.js'
import getObject from './get.service.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { getErrorMessage } from '@tess-f/backend-utils'

export default async function (objectID: string, userID: string): Promise<number> {
  const learningObject = await getObject(objectID)
  let learningProgress: LearnerProgress[] = []
  try {
    learningProgress = await getLearningProgress(userID, objectID)
  } catch (error) {
    if (getErrorMessage(error) !== dbErrors.default.NOT_FOUND_IN_DB) {
      throw error
    }
    return 0
  }

  learningProgress.sort((a, b) => {
    if (a.fields.CreatedOn === b.fields.CreatedOn) {
      return 0
    } else if (a.fields.CreatedOn! < b.fields.CreatedOn!) {
      return 1
    } else {
      return -1
    }
  })
  let completion = 0

  for (const progress of learningProgress) {
    if (progress.fields.LessonStatusID === LessonStatuses.completed ||
      progress.fields.LessonStatusID === LessonStatuses.fail ||
      progress.fields.LessonStatusID === LessonStatuses.passed) {
      completion = 100
      break
    } else if ((learningObject.fields.LearningObjectTypeID === LearningObjectTypes.Audio || learningObject.fields.LearningObjectTypeID === LearningObjectTypes.Video) &&
      progress.fields.RawScore !== null && progress.fields.RawScore !== undefined) {
      completion = progress.fields.RawScore
      break
    }
  }

  return completion
}
