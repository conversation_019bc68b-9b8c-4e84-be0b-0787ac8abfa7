import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import LearningContextBookmark from '../../../models/learning-context-user-bookmark.model.js'
import get, { getContextBookmarksForUser as getForUser, getUserBookmarksForContext as getForContext } from './get.service.js'
import create from './create.service.js'
import remove from './delete.service.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

let learningContext: LearningContext
let learningContextBookmark: LearningContextBookmark

describe('MSSQL Learning Context User Bookmarks', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test Parent Context',
      Description: `Running learning context bookmarks on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Collection
    }))
  })

  it('creates a learning context user bookmark', async () => {
    learningContextBookmark = await create(new LearningContextBookmark({
      LearningContextID: learningContext.ID,
      UserID: AdminUserId
    }))

    expect(learningContextBookmark.fields.LearningContextID).to.equal(learningContext.ID)
    expect(learningContextBookmark.fields.UserID).to.equal(AdminUserId)
  })

  it('gets a users bookmark context', async () => {
    const bookmark = await get(learningContext.ID!, AdminUserId)
    expect(bookmark.fields.UserID).to.equal(AdminUserId)
    expect(bookmark.fields.LearningContextID).to.equal(learningContext.ID)
  })

  it('gets bookmark contexts for a user', async () => {
    const bookmarks = await getForUser(AdminUserId)

    for (let i = 0; i < bookmarks.length; i++) {
      expect(bookmarks[i].fields.UserID).to.equal(AdminUserId)
    }
  })

  it('get user bookmarks for a learning context', async () => {
    const bookmarks = await getForContext(learningContext.ID!)

    for (let i = 0; i < bookmarks.length; i++) {
      expect(bookmarks[i].fields.LearningContextID).to.equal(learningContext.ID)
    }
  })

  it('should fail creating the same context/user combo', async () => {
    try {
      await create(learningContextBookmark)
      throw new Error('Created a duplicate user learning context bookmark')
    } catch (error) {
      expect(error).to.exist
    };
  })

  it('deletes the user learning context bookmark', async () => {
    await remove(learningContext.ID!, AdminUserId)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { CreatedBy: AdminUserId })
  })
})
