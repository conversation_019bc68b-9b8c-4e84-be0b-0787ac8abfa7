import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextModel from '../../../models/learning-context.model'

describe('HTTP update controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextModel({})))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: uuid()
            },
            body: {
                CourseID: uuid(),
                ContextID: uuid()
            }
            
            
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextModel({})))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: uuid()
            },
            body: {
                CourseID: false,
                ContextID: false
            }
            
            
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('CourseID')

    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context/update.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new LearningContextModel({})))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: uuid()
            },
            body: {
                CourseID: uuid(),
                ContextID: uuid()
            }
            
            
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })

   

})