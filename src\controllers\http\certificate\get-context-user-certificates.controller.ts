import logger from '@lcs/logger'
import { Request, Response } from 'express'
import searchUserCompletedContexts from '../../../services/mssql/user-completed-learning-contexts/search.service.js'
import { getUserCompletedStartedDate } from '../../../services/mssql/learning-context/get-user-completion-dates.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-learning-context-user-certificates', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    // parse incoming data
    const { offset, limit, search, groupIDs } = z.object({
      offset: zodOffset,
      limit: zodLimit,
      search: z.string().optional(),
      groupIDs: z.array(zodGUID).optional()
    }).parse(req.body)

    const { id } = z.object({ id: zodGUID }).parse(req.params)

    const { from, to } = z.object({
      from: z.coerce.date().optional(),
      to: z.coerce.date().optional()
    }).parse(req.query)

    const userCertificates = await searchUserCompletedContexts(id, offset, limit, search, groupIDs, from, to)

    const certificates = await Promise.all(userCertificates.records.map(async cert => {
      let startedOn: Date
      try {
        startedOn = await getUserCompletedStartedDate(id, cert.fields.UserID!, cert.fields.CompletedOn!)
      } catch (error) {
        if (getErrorMessage(error) !== DB_Errors.default.NOT_FOUND_IN_DB) {
          throw error
        }
        startedOn = cert.fields.CompletedOn!
      }
      const certificate: any = cert.fields
      certificate.StartedOn = startedOn
      return certificate
    }))

    log('info', 'Successfully fetched user certificates for learning context', {
      search,
      groupIDs,
      from,
      to,
      contextID: id,
      count: certificates.length,
      totalRecords: userCertificates.totalRecords,
      success: true,
      req
    })

    res.json({
      totalRecords: userCertificates.totalRecords,
      users: certificates
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get user certificates for learning context: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      const errorMessage = getErrorMessage(error)
      log('error', 'Failed to get user certificates for learning context', { errorMessage, contextID: req.params.id, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
