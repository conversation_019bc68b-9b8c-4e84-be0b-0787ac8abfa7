import mssql, { getRows } from '@lcs/mssql-utility'
import { Widget, WidgetsTableName } from '@tess-f/sql-tables/dist/lms/widget.js'
import WidgetModel from '../../../models/widget.model.js'

export default async function getWidgetService (widgetID: string): Promise<WidgetModel> {
  const pool = mssql.getPool()
  const records = await getRows<Widget>(WidgetsTableName, pool.request(), { ID: widgetID })
  return new WidgetModel(undefined, records[0])
}
