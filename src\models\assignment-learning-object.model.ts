import { Table } from '@lcs/mssql-utility'
import { AssignmentLearningObject, AssignmentLearningObjectFields, AssignmentLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-object.js'

export default class AssignmentLearningObjectModel extends Table<AssignmentLearningObject, AssignmentLearningObject> {
  public fields: AssignmentLearningObject

  constructor (fields?: AssignmentLearningObject) {
    super(AssignmentLearningObjectsTableName, [
      AssignmentLearningObjectFields.AssignmentID,
      AssignmentLearningObjectFields.LearningObjectID,
      AssignmentLearningObjectFields.OrderID,
      AssignmentLearningObjectFields.CreatedBy,
      AssignmentLearningObjectFields.CreatedOn
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: AssignmentLearningObject): void {
    this.fields = record
  }

  public exportJsonToDatabase (): AssignmentLearningObject {
    return this.fields
  }
}
