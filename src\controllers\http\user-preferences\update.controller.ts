import UserPreference, { userPreferenceSchema } from '../../../models/user-preference.model.js'
import updateService from '../../../services/mssql/user-preferences/update.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.update-user-preference', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIG-TEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-update-user-preference' label."
  try {
    const updateModel = new UserPreference(userPreferenceSchema.parse(req.body))
    const { userID } = z.object({ userID: zodGUID }).parse(req.params)
    updateModel.fields.UserID = userID
    const result = await updateService(updateModel)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-update-user-preference' label and message indicating a successful update."
    log('info', 'Successfully updated user preferences for user', { success: true, userID, req })

    res.json(result.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update user preferences due to invalid data in the request.', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to update user preferences.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
