import { Table } from '@lcs/mssql-utility'
import { WidgetKey<PERSON>, WidgetKeywordFields, WidgetKeywordsTableName } from '@tess-f/sql-tables/dist/lms/widget-keyword.js'

export default class WidgetKeywordModel extends Table<WidgetKeyword, WidgetKeyword> {
  public fields: WidgetKeyword

  constructor (fields?: WidgetKeyword) {
    super(WidgetKeywordsTableName, [
      WidgetKeywordFields.WidgetID,
      WidgetKeywordFields.Keyword
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: WidgetKeyword): void {
    this.fields = record
  }

  public exportJsonToDatabase (): WidgetKeyword {
    return this.fields
  }
}
