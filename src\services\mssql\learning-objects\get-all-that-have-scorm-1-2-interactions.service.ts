import mssql, { streamQuery } from '@lcs/mssql-utility'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObject, LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { ScormInteractionFields, ScormInteractionsTableName } from '@tess-f/sql-tables/dist/lms/scorm-1-2-interaction.js'

export default async function getAllObjectsThatHaveScormInteractions (): Promise<LearningObjectModel[]> {
  const results = await streamQuery<LearningObject>(mssql.getPool().request(), `
    SELECT *
    FROM [${LearningObjectsTableName}]
    WHERE [${LearningObjectFields.ID}] IN (
      SELECT [${LearnerProgressFields.LearningObjectID}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.ID}] IN (
        SELECT [${ScormInteractionFields.LearnerProgressID}]
        FROM [${ScormInteractionsTableName}]
      )
    )
  `)
  return results.map(record => new LearningObjectModel(undefined, record))
}
