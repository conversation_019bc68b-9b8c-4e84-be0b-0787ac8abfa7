import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import { SkillLevel } from '@tess-f/sql-tables/dist/lms/skill-level.js'
import SkillLevelModel from '../../../models/skill-level.model'

describe('HTTP create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/skill-levels/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SkillLevelModel({ID: uuid(), Name: "Test", OrderID: 1})))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                Name: "Test",
                OrderID: 1
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })


    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/skill-levels/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SkillLevelModel({ID: uuid(), Name: "Test", OrderID: 1})))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                Name: false,
                OrderID: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Name')
        expect(mocks.res._getData()).include('OrderID')
        expect(mocks.res._getData()).include('Expected string, received boolean')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/skill-levels/create.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new SkillLevelModel({ID: uuid(), Name: "Test", OrderID: 1})))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                Name: "Test",
                OrderID: 1
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

})