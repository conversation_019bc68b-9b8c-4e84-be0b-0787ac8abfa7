import mssql, { updateRow } from '@lcs/mssql-utility'
import LearningObjectObjectiveModel from '../../../models/learning-object-objective.model.js'
import { LearningObjectObjective } from '@tess-f/sql-tables/dist/lms/learning-object-objective.js'
import { Request } from 'mssql'

export default async function updateLearningObjectObjective (objectObjective: LearningObjectObjectiveModel, request?: Request): Promise<LearningObjectObjectiveModel> {
  if (request === undefined) {
    request = mssql.getPool().request()
  }

  const updated = await updateRow<LearningObjectObjective>(request, objectObjective, { LearningObjectId: objectObjective.fields.LearningObjectId, ObjectiveId: objectObjective.fields.ObjectiveId })
  objectObjective.importFromDatabase(updated[0])
  return objectObjective
}
