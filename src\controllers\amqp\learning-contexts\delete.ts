import logger from '@lcs/logger'
import { RpcMessage } from '@tess-f/shared-config/dist/types/rpc-message.js'
import del from '../../../services/mssql/learning-context/delete.service.js'
import { DeleteContextRequest } from '@tess-f/lms/dist/amqp/delete-learning-context.js'
import { RpcResponse } from '@tess-f/shared-config/dist/types/rpc-response.js'
import { getErrorMessage, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-AMQP.delete-learning-contexts')

export default async function (req: RpcMessage<DeleteContextRequest>): Promise<RpcResponse<any>> {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.data)
    await del(id)

    log('info', 'Successfully deleted learning context.', { id, success: true })

    return { success: true }
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete learning context: input validation error', { errorMessage: error.message, success: false })
      return {
        success: false,
        message: zodErrorToMessage(error, 'Invalid request data: ')
      }
    }

    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to delete learning context.', { id: req.data.id, errorMessage, success: false })

    return {
      success: false,
      message: errorMessage,
      error
    }
  }
}
