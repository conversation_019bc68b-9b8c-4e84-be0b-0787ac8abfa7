import { UserFields } from '@tess-f/sql-tables/dist/id-mgmt/user.js'

export function getBasicUserFields (): string {
  return `
    [${UserFields.Avatar}],
    [${UserFields.City}],
    [${UserFields.Company}],
    [${UserFields.Country}],
    [${UserFields.Department}],
    [${UserFields.DisplayName}],
    [${UserFields.Email}],
    [${UserFields.FirstName}],
    [${UserFields.ID}],
    [${UserFields.IsActive}],
    [${UserFields.JobTitle}],
    [${UserFields.LastLogin}],
    [${UserFields.LastName}],
    [${UserFields.MainPhone}],
    [${UserFields.ManagerID}],
    [${UserFields.MiddleInitial}],
    [${UserFields.Office}],
    [${UserFields.State}],
    [${UserFields.Username}],
    [${UserFields.ZipCode}]
  `
}
