import logger from '@lcs/logger'
import Statement from '../../../models/amqp/lrs/statement.model.js'
import { CMI5Verbs } from '@tess-f/sql-tables/dist/lrs/verb.js'
import getUserByLrsAgent from '../../../services/mssql/users/get-lrs-agent.service.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import getMultipleByContentIdService from '../../../services/mssql/learning-objects/get-multiple-by-content-id.service.js'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import createLearnerProgress from '../../../services/mssql/learner-progress/create.service.js'
import { cmi5VerbToLessonStatus } from '../../../utils/lrs-statement/verb-to-status-id.util.js'
import getScore from '../../../utils/lrs-statement/get-score-from-statement.util.js'
import gradeContextsService from '../../../services/background/grade-contexts.service.js'
import getLearningContextSessionsForUserAndExam from '../../../services/mssql/learning-context-sessions/get-enrolled-sessions-for-user-and-exam.service.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'

const log = logger.create('Controller-AMQP.process-evaluation-statement')

export default async function (statement: Statement) {
  if (statement.verb.id !== CMI5Verbs.Passed && statement.verb.id !== CMI5Verbs.Failed) {
    log('debug', 'Bypassed processing of evaluation statement: not completion statement', { success: false })
    return
  }

  try {
    // get the user
    const user = await getUserByLrsAgent(statement.actor)
    log('debug', 'Found user for statement', { userId: user.ID, success: true })
    // get the learning object
    const evaluationId = statement.object.id.substring(statement.object.id.lastIndexOf('/') + 1)
    log('debug', 'Extracted evaluation ID', { evaluationId, success: true })
    const learningObject = (await getMultipleByContentIdService(evaluationId))[0]
    log('debug', 'Found learning object', { id: learningObject.fields.ID, success: true })
    // get the users training event session that this checklist marks completion for
    // only get sessions that have no progress and the user is enrolled in
    const sessions = await getLearningContextSessionsForUserAndExam(user.ID ?? '', learningObject.fields.ID ?? '')
    log('debug', 'Found sessions for user', { count: sessions.length, success: true })

    // loop through sessions and mark browsed
    for (const session of sessions) {
      await createLearnerProgress(new LearnerProgressModel({
        UserID: user.ID,
        LearningContextSessionID: session.fields.ID,
        CreatedOn: new Date(),
        LessonStatusID: LessonStatuses.browsed
      }))
    }

    // create a new learner progress record
    const learnerProgress = await createLearnerProgress(new LearnerProgressModel({
      UserID: user.ID,
      LearningObjectID: learningObject.fields.ID,
      CreatedOn: new Date(statement.timestamp),
      LessonStatusID: cmi5VerbToLessonStatus(statement.verb.id),
      RawScore: getScore(statement)
    }))
    log('debug', 'Created learner progress record', { id: learnerProgress.fields.ID, success: true })
    // grade any learning contexts that use this learning object
    await gradeContextsService(learnerProgress, true)
  } catch (error) {
    log('error', 'Failed to process evaluation statement', { errorMessage: getErrorMessage(error), success: false })
  }
}
