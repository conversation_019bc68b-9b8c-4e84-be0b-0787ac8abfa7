import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextConnection from '../../../models/learning-context-connection.model.js'

describe('HTTP Create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context-connections/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextConnection({ParentContextID: uuid(), ConnectedContextID: uuid(), OrderID: 1, CreatedBy: uuid(), CreatedOn: new Date()})))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                ParentContextID: uuid(),
                ConnectedContextID: uuid(),
                OrderID: 1,
            },
            session: { userId: uuid() }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context-connections/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextConnection({ParentContextID: uuid(), ConnectedContextID: uuid(), OrderID: 1, CreatedBy: uuid(), CreatedOn: new Date()})))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                ParentContextID: false,
                ConnectedContextID: false,
                OrderID: false,
            },
            session: { userId: uuid() }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).to.include('Invalid request data')
        expect(data).to.include('ParentContextID: Expected string, received boolean')
        expect(data).to.include('ConnectedContextID: Expected string, received boolean')
        expect(data).to.include('OrderID: Expected number, received boolean')
    })

    it('returns an internal server error if the request data is rejected', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context-connections/create.service.js': {
                default: Sinon.stub().returns(Promise.reject(new LearningContextConnection({ParentContextID: uuid(), ConnectedContextID: uuid(), OrderID: 1, CreatedBy: uuid(), CreatedOn: new Date()})))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                ParentContextID: uuid(),
                ConnectedContextID: uuid(),
                OrderID: 1,
            },
            session: { userId: uuid() }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

})