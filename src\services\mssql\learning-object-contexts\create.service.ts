import mssql, { addRow } from '@lcs/mssql-utility'
import { Request } from 'mssql'
import LearningObjectContextModel from '../../../models/learning-object-context.model.js'
import { LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningObjectContext, LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'

export default async function (objectContext: LearningObjectContextModel): Promise<LearningObjectContextModel> {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    // this will allow the user to create an object context connection in any position
    // if it's going on the end (the default behavior) this function will not update any records
    await updateOrderIDs(transaction.request(), objectContext.fields.LearningContextID!, objectContext.fields.OrderID!)

    const record = await addRow<LearningObjectContext>(transaction.request(), objectContext)

    await transaction.commit()
    return new LearningObjectContextModel(record)
  } catch (err) {
    // rollback on failure
    if (!rolledBack) await transaction.rollback()
    throw err
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}

// update the order ID's
async function updateOrderIDs (request: Request, parentID: string, orderID: number) {
  request.input('orderID', orderID)
  request.input('parentID', parentID)

  await request.query(`
    UPDATE [${LearningContextTableName}]
    SET [${LearningContextFields.OrderID}] = [${LearningContextFields.OrderID}] + 1
    WHERE [${LearningContextFields.OrderID}] >= @orderID
    AND [${LearningContextFields.ParentContextID}] = @parentID
  `)

  await request.query(`
    UPDATE [${LearningContextConnectionsTableName}]
    SET [${LearningContextConnectionFields.OrderID}] = [${LearningContextConnectionFields.OrderID}] + 1
    WHERE [${LearningContextConnectionFields.OrderID}] >= @orderID
    AND [${LearningContextConnectionFields.ParentContextID}] = @parentID
  `)

  await request.query(`
    UPDATE [${LearningObjectContextsTableName}]
    SET [${LearningObjectContextFields.OrderID}] = [${LearningObjectContextFields.OrderID}] + 1
    WHERE [${LearningObjectContextFields.OrderID}] >= @orderID
    AND [${LearningObjectContextFields.LearningContextID}] = @parentID
  `)
}
