import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import create from './create.service.js'
import get from './get.service.js'
import getMultiple from './get-multiple.service.js'
import remove from './delete.service.js'
import SystemWidgetModel from '../../../models/system-widget.model.js'

let systemWidget: SystemWidgetModel

describe('MSSQL System Widgets Objects', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
  })

  it('create system widget', async () => {
    systemWidget = await create(new SystemWidgetModel({
      Title: 'Testing System Widgets',
      Everyone: false,
      Published: false
    }))
    expect(systemWidget.fields.Title).to.eq('Testing System Widgets')
  })

  it('can get system widget', async () => {
    const result = await get(systemWidget.fields.ID!)
    expect(result).to.not.be.eq(undefined)
  })

  it('can get all records', async () => {
    const result = await getMultiple(0, 150)
    expect(result.totalRecords).to.gte(1)
  })

  it('can delete system widget', async () => {
    await remove(systemWidget.fields.ID!)
  })
})
