import update from '../../../services/mssql/learning-objects/update.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import LearningObject, { updateLearningObjectSchema } from '../../../models/learning-object.model.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.update-learning-object', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, as an admin go to Files, click on an existing file, modify its description and save. Note the time. Check the LMS API log for the 'http-update-learning-object' label."
  try {
    const learningObject = new LearningObject(updateLearningObjectSchema.parse(req.body))
    const { ID } = z.object({ ID: zodGUID }).parse(req.body) // FIXME: this should come from the request params and not the body

    learningObject.fields.ID = ID
    learningObject.fields.ModifiedBy = req.session.userId
    learningObject.fields.ModifiedOn = new Date()

    const result = await update(learningObject)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, as an admin go to Files, click on an existing file, modify its description and save. Note the time. Check the LMS API log for the 'http-update-learning-object' label and message indicating a successful update."
    log('info', 'Successfully updated learning object', { id: result.fields.ID, success: true, req })

    res.json(result.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update learning object: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to update learning object because it was not found in the database.', { id: req.body.ID, success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else if (errorMessage === 'Bad object keyword') {
      log('warn', 'Failed to update learning object due to a bad keyword.', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send('Keyword does not exist in system. Please add keyword and try again.')
    } else {
      log('error', 'Failed to update learning object.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
