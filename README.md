﻿# Learning Management Server

Use VS Code for enhanced debugging tools
See package.json for scripts to run

## SonarQube Code Quality

[![Quality Gate Status](https://sonarqube.northgrum.com/api/project_badges/measure?project=TESS-LMS-API&metric=alert_status&token=sqb_327a35e40af638509bf2ed996a68b6732bdfc80e)](https://sonarqube.northgrum.com/dashboard?id=TESS-LMS-API)
[![Maintainability Rating](https://sonarqube.northgrum.com/api/project_badges/measure?project=TESS-LMS-API&metric=sqale_rating&token=sqb_327a35e40af638509bf2ed996a68b6732bdfc80e)](https://sonarqube.northgrum.com/dashboard?id=TESS-LMS-API)
[![Reliability Rating](https://sonarqube.northgrum.com/api/project_badges/measure?project=TESS-LMS-API&metric=reliability_rating&token=sqb_327a35e40af638509bf2ed996a68b6732bdfc80e)](https://sonarqube.northgrum.com/dashboard?id=TESS-LMS-API)
[![Security Rating](https://sonarqube.northgrum.com/api/project_badges/measure?project=TESS-LMS-API&metric=security_rating&token=sqb_327a35e40af638509bf2ed996a68b6732bdfc80e)](https://sonarqube.northgrum.com/dashboard?id=TESS-LMS-API)
[![Bugs](https://sonarqube.northgrum.com/api/project_badges/measure?project=TESS-LMS-API&metric=bugs&token=sqb_327a35e40af638509bf2ed996a68b6732bdfc80e)](https://sonarqube.northgrum.com/dashboard?id=TESS-LMS-API)
[![Code Smells](https://sonarqube.northgrum.com/api/project_badges/measure?project=TESS-LMS-API&metric=code_smells&token=sqb_327a35e40af638509bf2ed996a68b6732bdfc80e)](https://sonarqube.northgrum.com/dashboard?id=TESS-LMS-API)
[![Coverage](https://sonarqube.northgrum.com/api/project_badges/measure?project=TESS-LMS-API&metric=coverage&token=sqb_327a35e40af638509bf2ed996a68b6732bdfc80e)](https://sonarqube.northgrum.com/dashboard?id=TESS-LMS-API)
[![Duplicated Lines (%)](https://sonarqube.northgrum.com/api/project_badges/measure?project=TESS-LMS-API&metric=duplicated_lines_density&token=sqb_327a35e40af638509bf2ed996a68b6732bdfc80e)](https://sonarqube.northgrum.com/dashboard?id=TESS-LMS-API)
[![Lines of Code](https://sonarqube.northgrum.com/api/project_badges/measure?project=TESS-LMS-API&metric=ncloc&token=sqb_327a35e40af638509bf2ed996a68b6732bdfc80e)](https://sonarqube.northgrum.com/dashboard?id=TESS-LMS-API)
[![Technical Debt](https://sonarqube.northgrum.com/api/project_badges/measure?project=TESS-LMS-API&metric=sqale_index&token=sqb_327a35e40af638509bf2ed996a68b6732bdfc80e)](https://sonarqube.northgrum.com/dashboard?id=TESS-LMS-API)
[![Vulnerabilities](https://sonarqube.northgrum.com/api/project_badges/measure?project=TESS-LMS-API&metric=vulnerabilities&token=sqb_327a35e40af638509bf2ed996a68b6732bdfc80e)](https://sonarqube.northgrum.com/dashboard?id=TESS-LMS-API)

## Env Variables

| Name | Default | Use |
| ---- | ------- | --- |
| REQUIRE_ASSIGNMENT_DUE_DATE | 'false' | When true assignment due dates will be required. |
| LOG_ALL_REQUESTS | 'false' | When true will log all requests to the server |
| SERVER_PORT | 8080 | What port should the server be accessible on |
| SERVER_ADDRESS | 'localhost' | What is the address of this service |
| ROOT_PATH | '/' | What is the base path to this service |
| ROOT_API_PATH | '/api/' | Where should api calls go to, in production this is usually an empty string '' |
| LOG_LEVEL | 'debug' | |
| LOG_NAME | 'learning_management_system' | |
| LOG_CONSOLE | undefined | If 'true' will log to the console |
| LOG_ELASTICSEARCH | undefined | If 'true' will send logs to elasticsearch |
| LOG_NO_COLOR | undefined | If 'true' will turn off color for logs |
| ELASTICSEARCH_INDEX | 'learning-mgmt-system' | The elasticsearch index name |
| CC_CERTS_TO_ADMIN | false | Should certificate of completions be cc'ed to the system administrator |
| CHECK_SESSION_IP | undefined | Should the session authority check ip addresses of requests |
| MSSQL_FORCE_ENCRYPTED | undefined | If 'true' will ensure that the mssql connection is encrypted, if not an error will be thrown |
| MSSQL_DEBUG | 'false' | |
| MSSQL_REQUEST_TIMEOUT | 30000 | |
| MSSQL_CONNECTION_TIMEOUT | 30000 | |
| MSSQL_ENCRYPT | undefined | Should the connection be encrypted? NOTE: moving to yaml config |
| MSSQL_TRUSTED | undefined | Should the connection be trusted? |
| MSSQL_TRUST_SERVER_CERT | 'true' | Should the server cert be trusted? |
