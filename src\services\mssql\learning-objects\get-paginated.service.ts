import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { isSortDirectionValid, isValidString } from '@tess-f/backend-utils/validators'
import { LearningObject, LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningObjectKeywordFields, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import { LearningObjectSubTypes } from '@tess-f/sql-tables/dist/lms/learning-object-sub-type.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'

// TODO: only retrieve objects that the user has permission to see

/**
 * retrieves a paginated list of learning objects
 * @param {number} offset - the number of records to offset the query by
 * @param {number} limit - the number of records to limit the query to
 * @param {string} search - terms to search for ( title, description )
 * @param {{
 *   typeIds: number[],
 *   visibilities: number[],
 *   keywords: string[],
 *   createdByIds: string[],
 *   modifiedByIds: string[]
 * }} filters - params for filtering the query
 * @param {{
 *   column: string,
 *   direction: string
 * }} sort - sort column and direction, direction = (ASC, DESC)
 * @returns {{ totalRecords: number, objects: LearningObjectModel[] }}
 */
export default async function (
  offset: number = 0,
  limit: number = 10,
  search?: string,
  filters?: { typeIds?: number[], visibilities?: number[], keywords?: string[], createdByIds?: string[], modifiedByIds?: string[], isReferenced?: boolean },
  sort?: { column: string, direction: string }
): Promise<{ totalRecords: number, objects: LearningObjectModel[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  // build query
  let query = `SELECT [${LearningObjectsTableName}].*, TotalRecords = COUNT(*) OVER(),
    STUFF(
        (SELECT ',' + [${LearningObjectKeywordsTableName}].[${LearningObjectKeywordFields.Keyword}]
        FROM [${LearningObjectKeywordsTableName}]
        WHERE [${LearningObjectKeywordsTableName}].[${LearningObjectKeywordFields.LearningObjectID}] = [${LearningObjectsTableName}].[${LearningObjectFields.ID}]
        ORDER BY [${LearningObjectKeywordsTableName}].[${LearningObjectKeywordFields.Keyword}]
        FOR XML PATH('')
    ), 1, 1, '') Keywords,
    [References].[ContextCount]
    FROM [${LearningObjectsTableName}]
      OUTER APPLY (
        SELECT COUNT (*) AS [ContextCount]
        FROM [${LearningObjectContextsTableName}]
        WHERE [${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningObjectID}] = [${LearningObjectsTableName}].[${LearningObjectFields.ID}]
      ) [References]
    WHERE ([${LearningObjectFields.LearningObjectSubTypeID}] <> ${LearningObjectSubTypes.CMI5AU} OR [${LearningObjectFields.LearningObjectSubTypeID}] IS NULL)`

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [LearningObjectFields.Title, LearningObjectFields.Description], 'any')}) `
  }

  if (filters) {
    if (filters.typeIds && filters.typeIds.length > 0) {
      query += 'AND ('
      filters.typeIds.forEach((id, index, original) => {
        request.input(`typeID_${index}`, id)
        query += `[${LearningObjectFields.LearningObjectTypeID}] = @typeID_${index} ${index + 1 < original.length ? 'OR ' : ''}`
      })
      query += ') '
    }

    if (filters.visibilities && filters.visibilities.length) {
      query += 'AND ('
      filters.visibilities.forEach((vis, index, original) => {
        request.input(`vis_${index}`, vis)
        query += `[${LearningObjectFields.VisibilityID}] = @vis_${index} ${index + 1 < original.length ? 'OR ' : ''}`
      })
      query += ') '
    }

    if (filters.keywords && filters.keywords.length > 0) {
      query += `AND ( [${LearningObjectFields.ID}] IN ( SELECT [${LearningObjectKeywordFields.LearningObjectID}] FROM [${LearningObjectKeywordsTableName}] WHERE `
      filters.keywords.forEach((key, index, original) => {
        request.input(`keyword_${index}`, key)
        query += `[${LearningObjectKeywordFields.Keyword}] = @keyword_${index} ${index + 1 < original.length ? 'OR ' : ''}`
      })
      query += '))'
    }

    if (filters.modifiedByIds && filters.modifiedByIds.length > 0) {
      query += 'AND ('
      filters.modifiedByIds.forEach((id, index, original) => {
        request.input(`modifiedById_${index}`, id)
        query += `[${LearningObjectFields.ModifiedBy}] = @modifiedById_${index} ${index + 1 < original.length ? 'OR ' : ''}`
      })
      query += ') '
    }

    if (filters.createdByIds && filters.createdByIds.length > 0) {
      query += 'AND ('
      filters.createdByIds.forEach((id, index, original) => {
        request.input(`createdById_${index}`, id)
        query += `[${LearningObjectFields.CreatedBy}] = @createdById_${index} ${index + 1 < original.length ? 'OR ' : ''}`
      })
      query += ') '
    }

    if (filters.isReferenced) {
      query += 'AND [References].[ContextCount] > 0 '
    } else if (filters.isReferenced === false) {
      query += 'AND [References].[ContextCount] <= 0 '
    }
  }

  // add offset and limit params to the query
  request.input('Offset', offset)
  request.input('Limitby', limit)

  // order, limit, and offset query

  if (sort && isSortDirectionValid(sort.direction) &&
    isValidString(sort.column, [LearningObjectFields.Title, LearningObjectFields.LearningObjectTypeID, LearningObjectFields.MinutesToComplete, LearningObjectFields.CreatedOn, LearningObjectFields.ModifiedOn, LearningObjectFields.VisibilityID, 'ContextCount'])) {
    query += `ORDER BY [${sort.column}] ${sort.direction} `
  } else {
    query += `ORDER BY [${LearningObjectFields.CreatedOn}] DESC, [${LearningObjectFields.ModifiedOn}] DESC, [${LearningObjectFields.Title}] `
  }

  query += `
        OFFSET @Offset ROWS
        FETCH FIRST @Limitby ROWS ONLY
    `

  const result = await request.query<LearningObject & {TotalRecords: number, Keywords: string, ContextCount: number}>(query)

  // return results
  return {
    totalRecords: result.recordset.length > 0 ? result.recordset[0].TotalRecords : 0,
    objects: result.recordset.map(record => {
      const model = new LearningObjectModel(undefined, record)
      model.fields.ContextCount = record.ContextCount
      return model
    })
  }
}
