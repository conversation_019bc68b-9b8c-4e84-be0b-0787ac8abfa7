import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { GhostUserId, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { UserGroupFields, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { SessionEnrollmentFields, SessionEnrollmentsTableName } from '@tess-f/sql-tables/dist/lms/session-enrollment.js'

export default async function (contextID: string, offset = 0, limit = 10, search?: string, groupIDs?: string[], from?: Date, to?: Date): Promise<{ totalRecords: number, enrollments: { UserID: string, CreatedOn: Date, SessionID: string, SessionTitle: string }[] }> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('contextID', contextID)
  request.input('offset', offset)
  request.input('limit', limit)

  let query = `
    SELECT [${SessionEnrollmentFields.UserID}], [${SessionEnrollmentsTableName}].[${SessionEnrollmentFields.CreatedOn}], [${LearningContextSessionsTableName}].[${LearningContextSessionFields.SessionID}] AS [SessionTitle],
      [${SessionEnrollmentsTableName}].[${SessionEnrollmentFields.SessionID}], [${UserTableName}].[${UserFields.FirstName}], [${UserTableName}].[${UserFields.LastName}], [TotalRecords] = COUNT(*) OVER()
    FROM [${SessionEnrollmentsTableName}]
    JOIN [${LearningContextSessionsTableName}] ON [${LearningContextSessionsTableName}].[${LearningContextSessionFields.ID}] = [${SessionEnrollmentsTableName}].[${SessionEnrollmentFields.SessionID}]
      AND [${LearningContextSessionsTableName}].[${LearningContextSessionFields.LearningContextID}] = @contextID
    JOIN [${UserTableName}] ON [${UserTableName}].[${UserFields.ID}] = [${SessionEnrollmentsTableName}].[${SessionEnrollmentFields.UserID}]
    WHERE 1 = 1
  `

  if (search) {
    query += `
      AND [${SessionEnrollmentFields.UserID}] IN (
        SELECT [${UserFields.ID}]
        FROM [${UserTableName}]
        WHERE ${parseSearchTerms(request, search, [UserFields.FirstName, UserFields.LastName], 'any')}
        AND [${UserFields.ID}] != '${GhostUserId}'
      )
    `
  }

  if (groupIDs && groupIDs.length > 0) {
    const conds = groupIDs.map((id, index) => {
      request.input(`group_${index}`, id)
      return `@group_${index}`
    })
    query += `
      AND [${SessionEnrollmentFields.UserID}] IN (
        SELECT [${UserGroupFields.UserID}]
        FROM [${UserGroupTableName}]
        WHERE [${UserGroupFields.GroupID}] IN (
          ${conds.join(', ')}
        )
      )
    `
  }

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
    query += `AND [${SessionEnrollmentsTableName}].[${SessionEnrollmentFields.CreatedOn}] BETWEEN @from AND @to`
  }

  query += `
    ORDER BY [${UserFields.FirstName}], [${UserFields.LastName}]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<{ UserID: string, CreatedOn: Date, SessionID: string, SessionTitle: string, TotalRecords: number}>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    enrollments: results.recordset
  }
}
