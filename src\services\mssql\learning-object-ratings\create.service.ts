import mssql, { addRow } from '@lcs/mssql-utility'
import getService from '../learning-objects/get.service.js'
import createService from '../activity-stream/create.service.js'
import ActivityStreamModel from '../../../models/activity-stream.model.js'
import LearningObjectRatingModel from '../../../models/learning-object-rating.model.js'
import { LearningObjectRating } from '@tess-f/sql-tables/dist/lms/learning-object-rating.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'

export default async function (learningObjectRating: LearningObjectRatingModel): Promise<LearningObjectRatingModel> {
  const pool = mssql.getPool()
  // create an activity stream record
  const lo = await getService(learningObjectRating.fields.LearningObjectID!)
  const activity = new ActivityStreamModel({
    UserID: learningObjectRating.fields.UserID,
    LinkText: lo.fields.Title,
    LinkID: learningObjectRating.fields.LearningObjectID,
    ActivityID: Activities.RatedLearningObject,
    Rating: learningObjectRating.fields.Rating,
    CreatedOn: new Date()
  })
  await createService(activity)
  const record = await addRow<LearningObjectRating>(pool.request(), learningObjectRating)

  return new LearningObjectRatingModel(record)
}
