import logger from '@lcs/logger'
import { User } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import AssignmentsModel from '../../../models/assignment.model.js'
import { Group } from '@tess-f/sql-tables/dist/id-mgmt/group.js'
import getAssignmentUsers from './get-multiple.service.js'
import getMultipleUsersByIds from '../users/get-multiple-by-ids.service.js'
import getMultipleGroupsByIds from '../groups/get-multiple-by-ids.service.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'

const log = logger.create('Service.MSSQL:get-users-and-groups-for-assignment')

export default async function getUsersAndGroupsForAssignment (assignment: AssignmentsModel): Promise<{ users: User[], groups: Group[] }> {
  if (assignment.fields.Everyone || assignment.fields.DirectReports) {
    return { users: [], groups: [] }
  }

  try {
    const assignmentUsers = await getAssignmentUsers(assignment.fields.ID!)
    const userIds: string[] = []
    const groupIds: string[] = []
    assignmentUsers.forEach(aUser => {
      // if it's assigned to a user add the id to the list
      if (aUser.fields.UserID) {
        userIds.push(aUser.fields.UserID)
      }
      // if it's assigned to a group add the id to the list
      if (aUser.fields.GroupID) {
        groupIds.push(aUser.fields.GroupID)
      }
    })

    return {
      users: userIds.length > 0 ? await getMultipleUsersByIds(userIds) : [],
      groups: groupIds.length > 0 ? await getMultipleGroupsByIds(groupIds) : []
    }
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Unable to retrieve users for assignment, because none where in the database', { assignmentId: assignment.fields.ID, success: false })
      // if there are no users in the database for this assignment lets still retrieve it but send back that no one is assigned
      return { users: [], groups: [] }
    } else {
      log('error', 'Unable to retrieve users for assignment', { assignmentId: assignment.fields.ID, success: false, errorMessage })
      throw error
    }
  }
}
