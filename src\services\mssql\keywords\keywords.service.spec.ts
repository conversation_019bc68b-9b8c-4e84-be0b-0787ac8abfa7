import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import KeywordModel from '../../../models/keyword.model.js'
import create from './create.service.js'
import getMultiple from './get-multiple.service.js'
import getTopTenAssignmentKeywordUseCount from './get-top-ten-assignment-keyword-use-count.service.js'
import getViewCount from './get-view-count.service.js'
import remove from './delete.service.js'
import { LearningObjectKeyword, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import { LearningContextKeyword, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { ConnectionPool } from 'mssql'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import LearningObjectKeywordModel from '../../../models/learning-object-keyword.model.js'
import LearningContextKeywordModel from '../../../models/learning-context-keyword.model.js'
import { v4 as uuidv4 } from 'uuid'

let pool: ConnectionPool
let keyword: KeywordModel
let learningObject: LearningObject
let learningContext: LearningContext

describe('Service [MSSQL]: Keyword', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    pool = mssql.getPool()
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test Keyword Child',
      Description: 'Running learning context for keyword unit test',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Course
    }))
    learningObject = await addRow<LearningObject>(pool.request(), new LearningObjectModel({
      Title: 'Test Object',
      Description: 'Running learning object for keyword unit test',
      LearningObjectTypeID: LearningObjectTypes.ExternalURL,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      ContentID: uuidv4()
    }))
  })

  it('creates a keyword', async () => {
    keyword = await create(new KeywordModel({
      Name: 'Testing keyword'
    }))
    await addRow<LearningObjectKeyword>(pool.request(), new LearningObjectKeywordModel({
      LearningObjectID: learningObject.ID,
      Keyword: keyword.fields.Name
    }))
    await addRow<LearningContextKeyword>(pool.request(), new LearningContextKeywordModel({
      LearningContextID: learningContext.ID,
      Keyword: keyword.fields.Name
    }))
    expect(keyword.fields.Name).to.eq('Testing keyword')
  })

  it('gets multiple keywords', async () => {
    const keywords = await getMultiple()
    expect(keywords.length).to.be.gte(0)
  })

  it('gets multiple keywords', async () => {
    const d = new Date()
    d.setDate(d.getDate() - 5)
    const keywords = await getTopTenAssignmentKeywordUseCount(d, new Date())
    expect(keywords.length).to.be.gte(0)
  })

  it('gets multiple keywords', async () => {
    const d = new Date()
    d.setDate(d.getDate() - 5)
    const keywords = await getViewCount(d, new Date(), AdminUserId)
    expect(keywords.length).to.be.gte(0)
  })

  it('deletes a keyword', async () => {
    await remove(keyword.fields.Name!)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningContextKeyword>(pool.request(), LearningContextKeywordsTableName, { LearningContextID: learningContext.ID })
    await deleteRow<LearningObjectKeyword>(pool.request(), LearningObjectKeywordsTableName, { LearningObjectID: learningObject.ID })
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { CreatedBy: AdminUserId })
    await deleteRow<LearningObject>(pool.request(), LearningObjectsTableName, { CreatedBy: AdminUserId })
  })
})
