import logger from '@lcs/logger'
import getLearningMetadata from '../../../services/mssql/my-learning/get-metadata.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import getPaginatedTeamService from '../../../services/mssql/users/get-paginated-team.service.js'
import { User } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR } = httpStatus
import { LearningMetadata } from '@tess-f/lms/dist/common/learning-metadata.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodDates, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-my-team-learning-metadata', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { search, offset, limit, from, to } = z.object({
      search: z.string().optional(),
      offset: zodOffset,
      limit: zodLimit,
      from: zodDates.optional(),
      to: zodDates.optional()
    }).parse(req.query)

    // get users that report to me (fetch from cache)
    const myTeam = await getPaginatedTeamService(req.session.userId, offset, limit, search)

    const userLearningMetadata: Array<LearningMetadata> =
      await Promise.all(myTeam.users.map(async (user: User) => {
        try {
          const metadata = await getLearningMetadata(user.ID!, from, to)
          metadata.UserID = user.ID
          return metadata
        } catch (error) {
          if (error instanceof Error && error.message !== dbErrors.default.NOT_FOUND_IN_DB) {
            throw error
          } else {
            return {
              Completed: 0,
              InProgress: 0,
              Assigned: 0,
              Overdue: 0,
              UserID: user.ID!,
              Certificates: 0,
              AverageScore: 0
            }
          }
        }
      }))

    log('info', 'Successfully retrieved user learning metadata', { count: userLearningMetadata.length, success: true, req })

    res.json({
      UserLearningMetadata: userLearningMetadata,
      TotalRecords: myTeam.totalRecords
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get my teams learning metadata: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request query data: '))
    } else {
      log('error', 'Failed to get my teams learning metadata', { error, req, success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
