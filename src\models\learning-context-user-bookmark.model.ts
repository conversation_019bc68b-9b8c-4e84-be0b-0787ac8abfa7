import { Table } from '@lcs/mssql-utility'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { LearningContextUserBookmark, LearningContextUserBookmarkFields, LearningContextUserBookmarksTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-bookmark.js'
import { z } from 'zod'

export const createLearningContextBookmarkSchema = z.object({
  [LearningContextUserBookmarkFields.UserID]: zodGUID,
  [LearningContextUserBookmarkFields.LearningContextID]: zodGUID
})

export default class LearningContextUserBookmarkModel extends Table<LearningContextUserBookmark, LearningContextUserBookmark> {
  public fields: LearningContextUserBookmark

  constructor (fields?: LearningContextUserBookmark) {
    super(LearningContextUserBookmarksTableName, [
      LearningContextUserBookmarkFields.UserID,
      LearningContextUserBookmarkFields.LearningContextID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearningContextUserBookmark): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningContextUserBookmark {
    return this.fields
  }
}
