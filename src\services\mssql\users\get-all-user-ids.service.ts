import mssql, { streamQuery } from '@lcs/mssql-utility'
import { GhostUserId, User, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'

export default async function (): Promise<string[]> {
  const pool = mssql.getPool()
  const records = await streamQuery<User>(pool.request(), `
    SELECT [${UserFields.ID}]
    FROM [${UserTableName}]
    WHERE [${UserFields.IsActive}] = 1
    AND [${UserFields.ID}] != '${GhostUserId}'
  `)
  return records.map(record => record.ID!)
}
