import get from '../../../services/mssql/learning-context/get.service.js'
import logger from '@lcs/logger'
import { RpcMessage } from '@tess-f/shared-config/dist/types/rpc-message.js'
import { LearningContextJson } from '@tess-f/lms/dist/common/learning-context.js'
import { GetLearningContextRequest } from '@tess-f/lms/dist/amqp/get-learning-context.js'
import { RpcResponse } from '@tess-f/shared-config/dist/types/rpc-response.js'
import { getErrorMessage, zodErrorToMessage } from '@tess-f/backend-utils'
import { z, ZodError } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-AMQP.get-learning-context')

export default async function (req: RpcMessage<GetLearningContextRequest>): Promise<RpcResponse<LearningContextJson>> {
  try {
    const { ID } = z.object({ ID: zodGUID }).parse(req.data)
    const res = await get(ID, undefined)

    log('info', 'Successfully retrieved learning context', { success: true })

    return {
      success: true,
      data: res.fields
    }
  } catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Failed to get learning context: input validation error', { errorMessage: error.message, success: false })
      return {
        success: false,
        message: zodErrorToMessage(error, 'Invalid request data: ')
      }
    }

    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to get learning context.', { errorMessage, success: false })

    return {
      success: false,
      message: errorMessage
    }
  }
}
