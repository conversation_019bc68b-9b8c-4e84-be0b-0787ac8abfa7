import deleteService from './delete.service.js'
import createService from './create.service.js'
import GroupClaim from '../../../../models/group-claim.model.js'

/**
 * "Update" here means dropping all claims associated with the given group ID
 * and then creating new claims.
 *
 * @param { String } groupID
 * @param { Array[GroupClaim] } groupClaims
 */
export default async function (groupID: string, groupClaims: GroupClaim[]) {
  await deleteService(groupID)
  await createService(groupClaims)
}
