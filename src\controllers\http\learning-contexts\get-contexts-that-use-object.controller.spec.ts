import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextModel from '../../../models/learning-context.model'
import exp from 'constants'


describe('HTTP get-contexts-that-use-object controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-contexts-that-use-object.controller', {
            '../../../services/mssql/learning-context/get-contexts-that-use-object.service.js': {
                default: Sinon.stub().returns(Promise.resolve([LearningContextModel]))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-contexts-that-use-object.controller', {
            '../../../services/mssql/learning-context/get-contexts-that-use-object.service.js': {
                default: Sinon.stub().returns(Promise.resolve([LearningContextModel]))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).to.include('Invalid request parameter data')
        expect(mocks.res._getData()).to.include('Expected string, received boolean')
        expect(mocks.res._getData()).to.include('id')

    })

    it('returns an internal server error if the request data is invalid', async () => {
        const controller = await esmock('./get-contexts-that-use-object.controller', {
            '../../../services/mssql/learning-context/get-contexts-that-use-object.service.js': {
                default: Sinon.stub().rejects(Promise.resolve([LearningContextModel]))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })




})