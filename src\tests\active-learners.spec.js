const chai = require('chai');
const expect = chai.expect;
const tester = require('../api/utils/test-agent.utils');
const settings = require('../api/config/settings');

const user_id = '20433cea-75fe-43b8-ab39-a0426c65d42b'.toUpperCase();
const context_id = '37CC7650-0079-4691-B24A-FADA19A0D0AD';

describe("Active Learners", () => {

    let isolated = false;
    
    before( done => {
        if(tester.agent == null) {
            isolated = true;
            tester.create()
            .then( agent => {
                done();
            })
            .catch( err => {
                console.error(err);
                done();
            });
        } else {
            done();
        }
    });

    // GET /api/active-learners
    it("can get active learners", done => {
        tester.agent
        .get(settings.server.root + 'active-learners')
        .end((err, res) => {
            expect(res.status).to.equal(200);
            expect(res.body).to.exist;
            console.log(res.body);
            done();
        });
    });

    // GET /api/active-learner-live/:user_id/:context_id
    it("can get active learner live data", done => {
        tester.agent
        .get(settings.server.root + 'active-learner-live/' + user_id + '/' + context_id)
        .end((err, res) => {
            expect(res.status).to.equal(200);
            expect(res.body).to.exist;
            done();
        });
    });

    // GET /api/active-learner-history/:user_id/:context_id
    it("can get active learner history data", done => {
        tester.agent
        .get(settings.server.root + 'active-learner-history/' + user_id + '/' + context_id)
        .end((err, res) => {
            expect(res.status).to.equal(200);
            expect(res.body).to.exist;
            done();
        });
    });

    // GET /api/active-learner-data/:user_id/:context_id
    it("can get active learner data", done => {
        tester.agent
        .get(settings.server.root + 'active-learner-history/' + user_id + '/' + context_id)
        .end((err, res) => {
            expect(res.status).to.equal(200);
            expect(res.body).to.exist;
            done();
        });
    });

    after((done)=>{
        if(isolated) {
            tester.close();
        } 
        done();
    });

})