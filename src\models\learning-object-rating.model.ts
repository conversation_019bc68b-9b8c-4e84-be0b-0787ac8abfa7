import { Table } from '@lcs/mssql-utility'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { LearningObjectRating, LearningObjectRatingFields, LearningObjectRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-rating.js'
import { z } from 'zod'

export const learningObjectRatingSchema = z.object({
  [LearningObjectRatingFields.UserID]: zodGUID,
  [LearningObjectRatingFields.LearningObjectID]: zodGUID,
  [LearningObjectRatingFields.Rating]: z.number().int().min(1).max(5)
})

export default class LearningObjectRatingModel extends Table<LearningObjectRating, LearningObjectRating> {
  public fields: LearningObjectRating

  constructor (fields?: LearningObjectRating) {
    super(LearningObjectRatingsTableName, [
      LearningObjectRatingFields.UserID,
      LearningObjectRatingFields.Rating,
      LearningObjectRatingFields.LearningObjectID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearningObjectRating): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningObjectRating {
    return this.fields
  }
}
