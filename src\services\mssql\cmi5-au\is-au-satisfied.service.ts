import mssql from '@lcs/mssql-utility'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'

export default async function isAuSatisfied (auId: string, userId: string): Promise<boolean> {
  /**
   * An AU is satisfied if there is a learner progress record for the au
   * And the record has completed date
   * And the record has a status greater than or equal to passed
   */
  const request = mssql.getPool().request()
  request.input('auId', auId)
  request.input('userId', userId)
  const results = await request.query(`
    SELECT *
    FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @userId
    AND [${LearnerProgressFields.LearningObjectID}] = @auId
    AND [${LearnerProgressFields.CompletedDate}] IS NOT NULL
    AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.passed}
  `)

  return results.recordset.length > 0
}
