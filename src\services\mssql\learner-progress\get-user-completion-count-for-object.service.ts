import mssql from '@lcs/mssql-utility'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'

export default async function (userID: string, learningObjectID: string, from?: Date, to?: Date): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('userID', userID)
  request.input('objectID', learningObjectID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const results = await request.query<{ Completions: number }>(`
    SELECT COUNT(*) AS Completions
    FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @userID
    AND [${LearnerProgressFields.LearningObjectID}] = @objectID
    AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.fail}
    ${from && to ? `AND [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to` : ''}
  `)
  return results.recordset[0].Completions
}
