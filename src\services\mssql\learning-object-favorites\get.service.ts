import mssql, { getRows } from '@lcs/mssql-utility'
import { LearningObjectUserFavorite, LearningObjectUserFavoritesTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-favorite.js'
import LearningObjectFavoritesModel from '../../../models/learning-object-user-favorite.model.js'

export default async function getUserFavoriteForObject (objectID: string, userID: string): Promise<LearningObjectFavoritesModel> {
  const pool = mssql.getPool()
  const records = await getRows<LearningObjectUserFavorite>(LearningObjectUserFavoritesTableName, pool.request(), { LearningObjectID: objectID, UserID: userID })
  return new LearningObjectFavoritesModel(records[0])
}

export async function getUserFavoritesForObject (objectID: string): Promise<LearningObjectFavoritesModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningObjectUserFavorite>(LearningObjectUserFavoritesTableName, pool.request(), { LearningObjectID: objectID })
  return records.map(record => new LearningObjectFavoritesModel(record))
}

export async function getObjectFavoritesForUser (userID: string): Promise<LearningObjectFavoritesModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningObjectUserFavorite>(LearningObjectUserFavoritesTableName, pool.request(), { UserID: userID })
  return records.map(record => new LearningObjectFavoritesModel(record))
}
