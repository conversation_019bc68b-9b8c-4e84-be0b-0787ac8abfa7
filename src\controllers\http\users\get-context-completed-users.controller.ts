import logger from '@lcs/logger'
import { Request, Response } from 'express'
import { getCompletedUserIDs } from '../../../services/mssql/learning-context/get-completion-user-ids.service.js'
import { getUserCompletedStartedDate } from '../../../services/mssql/learning-context/get-user-completion-dates.service.js'
import { getMostRecentCompletion } from '../../../services/mssql/user-completed-learning-contexts/get-for-user.service.js'
import getNumberOfCompletions from '../../../services/mssql/user-completed-learning-contexts/get-user-completion-count-for-context.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodDates, zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-completed-context-users', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { offset, limit, search, groupIDs } = z.object({
      offset: zodOffset,
      limit: zodLimit,
      search: z.string().optional(),
      groupIDs: z.array(zodGUID).optional()
    }).parse(req.body)

    const { from, to } = z.object({
      from: zodDates.optional(),
      to: zodDates.optional()
    }).parse(req.query)

    const { id } = z.object({ id: zodGUID }).parse(req.params)

    const userIds = await getCompletedUserIDs(offset, limit, id, from, to, search, groupIDs)

    const users = await Promise.all(userIds.ids.map(async userId => {
      const completionRecord = await getMostRecentCompletion(userId, id, to)
      let startedOn: Date
      try {
        startedOn = await getUserCompletedStartedDate(id, userId, completionRecord.fields.CompletedOn!)
      } catch (error) {
        if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
          throw error
        } else {
          startedOn = completionRecord.fields.CompletedOn!
        }
      }
      const completions = await getNumberOfCompletions(userId, id, from, to)

      return {
        UserID: userId,
        CompletedOn: completionRecord.fields.CompletedOn,
        LessonStatusID: completionRecord.fields.LessonStatusID,
        RawScore: completionRecord.fields.RawScore,
        GradeTypeID: completionRecord.fields.GradeTypeID,
        StartedOn: startedOn,
        Completions: completions
      }
    }))

    log('info', 'Successfully fetched completed learning context users', {
      search,
      groupIDs,
      from,
      to,
      contextID: id,
      count: users.length,
      totalRecords: userIds.totalRecords,
      success: true,
      req
    })

    res.json({
      totalRecords: userIds.totalRecords,
      users
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get completed users for learning context: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to get completed users for learning context', { contextID: req.params.id, success: false, error, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
