import { Request<PERSON><PERSON><PERSON>, Router } from 'express'
import getLearningMetadataController from './get-learning-metadata.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.get('/learning-metadata/:id', checkClaims([Claims.VIEW_ADMIN_OVERVIEW, Claims.VIEW_MY_LEARNING]), getLearningMetadataController as RequestHandler)

export default router
