import mssql from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { UserAssignedMultiSessionCoursesFields, UserAssignedMultiSessionCoursesTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-multi-session-course.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service.Update-User-Assigned-Multi-Session-Course-Status')

export default async function (UserID: string, LearningContextID: string, StatusID: number, SessionID: string, ProgressID: string): Promise<number> {
  try {
    const pool = mssql.getPool()
    const query = `
      UPDATE [${UserAssignedMultiSessionCoursesTableName}]
      SET [${UserAssignedMultiSessionCoursesFields.LessonStatusID}] = @statusID,
      [${UserAssignedMultiSessionCoursesFields.ContextSessionID}] = @sessionID,
      [${UserAssignedMultiSessionCoursesFields.LearnerProgressID}] = @progressID,
      [${UserAssignedMultiSessionCoursesFields.Completed}] = ${StatusID >= LessonStatuses.passed ? 1 : 0}
      WHERE [${UserAssignedMultiSessionCoursesFields.UserID}] = @userID
      AND [${UserAssignedMultiSessionCoursesFields.LearningContextID}] = @contextID
      AND [${UserAssignedMultiSessionCoursesFields.LessonStatusID}] < @statusID
    `

    const request = pool.request()
    request.input('statusID', StatusID)
    request.input('userID', UserID)
    request.input('sessionID', SessionID)
    request.input('progressID', ProgressID)
    request.input('contextID', LearningContextID)

    const res = await request.query(query)
    log('info', `Successfully updated ${res.rowsAffected[0]} records`, { userId: UserID, contextId: LearningContextID, sessionId: SessionID })
    return res.rowsAffected[0]
  } catch (error) {
    log('error', 'Unexpected error', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}
