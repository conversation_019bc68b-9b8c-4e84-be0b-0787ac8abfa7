import mssql from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import LearningContext from '../../../models/learning-context.model.js'
import { getLearnerProgressForILT, getLearnerProgressForObject } from './utils.js'
import getLearningObjectsService from '../../mssql/learning-objects/get-multiple.service.js'
import { getContextPassFail } from './grade-context-pass-fail.service.js'
import { getContextCompletion } from './grade-context-completion.service.js'
import { getContextExamScore } from './grade-exam.service.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { GradeTypes } from '@tess-f/sql-tables/dist/lms/grade-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import { UserCompletedLearningContext, UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const passIDs = [LessonStatuses.completed, LessonStatuses.passed]
const log = logger.create('Services-Background.grade-context-percentage')

export default async function gradeContextPercentage (context: LearningContext, userID: string, minProgressAge: Date | undefined, maxProgressAge: Date): Promise<{ LessonStatusID: number, RawScore: number, CompletionDate: Date | null }> {
  const pool = mssql.getPool()

  if (context.fields.ContextTypeID === LearningContextTypes.Section) {
    // let's get the content for this context
    const learningObjects = await getLearningObjectsService(context.fields.ID)
    if (!context.fields.Contexts) {
      context.fields.Contexts = []
    }

    if (learningObjects.length <= 0 && context.fields.Contexts.length <= 0) {
      return {
        LessonStatusID: LessonStatuses.passed,
        RawScore: 100,
        CompletionDate: null
      } // no content the user has passed
    }

    let overallScore = 0
    let completedDate: Date | undefined
    let numberItemsAttempted = 0

    for (const obj of learningObjects) {
      let objectScore = 0
      let attempted = false
      let objectCompletedDate: Date | undefined

      const progress = await getLearnerProgressForObject(pool.request(), obj.fields.ID!, userID, minProgressAge, maxProgressAge)
      if (progress.length <= 0) {
        // the user has not attempted this, the context is not complete
        continue
      }
      progress.forEach(p => {
        if (p.fields.RawScore && !isNaN(p.fields.RawScore) && objectScore < p.fields.RawScore) {
          objectScore = p.fields.RawScore
          attempted = true
          // check the completion date
          if (!objectCompletedDate || objectCompletedDate > (p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!)) {
            objectCompletedDate = p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!
          }
        } else if (passIDs.includes(p.fields.LessonStatusID!)) {
          objectScore = 100
          attempted = true
          // check the completion date
          if (!objectCompletedDate || objectCompletedDate > (p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!)) {
            objectCompletedDate = p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!
          }
        } else if (p.fields.LessonStatusID! > LessonStatuses.browsed) {
          attempted = true
          // check the completion date
          if (!objectCompletedDate || objectCompletedDate > (p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!)) {
            objectCompletedDate = p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!
          }
        }
      })

      if (attempted) {
        numberItemsAttempted++
      }
      // set the object score into the overall
      overallScore += objectScore
      if (objectCompletedDate && (!completedDate || objectCompletedDate > completedDate)) {
        completedDate = objectCompletedDate
      }
    }

    // now we need to go through the child content
    for (const lc of context.fields.Contexts) {
      // check the grade type and get the completion
      let grade!: { LessonStatusID: number, RawScore: number, CompletionDate: Date | null }
      const child = new LearningContext(lc)
      if (!child.fields.GradeTypeID) {
        grade = await getContextCompletion(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.CompleteIncomplete || child.fields.GradeTypeID === GradeTypes.PassFail) {
        grade = await getContextPassFail(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.Percentage) {
        grade = await getContextPercentage(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.Exam) {
        grade = await getContextExamScore(child, userID, maxProgressAge)
      }

      if (grade.LessonStatusID > LessonStatuses.browsed) {
        numberItemsAttempted++
      }
      // set the score
      overallScore += grade.RawScore
      if (grade.CompletionDate && (!completedDate || grade.CompletionDate > completedDate)) {
        completedDate = grade.CompletionDate
      }
    }

    // set the final score
    const finalScore = overallScore / (learningObjects.length + context.fields.Contexts.length)

    // we have looked through all the content, it's time to check the completion and
    // if the user has attempted all the content
    if (numberItemsAttempted === (learningObjects.length + context.fields.Contexts.length)) {
      // we have completed the context, lets get the status
      let status
      if (finalScore >= context.fields.MinScore! && finalScore <= context.fields.MaxScore!) {
        status = LessonStatuses.passed
      } else {
        status = LessonStatuses.fail
      }
      return {
        LessonStatusID: status,
        RawScore: finalScore,
        CompletionDate: completedDate ?? new Date()
      }
    } else {
      return {
        LessonStatusID: numberItemsAttempted > 0 ? LessonStatuses.browsed : LessonStatuses.notAttempted,
        RawScore: finalScore,
        CompletionDate: null
      }
    }
  } else if (context.fields.ContextTypeID === LearningContextTypes.Course && context.fields.CourseTypeID === CourseTypes.InstructorLed) {
    // this is an ILT course
    const progress = await getLearnerProgressForILT(pool.request(), context.fields.ID!, userID, minProgressAge, maxProgressAge)
    if (progress.length <= 0) {
      // the user has not attempted this
      return {
        LessonStatusID: LessonStatuses.notAttempted,
        RawScore: 0,
        CompletionDate: null
      }
    }

    let score = 0
    let completionDate: Date | null = null
    progress.forEach(p => {
      if (p.fields.RawScore && !isNaN(p.fields.RawScore) && score < p.fields.RawScore) {
        score = p.fields.RawScore
        // check the completion date
        if (!completionDate || completionDate > (p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!)) {
          completionDate = p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!
        }
      } else if (passIDs.includes(p.fields.LessonStatusID!)) {
        score = 100
        // check the completion date
        if (!completionDate || completionDate > (p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!)) {
          completionDate = p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!
        }
      } else if (p.fields.LessonStatusID! > LessonStatuses.notAttempted) {
        // check the completion date
        if (!completionDate || completionDate > (p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!)) {
          completionDate = p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!
        }
      }
    })

    let status
    if (score === 0) {
      status = LessonStatuses.browsed
    } else if (score >= context.fields.MinScore! && score <= context.fields.MaxScore!) {
      status = LessonStatuses.passed
    } else {
      status = LessonStatuses.fail
    }

    return {
      LessonStatusID: status,
      RawScore: score,
      CompletionDate: completionDate
    }
  } else {
    // nothing should hit this point unless the data schema changes
    return {
      LessonStatusID: -1,
      RawScore: 0,
      CompletionDate: null
    }
  }
}

export async function getContextPercentage (context: LearningContext, userID: string, minProgressAge: Date | undefined, maxProgressAge: Date): Promise<{ LessonStatusID: number, RawScore: number, CompletionDate: Date | null }> {
  try {
    // find the latest score
    const request = mssql.getPool().request()
    request.input('contextID', context.fields.ID)
    request.input('userID', userID)
    request.input('to', maxProgressAge)

    const res = await request.query<UserCompletedLearningContext>(`
      SELECT TOP(1) *
      FROM [${UserCompletedLearningContextsTableName}]
      WHERE [${UserCompletedLearningContextFields.LearningContextID}] = @contextID
      AND [${UserCompletedLearningContextFields.UserID}] = @userID
      AND [${UserCompletedLearningContextFields.CompletedOn}] <= @to
      ORDER BY [${UserCompletedLearningContextFields.RawScore}] DESC, [${UserCompletedLearningContextFields.CompletedOn}] DESC
    `)

    if (res.recordset.length > 0) {
      return {
        LessonStatusID: res.recordset[0].LessonStatusID!,
        RawScore: res.recordset[0].RawScore!,
        CompletionDate: res.recordset[0].CompletedOn!
      }
    } else {
      return gradeContextPercentage(context, userID, minProgressAge, maxProgressAge)
    }
  } catch (error) {
    log('error', 'Unknown database error', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}
