import mssql from '@lcs/mssql-utility'
import { UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'

export default async function getTeamsAverageScore (managerId: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('managerId', managerId)

  const result = await request.query<{ AverageScore: number }>(`
    SELECT [AverageScore] = COALESCE(AVG([${LearnerProgressFields.RawScore}]), 0)
    FROM (
      SELECT [${LearnerProgressFields.RawScore}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.UserID}] IN (
        SELECT [${UserFields.ID}]
        FROM [${UserTableName}]
        WHERE [${UserFields.ManagerID}] = @managerId
      )
      AND [${LearnerProgressFields.RawScore}] IS NOT NULL
      AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.fail}

      UNION ALL

      SELECT [${UserCompletedLearningContextFields.RawScore}]
      FROM [${UserCompletedLearningContextsTableName}]
      WHERE [${UserCompletedLearningContextFields.UserID}] IN (
        SELECT [${UserFields.ID}]
        FROM [${UserTableName}]
        WHERE [${UserFields.ManagerID}] = @managerId
      )
      AND [${UserCompletedLearningContextFields.RawScore}] IS NOT NULL
      AND [${UserCompletedLearningContextFields.LessonStatusID}] >= ${LessonStatuses.fail}
    ) AS [CombinedScores]
  `)

  return result.recordset[0].AverageScore
}
