import mssql from '@lcs/mssql-utility'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { LearningObject, LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { Request } from 'mssql'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { getElectiveContexts } from './utils.service.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { FlatLearningContextTreeFields, FlatLearningContextTreeViewName } from '@tess-f/sql-tables/dist/lms/flat-learning-context-view.js'

// Gets the top one for whatever the search field is
export default async function (contextID: string): Promise<number> {
  const pool = mssql.getPool()
  // get the duration in minutes of all the required content
  let duration = await getRequiredContentDuration(pool.request(), contextID)
  // get the duration of elective/optional content
  const electiveContexts = await getElectiveContexts(pool.request(), contextID)
  // calculate the duration of the electives
  const electiveDurations = await Promise.all(electiveContexts.map(async context => {
    return await getDurationOfElective(pool.request(), context)
  }))
  // add the durations of the electives to the required content
  duration += electiveDurations.reduce((minutesToComplete, duration) => minutesToComplete + duration, 0)

  return duration
}

async function getRequiredContentDuration (request: Request, contextId: string): Promise<number> {
  request.input('contextId', contextId)

  const query = `
    WITH Child_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}], [${LearningContextFields.VisibilityID}], [${LearningContextFields.ContextTypeID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] = @contextId
      
      UNION ALL
      
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}],
             [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ContextTypeID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Child_Nodes] ON [Child_Nodes].[${FlatLearningContextTreeFields.ID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}] AND [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}] != ${Visibilities.Hidden}
    )
    SELECT SUM([${LearningObjectFields.MinutesToComplete}]) AS TotalMinutes
    FROM [${LearningObjectContextsTableName}]
    JOIN [${LearningObjectsTableName}] ON [${LearningObjectsTableName}].[${LearningObjectFields.ID}] = [${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningObjectID}]
    WHERE [${LearningObjectContextFields.LearningContextID}] IN (
      SELECT [${LearningContextFields.ID}]
      FROM [Child_Nodes]
      WHERE [${LearningContextFields.ContextTypeID}] != ${LearningContextTypes.ElectiveOptional}
    )
  `

  const res = await request.query<{TotalMinutes: number}>(query)

  return res.recordset[0].TotalMinutes
}

async function getDurationOfElective (request: Request, elective: LearningContextModel): Promise<number> {
  request.input('contextId', elective.fields.ID)
  request.input('requiredCount', elective.fields.RequiredContentCount)

  const response = await request.query<LearningObject>(`
    SELECT TOP(COALESCE(@requiredCount, 0)) COALESCE([${LearningObjectFields.MinutesToComplete}], 0) AS ${LearningObjectFields.MinutesToComplete}
    FROM [${LearningObjectContextsTableName}]
    JOIN [${LearningObjectsTableName}] ON [${LearningObjectsTableName}].[${LearningObjectFields.ID}] = [${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningObjectID}]
    WHERE [${LearningObjectContextFields.LearningContextID}] = @contextId
    ORDER BY [${LearningObjectFields.MinutesToComplete}] DESC
  `)

  return response.recordset.reduce((minutesToComplete, learningObject) => {
    return minutesToComplete + learningObject.MinutesToComplete!
  }, 0)
}
