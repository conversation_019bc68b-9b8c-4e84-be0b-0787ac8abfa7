import logger from '@lcs/logger'
import LearningObjectModel from '../../../models/learning-object.model.js'
import Sinon from 'sinon'
import { expect } from 'chai'
import { v4 as uuid } from 'uuid'
import esmock from 'esmock'

describe('Controller.AMQP: get learning object', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns the requested learning object', async () => {
    const controller = await esmock('./get.js', {
      '../../../services/mssql/learning-objects/get.service.js': {
        default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({
          ID: uuid()
        })))
      }
    })
    const response = await controller({ command: 'test', data: { ID: uuid() } })
    expect(response.success).to.be.true
    expect(response.data).to.exist
    expect(response.data?.ID).to.exist
  })

  it('returns unsuccessful when the service encounters an error', async () => {
    const controller = await esmock('./get.js', {
      '../../../services/mssql/learning-objects/get.service.js': {
        default: Sinon.stub().returns(Promise.reject(new Error('Service Error')))
      }
    })
    const response = await controller({ command: 'test', data: { ID: uuid() } })
    expect(response.success).to.be.false
    expect(response.message).to.equal('Service Error')
  })

  it('returns unsuccessful when the request ID is missing', async () => {
    const controller = await esmock('./get.js')
    const response = await controller({ command: 'test', data: { } })
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('ID: Required')
  })

  it('returns unsuccessful when the request ID is not a valid uuid', async () => {
    const controller = await esmock('./get.js')
    const response = await controller({ command: 'test', data: { ID: 'test' } })
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('ID: Invalid')
  })

  it('returns unsuccessful when the request ID is not a string', async () => {
    const controller = await esmock('./get.js')
    const response = await controller({ command: 'test', data: { ID: 123 } })
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('ID: Expected string, received number')
  })
})
