import mssql, { addRow } from '@lcs/mssql-utility'
import { UserWidgetOrderOverride, UserWidgetOrderOverrideFields, UserWidgetOrderOverridesTableName } from '@tess-f/sql-tables/dist/lms/user-widget-order-override.js'
import UserWidgetOrderOverrideModel from '../../../models/user-widget-order-override.model.js'

export default async function createUserWidgetOrderOverrideService (overrides: UserWidgetOrderOverrideModel[]): Promise<UserWidgetOrderOverrideModel[]> {
  if (overrides.length <= 0) {
    return []
  }
  const pool = mssql.getPool()
  // remove the current order overrides
  const deleteRequest = pool.request()
  deleteRequest.input('userID', overrides[0].fields.UserID)
  await deleteRequest.query(`DELETE FROM [${UserWidgetOrderOverridesTableName}] WHERE [${UserWidgetOrderOverrideFields.UserID}] = @userID`)

  const records: UserWidgetOrderOverride[] = []
  for (const override of overrides) {
    records.push(await addRow<UserWidgetOrderOverride>(pool.request(), override))
  }
  return records.map(record => new UserWidgetOrderOverrideModel(record))
}
