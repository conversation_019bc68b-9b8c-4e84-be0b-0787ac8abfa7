import mssql, { updateRow } from '@lcs/mssql-utility'
import { Location } from '@tess-f/sql-tables/dist/lms/location.js'
import LocationModel from '../../../models/location.model.js'

export default async function (model: LocationModel): Promise<LocationModel> {
  const pool = mssql.getPool()
  const updated = await updateRow<Location>(pool.request(), model, { ID: model.fields.ID })
  return new LocationModel(updated[0])
}
