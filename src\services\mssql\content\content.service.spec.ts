import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'

import getContentCountForContext from './get-content-count-for-context.service.js'
import getWidgetSpecifiedContent from './get-widget-specified-content.service.js'
import search from './search.service.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import WidgetModel from '../../../models/widget.model.js'
import { Widget, WidgetsTableName } from '@tess-f/sql-tables/dist/lms/widget.js'
import { WidgetLearningContext } from '@tess-f/sql-tables/dist/lms/widget-learning-context.js'
import WidgetLearningContextModel from '../../../models/widget-learning-context.model.js'
import { WidgetLearningObject } from '@tess-f/sql-tables/dist/lms/widget-learning-object.js'
import WidgetLearningObjectModel from '../../../models/widget-learning-object.model.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { v4 as uuidv4 } from 'uuid'

let learningContext: LearningContext
let widget: Widget

describe('MSSQL Content Service', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test Content Service',
      Description: 'Running learning context for content unit test',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Collection
    }))
    const learningObject = await addRow<LearningObject>(pool.request(), new LearningObjectModel({
      Title: 'Test Content Service',
      Description: 'Running learning object for content unit test',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      LearningObjectTypeID: LearningObjectTypes.Audio,
      ContentID: uuidv4()
    }))
    widget = await addRow<Widget>(pool.request(), new WidgetModel({
      Title: 'Testing content service',
      Carousel: false,
      Filter: false,
      IsAdmin: false,
      CreatedBy: AdminUserId,
      OrderID: 1,
      TypeID: 1
    }))
    await addRow<WidgetLearningContext>(pool.request(), new WidgetLearningContextModel({
      WidgetID: widget.ID,
      LearningContextID: learningContext.ID,
      OrderID: 1
    }))
    await addRow<WidgetLearningObject>(pool.request(), new WidgetLearningObjectModel({
      WidgetID: widget.ID,
      LearningObjectID: learningObject.ID,
      OrderID: 1
    }))
  })

  it('gets a count of all content that is a part of a context', async () => {
    const result = await getContentCountForContext(learningContext.ID!)
    expect(result).to.be.gte(0)
  })

  it('gets a count of all content that is a part of a context', async () => {
    const result = await getWidgetSpecifiedContent(widget.ID!)
    expect(result.length).to.be.gte(0)
  })

  it('searches content', async () => {
    const results = await search(0, 10, '1')
    expect(results.totalRecords).to.be.gte(0)
    expect(results.elements.length).to.be.gte(0)
    expect(results.elements.length).to.be.lte(10)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { CreatedBy: AdminUserId })
    await deleteRow<LearningObject>(pool.request(), LearningObjectsTableName, { CreatedBy: AdminUserId })
    await deleteRow<Widget>(pool.request(), WidgetsTableName, { ID: widget.ID })
  })
})
