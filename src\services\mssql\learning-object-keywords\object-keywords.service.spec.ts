import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import get from './get.service.js'
import { Keyword, KeywordsTableName } from '@tess-f/sql-tables/dist/lms/keyword.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import LearningObjectKeywordModel from '../../../models/learning-object-keyword.model.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import KeywordModel from '../../../models/keyword.model.js'
import { v4 as uuidv4 } from 'uuid'

let learningObject: LearningObject
let learningObjectKeyword: LearningObjectKeywordModel
let keyword: Keyword

// FIXME: need to update the set up and tear down for these tests
xdescribe('MSSQL Learning Object Keywords', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningObject = await addRow<LearningObject>(pool.request(), new LearningObjectModel({
      Title: 'Test learning object keywords',
      Description: `Running object-keywords on ${new Date()}`,
      ContentID: uuidv4(),
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      LearningObjectTypeID: LearningObjectTypes.PDF
    }))
    keyword = await addRow<Keyword>(pool.request(), new KeywordModel({
      Name: 'OBJECT-KEYWORD-TEST'
    }))
  })

  it('gets a learning object keyword', async () => {
    const _objectKeywords = await get(learningObjectKeyword.fields.LearningObjectID!)

    for (let i = 0; i < _objectKeywords.length; i++) {
      expect(_objectKeywords[i].fields.LearningObjectID).to.equal(learningObjectKeyword.fields.LearningObjectID)
    }
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningObject>(pool.request(), LearningObjectsTableName, { ID: learningObject.ID })
    await deleteRow<Keyword>(pool.request(), KeywordsTableName, { Name: keyword.Name })
  })
})
