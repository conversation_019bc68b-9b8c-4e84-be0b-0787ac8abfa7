import { Table } from '@lcs/mssql-utility'
import { SkillLevel, SkillLevel<PERSON>ields, SkillLevelsTableName } from '@tess-f/sql-tables/dist/lms/skill-level.js'
import { z } from 'zod'

export const skillLevelSchema = z.object({
  [SkillLevelFields.Name]: z.string().max(50),
  [SkillLevelFields.OrderID]: z.number().int().optional().default(1)
})

export default class SkillLevelModel extends Table<SkillLevel, SkillLevel> {
  public fields: SkillLevel

  constructor (fields?: SkillLevel) {
    super(SkillLevelsTableName, [
      SkillLevelFields.Name,
      SkillLevelFields.OrderID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: SkillLevel): void {
    this.fields = record
  }

  public exportJsonToDatabase (): SkillLevel {
    return this.fields
  }
}
