import mssql, { deleteRow } from '@lcs/mssql-utility'
import { SCORM_1_2_Interaction, ScormInteractionsTableName } from '@tess-f/sql-tables/dist/lms/scorm-1-2-interaction.js'

export default async function deleteInteractionsForUserSession (userId: string, learnerProgressID: string): Promise<number> {
  return await deleteRow<SCORM_1_2_Interaction>(mssql.getPool().request(), ScormInteractionsTableName, { UserID: userId, LearnerProgressID: learnerProgressID })
}
