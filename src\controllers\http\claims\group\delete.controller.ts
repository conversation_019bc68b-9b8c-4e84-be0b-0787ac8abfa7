import logger from '@lcs/logger'
import del from '../../../../services/mssql/claims/group/delete.service.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.delete-group-claim', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    // Ensure body is an array of claims
    const { claims } = z.object({
      claims: z.array(z.string()).min(1)
    }).parse({ claims: req.body })

    const groupID = (z.object({ id: zodGUID }).parse(req.params)).id
    let countDeleted = 0
    for (const claim of claims) {
      await del(groupID, claim)
      countDeleted++
    }

    log('info', `Successfully deleted ${countDeleted} claims for group ID: ${groupID}`, { success: true, req })

    res.sendStatus(httpStatus.NO_CONTENT)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete group claims: input validation error', { success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to delete group claims.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
