import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getTopLevelService from '../../../services/mssql/catalog/get-top-level.service.js'
import httpStatus from 'http-status'
import { httpLogTransformer, getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Controller-HTTP.Get-Top-Level-Catalog', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // This controller will fetch the top level learning contexts (that are not ILT courses)
  // A top level context has no parent

  try {
    const result = await getTopLevelService()
    log('info', 'Successfully retrieved top level of the catalog', { count: result.length, success: true, req })
    res.json(result.map(context => context.fields))
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to retrieve top level of the catalog. No records found', { success: false, req })
      res.json([])
    } else {
      log('error', 'Failed to retrieve top level of the catalog', { errorMessage, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
