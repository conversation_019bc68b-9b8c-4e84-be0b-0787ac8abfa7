import * as getConfig from '../system/get-system-config.service.js'
import * as createStatementService from './create-statement.service.js'
import service from './send-au-satisfied-statement.service.js'
import Sinon from 'sinon'
import { expect } from 'chai'
import { v4 as uuid } from 'uuid'

xdescribe('Service.AMQP: send au satisfied statement', () => {
  afterEach(() => Sinon.restore())
  beforeEach(() => {
    Sinon.stub(getConfig, 'getSystemConfig').returns(Promise.resolve({
      Id: 1,
      DisplayName: 'test',
      Description: '',
      AdminEmails: '',
      SupportEmail: '<EMAIL>',
      Theme: '',
      Logo: 'test',
      Domain: 'http://example.com',
      DarkLogo: '',
      SessionTimeout: 30000,
      Apps: [],
      Services: [],
      Version: 'v1.0.0'
    }))
  })

  it('sends the statement', done => {
    Sinon.stub(createStatementService, 'default').returns(Promise.resolve(uuid()))
    service(uuid(), { objectType: 'Agent', name: 'test', mbox: '<EMAIL>' }, uuid(), uuid(), uuid())
      .then(() => {
        done()
      }, (error) => {
        done(error)
      })
  })

  it('throws an error when the service fails', done => {
    Sinon.stub(createStatementService, 'default').returns(Promise.reject(new Error('Service Failure')))
    service(uuid(), { objectType: 'Agent', name: 'test', mbox: '<EMAIL>' }, uuid(), uuid(), uuid())
      .then(() => {
        done(new Error('Should not pass'))
      }, (error) => {
        expect(error).to.exist
        expect(error instanceof Error).to.be.true
        expect(error.message).to.equal('Service Failure')
        done()
      })
  })
})
