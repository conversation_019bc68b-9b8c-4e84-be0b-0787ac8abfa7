import logger from '@lcs/logger'
import httpStatus from 'http-status'
import Sinon from 'sinon'
import { expect } from 'chai'
import httpMock from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: get context user certificates', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('should return bad request when the id parameter is missing', async () => {
    const controller = await esmock('./get-context-user-certificates.controller.js')
    const mocks = httpMock.createMocks()
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Required')
  })

  it('should return bad request when the id parameter is not a valid uuid', async () => {
    const controller = await esmock('./get-context-user-certificates.controller.js')
    const mocks = httpMock.createMocks({ params: { id: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Invalid')
  })

  it('should return bad request when the id parameter is a number', async () => {
    const controller = await esmock('./get-context-user-certificates.controller.js')
    const mocks = httpMock.createMocks({ params: { id: 123 } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Expected string, received number')
  })

  it('should return bad request when the from query param is not a valid date', async () => {
    const controller = await esmock('./get-context-user-certificates.controller.js')
    const mocks = httpMock.createMocks({ params: { id: uuid() }, query: { from: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('from: Invalid date')
  })

  it('should return bad request when the to query param is not a valid date', async () => {
    const controller = await esmock('./get-context-user-certificates.controller.js')
    const mocks = httpMock.createMocks({ params: { id: uuid() }, query: { to: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('to: Invalid date')
  })

  it('should return bad request when the group id filter is not an array of guids', async () => {
    const controller = await esmock('./get-context-user-certificates.controller.js')
    const mocks = httpMock.createMocks({ params: { id: uuid() }, body: { groupIDs: ['test'] } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('groupIDs.0: Invalid')
  })

  it('should return ok when request data is valid', async () => {
    const controller = await esmock('./get-context-user-certificates.controller.js', {
      '../../../services/mssql/user-completed-learning-contexts/search.service.js': {
        default: Sinon.stub().resolves({ totalRecords: 0, records: [] })
      }
    })
    const mocks = httpMock.createMocks({ params: { id: uuid() }, body: { groupIDs: [uuid()] } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const data = JSON.parse(mocks.res._getData())
    expect(data).to.not.be.undefined
    expect(data.totalRecords).to.not.be.undefined
    expect(data.users).to.not.be.undefined
  })
})
