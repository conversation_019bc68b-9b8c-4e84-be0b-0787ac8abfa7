import { Table } from '@lcs/mssql-utility'
import { UserAssignedLearningObject, UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'

export default class UserAssignedLearningObjectModel extends Table<UserAssignedLearningObject, UserAssignedLearningObject> {
  public fields: UserAssignedLearningObject

  constructor (fields?: UserAssignedLearningObject) {
    super(UserAssignedLearningObjectsTableName, [
      UserAssignedLearningObjectFields.AssignmentID,
      UserAssignedLearningObjectFields.UserID,
      UserAssignedLearningObjectFields.LearningObjectID,
      UserAssignedLearningObjectFields.LessonStatusID,
      UserAssignedLearningObjectFields.CreatedOn,
      UserAssignedLearningObjectFields.Deleted
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: UserAssignedLearningObject): void {
    this.fields = record
  }

  public exportJsonToDatabase (): UserAssignedLearningObject {
    return this.fields
  }
}
