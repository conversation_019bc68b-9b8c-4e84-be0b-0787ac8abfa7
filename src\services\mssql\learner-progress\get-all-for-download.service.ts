import mssql, { streamQuery } from '@lcs/mssql-utility'
import { UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearnerProgress, LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningContextSessionInstructorFields, LearningContextSessionInstructorsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session-instructor.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningObjectRatingFields, LearningObjectRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-rating.js'
import { LearningObjectTypeFields, LearningObjectTypeTableName } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'

type LearnerProgressDownload = LearnerProgress & {
  Rating: number | null
  Title: string
  TimeToComplete: number | null
  FileType: string
  Instructors: string
}

export default async function (): Promise<LearnerProgressDownload[]> {
  const pool = mssql.getPool()

  return await streamQuery<LearnerProgressDownload>(pool.request(), `
    SELECT [${LearnerProgressTableName}].*, ( 
      SELECT AVG([${LearningObjectRatingFields.Rating}]) AS Rating
      FROM [${LearningObjectRatingsTableName}]
      WHERE [${LearningObjectRatingFields.LearningObjectID}] = [${LearnerProgressTableName}].[${LearnerProgressFields.LearningObjectID}]
    ) AS Rating, COALESCE((
      SELECT [${LearningObjectFields.Title}]
      FROM [${LearningObjectsTableName}]
      WHERE [${LearningObjectFields.ID}] = [${LearnerProgressTableName}].[${LearnerProgressFields.LearningObjectID}]
    ), (
      SELECT TOP(1) [${LearningContextFields.Title}]
      FROM [${LearningContextSessionsTableName}]
        JOIN [${LearningContextTableName}] ON [${LearningContextTableName}].[${LearningContextFields.ID}] = [${LearningContextSessionsTableName}].[${LearningContextSessionFields.LearningContextID}]
      WHERE [${LearningContextSessionsTableName}].[${LearningContextSessionFields.ID}] = [${LearnerProgressTableName}].[${LearnerProgressFields.LearningContextSessionID}]
    )) AS Title, (
      SELECT [${LearningObjectFields.MinutesToComplete}]
      FROM [${LearningObjectsTableName}]
      WHERE [${LearningObjectFields.ID}] = [${LearnerProgressTableName}].[${LearnerProgressFields.LearningObjectID}]
    ) AS TimeToComplete, COALESCE((
      SELECT [${LearningObjectTypeFields.Name}]
      FROM [${LearningObjectTypeTableName}]
      WHERE [${LearningObjectTypeFields.ID}] = (
        SELECT [${LearningObjectFields.LearningObjectTypeID}]
        FROM [${LearningObjectsTableName}]
        WHERE [${LearningObjectFields.ID}] = [${LearnerProgressTableName}].[${LearnerProgressFields.LearningObjectID}]
      )
    ), (
      SELECT [${LearningContextTableName}].[${LearningContextFields.Label}]
      FROM [${LearningContextSessionsTableName}]
        JOIN [${LearningContextTableName}] ON [${LearningContextTableName}].[${LearningContextFields.ID}] = [${LearningContextSessionsTableName}].[${LearningContextSessionFields.LearningContextID}]
      WHERE [${LearningContextSessionsTableName}].[${LearningContextSessionFields.ID}] = [${LearnerProgressTableName}].[${LearnerProgressFields.LearningContextSessionID}]
    )) AS FileType, COALESCE((
      SELECT STUFF((
        SELECT ', ' + [${UserFields.FirstName}] + ' ' + [${UserFields.LastName}]
        FROM [${UserTableName}]
          JOIN [${LearningContextSessionInstructorsTableName}] ON [${LearningContextSessionInstructorsTableName}].[${LearningContextSessionInstructorFields.UserID}] = [${UserTableName}].[${UserFields.ID}]
            AND [${LearningContextSessionInstructorsTableName}].[${LearningContextSessionInstructorFields.SessionID}] = [${LearnerProgressTableName}].[${LearnerProgressFields.LearningContextSessionID}]
        FOR XML PATH('')
      ), 1, 2, '')
    ), 'N/A') AS Instructors
    FROM [${LearnerProgressTableName}]
  `)
}
