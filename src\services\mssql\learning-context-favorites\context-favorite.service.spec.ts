import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import LearningContextFavoriteModel from '../../../models/learning-context-user-favorite.model.js'
import get, { getFortiesForContext, getContextFavoritesForUser } from './get.service.js'
import create from './create.service.js'
import remove from './delete.service.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

let learningContext: LearningContext
let learningContextFavorite: LearningContextFavoriteModel

describe('MSSQL Learning Context User Favorites', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test learning context user favorites',
      Description: 'Running learning context user favorites',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Collection
    }))
  })

  it('creates a learning context user favorite', async () => {
    learningContextFavorite = await create(new LearningContextFavoriteModel({
      LearningContextID: learningContext.ID,
      UserID: AdminUserId
    }))

    expect(learningContextFavorite.fields.LearningContextID).to.equal(learningContext.ID)
    expect(learningContextFavorite.fields.UserID).to.equal(learningContext.CreatedBy)
  })

  it('gets a users favorite context', async () => {
    const _favorite = await get(learningContext.ID!, AdminUserId)
    expect(_favorite.fields.UserID).to.equal(AdminUserId)
    expect(_favorite.fields.LearningContextID).to.equal(learningContext.ID)
  })

  it('gets favorite contexts for a user', async () => {
    const _favorites = await getContextFavoritesForUser(AdminUserId)

    for (let i = 0; i < _favorites.length; i++) {
      expect(_favorites[i].fields.UserID).to.equal(AdminUserId)
    }
  })

  it('get user favorites for a learning context', async () => {
    const _favorites = await getFortiesForContext(learningContext.ID!)

    for (let i = 0; i < _favorites.length; i++) {
      expect(_favorites[i].fields.LearningContextID).to.equal(learningContext.ID)
    }
  })

  it('should fail creating the same context/user combo', async () => {
    try {
      await create(new LearningContextFavoriteModel({
        LearningContextID: learningContext.ID,
        UserID: AdminUserId
      }))
      throw new Error('Created a duplicate user learning context favorite')
    } catch (error) {};
  })

  it('deletes the user learning context favorite', async () => {
    await remove(learningContext.ID!, AdminUserId)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { ID: learningContext.ID })
  })
})
