import { Request, Response } from 'express'
import getObjectModifiers from '../../../services/mssql/learning-objects/get-modifier-ids.service.js'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
import { httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('Controller-HTTP.get-learning-object-modifiers', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const modifiers = await getObjectModifiers()

    if (modifiers.length > 0) {
      log('info', 'Successfully retrieved learning object modifiers.', { count: modifiers.length, success: true, req })
      res.json(modifiers)
    } else {
      log('info', 'No learning object modifiers found', { success: true, req })
      res.json([])
    }
  } catch (error) {
    log('error', 'Failed to get learning object modifiers.', { error, success: false, req })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
