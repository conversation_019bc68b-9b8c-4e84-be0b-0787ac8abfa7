import mssql, { addRow } from '@lcs/mssql-utility'
import { FlatLearningContextTreeFields, FlatLearningContextTreeViewName } from '@tess-f/sql-tables/dist/lms/flat-learning-context-view.js'
import { LearnerProgress } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningObject, LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import getUserById from '../users/get-by-id.service.js'
import { getSystemConfig } from '../../amqp/system/get-system-config.service.js'
import gradeContextsService from '../../background/grade-contexts.service.js'
import sendAuSatisfiedStatement from '../../amqp/lrs/send-au-satisfied-statement.service.js'

export default async function markNaAusSatisfied (courseId: string, userId: string, registrationId: string): Promise<void> {
  // First we need to get all the AU (learning objects) for this course that have a move on criteria of not applicable
  const request = mssql.getPool().request()
  request.input('rootContext', courseId)
  const aus = await request.query<LearningObject>(`
    WITH structure AS (
      SELECT [${FlatLearningContextTreeFields.ID}]
      FROM [${FlatLearningContextTreeViewName}]
      WHERE [${FlatLearningContextTreeFields.ID}] = @rootContext

      UNION ALL

      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}]
      FROM [${FlatLearningContextTreeViewName}]
      INNER JOIN [structure] ON [structure].[${FlatLearningContextTreeFields.ID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}] AND [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}] != ${Visibilities.Hidden}
    )
    SELECT *
    FROM [${LearningObjectsTableName}]
    WHERE [${LearningObjectsTableName}].[${LearningObjectFields.ID}] IN (
      SELECT [OC].[${LearningObjectContextFields.LearningObjectID}]
      FROM [structure]
      LEFT JOIN [${LearningObjectContextsTableName}] AS OC ON [OC].[${LearningObjectContextFields.LearningContextID}] = [structure].[${FlatLearningContextTreeFields.ID}]
      WHERE [OC].[${LearningObjectContextFields.LearningObjectID}] IS NOT NULL
    )
    AND [${LearningObjectFields.MoveOn}] = 'NotApplicable'
  `)

  // if we have no au's to process just return
  if (aus.recordset.length <= 0) {
    return
  }

  const currentUser = await getUserById(userId)
  const config = await getSystemConfig()

  // Otherwise we need to mark these as satisfied
  for (const learningObject of aus.recordset) {
    // first we need to create a learner progress record for the LMS to track this completion
    const progress = await addRow<LearnerProgress>(mssql.getPool().request(), new LearnerProgressModel({
      LessonStatusID: LessonStatuses.completed,
      UserID: currentUser.ID,
      LearningObjectID: learningObject.ID,
      StartedDate: new Date(),
      CompletedDate: new Date()
    }))

    // create a satisfied statement for the au (learning object)
    await sendAuSatisfiedStatement(learningObject.ID!, { objectType: 'Agent', name: `${currentUser.FirstName} ${currentUser.LastName}`, account: { name: currentUser.Username!, homePage: config.Domain } }, registrationId, learningObject.IRI!, progress.ID!)

    // we need to grade the course/blocks associated with this au and mark satisfied if all the au's are now complete
    await gradeContextsService(new LearnerProgressModel(progress), true)
  }
}
