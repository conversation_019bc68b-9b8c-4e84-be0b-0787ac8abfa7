import mssql, { getRows } from '@lcs/mssql-utility'
import { LearningContextSessionInstructor, LearningContextSessionInstructorsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session-instructor.js'
import LearningContextSessionInstructorModel from '../../../models/learning-context-session-instructor.model.js'

export default async function (sessionID?: string, userID?: string): Promise<LearningContextSessionInstructorModel[]> {
  const pool = mssql.getPool()
  let records: LearningContextSessionInstructor[]
  if (sessionID) {
    records = await getRows<LearningContextSessionInstructor>(LearningContextSessionInstructorsTableName, pool.request(), { SessionID: sessionID })
  } else if (userID) {
    records = await getRows<LearningContextSessionInstructor>(LearningContextSessionInstructorsTableName, pool.request(), { UserID: userID })
  } else {
    records = await getRows<LearningContextSessionInstructor>(LearningContextSessionInstructorsTableName, pool.request())
  }
  return records.map(record => new LearningContextSessionInstructorModel(record))
}
