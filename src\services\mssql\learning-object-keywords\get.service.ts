import mssql, { getRows } from '@lcs/mssql-utility'
import { LearningObjectKeyword, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import LearningObjectKeywordModel from '../../../models/learning-object-keyword.model.js'

export default async function (objectID: string): Promise<LearningObjectKeywordModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningObjectKeyword>(LearningObjectKeywordsTableName, pool.request(), { LearningObjectID: objectID })
  return records.map(record => new LearningObjectKeywordModel(record))
}
