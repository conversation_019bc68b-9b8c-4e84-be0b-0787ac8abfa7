import rabbitmq from '@lcs/rabbitmq'
import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import { type RpcResponse } from '@tess-f/shared-config/dist/types/rpc-response.js'
import { type RpcMessage } from '@tess-f/shared-config/dist/types/rpc-message.js'
import { Notification } from '@tess-f/sql-tables/dist/notifications/notifications.js'

const log = logger.create('Service-Notification.send-notification-message')
type NotificationJson = Notification & {UserIDs?: string[], GroupIDs?: string[], Active?: boolean}

export default async function sendNotificationService (notification: NotificationJson): Promise<boolean> {
  try {
    const message: RpcMessage<NotificationJson> = {
      command: 'create',
      data: notification
    }

    const response: RpcResponse<NotificationJson> = await rabbitmq.executeRPC(
      settings.amqp.service_queues.notification,
      message,
      settings.amqp.rpc_timeout
    )
    log('info', `Successfully created notification`, { id: response.data?.ID, success: true })
    return response.success
  } catch (error) {
    log('error', 'Failed to create notification', { success: false, errorMessage: getErrorMessage(error) })
    throw error
  }
}
