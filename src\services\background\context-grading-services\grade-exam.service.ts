/* eslint-disable camelcase */
import mssql, { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import LearningContext from '../../../models/learning-context.model.js'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import { getLearnerProgressForObject } from './utils.js'
import getLearningObjectsService from '../../mssql/learning-objects/get-multiple.service.js'
import { getContextCompletion } from './grade-context-completion.service.js'
import { getContextPassFail } from './grade-context-pass-fail.service.js'
import { getContextPercentage } from './grade-percentage.service.js'
import { Request } from 'mssql'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { GradeTypes } from '@tess-f/sql-tables/dist/lms/grade-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import { UserCompletedLearningContext, UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import { LearnerProgress, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const passIDs = [LessonStatuses.passed, LessonStatuses.completed]
const log = logger.create('Service-Background.grade-context-exam')

export default async function gradeContextExam (context: LearningContext, userID: string, minProgressAge: Date | undefined, maxProgressAge: Date): Promise<{ LessonStatusID: number, RawScore: number, CompletionDate: Date | null }> {
  const pool = mssql.getPool()

  if (context.fields.ContextTypeID === LearningContextTypes.Section) {
    // get the content for this context
    const learningObjects = await getLearningObjectsService(context.fields.ID)
    if (!context.fields.Contexts) {
      context.fields.Contexts = []
    }

    let numberItemsAttempted = 0

    for (const obj of learningObjects) {
      const progress = await getLearnerProgressForObject(pool.request(), obj.fields.ID!, userID)
      if (progress.length <= 0) {
        break // the user has not attempted this object
      }
      if (progress.filter(p => p.fields.LessonStatusID! > LessonStatuses.notAttempted).length > 0) {
        numberItemsAttempted++
      }
    }

    // next we need to go through the child contexts
    for (const lc of context.fields.Contexts) {
      let grade!: { LessonStatusID: number, RawScore: number, CompletionDate: Date | null }
      const child = new LearningContext(lc)
      if (!child.fields.GradeTypeID) {
        grade = await getContextCompletion(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.CompleteIncomplete || child.fields.GradeTypeID === GradeTypes.PassFail) {
        grade = await getContextPassFail(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.Percentage) {
        grade = await getContextPercentage(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.Exam) {
        grade = await getContextExamScore(child, userID, maxProgressAge)
      }

      if (grade.LessonStatusID > LessonStatuses.notAttempted) {
        numberItemsAttempted++
      }
    }

    // has the user attempted all the content?
    if (numberItemsAttempted === (learningObjects.length + context.fields.Contexts.length)) {
      try {
        const progress = await getExamProgress(pool.request(), context.fields.ExamID!, userID, maxProgressAge)
        let score = 0
        if (progress.fields.RawScore && !isNaN(progress.fields.RawScore)) {
          score = progress.fields.RawScore
        } else if (passIDs.includes(progress.fields.LessonStatusID!)) {
          score = 100
        }
        return {
          LessonStatusID: progress.fields.LessonStatusID!,
          RawScore: score,
          CompletionDate: progress.fields.CompletedDate ? progress.fields.CompletedDate : progress.fields.CreatedOn!
        }
      } catch (error) {
        if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
          return {
            LessonStatusID: LessonStatuses.notAttempted,
            RawScore: 0,
            CompletionDate: null
          }
        } else {
          throw error
        }
      }
    } else {
      return {
        LessonStatusID: numberItemsAttempted > 0 ? LessonStatuses.browsed : LessonStatuses.notAttempted,
        RawScore: 0,
        CompletionDate: null
      }
    }
  } else if (context.fields.ContextTypeID === LearningContextTypes.Course && context.fields.CourseTypeID === CourseTypes.InstructorLed) {
    // this is an ILT course its completion is based on the exam
    try {
      const progress = await getExamProgress(pool.request(), context.fields.ExamID!, userID, maxProgressAge)
      let score = 0
      if (progress.fields.RawScore && !isNaN(progress.fields.RawScore)) {
        score = progress.fields.RawScore
      } else if (passIDs.includes(progress.fields.LessonStatusID!)) {
        score = 100
      }
      return {
        LessonStatusID: progress.fields.LessonStatusID!,
        RawScore: score,
        CompletionDate: progress.fields.CompletedDate ? progress.fields.CompletedDate : progress.fields.CreatedOn!
      }
    } catch (error) {
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        return {
          LessonStatusID: LessonStatuses.notAttempted,
          RawScore: 0,
          CompletionDate: null
        }
      } else {
        throw error
      }
    }
  } else {
    // nothing should hit this point unless the data schema changes
    return {
      LessonStatusID: -1,
      RawScore: 0,
      CompletionDate: null
    }
  }
}

export async function getContextExamScore (context: LearningContext, userID: string, maxProgressAge: Date): Promise<{ LessonStatusID: number, RawScore: number, CompletionDate: Date | null }> {
  try {
    // find the latest score
    const request = mssql.getPool().request()
    request.input('contextID', context.fields.ID)
    request.input('userID', userID)
    request.input('to', maxProgressAge)

    const res = await request.query<UserCompletedLearningContext>(`
      SELECT TOP (1) *
      FROM [${UserCompletedLearningContextsTableName}]
      WHERE [${UserCompletedLearningContextFields.LearningContextID}] = @contextID
      AND [${UserCompletedLearningContextFields.UserID}] = @userID
      AND [${UserCompletedLearningContextFields.CompletedOn}] <= @to
      ORDER BY [${UserCompletedLearningContextFields.RawScore}] DESC, [${UserCompletedLearningContextFields.CompletedOn}] DESC
    `)

    if (res.recordset.length > 0) {
      return {
        LessonStatusID: res.recordset[0].LessonStatusID!,
        RawScore: res.recordset[0].RawScore!,
        CompletionDate: res.recordset[0].CompletedOn!
      }
    } else {
      // if we have no score for the exam let's not attempt to grade it
      // the user may not have attempted the exam yet
      return {
        LessonStatusID: LessonStatuses.notAttempted,
        RawScore: 0,
        CompletionDate: null
      }
    }
  } catch (error) {
    log('error', 'Unknown database error', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}

// get the most recent attempt at the exam
// the users visible score will be the highest score
async function getExamProgress (request: Request, examID: string, userID: string, to: Date): Promise<LearnerProgressModel> {
  request.input('objectID', examID)
  request.input('userID', userID)
  request.input('to', to)
  // we need to get the learner progress records
  // for the exam ordered by highest score, soonest date that score was earned
  // if the user has completed we want to look for the highest score since completing
  const res = await request.query<LearnerProgress>(`
    SELECT TOP (1) *
    FROM [${LearnerProgressTableName}]
    WHERE [UserID] = @userID
    AND [LearningObjectID] = @objectID
    AND [LessonStatusID] > ${LessonStatuses.browsed}
    AND [CreatedOn] <= @to
    ORDER BY [LessonStatusID] DESC, [RawScore] DESC, [CreatedOn] DESC, [CompletedDate] DESC
  `)

  if (res.recordset.length > 0) {
    return new LearnerProgressModel(res.recordset[0])
  } else {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }
}
