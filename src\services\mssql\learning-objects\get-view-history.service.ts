import mssql from '@lcs/mssql-utility'
import { Request } from 'mssql'
import { UserViewedLearningObjectsView, UserViewedLearningObjectsViewName, UserViewedLearningObjectsViewFields } from '@tess-f/sql-tables/dist/lms/user-viewed-learning-objects-view.js'
import { LearningObjectUserViewFields, LearningObjectUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-view.js'

export default async function (userID: string, from?: Date, to?: Date): Promise<UserViewedLearningObjectsView[]> {
  const pool = mssql.getPool()
  const learningObjectViewHistory = await getViewedLearningObjects(pool.request(), userID, from, to)
  return learningObjectViewHistory
}

async function getViewedLearningObjects (request: Request, userID: string, from?: Date, to?: Date): Promise<UserViewedLearningObjectsView[]> {
  let query = `SELECT * FROM [${UserViewedLearningObjectsViewName}] WHERE [${UserViewedLearningObjectsViewFields.UserID}] = @userID `
  request.input('userID', userID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
    query += `
      AND [${UserViewedLearningObjectsViewFields.ID}] IN (
        SELECT [${LearningObjectUserViewFields.LearningObjectID}]
        FROM [${LearningObjectUserViewsTableName}]
        WHERE [${LearningObjectUserViewFields.CreatedOn}] BETWEEN @from AND @to
      )
    `
  }

  query += `ORDER BY [${UserViewedLearningObjectsViewFields.LastAccessed}] DESC`
  const results = await request.query<UserViewedLearningObjectsView>(query)
  return results.recordset
}
