import mssql, { DB_Errors } from '@lcs/mssql-utility'
import { AssignmentFields, AssignmentsTableName } from '@tess-f/sql-tables/dist/lms/assignment.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { AssignmentsWithNoContentViewFields, AssignmentsWithNoContentViewName } from '@tess-f/sql-tables/dist/lms/assignments-with-no-content-view.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'
import { UserAssignedMultiSessionCoursesFields, UserAssignedMultiSessionCoursesTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-multi-session-course.js'
import { FlatLearningContextTreeFields, FlatLearningContextTreeViewName } from '@tess-f/sql-tables/dist/lms/flat-learning-context-view.js'
import { AssignmentStatus } from '../../../utils/assignment-status.js'

export default async function (contextID: string, assignmentID: string, userID: string, from?: Date, to?: Date): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('contextID', contextID)
  request.input('userID', userID)
  request.input('assignmentID', assignmentID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const query = `
    WITH Parent_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}], [${LearningContextFields.VisibilityID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] = @contextID
      UNION ALL
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Parent_Nodes] ON [Parent_Nodes].[${LearningContextFields.ParentContextID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}]
    ), Assignment_Status AS (
      SELECT [${UserAssignedLearningObjectFields.AssignmentID}], [${UserAssignedLearningObjectFields.DueDate}], [${UserAssignedLearningObjectFields.LessonStatusID}], [${UserAssignedLearningObjectFields.Deleted}], [${UserAssignedLearningObjectFields.UserID}], [${UserAssignedLearningObjectFields.LearnerProgressID}]
      FROM [${UserAssignedLearningObjectsTableName}]
      WHERE [${UserAssignedLearningObjectFields.UserID}] = @userID
      AND [${UserAssignedLearningObjectFields.ForContextID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [Parent_Nodes]
        WHERE [${LearningContextFields.VisibilityID}] != ${Visibilities.Hidden}
      )
      ${from && to ? `AND [${UserAssignedMultiSessionCoursesFields.CreatedOn}] BETWEEN @from AND @to` : ''}
      UNION ALL
      SELECT [${UserAssignedMultiSessionCoursesFields.AssignmentID}], [${UserAssignedMultiSessionCoursesFields.DueDate}], [${UserAssignedMultiSessionCoursesFields.LessonStatusID}], [${UserAssignedMultiSessionCoursesFields.Deleted}], [${UserAssignedMultiSessionCoursesFields.UserID}], [${UserAssignedMultiSessionCoursesFields.LearnerProgressID}]
      FROM [${UserAssignedMultiSessionCoursesTableName}]
      WHERE [${UserAssignedMultiSessionCoursesFields.UserID}] = @userID
      AND ([${UserAssignedMultiSessionCoursesFields.LearningContextID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [Parent_Nodes]
        WHERE [${LearningContextFields.VisibilityID}] != ${Visibilities.Hidden}
      ) OR [${UserAssignedMultiSessionCoursesFields.ForContextID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [Parent_Nodes]
        WHERE [${LearningContextFields.VisibilityID}] != ${Visibilities.Hidden}
      ))
      ${from && to ? `AND [${UserAssignedMultiSessionCoursesFields.CreatedOn}] BETWEEN @from AND @to` : ''}
    ), Overdue_Status AS (
      SELECT [${AssignmentFields.ID}]
      FROM [${AssignmentsTableName}]
      WHERE [${AssignmentFields.ID}] IN (
        SELECT [${UserAssignedLearningObjectFields.AssignmentID}]
        FROM [Assignment_Status]
        WHERE [${UserAssignedLearningObjectFields.DueDate}] IS NOT NULL
        AND [${UserAssignedLearningObjectFields.DueDate}] < ${to ? '@to' : 'GETDATE()'}
        AND [${UserAssignedLearningObjectFields.LessonStatusID}] < ${LessonStatuses.fail}
        AND [${UserAssignedLearningObjectFields.Deleted}] = 0
      )
    ), Completed_Status AS (
      SELECT [${AssignmentFields.ID}]
      FROM [${AssignmentsTableName}]
      WHERE [${AssignmentFields.ID}] IN (
        SELECT [${UserAssignedLearningObjectFields.AssignmentID}]
        FROM [Assignment_Status]
        WHERE [${UserAssignedLearningObjectFields.LessonStatusID}] >= ${LessonStatuses.fail}
        AND [${UserAssignedLearningObjectFields.Deleted}] = 0
      ) AND [${AssignmentFields.ID}] NOT IN (
        SELECT [${UserAssignedLearningObjectFields.AssignmentID}]
        FROM [Assignment_Status]
        WHERE [${UserAssignedLearningObjectFields.LessonStatusID}] < ${LessonStatuses.fail}
        AND [${UserAssignedLearningObjectFields.Deleted}] = 0
      )
    ), In_Progress_Status AS (
      SELECT [${AssignmentFields.ID}]
      FROM [${AssignmentsTableName}]
      WHERE [${AssignmentFields.ID}] NOT IN (
        SELECT [${AssignmentFields.ID}]
        FROM [Overdue_Status]
      ) AND [${AssignmentFields.ID}] NOT IN (
        SELECT [${AssignmentFields.ID}]
        FROM [Completed_Status]
      ) AND [${AssignmentFields.ID}] IN (
        SELECT [${UserAssignedLearningObjectFields.AssignmentID}]
        FROM [Assignment_Status]
        WHERE [${UserAssignedLearningObjectFields.LessonStatusID}] > ${LessonStatuses.notAttempted}
        AND [${UserAssignedLearningObjectFields.Deleted}] = 0
      )
    ), Not_Started_Status AS (
      SELECT [${AssignmentFields.ID}]
      FROM [${AssignmentsTableName}]
      WHERE [${AssignmentFields.ID}] NOT IN (
        SELECT [${AssignmentFields.ID}]
        FROM [Overdue_Status]
      ) AND [${AssignmentFields.ID}] NOT IN (
        SELECT [${AssignmentFields.ID}]
        FROM [Completed_Status]
      ) AND [${AssignmentFields.ID}] NOT IN (
        SELECT [${AssignmentFields.ID}]
        FROM [In_Progress_Status]
      ) AND [${AssignmentFields.ID}] IN (
        SELECT [${UserAssignedLearningObjectFields.AssignmentID}]
        FROM [Assignment_Status]
      )
    )
    SELECT [${AssignmentFields.ID}],
      CASE
        WHEN [${AssignmentFields.ID}] IN (SELECT [${AssignmentFields.ID}] FROM [Not_Started_Status]) THEN ${AssignmentStatus.Assigned}
        WHEN [${AssignmentFields.ID}] IN (SELECT [${AssignmentFields.ID}] FROM [In_Progress_Status]) THEN ${AssignmentStatus.InProgress}
        WHEN [${AssignmentFields.ID}] IN (SELECT [${AssignmentFields.ID}] FROM [Completed_Status]) THEN ${AssignmentStatus.Completed}
        WHEN [${AssignmentFields.ID}] IN (SELECT [${AssignmentFields.ID}] FROM [Overdue_Status]) THEN ${AssignmentStatus.Overdue}
        WHEN [${AssignmentFields.ID}] IN (SELECT [${AssignmentsWithNoContentViewFields.ID}] FROM [${AssignmentsWithNoContentViewName}]) THEN ${AssignmentStatus.NoContent}
      END AS [Status]
    FROM [${AssignmentsTableName}]
    WHERE [${AssignmentFields.ID}] = @assignmentID
  `

  const results = await request.query<{ ID: string, Status: number }>(query)

  if (results.recordset.length <= 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }
  return results.recordset[0].Status
}
