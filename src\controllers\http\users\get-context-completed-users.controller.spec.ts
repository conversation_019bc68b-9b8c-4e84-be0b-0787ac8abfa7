import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import UserCompletedLearningContextModel from '../../../models/user-completed-learning-context.model.js'

describe('HTTP get-context-completed-users controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-context-completed-users.controller', {
            '../../../services/mssql/learning-context/get-completion-user-ids.service.js': {
                getCompletedUserIDs: Sinon.stub().returns(Promise.resolve({totalRecords: 0, ids: ['']}))
            },
            '../../../services/mssql/user-completed-learning-contexts/get-for-user.service.js': {
                getMostRecentCompletion: Sinon.stub().returns(Promise.resolve(new UserCompletedLearningContextModel({CompletedOn: new Date()})))
            },
            '../../../services/mssql/learning-context/get-user-completion-dates.service.js': {
                getUserCompletedStartedDate: Sinon.stub().returns(Promise.resolve(Date.now()))
            },
            '../../../services/mssql/user-completed-learning-contexts/get-user-completion-count-for-context.service.js': {
                default: Sinon.stub().returns(Promise.resolve(1))
            }
        })

        const mocks = httpMocks.createMocks({
            
            
            query: {
            },
            body: {
                limit: 10,
                offset: 1
            },
            params: {
                id: uuid()
            }
            



        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-context-completed-users.controller', {
            '../../../services/mssql/learning-context/get-completion-user-ids.service.js': {
                getCompletedUserIDs: Sinon.stub().returns(Promise.resolve({totalRecords: 0, ids: ['']}))
            },
            '../../../services/mssql/user-completed-learning-contexts/get-for-user.service.js': {
                getMostRecentCompletion: Sinon.stub().returns(Promise.resolve(new UserCompletedLearningContextModel({CompletedOn: new Date()})))
            },
            '../../../services/mssql/learning-context/get-user-completion-dates.service.js': {
                getUserCompletedStartedDate: Sinon.stub().returns(Promise.resolve(Date.now()))
            },
            '../../../services/mssql/user-completed-learning-contexts/get-user-completion-count-for-context.service.js': {
                default: Sinon.stub().returns(Promise.resolve(1))
            }
        })

        const mocks = httpMocks.createMocks({
            
            
            query: {
            },
            body: {
                limit: 10,
                offset: 1
            },
            params: {
                id: false
            }
            



        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('id')
        expect(mocks.res._getData()).include('Expected string, received boolean')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-context-completed-users.controller', {
            '../../../services/mssql/learning-context/get-completion-user-ids.service.js': {
                getCompletedUserIDs: Sinon.stub().rejects(Promise.resolve({totalRecords: 0, ids: ['']}))
            },
            '../../../services/mssql/user-completed-learning-contexts/get-for-user.service.js': {
                getMostRecentCompletion: Sinon.stub().rejects(Promise.resolve(new UserCompletedLearningContextModel({CompletedOn: new Date()})))
            },
            '../../../services/mssql/learning-context/get-user-completion-dates.service.js': {
                getUserCompletedStartedDate: Sinon.stub().rejects(Promise.resolve(Date.now()))
            },
            '../../../services/mssql/user-completed-learning-contexts/get-user-completion-count-for-context.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(1))
            }
        })

        const mocks = httpMocks.createMocks({
            
            
            query: {
            },
            body: {
                limit: 10,
                offset: 1
            },
            params: {
                id: uuid()
            }
            



        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

    
  

})