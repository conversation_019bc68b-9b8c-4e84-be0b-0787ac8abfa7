import { RequestHand<PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getMultipleController from './get-multiple.controller.js'
import getController from './get.controller.js'
import updateController from './update.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.get('/skill-level/:id', getController as RequestHandler)
router.put('/skill-level/:id', checkClaims([Claims.MODIFY_SYSTEM_LABELS, Claims.CREATE_SYSTEM_LABELS]), updateController as RequestHandler)
router.delete('/skill-level/:id', checkClaims([Claims.DELETE_SYSTEM_LABELS]), deleteController as RequestHand<PERSON>)
router.post('/skill-level', checkClaims([Claims.CREATE_SYSTEM_LABELS]), createController as RequestHandler)
router.get('/skill-levels', getMultipleController as RequestHandler)

export default router
