import mssql from '@lcs/mssql-utility'
import { User, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'

/**
 * Gets a list users that have created learning contexts
 * @param {number} learningContextTypeId context type ID to filter results by
 * @returns {string[]}
 */
export default async function (learningContextTypeId?: number): Promise<User[]> {
  const request = mssql.getPool().request()
  const query = `
    SELECT [${UserFields.ID}], [${UserFields.Avatar}], [${UserFields.FirstName}], [${UserFields.MiddleInitial}], [${UserFields.LastName}]
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] IN (
      SELECT [${LearningContextFields.CreatedBy}]
      FROM [${LearningContextTableName}]
      ${learningContextTypeId ? `WHERE [${LearningContextFields.ContextTypeID}] = @typeID` : ''}
    )
  `
  if (learningContextTypeId) {
    request.input('typeID', learningContextTypeId)
  }
  const results = await request.query<User>(query)
  return results.recordset
}
