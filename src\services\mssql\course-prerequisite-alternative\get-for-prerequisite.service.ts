import mssql, { getRows, DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { CoursePrerequisiteAlternative, CoursePrerequisiteAlternativesTableName } from '@tess-f/sql-tables/dist/lms/course-prerequisite-alternative.js'
import { getErrorMessage } from '@tess-f/backend-utils'

export default async function getAlternativesForPrerequisite (prerequisiteId: string): Promise<CoursePrerequisiteAlternative[]> {
  try {
    const pool = mssql.getPool()
    const records = await getRows<CoursePrerequisiteAlternative>(CoursePrerequisiteAlternativesTableName, pool.request(), { ForPrerequisiteId: prerequisiteId })
    return records
  } catch (error) {
    if (getErrorMessage(error) === dbErrors.default.NOT_FOUND_IN_DB) {
      return []
    }
    throw error
  }
}
