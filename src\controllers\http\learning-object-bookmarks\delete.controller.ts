import del from '../../../services/mssql/learning-object-bookmarks/delete.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.delete-learning-object-bookmark', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, un-bookmark a learning object within a course. Note the time. Check the LMS API log for the 'http-delete-learning-object-bookmark' label."
  try {
    const { userID, objectID } = z.object({ userID: zodGUID, objectID: zodGUID }).parse(req.params)
    await del(objectID, userID)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, un-bookmark a learning object within a course. Note the time. Check the LMS API log for the 'http-delete-learning-object-bookmark' label and message indicating successful deletion."
    log('info', 'Successfully deleted user bookmark for learning object', { success: true, req, userID, objectID })

    res.sendStatus(httpStatus.NO_CONTENT)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete learning object bookmark: input validation error', { success: false, req, error })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter: '))
    } else {
      log('error', 'Failed to delete learning object bookmark.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
