import { Table } from '@lcs/mssql-utility'
import { Assignment, AssignmentFields, AssignmentsTableName } from '@tess-f/sql-tables/dist/lms/assignment.js'
import { AssignmentJson } from '@tess-f/lms/dist/common/assignment.js'
import settings from '../config/settings.js'
import { z } from 'zod'
import { zodDates, zodGUID } from '@tess-f/backend-utils/validators'
import { AssignmentTypes } from '@tess-f/sql-tables/dist/lms/assignment-type.js'

export const createAssignmentSchema = z.object({
  Title: z.string().max(250),
  EmailMessage: z.string().max(1000).optional(),
  DueDate: zodDates.optional(),
  TypeID: z.nativeEnum(AssignmentTypes),
  Everyone: z.boolean().default(false),
  DirectReports: z.boolean().optional(),
  Users: z.array(z.object({ ID: zodGUID })).optional(),
  Groups: z.array(z.object({ ID: zodGUID })).optional(),
}).superRefine(({ Users, Groups, Everyone, DirectReports }, ctx) => {
  if (!Everyone && !DirectReports && (!Users || Users.length <= 0) && (!Groups || Groups.length <= 0)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'No assignees defined'
    })
  }
})

export const updateAssignmentSchema = z.object({
  Title: z.string().max(250).optional(),
  EmailMessage: z.string().max(1000).optional(),
  DueDate: zodDates.optional(),
  Everyone: z.boolean().optional(),
  DirectReports: z.boolean().optional(),
  Users: z.array(z.object({ ID: zodGUID })).optional(),
  Groups: z.array(z.object({ ID: zodGUID })).optional()
})

const requiredFields: Array<keyof Assignment> = [
  AssignmentFields.Title,
  AssignmentFields.TypeID,
  AssignmentFields.Everyone,
  AssignmentFields.CreatedBy,
  AssignmentFields.CreatedOn
]

if (settings.assignments.requireDueDates) {
  requiredFields.push(AssignmentFields.DueDate)
}

export default class AssignmentsModel extends Table<AssignmentJson, Assignment> {
  public fields: AssignmentJson

  constructor (fields?: AssignmentJson, record?: Assignment) {
    super(AssignmentsTableName, requiredFields)
    if (fields) this.fields = fields
    else this.fields = {}

    if (record) this.importFromDatabase(record)
  }

  public importFromDatabase (record: Assignment): void {
    this.fields = record
  }

  public exportJsonToDatabase (): Assignment {
    return {
      ID: this.fields.ID,
      Title: this.fields.Title,
      EmailMessage: this.fields.EmailMessage,
      DueDate: this.fields.DueDate,
      TypeID: this.fields.TypeID,
      Everyone: this.fields.Everyone,
      CreatedBy: this.fields.CreatedBy,
      CreatedOn: this.fields.CreatedOn,
      ModifiedBy: this.fields.ModifiedBy,
      ModifiedOn: this.fields.ModifiedOn,
      DirectReports: this.fields.DirectReports
    }
  }
}
