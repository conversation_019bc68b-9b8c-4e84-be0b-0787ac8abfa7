import mssql from '@lcs/mssql-utility'
import { LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { Request } from 'mssql'

// gets learning object uploads by type
export default async function (from?: Date, to?: Date): Promise<{ Uploads: number, LearningObjectTypeID: number }[]> {
  const pool = mssql.getPool()
  return await getUploads(pool.request(), from, to)
}

async function getUploads (request: Request, from?: Date, to?: Date): Promise<{ Uploads: number, LearningObjectTypeID: number }[]> {
  let query = `SELECT COUNT([${LearningObjectFields.LearningObjectTypeID}]) as Uploads, `
  query += `[${LearningObjectFields.LearningObjectTypeID}] FROM [${LearningObjectsTableName}] `
  if (from && to) {
    request.input('from', from)
    request.input('to', to)
    query += `WHERE [${LearningObjectFields.CreatedOn}] BETWEEN @from AND @to `
  }
  query += `GROUP BY [${LearningObjectFields.LearningObjectTypeID}]`

  const res = await request.query<{ Uploads: number, LearningObjectTypeID: number }>(query)
  return res.recordset
}
