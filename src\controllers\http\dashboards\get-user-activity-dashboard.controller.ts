import getLearningMetadata from '../../../services/mssql/my-learning/get-metadata.service.js'
import getContentViewsByType from '../../../services/mssql/learning-object-views/get-user-views-by-type.service.js'
import getContextViewsByType from '../../../services/mssql/learning-context-views/get-user-views-by-type.service.js'
import getKeywordViewCounts from '../../../services/mssql/keywords/get-view-count.service.js'
import getViewHistory from '../../../services/mssql/learning-objects/get-view-history.service.js'
import getActivityStream from '../../../services/mssql/activity-stream/get-by-user.service.js'
import logger from '@lcs/logger'
import { DB_Errors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import ActivityStream from '../../../models/activity-stream.model.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'
const { INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.get-user-activity-dashboard', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, go to the "My Activity" dashboard. Note the time. Check the LMS API log for the 'http-get-user-activity-dashboard' label."

  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)

    let from: Date | undefined
    let to: Date | undefined
    const filterDates = z.object({
      from: z.coerce.date().optional(),
      to: z.coerce.date().optional()
    }).safeParse(req.query)

    if (filterDates.success) {
      from = filterDates.data.from
      to = filterDates.data.to
    }

    let metadata, contentTypeViews: Array<{Views: number, UserID: string, LearningObjectTypeID: number} | {Views: number, UserID: string, Label: string}>, contentKeywordViews, viewHistory

    try {
      metadata = await getLearningMetadata(id, from, to)
      // STIG V-69425 data access (success)
      log('info', 'Successfully retrieved learning metadata for user.', { userId: id, success: true, req })
    } catch (error) {
      // STIG V-69425 data access (failure)
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        log('info', 'Failed to retrieve learning metadata for user because no record was found', { userId: id, success: false, req })
        metadata = {}
      } else {
        log('error', 'Failed to retrieve learning metadata for user', { userId: id, error, req, success: false })
        throw error
      }
    }

    try {
      contentTypeViews = await getContentViewsByType(id, from, to)
      const contextTypeViews = await getContextViewsByType(id, from, to)
      contentTypeViews = contentTypeViews.concat(contextTypeViews)
      // STIG V-69425 data access (success)
      log('info', 'Successfully retrieved content views by type for user', { userId: id, success: true })
    } catch (error) {
      // STIG V-69425 data access (failure)
      log('error', 'Failed to retrieve content views by type for user', { userId: id, error, req, success: false })
      throw error
    }

    try {
      contentKeywordViews = await getKeywordViewCounts(from, to, id)
      // sort by name
      contentKeywordViews.sort((a, b) => a.Name.localeCompare(b.Name))
      // STIG V-69425 data access (success)
      log('info', 'Successfully retrieved content views by keyword for user', { userId: id, success: true, req })
    } catch (error) {
      // STIG V-69425 data access (failure)
      log('error', 'Failed to retrieve content views by keyword for user', { userId: id, error, success: false, req })
      throw error
    }

    try {
      viewHistory = await getViewHistory(id, from, to)
      // STIG V-69425 data access (success)
      log('info', 'Successfully retrieved view history for user', { userId: id, success: true, req })
    } catch (error) {
      // STIG V-69425 data access (failure)
      log('error', 'Failed to retrieve view history for user', { userId: id, error, req, success: false })
      throw error
    }

    let activityStream: ActivityStream[]
    try {
      activityStream = await getActivityStream(id, from, to)
      // STIG V-69425 data access (success)
      log('info', 'Successfully retrieved activity stream for user', { userId: id, success: true, req })
    } catch (error) {
      // STIG V-69425 data access (failure)
      log('error', 'Failed to retrieve activity stream for user', { userId: id, error, success: false, req })
      throw error
    }

    // STIG V-69425 data access (success)
    log('info', 'Successfully retrieved user activity dashboard data for user', + { userId: id, success: true, req })

    res.json({
      LearningMetadata: metadata,
      ContentTypeViews: contentTypeViews,
      ContentKeywordViews: contentKeywordViews,
      ContentViewHistory: viewHistory,
      ActivityStream: activityStream.map(stream => stream.fields)
    })
  } catch (error) {
    // STIG V-69425 data access (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to retrieve user activity dashboard data: validation error', { error, req, success: false })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter(s), '))
    } else {
      log('error', 'Failed to retrieve user activity dashboard data.', { error, req, success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
