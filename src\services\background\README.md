# Background Services
This folder contains a set of services that run in the background processes that the user should not wait for a response from. These service may provide vital functions such as analytics or grading but if run in the span of an HTTP call would take too long for the response to generate.

# Grade Contexts Service
This service is triggered after a learner progress record is created or updated. The service will first gather a list of all the learning contexts that use the given learning object or ILT session and then grade and store completions accordingly. The sub services (context-grading-services) will recursively call each other to determine completion of each of the nested contexts. The context being graded must have the nested structure already fetched (this increases the performance of the functions).

## No Grade Type
If the context has no grade type the completion will be checked.

1. Check when the last time the context was completed
    1. If the context has not been completed we will check all records for completion
    2. If the context has been completed previously we will check all records from the last time completed to today
2. If all the required items for the context has been completed a completion record will be created in the database for the given date.
3. If the context is completed and it is marked to issue a certificate a new certificate will be issued for the new completion.

## Complete/Incomplete & Pass/Fail Grade Type
A context will be considered complete/passed if all the content has a lesson status of passed or completed. A context will be considered incomplete or failed if any one of the learning content has a lesson status of fail/incomplete/browsed. All content must have been attempted to be graded.

1. Check when the last the context passed/completed
    1. If the context has not been passed/completed we will check all learner progress records for completion
    2. If the context has been passed/completed previously we will check all learner progress records from the last time the context was completed though the current date
2. If all required items have been passed/completed a complete/passed record will be created in the database. If any record has less than a pass/completed status a fail/incomplete status context completion record will be created.
3. If the context is passed/completed and this context is marked to issue certificates a new completion certificate will be issued.

## Percentage Grade Type
A context will be considered passed if the score is within the min and max range for the given context. Any score outside of the min and max range will be considered failed. All content must have been attempted to be graded.

### How Percentage Scores Are Calculated
1. If the file type supports a score (SCORM 1.2 content that records a score, ILT content that is graded by percentage, etc.) the score from the content will be used.
2. If the content type does not support a score a status of pass/complete will be considered 100 any other status will considered a 0. For example an image will score 100 if the image was viewed. A PDF file that has not been viewed will receive a 0.
3. The highest score for a given learning element will be used. For example if a user attempted an e-learning course 5 times and scored a 50, 70, 38, 90, and 72 the score of 90 will be used.
4. The average score of all the content will be used to determine the overall score (no weighting).  

### Order of operations

1. Check when the last time this context was passed
    1. If the context has not been passed we will check all the learner progress records
    2. If the context has been passed previously we will check all learner progress records from the time the context was last completed through today
2. Calculate the overall score using the method described above
3. If the overall score is between the min and max required score (inclusive) a passed recorded will be created in the database. If the overall score is outside the min and max range a completed record with the status of failed will be created.
4. If the context has been passed and the context is marked to issue certificates a new certificate of completion will be issued.

## Exam Grade Type
A context with a grade type of exam will only be graded if the following conditions are met:

- The file that triggered the grading is the exam for this context
- All of the required content has been completed since the last time the context was marked completed/passed

### Order of operations

1. Check if the learner progress that triggered the grading is the exam for this context
2. Check when the last time the context was passed
    - If the context has not been passed we will check the all progress records
    - If the context has been passed we will check progress records since the last time the context was completed through today
3. Check that all required content has been completed
4. If all content has been completed the status of the exam will be used to mark the completion status of the context
5. If the context has been passed/completed successfully and the context is marked to issue certificates a new certificate of completion will be issued.
