import UserPreference, { userPreferenceSchema } from '../../../models/user-preference.model.js'
import createService from '../../../services/mssql/user-preferences/create.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { ZodError } from 'zod'

const log = logger.create('Controller-HTTP.create-user-preferences', httpLogTransformer)

/**
 * Express route for creating a user preference
 */
export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-create-user-preferences' label."
  try {
    const userPref = new UserPreference(userPreferenceSchema.parse(req.body))
    const result = await createService(userPref)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-create-user-preferences' label and message indicating successful creation."
    log('info', 'Successfully created user preferences for user', { success: true, userId: result.fields.UserID, req })

    res.json(result.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create user preferences due to invalid data in the request.', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to create user preferences.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
