import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import getAvailableParentNodes from './get-available-parent-nodes.service.js'
import getChildContexts from './get-child-contexts.service.js'
import getTopLevel from './get-top-level.service.js'
import search from './search.service.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

let learningContext: LearningContext

describe('MSSQL Catalog', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test Catalog Service',
      Description: `Running catalog on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Collection
    }))
  })

  it('gets available parent nodes', async () => {
    const contexts = await getAvailableParentNodes(learningContext.ID)
    expect(contexts.length).to.be.gte(0)
  })

  it('gets available parent nodes', async () => {
    const contexts = await getChildContexts(learningContext.ID!)
    expect(contexts.length).to.be.gte(0)
  })

  it('gets available parent nodes', async () => {
    const contexts = await getTopLevel()
    expect(contexts.length).to.be.gte(0)
  })

  it('gets available parent nodes', async () => {
    const contexts = await search(learningContext.Title!, 1, 150)
    expect(contexts.totalRecords).to.be.gte(0)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { ID: learningContext.ID })
  })
})
