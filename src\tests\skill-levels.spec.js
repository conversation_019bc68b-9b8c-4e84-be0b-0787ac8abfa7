const chai = require('chai');
const expect = chai.expect;
const uuidv4 = require('uuid/v4');
const tester = require('../api/utils/test-agent.utils');
const settings = require('../api/config/settings');


let skillLevel = { Name: uuidv4() };
let skillLevelUpdated = { Name: uuidv4() };

let skillLevelID = 'F619495F-7F8F-4828-BBFF-D45FE21F23B6'; // Skill Level 1
let isolated = false;

describe('End-to-end: Skill Levels', () => {


    before( done => {
        if(tester.agent == null) {
            isolated = true;
            tester.create()
            .then( agent => {
                done();
            })
            .catch( err => {
                console.error(err);
                done();
            });
        } else {
            done();
        }
    });

    it('creates a skill level', done => {
        tester.agent.post(settings.server.root + 'skill-level')
        .send({ skillLevel: skillLevel })
        .end((err, res) => {

            expect(res.statusCode).to.equal(200);
            expect(res.body).to.exist;
            expect(res.body.ID).to.exist;

            skillLevel = res.body;
            skillLevelUpdated.ID = res.body.ID;

            done();
        });
    });

    it('updates a skill level', done => {
        tester.agent.put(settings.server.root + 'skill-level/' + skillLevelUpdated.ID)
        .send({ skillLevel: skillLevelUpdated })
        .end((err, res) => {

            expect(res.statusCode).to.equal(200);
            expect(res.body).to.exist;
            expect(res.body.Name).to.equal( skillLevelUpdated.Name );

            skillLevel = res.body;

            done();
        });
    })

    it('deletes a skill level', done => {
        tester.agent.delete(settings.server.root + 'skill-level/' + skillLevel.ID)
        .end((err, res) => {

            expect(res.statusCode).to.equal(204);

            skillLevel = res.body;

            done();
        });
    });

    it('gets a skill level', done => {
        tester.agent.get(settings.server.root + 'skill-level/' + skillLevelID)
        .end((err, res) => {

            expect(res.statusCode).to.equal(200);
            expect(res.body.ID).to.equal(skillLevelID);

            done();
        });
    });

    it('get multiple skill levels', done => {
        tester.agent.get(settings.server.root + 'skill-levels')
        .end((err, res) => {

            expect(res.statusCode).to.equal(200);
            expect(res.body.length).to.be.gt(1);

            done();
        });
    });

    after((done)=>{
        if(isolated) {
            tester.close();
        } 
        done();
    });


});