import mssql, { DB_Errors as DbErrors, getRows } from '@lcs/mssql-utility'
import { LearningObjectObjective, LearningObjectObjectiveTableName } from '@tess-f/sql-tables/dist/lms/learning-object-objective.js'
import { getErrorMessage } from '@tess-f/backend-utils'

export default async function getObjectiveIdsForLearningObject (learningObjectId: string): Promise<string[]> {
  try {
    const records = await getRows<LearningObjectObjective>(LearningObjectObjectiveTableName, mssql.getPool().request(), { LearningObjectId: learningObjectId })
    records.sort((a, b) => (a.OrderId ?? 1) - (b.OrderId ?? 1))
    return records.map(record => record.ObjectiveId!)
  } catch (error) {
    if (getErrorMessage(error) === DbErrors.default.NOT_FOUND_IN_DB) {
      return []
    }
    throw error
  }
}
