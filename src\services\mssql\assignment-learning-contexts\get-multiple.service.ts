import mssql, { getRows } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { AssignmentLearningContext, AssignmentLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-context.js'
import AssignmentLearningContextsModel from '../../../models/assignment-learning-context.model.js'
import { getErrorMessage } from '@tess-f/backend-utils'
const log = logger.create('Service.Get-Multiple-Assignment-Learning-Contexts')

export default async function (assignmentID: string): Promise<AssignmentLearningContextsModel[]> {
  try {
    const pool = mssql.getPool()
    const records = await getRows<AssignmentLearningContext>(AssignmentLearningContextsTableName, pool.request(), { AssignmentID: assignmentID })
    return records.map(record => new AssignmentLearningContextsModel(record))
  } catch (error) {
    // Unexpected error
    log('error', 'Unexpected error', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}
