import mssql, { addRow } from '@lcs/mssql-utility'
import SCORM1_2InteractionModel from '../../../models/scorm-1-2-interaction.model.js'
import { SCORM_1_2_Interaction } from '@tess-f/sql-tables/dist/lms/scorm-1-2-interaction.js'

export default async function createScorm12Interaction (interaction: SCORM1_2InteractionModel): Promise<SCORM1_2InteractionModel> {
  const record = await addRow<SCORM_1_2_Interaction>(mssql.getPool().request(), interaction)
  return new SCORM1_2InteractionModel(record)
}
