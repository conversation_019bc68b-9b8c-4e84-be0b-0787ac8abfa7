import { expect } from 'chai'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'
import esmock from 'esmock'
import Sinon from 'sinon'
import logger from '@lcs/logger'

describe('AMQP User Claims', function () {
  before(() => {
    logger.init({ level: 'error' })
  })

  afterEach(() => Sinon.restore())

  it('should check that a user has a given claim', async () => {
    const checkUserHasClaim = await esmock('./check-user-has-claim.js', {
      '../../../services/mssql/claims/has-claim.service.js': { default: Sinon.stub().returns(Promise.resolve(true)) }
    })
    const res = await checkUserHasClaim({ data: { claim: Claims.BROWSE_CONTENT, userID: AdminUserId }, command: '' })
    expect(res.success).to.be.true
    expect(res.data?.valid).to.be.true
  })
})
