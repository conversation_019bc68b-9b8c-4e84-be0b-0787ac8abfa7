import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import ActivityStreamModel from '../../../models/activity-stream.model.js'
import create from './create.service.js'
import getByUser from './get-by-user.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { v4 as uuidv4 } from 'uuid'
import { ActivityStreamTableName } from '@tess-f/sql-tables/dist/lms/activity-stream.js'

let activityStream: ActivityStreamModel

describe('MSSQL Activity Stream', () => {
  const ActivityStreamInput = new ActivityStreamModel({
    UserID: AdminUserId,
    LinkText: 'Testing Activity Stream Service',
    LinkID: uuidv4(),
    ActivityID: 1,
    Rating: 3,
    CreatedOn: new Date()
  })
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
  })

  it('creates an activity in the activity stream', async () => {
    activityStream = await create(ActivityStreamInput)
    expect(activityStream.fields.ActivityID).to.eq(1)
    expect(activityStream.fields.UserID).to.eq(AdminUserId)
    expect(activityStream.fields.Rating).to.eq(3)
  })

  it('should fail to create the same activity stream', async () => {
    try {
      const results = await create(ActivityStreamInput)
      expect(results.fields.ActivityID).to.eq(1)
      expect(results.fields.UserID).to.eq(AdminUserId)
      expect(results.fields.Rating).to.eq(3)
    } catch (error) {
      expect(error).to.exist
    }
  })

  it('get activity stream get by user', async () => {
    const d = activityStream.fields.CreatedOn!
    d.setDate(d.getDate() - 5)
    const indices = await getByUser(AdminUserId, d, new Date())
    expect(indices.length).to.be.gte(0)
  })

  it('should fail to get activity stream get by unknown user', async () => {
    const d = activityStream.fields.CreatedOn!
    d.setDate(d.getDate() - 5)
    const indices = await getByUser(uuidv4(), d, new Date())
    expect(indices.length).to.be.eq(0)
  })

  after(async () => {
    const request = mssql.getPool().request()
    await request.query(`DELETE FROM [${ActivityStreamTableName}]`)
  })
})
