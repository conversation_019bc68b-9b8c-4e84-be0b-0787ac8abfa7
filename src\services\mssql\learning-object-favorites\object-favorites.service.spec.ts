import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import LearningObjectFavoriteModel from '../../../models/learning-object-user-favorite.model.js'
import get, { getUserFavoritesForObject, getObjectFavoritesForUser } from './get.service.js'
import create from './create.service.js'
import remove from './delete.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { v4 as uuidv4 } from 'uuid'

let learningObject: LearningObject
let learningObjectFavorite: LearningObjectFavoriteModel

describe('MSSQL Learning Object User Favorites', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningObject = await addRow<LearningObject>(pool.request(), new LearningObjectModel({
      Title: 'Test object user favorites',
      Description: `Running object-favorites on ${new Date()}`,
      ContentID: uuidv4(),
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      LearningObjectTypeID: LearningObjectTypes.PDF
    }))
  })

  it('creates a learning object user favorite', async () => {
    learningObjectFavorite = await create(new LearningObjectFavoriteModel({
      LearningObjectID: learningObject.ID,
      UserID: AdminUserId
    }))

    expect(learningObjectFavorite.fields.LearningObjectID).to.equal(learningObject.ID)
  })

  it('gets a users favorite object', async () => {
    const _favorite = await get(learningObject.ID!, AdminUserId)
    expect(_favorite.fields.UserID).to.equal(AdminUserId)
    expect(_favorite.fields.LearningObjectID).to.equal(learningObject.ID)
  })

  it('gets favorite objects for a user', async () => {
    const _favorites = await getObjectFavoritesForUser(AdminUserId)

    for (let i = 0; i < _favorites.length; i++) {
      expect(_favorites[i].fields.UserID).to.equal(AdminUserId)
    }
  })

  it('get user favorites for a learning object', async () => {
    const _favorites = await getUserFavoritesForObject(learningObject.ID!)

    for (let i = 0; i < _favorites.length; i++) {
      expect(_favorites[i].fields.LearningObjectID).to.equal(learningObject.ID)
    }
  })

  it('should fail creating the same object/user combo', async () => {
    try {
      await create(new LearningObjectFavoriteModel({
        LearningObjectID: learningObject.ID,
        UserID: AdminUserId
      }))
      throw new Error('Created a duplicate user learning object favorite')
    } catch (error) {};
  })

  it('deletes the user learning object favorite', async () => {
    await remove(learningObject.ID!, AdminUserId)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningObject>(pool.request(), LearningObjectsTableName, { ID: learningObject.ID })
  })
})
