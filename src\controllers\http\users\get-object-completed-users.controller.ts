import logger from '@lcs/logger'
import { Request, Response } from 'express'
import { getCompletedUserIDs } from '../../../services/mssql/learning-objects/get-completion-user-ids.service.js'
import searchCompletedProgressForObject from '../../../services/mssql/learner-progress/search-user-completed-object.service.js'
import getNumberOfCompletions from '../../../services/mssql/learner-progress/get-user-completion-count-for-object.service.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodDates, zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-completed-learning-object-users', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { offset, limit, search, groupIDs } = z.object({
      offset: zodOffset,
      limit: zodLimit,
      search: z.string().optional(),
      groupIDs: z.array(zodGUID).optional()
    }).parse(req.body)
    
    const { from, to } = z.object({
      from: zodDates.optional(),
      to: zodDates.optional()
    }).parse(req.query)

    const { id } = z.object({ id: zodGUID }).parse(req.params)

    const userIds = await getCompletedUserIDs(offset, limit, id, from, to, search, groupIDs)

    const users = await Promise.all(userIds.ids.map(async userId => {
      const completionRecord = await searchCompletedProgressForObject(userId, id, to)
      const completions = await getNumberOfCompletions(userId, id, from, to)

      return {
        UserID: userId,
        CompletedOn: completionRecord.fields.CompletedDate,
        LessonStatusID: completionRecord.fields.LessonStatusID,
        RawScore: completionRecord.fields.RawScore,
        GradeTypeID: null,
        StartedOn: completionRecord.fields.CreatedOn,
        Completions: completions
      }
    }))

    log('info', 'Successfully fetched completed learning object users', {
      search,
      groupIDs,
      from,
      to,
      objectID: id,
      count: users.length,
      totalRecords: userIds.totalRecords,
      success: true,
      req
    })

    res.json({
      totalRecords: userIds.totalRecords,
      users
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get completed users for learning object: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to get completed users for learning object', { objectID: req.params.id, error, req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
