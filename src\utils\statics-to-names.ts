import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'

export function getLearningObjectType (key: number): string {
  switch (key) {
    case LearningObjectTypes.PPT: return 'PPT'
    case LearningObjectTypes.DOC: return 'DOC'
    case LearningObjectTypes.CBT: return 'eLearning'
    case LearningObjectTypes.AR: return 'AR'
    case LearningObjectTypes.VR: return 'VR'
    case LearningObjectTypes.Video: return 'Video'
    case LearningObjectTypes.PDF: return 'PDF'
    case LearningObjectTypes.Text: return 'Text'
    case LearningObjectTypes.Image: return 'Image'
    case LearningObjectTypes.ExternalURL: return 'External URL'
    case LearningObjectTypes.Audio: return 'Audio'
    default: return 'Unknown File Type'
  }
}

export function getLearningContextType (key: number): string {
  switch (key) {
    case LearningContextTypes.Course:
    case LearningContextTypes.CMI5Course:
      return 'Course'
    case LearningContextTypes.Section: return 'Section'
    case LearningContextTypes.Path: return 'Path'
    case LearningContextTypes.Collection: return 'Collection'
    case LearningContextTypes.ElectiveOptional: return 'Elective / Optional'
    case LearningContextTypes.CMI5Block: return 'Block'
    default: return 'Unknown Folder Type'
  }
}

export function getLearningProgressName (status: number): string {
  switch (status) {
    case LessonStatuses.notAttempted:
      return 'Not Attempted'
    case LessonStatuses.browsed:
      return 'Browsed'
    case LessonStatuses.incomplete:
      return 'Incomplete'
    case LessonStatuses.fail:
      return 'Failed'
    case LessonStatuses.passed:
      return 'Passed'
    case LessonStatuses.completed:
      return 'Completed'
    default:
      return 'Unknown'
  }
}
