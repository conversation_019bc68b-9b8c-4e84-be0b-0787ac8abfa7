# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/) and this project adheres to [Semantic Versioning](https://semver.org/).

## [Unreleased](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/compare/v2.1.3...development)

### Added

### Changed

### Deprecated

### Removed

### Fixed

### Security

## [v2.1.3](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/compare/v2.1.3...v2.1.2)

### Added

- tess-f backend utils library (contains shared code for utilities such as clamping numbers, parsing error messages/stack trace, http log transforms, etc.)
- cookieSecure and cookieSigned settings
- Input validation
- Prometheus metrics endpoint at `/metrics`
- User input validation and data parsing
- Support for obsolete status on learning objects and learning contexts
- isReferenced filter for get paginated learning objects query

### Changed

- Updated http controller logging to use httpLogTransformer from tess-f backend utils. All http controller and middlewares logs will now contain the following data:
  + ip address of incoming request
  + active user id
  + request headers
  + request method
  + active user session id
  + error message - for error objects
  + error stack trace - for error objects
- Update error responses so that they do not include any error data

### Removed

- Local utils, updated to tess-f backend utils library.

### Security

- Added claims checks for http routes
- Updated Express to v5

## [v2.1.2](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/compare/v2.1.2...v2.1.1)

### Fixed

- Fixed the get learning contexts that use ilt session and have exam or survey query so that it properly returns the expected data.
- Fixed the exam ready emails for folders to address issue where the href quotes where not properly terminated.

## [v2.1.1](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/compare/v2.1.1...v2.1.0)

### Fixed

- Fixed query for fetching related learning context records

### Security

- Updated express to latest.

## [v2.1.0](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/compare/v2.1.0...v2.0.0)

### Added

- Added connections to the Objectives Module. When a list of objective ids is passed in with a learning object or learning context on creation or update the system call the objective service to make a connections between the lms the objective service.
- Added route to check if a user has satisfied all required prerequisites for a context
- Added ability to use a learning object as a prerequisite to a learning context
- Added ability to designate alternatives for a prerequisite
- Added route to check the status of a given prerequisite
- Added prerequisites to learning contexts returned when fetching a learning context
- Added route to check if content in a folder is locked.
  - If the content is cmi5:
    - blocks will not be locked
    - au's will be locked if the previous content has not been completed
  - If the content is not cmi5 and sequencing is enforced
    - the route will check for the content in the given folder
    - if any content prior to the item is not complete the content will be locked
- Added enforced content sequencing flag to learning contexts
- Added route to check if content in a learning context is locked

### Changed

### Deprecated

### Removed

### Fixed

- Fixed the creation of learning context object assignments
- default home image location

### Security


## [v2.0.0](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/compare/v2.0.0...v1.0.20)

### Added

- Added system notifications
- chart theme and home page image are now contained in the service config
  - fields can be updated the process env vars `CHART_THEME` and `HOME_IMAGE`
- Updated to work with new system config service

### Security

- Updated session authority and added an override for the ws package to address security vulnerabilities.

## [v1.0.20](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/compare/v1.0.20...v1.0.19)

### Security

- Updated upstream to ubi9 micro nodejs 20.14.0, resolves vulnerabilities

## [v1.0.19](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/compare/v1.0.19...v1.0.18)

### Added

- Added a route to download all SCORM 1.2 interaction data. The resulting file contains a sheet with all the interaction data mapped to the SCORM 1.2 file it came from and 1 sheet per SCORM 1.2 file containing the interaction data for just that file.

### Fixed

- User learning metadata certificate count (uses certificate view to generate count)
- Updating an assignment from everyone to selected users
- Updating the users in a given assignment


## [v1.0.17](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/relases/tag/v1.0.17)

### Added

- Http route to get or create cmi5 course registrations
- mssql service to get the id of the course node of a cmi5 au
- mssql service to get or create cmi5 course registration records
- type casting of request controllers to resolve issues with sonarqube bug reports of misuse of promise resolves
- routes and service to determine if a user has met the criteria to launch an au

### Changed

- Updated routing to remove duplication of route bases (a.k.a api)
- Updated the LRS create statement exchange controller to update the associated learner progress record with the appropriate status and mark the AU as satisfied when the move on criteria for the AU has been met.
- Updated the learning context grading logic to account for checking the completion of AU learner progress records.
- Added logic to the grade context service to issue certificates for cmi5 blocks and courses when enabled
- Added logic to the grade context service to create context completion records for cmi5 bocks and courses
- Added logic to the grade context service to issue satisfied statements for cmi5 bock and course elements
- Added logic to the grade context service to mark context registrations as complete when the cmi5 course is completed
- Added logic to the get or create context registration to mark AU with a move on criteria of not applicable as satisfied when the cmi5 course registration is created
- Updated the LRS create statement exchange controller to grade the contexts associated with au when the au's status is updated
- Updated Common Table Expressions to use new database views where appropriate
- Added function to make user names and content descriptions on certificates more responsive. The function will attempt to add the text in the largest font possible without wrapping the text to a new line.
- Updated the default certificate background (note this background is specific to CSMU)

### Removed

- tests for deprecated or removed services

### Fixed

- Fixed unit tests

## [v1.0.16](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/relases/tag/v1.0.16)

### Change

- Update files page to not fetch/display CMI5 AU objects

### Fixed

- Fixed assignment content creation/update with due dates

## [v1.0.15](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/relases/tag/v1.0.15)

### Fixed

- Fixed learning context start date service bug

## [v1.0.14](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/relases/tag/v1.0.14)

### Fixed

- Fixed logger to work with elasticsearch v7

## [v1.0.13](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/relases/tag/v1.0.13)

### Added

* Added a flag to the search content service for all content. This flag should be set to true only when searching content internally or for searches that should return all content regardless of visibility.
* Updated the content overview controller to search for all content not just browsable content

### Removed

- Removed the check for account names when processing xAPI statements received from the LRS

### Fixed

- Updated various logging statements to address errors when logging
- Updated the logging package to the latest version which addresses issues with killing the active process when elasticsearch returns an error

### Security

- updated dependencies
- updated upstream image to UBI 8 with Node.JS 20

## [v1.0.11](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/relases/tag/v1.0.11)

### Fixed

* Resolved issue with column grouping for new special instructions column for learning contexts.

## [v1.0.10](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/relases/tag/v1.0.10)

### Added

* Added the ability to notify admin(s) of session enrollments
* Added PDFKit package to replace the vulnerable PDFMake

### Updated

* Updated sql package for the addition of the Learning Context Special Instructions field

### Removed

* Removed dependency on PDFMake

## [v1.0.9](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/relases/tag/v1.0.9)

### Changed

* Updated MSSQL settings to use new shared config properties

## [v1.0.8](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/releases/tag/v1.0.8)

### Changed

* Added `SessionCount` to the sort order of the upcoming dates widget so that user defined sort orders do not cause the contexts with sessions to fall off the query.

## [v1.0.7](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/releases/tag/v1.0.7)

### Added

* Added config variable to make assignment dues required. If `REQUIRE_ASSIGNMENT_DUE_DATE` is set to 'true' the assignments model will require the due date to be set on creation. This setting is available to the UI via the service config endpoint, this will allow the UI to know if assignment due dates are enforced and require the field to be set. The default behavior is that assignment due dates are not required.
* Added endpoint to get service config, returns if assignment due dates are required.

### Changed

* Update retrieve labels functions to retrieve labels using Context Types
* Updated the update assignment controller to send emails to users that have been added to an assignment
* Updated the update assignment service to update the due date of current assigned content
* Updated the update assignment controller to set the deleted on date to null when the assignment is updated
* Updated the update assignment controller to set the notification sent flag to false on update

## [v1.0.5](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/releases/tag/v1.0.5)

Added learning context extra mapper to context children controller to map keywords. This fixes a bug when removing a learning context from a folder the update causes the keywords to be removed.

## [v1.0.4](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/releases/tag/v1.0.4)

Updated logging for tracing errors/problems with creating user assigned learning object/contexts.

## [v1.0.3](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/releases/tag/v1.0.3)

### Fixed

Fixed the data object labels for errors, when the error is a string message set the data property to errorMessage.

## [v1.0.2](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/releases/tag/v1.0.2)

### Fixed

Fixed a bug with the system widgets route where if a system widget did not have an audience the get multi route would return an error, causing the ui page to fail to load.

## [v1.0.1](https://github.northgrum.com/LCS-TESS/TESS-LMS-Server/releases/tag/v1.0.1)

### Fixed

Fixed the learning object sort order to sort by the date created in descending order then by date modified in descending order, then by title in ascending order. This will keep the most recently created learning object (or file as labeled in the UI) at the top of the list by default.
