import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import create from './create.service.js'
import get, { getSessionsForContext } from './get.service.js'
import getEnrolledSessionsForUser from './get-enrolled-sessions-for-user.service.js'
import getPastForContext from './get-past-for-context.service.js'
import getSessionsWithProgress from './get-sessions-with-progress.service.js'
import getUpcomingForContext from './get-upcoming-for-context.service.js'
import update from './update.service.js'
import remove from './delete.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'

let learningContext: LearningContext
let learningContextSession: LearningContextSessionModel

describe('MSSQL Learning Context Sessions', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test context sessions',
      Description: `Running learning-context-sessions on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Course
    }))
  })

  it('Should create a new learning object rating object', async () => {
    learningContextSession = await create(new LearningContextSessionModel({
      SessionID: 'Service-Test-1',
      DisplayEnrollmentsOnHomePage: false,
      SessionStatusID: 1,
      CreatedOn: new Date(),
      CreatedBy: AdminUserId,
      LearningContextID: learningContext.ID,
      Timezone: 'MST',
      StartDate: new Date(),
      EndDate: new Date()
    }))
    expect(learningContextSession).to.not.be.eq(undefined)
  })

  it('Should get user rating by ID', async () => {
    const results = await get(learningContextSession.fields.ID!)
    expect(results.fields.CreatedBy).to.be.eq(AdminUserId)
    expect(results.fields.LearningContextID).to.be.eq(learningContext.ID)
  })

  it('Should get sessions for context', async () => {
    const results = await getSessionsForContext(learningContext.ID!)
    expect(results.length).to.be.gte(0)
  })

  it('Should get all enrolled sessions for user', async () => {
    const results = await getEnrolledSessionsForUser(learningContext.ID!, AdminUserId)
    expect(results.length).to.be.gte(0)
  })

  it('Should get past for context', async () => {
    const results = await getPastForContext(learningContext.ID!, 1, 150)
    expect(results.totalRecords).to.be.gte(0)
  })

  it('Should get sessions with progress', async () => {
    try {
      const results = await getSessionsWithProgress(learningContext.ID!, AdminUserId)
      expect(results.length).to.be.gte(0)
    } catch (error) {
      expect(error).to.exist
    }
  })

  it('Should get upcoming for context', async () => {
    try {
      const results = await getUpcomingForContext(learningContext.ID!)
      expect(results.length).to.be.gte(0)
    } catch (error) {
      expect(error).to.exist
    }
  })

  it('Should update the learning context object', async () => {
    learningContextSession.fields.ModifiedBy = AdminUserId
    learningContextSession.fields.ModifiedOn = new Date()

    const results = await update(learningContextSession)
    expect(results.fields.ModifiedBy).to.be.eq(AdminUserId)
    expect(results.fields.CreatedBy).to.be.eq(AdminUserId)
  })

  it('Should remove the learning context object from DB', async () => {
    const results = await remove(learningContextSession.fields.ID!)
    expect(results).to.be.eq(1)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { ID: learningContext.ID })
  })
})
