import { Table } from '@lcs/mssql-utility'
import { Keyword, KeywordFields, KeywordsTableName } from '@tess-f/sql-tables/dist/lms/keyword.js'
import { z } from 'zod'

export const createKeywordSchema = z.object({
  Name: z.string().max(50)
})

export default class KeywordModel extends Table<Keyword, Keyword> {
  public fields: Keyword

  constructor (fields?: Keyword) {
    super(KeywordsTableName, [
      KeywordFields.Name
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: Keyword): void {
    this.fields = record
  }

  public exportJsonToDatabase (): Keyword {
    return this.fields
  }
}
