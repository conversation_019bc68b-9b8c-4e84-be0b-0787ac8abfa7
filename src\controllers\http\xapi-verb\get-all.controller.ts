import { Request, Response } from 'express'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
import getXAPIVerbs from '../../../services/mssql/xapi-verb/get-all.service.js'
import { httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('Controller-HTTP.get-all-xapi-verbs', httpLogTransformer)

export default async function (req: Request, res: Response): Promise<void> {
  try {
    const verbs = await getXAPIVerbs()
    log('info', 'Successfully retrieved all xapi verbs', { count: verbs.length, success: true, req })
    res.json(verbs.map(verb => verb.fields))
  } catch (error) {
    log('error', 'Failed to get all xapi verbs', { error, success: false, req })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
