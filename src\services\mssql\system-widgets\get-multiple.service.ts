import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { SystemWidgetGroupFields, SystemWidgetGroupsTableName } from '@tess-f/sql-tables/dist/lms/system-widget-group.js'
import { SystemWidget, SystemWidgetFields, SystemWidgetsTableName } from '@tess-f/sql-tables/dist/lms/system-widget.js'
import { WidgetFields, WidgetsTableName } from '@tess-f/sql-tables/dist/lms/widget.js'
import { Request } from 'mssql'
import SystemWidgetModel from '../../../models/system-widget.model.js'

export default async function getMultipleSystemWidgets (offset: number = 0, limit: number = 10, search?: string, groupIDs?: string[], everyone?: boolean, published?: boolean): Promise<{ totalRecords: number, systemWidgets: SystemWidgetModel[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  let query = `
    SELECT *, TotalRecords = COUNT(*) OVER()
    FROM [${SystemWidgetsTableName}]
    WHERE 1 = 1
  `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [SystemWidgetFields.Title], 'any')}) `
  }

  if (groupIDs && groupIDs.length > 0 && everyone !== undefined) {
    const conds = groupIDs.map((id, index) => {
      request.input(`group_${index}`, id)
      return `@group_${index}`
    })
    request.input('everyone', everyone)
    query += `
      AND ([${SystemWidgetFields.ID}] IN (
        SELECT [${SystemWidgetGroupFields.SystemWidgetID}]
        FROM [${SystemWidgetGroupsTableName}]
        WHERE [${SystemWidgetGroupFields.GroupID}] IN (
          ${conds.join(', ')}
        )
      ) OR [${SystemWidgetFields.Everyone}] = @everyone)
    `
  } else if (everyone !== undefined) {
    request.input('everyone', everyone)
    query += `AND ([${SystemWidgetFields.Everyone}] = @everyone) `
  } else if (groupIDs && groupIDs.length > 0) {
    const conds = groupIDs.map((id, index) => {
      request.input(`group_${index}`, id)
      return `@group_${index}`
    })
    query += `
      AND ([${SystemWidgetFields.ID}] IN (
        SELECT [${SystemWidgetGroupFields.SystemWidgetID}]
        FROM [${SystemWidgetGroupsTableName}]
        WHERE [${SystemWidgetGroupFields.GroupID}] IN (
          ${conds.join(', ')}
        )
      ))
    `
  }

  if (published !== undefined) {
    request.input('published', published)
    query += `AND ([${SystemWidgetFields.Published}] = @published)`
  }

  // add offset and limit
  request.input('offset', offset)
  request.input('limit', limit)

  query += `
    ORDER BY [${SystemWidgetFields.Title}] ASC
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<SystemWidget & {TotalRecords: number}>(query)

  // map the widget count
  const widgets = await Promise.all(results.recordset.map(async record => {
    const widget = new SystemWidgetModel(record)
    widget.fields.WidgetCount = await getWidgetCount(pool.request(), record.ID!)
    return widget
  }))

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    systemWidgets: widgets
  }
}

async function getWidgetCount (request: Request, systemID: string): Promise<number> {
  request.input('systemID', systemID)
  const records = await request.query<{ count: number }>(`
    SELECT COUNT(*) as count
    FROM [${WidgetsTableName}]
    WHERE [${WidgetFields.CreatedBy}] = @systemID
  `)

  if (records.recordset.length > 0) {
    return records.recordset[0].count
  } else {
    return 0
  }
}
