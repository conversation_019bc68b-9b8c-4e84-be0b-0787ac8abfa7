import mssql, { DB_Errors } from '@lcs/mssql-utility'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import { LearningContextSession, LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { SessionStatuses } from '@tess-f/sql-tables/dist/lms/session-status.js'

/**
 * Returns a list of upcoming sessions for a learning context
 * @param {string} contextID
 * @returns {LearningContextSessionModel[]}
 */
export default async function (contextID: string): Promise<LearningContextSessionModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  const query = `
      SELECT *
      FROM [${LearningContextSessionsTableName}]
      WHERE [${LearningContextSessionFields.LearningContextID}] = @contextID
      AND [${LearningContextSessionFields.StartDate}] >= GETDATE()
      AND [${LearningContextSessionFields.SessionStatusID}] = ${SessionStatuses.Open}
  `

  request.input('contextID', contextID)
  const results = await request.query<LearningContextSession>(query)

  if (results.recordset.length <= 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  } else {
    return results.recordset.map(record => new LearningContextSessionModel(undefined, record))
  }
}
