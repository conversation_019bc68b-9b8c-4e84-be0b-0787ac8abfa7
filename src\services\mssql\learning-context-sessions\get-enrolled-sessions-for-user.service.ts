import mssql from '@lcs/mssql-utility'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import { LearningContextSession, LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { SessionEnrollmentFields, SessionEnrollmentsTableName } from '@tess-f/sql-tables/dist/lms/session-enrollment.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'

export default async function getEnrolledSessionsForUser (contextId: string, userId: string): Promise<LearningContextSessionModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('contextId', contextId)
  request.input('userId', userId)

  const query = `
    SELECT *
    FROM [${LearningContextSessionsTableName}]
    WHERE [${LearningContextSessionFields.ID}] IN (
      SELECT [${SessionEnrollmentFields.SessionID}]
      FROM [${SessionEnrollmentsTableName}]
      WHERE [${SessionEnrollmentFields.UserID}] = @userId
    ) AND [${LearningContextSessionFields.LearningContextID}] = @contextId
    AND [${LearningContextSessionFields.ID}] NOT IN (
      SELECT [${LearnerProgressFields.LearningContextSessionID}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.UserID}] = @userId
      AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.fail}
      AND [${LearnerProgressFields.LearningContextSessionID}] IS NOT NULL
    )
  `

  const results = await request.query<LearningContextSession>(query)

  return results.recordset.map(record => new LearningContextSessionModel(undefined, record))
}
