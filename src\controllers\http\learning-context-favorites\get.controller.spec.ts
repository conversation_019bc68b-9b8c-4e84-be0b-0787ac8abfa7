import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextFavoritesModel from '../../../models/learning-context-user-favorite.model.js'


describe('HTTP Delete controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-context-favorites/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextFavoritesModel()))
            }
        })

        const mocks = httpMocks.createMocks({
            query: {
                contextID: uuid(),
                userID: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
        
    })

    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-context-favorites/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextFavoritesModel()))
            }
        })

        const mocks = httpMocks.createMocks({
            query: {
                contextID: false,
                userID: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request parameter data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('contextID')
        expect(mocks.res._getData()).include('userID')
        
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-context-favorites/get.service.js': {
                default: Sinon.stub().returns(Promise.reject(new LearningContextFavoritesModel()))
            }
        })

        const mocks = httpMocks.createMocks({
            query: {
                contextID: uuid(),
                userID: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
        
    })

    

    

})