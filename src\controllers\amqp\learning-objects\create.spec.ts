import logger from '@lcs/logger'
import LearningObjectModel from '../../../models/learning-object.model.js'
import Sinon from 'sinon'
import { expect } from 'chai'
import { v4 as uuid } from 'uuid'
import esmock from 'esmock'

describe('Controller.AMQP: create learning object', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns unsuccessful when the request data is missing', async () => {
    const controller = await esmock('./create.js')
    const message: any = { command: 'test', data: undefined }
    const response = await controller(message)
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('Required')
  })

  it('returns unsuccessful when the request data is missing required fields', async () => {
    const controller = await esmock('./create.js')
    const response = await controller({
      command: 'test',
      data: {
        EnableCertificates: true,
        URL: 'index.html',
        Keywords: ['test'],
        VisibilityID: 1
      }
    })
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('Title: Required')
    expect(response.message).to.include('Description: Required')
    expect(response.message).to.include('LearningObjectTypeID: Required')
  })

  it('returns unsuccessful when the request created by field is missing', async () => {
    const controller = await esmock('./create.js')
    const response = await controller({
      command: 'test',
      data: {
        EnableCertificates: true,
        URL: 'index.html',
        Keywords: ['test'],
        VisibilityID: 1,
        Title: 'Test',
        Description: 'Test',
        LearningObjectTypeID: 1,
      }
    })
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('CreatedBy: Required')
  })

  it('returns unsuccessful when the request created by field is not a uuid', async () => {
    const controller = await esmock('./create.js')
    const response = await controller({
      command: 'test',
      data: {
        EnableCertificates: true,
        URL: 'index.html',
        Keywords: ['test'],
        VisibilityID: 1,
        Title: 'Test',
        Description: 'Test',
        LearningObjectTypeID: 1,
        CreatedBy: 'test'
      }
    })
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('CreatedBy: Invalid')
  })

  it('returns unsuccessful when the request data is invalid', async () => {
    const controller = await esmock('./create.js')
    const response = await controller({
      command: 'test',
      data: {
        Title: ['Test'],
        Description: true,
        LearningObjectTypeID: 156,
        MinutesToComplete: 'test',
        VisibilityID: '1',
        EnableCertificates: 'true',
        URL: 123,
        LearningObjectSubTypeID: '1',
        SystemId: '123456789012345678901234567890123456789012345678901234567890',
        Keywords: 'test',
        ObjectiveIds: ['test']
      }
    })
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('Title: Expected string, received array')
    expect(response.message).to.include('Description: Expected string, received boolean')
    expect(response.message).to.include('LearningObjectTypeID: Invalid enum value. Expected 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16, received \'156\'')
    expect(response.message).to.include('MinutesToComplete: Expected number, received string')
    expect(response.message).to.include('VisibilityID: Invalid enum value. Expected 1 | 2 | 3, received \'1\'')
    expect(response.message).to.include('EnableCertificates: Expected boolean, received string')
    expect(response.message).to.include('URL: Expected string, received number')
    expect(response.message).to.include('LearningObjectSubTypeID: Invalid enum value. Expected 1 | 2 | 3, received \'1\'')
    expect(response.message).to.include('SystemId: String must contain at most 50 character(s)')
    expect(response.message).to.include('Keywords: Expected array, received string')
    expect(response.message).to.include('ObjectiveIds.0: Invalid')
  })

  it('returns the created object', async () => {
    const controller = await esmock('./create.js', {
      '../../../services/mssql/learning-objects/create.service.js': {
        default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({
          ID: uuid(),
          Title: 'Test',
          Description: 'Test',
          LearningObjectTypeID: 1,
          CreatedBy: uuid(),
          CreatedOn: new Date(),
          ContentID: uuid()
        })))
      }
    })
    
    const response = await controller({
      command: 'test',
      data: {
        Title: 'Test',
        Description: 'Test',
        LearningObjectTypeID: 1,
        CreatedBy: uuid(),
        ContentID: uuid()
      }
    })
    expect(response.success).to.be.true
    expect(response.data).to.exist
    expect(response.data?.ID).to.exist
  })

  it('returns unsuccessful when the service encounters an error', async () => {
    const controller = await esmock('./create.js', {
      '../../../services/mssql/learning-objects/create.service.js': {
        default: Sinon.stub().returns(Promise.reject(new Error('Service Error')))
      }
    })
    const response = await controller({
      command: 'test',
      data: {
        Title: 'Test',
        Description: 'Test',
        LearningObjectTypeID: 1,
        CreatedBy: uuid(),
        ContentID: uuid()
      }
    })
    expect(response.success).to.be.false
    expect(response.message).to.equal('Service Error')
  })
})
