import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { AssignmentsWithStatusView, AssignmentsWithStatusViewFields, AssignmentsWithStatusViewName } from '@tess-f/sql-tables/dist/lms/assignments-with-status-view.js'
import AssignmentsModel from '../../../models/assignment.model.js'
import { isValidString, isSortDirectionValid } from '@tess-f/backend-utils/validators'

/**
 * Gets a paginated list of assignments with status
 * @param {number} offset - the number of records to offset
 * @param {number} limitby - the number of records to limit the search to
 * @param {string} search - the value to search for (title)
 * @param {date} dueDate - the due date of the assignment
 * @param {number[]} status - the assignment status to search for
 * @param {boolean | null} missingSessions - search only for assignments that are missing sessions
 * @param {string} sortColumn - the column to sort the data on
 * @param {string} sortDirection - the direction of the sort 'ASC' || 'DESC'
 * @returns { { totalRecords: number, assignments: AssignmentWithStatusView[] } }
 */
export default async function (offset: number = 0, limitby: number = 10, search?: string, dueDate?: Date, status?: number[], missingSessions?: boolean, sortColumn?: string, sortDirection?: string): Promise<{ totalRecords: number, assignments: AssignmentsModel[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  // add offset and limit params to the request
  request.input('Offset', offset)
  request.input('Limitby', limitby)

  // build query
  let query = `SELECT *, TotalRecords = Count(*) OVER() FROM [${AssignmentsWithStatusViewName}] WHERE 1 = 1 `

  // add search to query if one exists
  if (search) {
    query += `AND (${parseSearchTerms(request, search, [AssignmentsWithStatusViewFields.Title], 'any')})`
  }

  if (dueDate) {
    const startDate = new Date(dueDate)
    startDate.setHours(0, 0, 0, 0)
    const endDate = new Date(dueDate)
    endDate.setHours(23, 59, 59, 0)
    request.input('startDate', startDate)
    request.input('endDate', endDate)
    query += `AND ( [${AssignmentsWithStatusViewFields.DueDate}] BETWEEN @startDate AND @endDate ) `
  }

  if (status && status.length > 0) {
    query += 'AND ('
    status.forEach((id, index) => {
      request.input(`statusID_${index}`, id)
      query += `[${AssignmentsWithStatusViewFields.Status}] = @statusID_${index} ${index + 1 < status.length ? 'OR' : ''} `
    })
    query += ') '
  }

  if (missingSessions !== undefined) {
    query += `AND [${AssignmentsWithStatusViewFields.WithoutSessions}] = @missingSessions `
    request.input('missingSessions', missingSessions)
  }

  // order, limit, and offset query
  query += 'ORDER BY '
  if (sortColumn && sortDirection &&
    isValidString(
      sortColumn,
      [AssignmentsWithStatusViewFields.Title,
        AssignmentsWithStatusViewFields.DueDate,
        AssignmentsWithStatusViewFields.CreatedOn,
        AssignmentsWithStatusViewFields.Status,
        AssignmentsWithStatusViewFields.WithoutSessions
      ]) &&
    isSortDirectionValid(sortDirection)) {
    query += `[${sortColumn}] ${sortDirection}`
  } else {
    query += `[${AssignmentsWithStatusViewFields.CreatedOn}] DESC, [${AssignmentsWithStatusViewFields.Title}]`
  }

  query += `
    OFFSET @Offset ROWS
    FETCH FIRST @Limitby ROWS ONLY
  `

  const results = await request.query<AssignmentsWithStatusView & { TotalRecords: number }>(query)

  // return the results
  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    assignments: results.recordset.map(record => new AssignmentsModel(record))
  }
}
