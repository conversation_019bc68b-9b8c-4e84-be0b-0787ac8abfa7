import { Table } from '@lcs/mssql-utility'
import { WidgetLearningContext, WidgetLearningContextFields, WidgetLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/widget-learning-context.js'

export default class WidgetLearningContextModel extends Table<WidgetLearningContext, WidgetLearningContext> {
  public fields: WidgetLearningContext

  constructor (fields?: WidgetLearningContext) {
    super(WidgetLearningContextsTableName, [
      WidgetLearningContextFields.WidgetID,
      WidgetLearningContextFields.LearningContextID,
      WidgetLearningContextFields.OrderID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: WidgetLearningContext): void {
    this.fields = record
  }

  public exportJsonToDatabase (): WidgetLearningContext {
    return this.fields
  }
}
