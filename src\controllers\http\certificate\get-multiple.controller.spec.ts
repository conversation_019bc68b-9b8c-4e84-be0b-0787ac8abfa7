import logger from '@lcs/logger'
import httpStatus from 'http-status'
import Sinon from 'sinon'
import { expect } from 'chai'
import httpMock from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: get multiple certificates', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('it should get multiple certificates for the current user when the for user id is not a valid uuid', async () => {
    const userId = uuid()
    const getCertificatesStub = Sinon.stub().resolves([])
    const mocks = httpMock.createMocks({ query: { for: 'test' }, session: { userId } })
    const controller = await esmock('./get-multiple.controller.js', {
      '../../../services/mssql/certificate/get-multiple.service.js': {
        default: getCertificatesStub
      }
    })
    await controller(mocks.req, mocks.res)
    expect(getCertificatesStub.calledWith(userId)).to.be.true
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
  })

  it('it should get multiple certificates for requested user', async () => {
    const userId = uuid()
    const getCertificatesStub = Sinon.stub().resolves([])
    const forUser = uuid()
    const mocks = httpMock.createMocks({ query: { for: forUser }, session: { userId } })
    const controller = await esmock('./get-multiple.controller.js', {
      '../../../services/mssql/certificate/get-multiple.service.js': {
        default: getCertificatesStub
      }
    })
    await controller(mocks.req, mocks.res)
    expect(getCertificatesStub.calledWith(forUser)).to.be.true
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
  })
})
