import logger from '@lcs/logger'
import mssql, { addRow } from '@lcs/mssql-utility'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import { LearningContextSession } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service-MSSQL.create-multiple-widget-order-override-service')

export default async function (session: LearningContextSessionModel): Promise<LearningContextSessionModel> {
  try {
    const pool = mssql.getPool()
    const record = await addRow<LearningContextSession>(pool.request(), session)
    return new LearningContextSessionModel(undefined, record)
  } catch (error) {
    log('error', 'Unexpected error', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}
