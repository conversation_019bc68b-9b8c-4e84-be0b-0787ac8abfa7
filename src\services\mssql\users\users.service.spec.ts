import { expect } from 'chai'
import mssql, { getRows } from '@lcs/mssql-utility'
import getAllUserIds from './get-all-user-ids.service.js'
import getIdsOfMyDirectReports from './get-ids-of-my-direct-reports.service.js'
import getIdsOfUsersInGroup from './get-ids-of-users-in-group.service.js'
import getPaginatedTeam from './get-paginated-team.service.js'
import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import { AdminUserId, User, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { UserGroup, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'

let user: User
let groupId: string

describe('MSSQL Users Service', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    user = (await getRows<User>(UserTableName, pool.request(), { ID: AdminUserId }))[0]
    groupId = (await getRows<UserGroup>(UserGroupTableName, pool.request(), { UserID: AdminUserId }))[0].GroupID!
  })

  it('Unit test to get all user ids', async () => {
    const results = await getAllUserIds()
    expect(results.length).to.be.gte(1)
  })

  it('Unit test to get ids of direct reports', async () => {
    try {
      const results = await getIdsOfMyDirectReports(user.ManagerID!)
      expect(results.length).to.be.gte(0)
    } catch (error) {
      expect(error).to.exist
    }
  })

  it('Unit test to get ids of direct reports', async () => {
    try {
      const results = await getIdsOfUsersInGroup(groupId)
      expect(results.length).to.be.gte(0)
    } catch (error) {
      expect(error).to.exist
    }
  })

  it('Unit test to get paginated team', async () => {
    const results = await getPaginatedTeam(user.ManagerID!, 0, 50)
    expect(results.totalRecords).to.be.gte(0)
  })
})
