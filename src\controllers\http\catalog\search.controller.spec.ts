import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, OK } = httpStatus
import Sinon from 'sinon'
import { expect } from 'chai'
import httpMock from 'node-mocks-http'
import esmock from 'esmock'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContextJson } from '@tess-f/lms/dist/common/learning-context.js'

describe('HTTP-Controller: search catalog', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns a bad request when the request body does not contain a search', async () => {
    const mocks = httpMock.createMocks()
    const controller = await esmock('./search.controller.js')
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(BAD_REQUEST)
    expect(mocks.res._getData()).to.include('Invalid request data:')
    expect(mocks.res._getData()).to.include('search: Required')
  })

  it('returns a bad request when the search is not a string', async () => {
    const mocks = httpMock.createMocks({ body: { search: 1 } })
    const controller = await esmock('./search.controller.js')
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(BAD_REQUEST)
    expect(mocks.res._getData()).to.include('Invalid request data:')
    expect(mocks.res._getData()).to.include('search: Expected string, received number')
  })

  it('returns the catalog items from the search request', async () => {
    const mocks = httpMock.createMocks({ body: { search: 'test' } })
    const controller = await esmock('./search.controller.js', {
      '../../../services/mssql/catalog/search.service.js': {
        default: Sinon.stub().returns(Promise.resolve({
          totalRecords: 1,
          items: [
            new LearningContextModel({
              ID: 'test',
              Title: 'Test'
            })
          ]
        }))
      }
    })
    
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(OK)
    const data: { totalRecords: number, items: LearningContextJson[] } = JSON.parse(mocks.res._getData())
    expect(data).to.exist
    expect(data.totalRecords).to.equal(1)
    expect(data.items.length).to.equal(1)
    expect(data.items[0].ID).to.exist
  })

  it('returns the catalog items from the search request offset by 10 records', async () => {
    const mocks = httpMock.createMocks({ body: { search: 'test', offset: 10 } })
    const controller = await esmock('./search.controller.js', {
      '../../../services/mssql/catalog/search.service.js': {
        default: Sinon.stub().returns(Promise.resolve({
          totalRecords: 1,
          items: [
            new LearningContextModel({
              ID: 'test',
              Title: 'Test'
            })
          ]
        }))
      }
    })
    
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(OK)
    const data: { totalRecords: number, items: LearningContextJson[] } = JSON.parse(mocks.res._getData())
    expect(data).to.exist
    expect(data.totalRecords).to.equal(1)
    expect(data.items.length).to.equal(1)
    expect(data.items[0].ID).to.exist
  })

  it('returns the catalog items from the search request limited to 1 record', async () => {
    const mocks = httpMock.createMocks({ body: { search: 'test', limit: 1 } })
    const controller = await esmock('./search.controller.js', {
      '../../../services/mssql/catalog/search.service.js': {
        default: Sinon.stub().returns(Promise.resolve({
          totalRecords: 1,
          items: [
            new LearningContextModel({
              ID: 'test',
              Title: 'Test'
            })
          ]
        }))
      }
    })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(OK)
    const data: { totalRecords: number, items: LearningContextJson[] } = JSON.parse(mocks.res._getData())
    expect(data).to.exist
    expect(data.totalRecords).to.equal(1)
    expect(data.items.length).to.equal(1)
    expect(data.items[0].ID).to.exist
  })

  it('returns internal server error when the service fails', async () => {
    const mocks = httpMock.createMocks({ body: { search: 'test', limit: 1 } })
    const controller = await esmock('./search.controller.js', {
      '../../../services/mssql/catalog/search.service.js': {
        default: Sinon.stub().returns(Promise.reject(new Error('Service Error')))
      }
    })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
  })
})
