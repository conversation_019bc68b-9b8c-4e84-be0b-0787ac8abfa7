import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import LearnerProgress, { createLearnerProgressSchema } from '../../../models/learner-progress.model.js'
import { v4 as uuid } from 'uuid'
import exp from 'constants'

describe('HTTP Create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learner-progress/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearnerProgress({  })))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
               UserID: uuid(),
               LearningContextSessionID: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learner-progress/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearnerProgress({  })))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
               UserID: false,
               LearningContextSessionID: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const message = mocks.res._getData()
        expect(message).include('UserID')
        expect(message).include('LearningContextSessionID')
        expect(message).include('Expected string, received boolean')
    })


    

  
})