import mssql, { getRows } from '@lcs/mssql-utility'
import { ScormInteractionsTableName, SCORM_1_2_Interaction } from '@tess-f/sql-tables/dist/lms/scorm-1-2-interaction.js'
import SCORM1_2InteractionModel from '../../../models/scorm-1-2-interaction.model.js'

export default async function getInteractionsForLearnerProgress (learnerProgressId: string): Promise<SCORM1_2InteractionModel[]> {
  const result = await getRows<SCORM_1_2_Interaction>(ScormInteractionsTableName, mssql.getPool().request(), { LearnerProgressID: learnerProgressId })
  return result.map(record => new SCORM1_2InteractionModel(record))
}
