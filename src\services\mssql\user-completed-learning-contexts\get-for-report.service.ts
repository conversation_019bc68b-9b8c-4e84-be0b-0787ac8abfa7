import mssql from '@lcs/mssql-utility'
import { GradeTypeFields, GradeTypeTableName } from '@tess-f/sql-tables/dist/lms/grade-type.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LessonStatusFields, LessonStatusTableName } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'

export default async function (): Promise<{ Label: string, Title: string, UserID: string, Status: string, CompletedOn: Date, RawScore: number, GradeType: string }[]> {
  const pool = mssql.getPool()
  const res = await pool.request().query<{ Label: string, Title: string, UserID: string, Status: string, CompletedOn: Date, RawScore: number, GradeType: string }>(`
    SELECT
      [${LearningContextTableName}].[${LearningContextFields.Label}],
      [${LearningContextTableName}].[${LearningContextFields.Title}],
      [${UserCompletedLearningContextsTableName}].[${UserCompletedLearningContextFields.UserID}],
      [${LessonStatusTableName}].[${LessonStatusFields.Name}] AS [Status],
      [${UserCompletedLearningContextsTableName}].[${UserCompletedLearningContextFields.CompletedOn}],
      [${UserCompletedLearningContextsTableName}].[${UserCompletedLearningContextFields.RawScore}],
      COALESCE([${GradeTypeTableName}].[${GradeTypeFields.Name}], 'N/A') AS [GradeType]
    FROM [${UserCompletedLearningContextsTableName}]
    JOIN [${LearningContextTableName}] ON [${LearningContextTableName}].[${LearningContextFields.ID}] = [${UserCompletedLearningContextsTableName}].[${UserCompletedLearningContextFields.LearningContextID}]
    JOIN [${LessonStatusTableName}] ON [${LessonStatusTableName}].[${LessonStatusFields.ID}] = [${UserCompletedLearningContextsTableName}].[${UserCompletedLearningContextFields.LessonStatusID}]
    LEFT JOIN [${GradeTypeTableName}] ON [${GradeTypeTableName}].[${GradeTypeFields.ID}] = [${UserCompletedLearningContextsTableName}].[${UserCompletedLearningContextFields.GradeTypeID}]
    WHERE [${LearningContextTableName}].[${LearningContextFields.VisibilityID}] = ${Visibilities.Browsable} OR [${LearningContextTableName}].[${LearningContextFields.VisibilityID}] = ${Visibilities.Obsolete}
    ORDER BY [${UserCompletedLearningContextFields.CompletedOn}] ASC
  `)
  return res.recordset
}
