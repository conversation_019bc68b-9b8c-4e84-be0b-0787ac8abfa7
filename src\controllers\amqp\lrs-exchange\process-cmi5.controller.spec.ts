import logger from '@lcs/logger'
import Sinon from 'sinon'
import { v4 as uuid } from 'uuid'
import { CMI5Verbs } from '@tess-f/sql-tables/dist/lrs/verb.js'
import { expect } from 'chai'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import esmock from 'esmock'

describe('AMQP Exchange Controller: handle LRS statement created', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('should not take any action when the verb is not cmi5 completed, passed, or failed', async () => {
    const getProgressStub = Sinon.stub()
    const updateProgressStub = Sinon.stub()
    const gradeStub = Sinon.stub()
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) },
    }, {
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub }
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Satisfied
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      authority: {}
    })
    expect(getProgressStub.called).to.be.false
    expect(updateProgressStub.called).to.be.false
    expect(gradeStub.called).to.be.false
  })

  it('should mark the au satisfied when the move criteria is not applicable', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.resolve(new LearnerProgressModel({
      UserID: uuid(),
      ID: uuid(),
      LearningObjectID: uuid()
    })))
    const updateProgressStub = Sinon.stub()
    updateProgressStub.returns(Promise.resolve(new LearnerProgressModel()))
    const gradeStub = Sinon.stub()
    gradeStub.returns(Promise.resolve())
    const satisfyStub = Sinon.stub()
    satisfyStub.returns(Promise.resolve())
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) },
    }, {
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/amqp/lrs/send-au-satisfied-statement.service.js': { default: satisfyStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learning-objects/get.service.js': { default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), MoveOn: 'NotApplicable', IRI: uuid() }))) }
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Passed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.true
    expect(gradeStub.called).to.be.true
    expect(satisfyStub.called).to.be.true
  })

  it('should mark the au satisfied when the au has no move on criteria', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.resolve(new LearnerProgressModel({
      UserID: uuid(),
      ID: uuid(),
      LearningObjectID: uuid()
    })))
    const updateProgressStub = Sinon.stub()
    updateProgressStub.returns(Promise.resolve(new LearnerProgressModel()))
    const gradeStub = Sinon.stub()
    gradeStub.returns(Promise.resolve())
    const satisfyStub = Sinon.stub()
    satisfyStub.returns(Promise.resolve())
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) }
    }, {
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/amqp/lrs/send-au-satisfied-statement.service.js': { default: satisfyStub },
      '../../../services/mssql/learning-objects/get.service.js': { default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid() })))}
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Passed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.true
    expect(gradeStub.called).to.be.true
    expect(satisfyStub.called).to.be.true
  })

  it('should mark the au satisfied when the move criteria is passed and the verb is passed', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.resolve(new LearnerProgressModel({
      UserID: uuid(),
      ID: uuid(),
      LearningObjectID: uuid()
    })))
    const updateProgressStub = Sinon.stub()
    updateProgressStub.returns(Promise.resolve(new LearnerProgressModel()))
    const gradeStub = Sinon.stub()
    gradeStub.returns(Promise.resolve())
    const satisfyStub = Sinon.stub()
    satisfyStub.returns(Promise.resolve())
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) }
    }, {
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/amqp/lrs/send-au-satisfied-statement.service.js': { default: satisfyStub },
      '../../../services/mssql/learning-objects/get.service.js': { default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid(), MoveOn: 'Passed' })))}
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Passed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.true
    expect(gradeStub.called).to.be.true
    expect(satisfyStub.called).to.be.true
  })

  it('should mark the au satisfied when the move criteria is completed and the verb is completed', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.resolve(new LearnerProgressModel({
      UserID: uuid(),
      ID: uuid(),
      LearningObjectID: uuid()
    })))
    const updateProgressStub = Sinon.stub()
    updateProgressStub.returns(Promise.resolve(new LearnerProgressModel()))
    const gradeStub = Sinon.stub()
    gradeStub.returns(Promise.resolve())
    const satisfyStub = Sinon.stub()
    satisfyStub.returns(Promise.resolve())
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) }
    }, {
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/amqp/lrs/send-au-satisfied-statement.service.js': { default: satisfyStub },
      '../../../services/mssql/learning-objects/get.service.js': { default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid(), MoveOn: 'Completed' })))}
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Completed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.true
    expect(gradeStub.called).to.be.true
    expect(satisfyStub.called).to.be.true
  })

  it('should not mark the au satisfied when the move criteria is completed and the verb is passed', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.resolve(new LearnerProgressModel({
      UserID: uuid(),
      ID: uuid(),
      LearningObjectID: uuid()
    })))
    const updateProgressStub = Sinon.stub()
    updateProgressStub.returns(Promise.resolve(new LearnerProgressModel()))
    const gradeStub = Sinon.stub()
    gradeStub.returns(Promise.resolve())
    const satisfyStub = Sinon.stub()
    satisfyStub.returns(Promise.resolve())
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) }
    }, {
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/amqp/lrs/send-au-satisfied-statement.service.js': { default: satisfyStub },
      '../../../services/mssql/learning-objects/get.service.js': { default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid(), MoveOn: 'Completed' })))}
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Passed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.true
    expect(gradeStub.called).to.be.true
    expect(satisfyStub.called).to.be.false
  })

  it('should not mark the au satisfied when the move criteria is passed and the verb is failed', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.resolve(new LearnerProgressModel({
      UserID: uuid(),
      ID: uuid(),
      LearningObjectID: uuid()
    })))
    const updateProgressStub = Sinon.stub()
    updateProgressStub.returns(Promise.resolve(new LearnerProgressModel()))
    const gradeStub = Sinon.stub()
    gradeStub.returns(Promise.resolve())
    const satisfyStub = Sinon.stub()
    satisfyStub.returns(Promise.resolve())
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) }
    }, {
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/amqp/lrs/send-au-satisfied-statement.service.js': { default: satisfyStub },
      '../../../services/mssql/learning-objects/get.service.js': { default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid(), MoveOn: 'Passed' })))}
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Failed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.true
    expect(gradeStub.called).to.be.true
    expect(satisfyStub.called).to.be.false
  })

  it('should mark the au satisfied when the move criteria is completed or passed and the verb is completed', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.resolve(new LearnerProgressModel({
      UserID: uuid(),
      ID: uuid(),
      LearningObjectID: uuid()
    })))
    const updateProgressStub = Sinon.stub()
    updateProgressStub.returns(Promise.resolve(new LearnerProgressModel()))
    const gradeStub = Sinon.stub()
    gradeStub.returns(Promise.resolve())
    const satisfyStub = Sinon.stub()
    satisfyStub.returns(Promise.resolve())
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) }
    }, {
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/amqp/lrs/send-au-satisfied-statement.service.js': { default: satisfyStub },
      '../../../services/mssql/learning-objects/get.service.js': { default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid(), MoveOn: 'CompletedOrPassed' })))}
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Completed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.true
    expect(gradeStub.called).to.be.true
    expect(satisfyStub.called).to.be.true
  })

  it('should mark the au satisfied when the move criteria is completed or passed and the verb is passed', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.resolve(new LearnerProgressModel({
      UserID: uuid(),
      ID: uuid(),
      LearningObjectID: uuid()
    })))
    const updateProgressStub = Sinon.stub()
    updateProgressStub.returns(Promise.resolve(new LearnerProgressModel()))
    const gradeStub = Sinon.stub()
    gradeStub.returns(Promise.resolve())
    const satisfyStub = Sinon.stub()
    satisfyStub.returns(Promise.resolve())
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) }
    }, {
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/amqp/lrs/send-au-satisfied-statement.service.js': { default: satisfyStub },
      '../../../services/mssql/learning-objects/get.service.js': { default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid(), MoveOn: 'CompletedOrPassed' })))}
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Passed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      result: {
        score: {
          raw: 90
        }
      },
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.true
    expect(gradeStub.called).to.be.true
    expect(satisfyStub.called).to.be.true
  })

  it('should mark the au satisfied when the move criteria is completed and passed and the verb is passed and the lrs has a completed record', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.resolve(new LearnerProgressModel({
      UserID: uuid(),
      ID: uuid(),
      LearningObjectID: uuid()
    })))
    const updateProgressStub = Sinon.stub()
    updateProgressStub.returns(Promise.resolve(new LearnerProgressModel()))
    const gradeStub = Sinon.stub()
    gradeStub.returns(Promise.resolve())
    const satisfyStub = Sinon.stub()
    satisfyStub.returns(Promise.resolve())
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) }
    }, {
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/amqp/lrs/send-au-satisfied-statement.service.js': { default: satisfyStub },
      '../../../services/mssql/learning-objects/get.service.js': { default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid(), MoveOn: 'CompletedAndPassed' })))},
      '../../../services/amqp/lrs/get-statements.service.js': { default: Sinon.stub().returns({
        totalRecords: 1,
        statements: [{
          id: uuid(),
          verb: {
            id: CMI5Verbs.Completed
          },
          actor: {
            objectType: 'Agent',
            account: {
              name: 'test',
              homePage: 'http://example.com'
            }
          },
          timestamp: (new Date()).toISOString(),
          object: {
            id: uuid(),
            objectType: 'Activity'
          },
          authority: {}
        }]
      }) }
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Passed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      result: {
        score: {
          scaled: 0.9
        }
      },
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.true
    expect(gradeStub.called).to.be.true
    expect(satisfyStub.called).to.be.true
  })

  it('should mark the au satisfied when the move criteria is completed and passed and the verb is completed and the lrs has a passed record', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.resolve(new LearnerProgressModel({
      UserID: uuid(),
      ID: uuid(),
      LearningObjectID: uuid()
    })))
    const updateProgressStub = Sinon.stub()
    updateProgressStub.returns(Promise.resolve(new LearnerProgressModel()))
    const gradeStub = Sinon.stub()
    gradeStub.returns(Promise.resolve())
    const satisfyStub = Sinon.stub()
    satisfyStub.returns(Promise.resolve())
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) }
    }, {
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/amqp/lrs/send-au-satisfied-statement.service.js': { default: satisfyStub },
      '../../../services/mssql/learning-objects/get.service.js': { default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid(), MoveOn: 'CompletedAndPassed' })))},
      '../../../services/amqp/lrs/get-statements.service.js': { default: Sinon.stub().returns({
        totalRecords: 1,
        statements: [{
          id: uuid(),
          verb: {
            id: CMI5Verbs.Passed
          },
          actor: {
            objectType: 'Agent',
            account: {
              name: 'test',
              homePage: 'http://example.com'
            }
          },
          timestamp: (new Date()).toISOString(),
          object: {
            id: uuid(),
            objectType: 'Activity'
          },
          authority: {}
        }]
      }) }
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Completed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.true
    expect(gradeStub.called).to.be.true
    expect(satisfyStub.called).to.be.true
  })

  it('should not mark the au satisfied when the move criteria is completed and passed and the verb is passed and the lrs does not have a completed record', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.resolve(new LearnerProgressModel({
      UserID: uuid(),
      ID: uuid(),
      LearningObjectID: uuid()
    })))
    const updateProgressStub = Sinon.stub()
    updateProgressStub.returns(Promise.resolve(new LearnerProgressModel()))
    const gradeStub = Sinon.stub()
    gradeStub.returns(Promise.resolve())
    Sinon.stub().returns(Promise.resolve(new LearningObjectModel({
      ID: uuid(),
      MoveOn: 'CompletedAndPassed',
      IRI: uuid()
    })))
    const satisfyStub = Sinon.stub()
    satisfyStub.returns(Promise.resolve())
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) }
    }, {
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/amqp/lrs/send-au-satisfied-statement.service.js': { default: satisfyStub },
      '../../../services/mssql/learning-objects/get.service.js': { default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid(), MoveOn: 'CompletedAndPassed' })))},
      '../../../services/amqp/lrs/get-statements.service.js': { default: Sinon.stub().returns({
        totalRecords: 0,
        statements: []
      }) }
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Passed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.true
    expect(gradeStub.called).to.be.true
    expect(satisfyStub.called).to.be.false
  })

  it('should not mark the au satisfied when the move criteria is completed and passed and the verb is completed and the lrs does not have a passed record', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.resolve(new LearnerProgressModel({
      UserID: uuid(),
      ID: uuid(),
      LearningObjectID: uuid()
    })))
    const updateProgressStub = Sinon.stub()
    updateProgressStub.returns(Promise.resolve(new LearnerProgressModel()))
    const gradeStub = Sinon.stub()
    gradeStub.returns(Promise.resolve())
    const satisfyStub = Sinon.stub()
    satisfyStub.returns(Promise.resolve())
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) }
    }, {
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/amqp/lrs/send-au-satisfied-statement.service.js': { default: satisfyStub },
      '../../../services/mssql/learning-objects/get.service.js': { default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid(), MoveOn: 'CompletedAndPassed' })))},
      '../../../services/amqp/lrs/get-statements.service.js': { default: Sinon.stub().returns({
        totalRecords: 0,
        statements: []
      }) }
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Completed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.true
    expect(gradeStub.called).to.be.true
    expect(satisfyStub.called).to.be.false
  })

  it('should not mark the au satisfied when the move criteria is completed and passed and the verb is completed and the lrs does not have a passed record', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.resolve(new LearnerProgressModel({
      UserID: uuid(),
      ID: uuid(),
      LearningObjectID: uuid()
    })))
    const updateProgressStub = Sinon.stub()
    updateProgressStub.returns(Promise.resolve(new LearnerProgressModel()))
    const gradeStub = Sinon.stub()
    gradeStub.returns(Promise.resolve())
    const satisfyStub = Sinon.stub()
    satisfyStub.returns(Promise.resolve())
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) }
    }, {
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '../../../services/amqp/lrs/send-au-satisfied-statement.service.js': { default: satisfyStub },
      '../../../services/mssql/learning-objects/get.service.js': { default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid(), MoveOn: 'CompletedAndPassed' })))},
      '../../../services/amqp/lrs/get-statements.service.js': { default: Sinon.stub().returns({
        totalRecords: 0,
        statements: []
      })}
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Completed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.true
    expect(gradeStub.called).to.be.true
    expect(satisfyStub.called).to.be.false
  })

  it('should fail gracefully when the learner progress is not found', async () => {
    const getProgressStub = Sinon.stub()
    getProgressStub.returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))
    const updateProgressStub = Sinon.stub()
    const gradeStub = Sinon.stub()
    const controller = await esmock('./handle-create.controller.js', {
      '../../../services/amqp/system/get-system-config.service.js': { getSystemConfig: Sinon.stub().returns(Promise.resolve({ Domain: 'http://example.com' })) }
    }, {
      '../../../services/mssql/learner-progress/get.service.js': { getByID: getProgressStub },
      '../../../services/mssql/learner-progress/update.service.js': { default: updateProgressStub },
      '../../../services/background/grade-contexts.service.js': { default: gradeStub },
      '@lcs/logger': { default: { create: (label: string) => { return (level: string, message: string, data?: any) => { console.log(label, level, message, data) } }} }
    })
    await controller({
      id: uuid(),
      actor: {
        objectType: 'Agent',
        account: {
          name: 'test',
          homePage: 'http://example.com'
        }
      },
      object: {
        objectType: 'Activity',
        id: uuid()
      },
      verb: {
        id: CMI5Verbs.Completed
      },
      context: {
        registration: uuid(),
        extensions: {
          'https://w3id.org/xapi/cmi5/context/extensions/sessionid': uuid()
        }
      },
      timestamp: (new Date()).toISOString(),
      authority: {}
    })
    expect(getProgressStub.called).to.be.true
    expect(updateProgressStub.called).to.be.false
    expect(gradeStub.called).to.be.false
  })
})
