import mssql, { DB_Errors, getRows } from '@lcs/mssql-utility'
import { LearnerProgress, LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { Request } from 'mssql'
import LearnerProgressModel from '../../../models/learner-progress.model.js'

// Gets the top one for whatever the search field is
export default async function (userID: string, learningObjectId: string): Promise<LearnerProgressModel> {
  const pool = mssql.getPool()
  return await getLearningProgress(pool.request(), userID, learningObjectId)
}

export async function getByID (id: string): Promise<LearnerProgressModel> {
  const pool = mssql.getPool()
  const records = await getRows<LearnerProgress>(LearnerProgressTableName, pool.request(), { ID: id })
  return new LearnerProgressModel(records[0])
}

async function getLearningProgress (request: Request, userID: string, objectID: string): Promise<LearnerProgressModel> {
  request.input('userID', userID)
  request.input('objectID', objectID)

  const query = `
    SELECT TOP(1) * FROM [${LearnerProgressTableName}] 
    WHERE [${LearnerProgressFields.UserID}] = @userID 
    AND (
      [${LearnerProgressFields.LearningObjectID}] = @objectID 
      OR [${LearnerProgressFields.LearningContextSessionID}] = @objectID
    ) 
    ORDER BY [${LearnerProgressFields.CreatedOn}] DESC
  `

  const results = await request.query<LearnerProgress>(query)

  if (results.recordset.length === 0) { throw new Error(DB_Errors.default.NOT_FOUND_IN_DB) }

  return new LearnerProgressModel(results.recordset[0])
}
