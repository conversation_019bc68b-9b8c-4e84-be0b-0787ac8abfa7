import mssql from '@lcs/mssql-utility'
import { UserOverdueAssignmentFields, UserOverdueAssignmentsViewName } from '@tess-f/sql-tables/dist/lms/user-overdue-assignments-view.js'

export default async function getUsersOverdueAssignmentCount (userId: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('userId', userId)

  const result = await request.query<{ OverdueCount: number }>(`
    SELECT COUNT(*) AS [OverdueCount]
    FROM [${UserOverdueAssignmentsViewName}]
    WHERE [${UserOverdueAssignmentFields.UserID}] = @userId
  `)

  return result.recordset[0].OverdueCount
}
