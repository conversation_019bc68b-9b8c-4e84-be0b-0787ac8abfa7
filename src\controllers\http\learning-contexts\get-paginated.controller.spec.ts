import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import { count } from 'console'
import { LearningContextJson } from '@tess-f/lms/dist/common/learning-context'


describe('HTTP get-paginated controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-paginated.controller', {
            '../../../services/mssql/learning-context/get-paginated.service.js': {
                default: Sinon.stub().returns(Promise.resolve({totalRecords: 1, contexts: []}))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                offset: 1,
                limit: 10
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
        const data: { totalRecords: number, contexts: LearningContextJson[] } = JSON.parse(mocks.res._getData())
        expect(data).to.exist
        expect(data.totalRecords).to.be.a('number')
        expect(data.contexts).to.be.an('array')
        expect(data.contexts.length).to.equal(0)
        expect(data.totalRecords).to.equal(1)
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-paginated.controller', {
            '../../../services/mssql/learning-context/get-paginated.service.js': {
                default: Sinon.stub().rejects(Promise.resolve({totalRecords: 1, contexts: []}))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                offset: 1,
                limit: 10
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })



})
