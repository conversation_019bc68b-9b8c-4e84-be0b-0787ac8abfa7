import mssql, { updateRow } from '@lcs/mssql-utility'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'

/**
 * This service will take a context and remove it's parent and set it to null (root of the catalog tree)
 * The parent contexts content Order ID's will be updated
 */
export default async function (context: LearningContextModel): Promise<LearningContextModel> {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    // reset the order IDs of the learning contexts, context connections, and context objects for the old parent
    const request = transaction.request()
    request.input('parentID', context.fields.ParentContextID)
    request.input('oldOrderID', context.fields.OrderID)

    await request.query(`
      UPDATE [${LearningContextTableName}]
      SET [${LearningContextFields.OrderID}] = [${LearningContextFields.OrderID}] - 1
      WHERE [${LearningContextFields.OrderID}] > @oldOrderID
      AND [${LearningContextFields.ParentContextID}] = @parentID
    `)

    await request.query(`
      UPDATE [${LearningContextConnectionsTableName}]
      SET [${LearningContextConnectionFields.OrderID}] = [${LearningContextConnectionFields.OrderID}] - 1
      WHERE [${LearningContextConnectionFields.OrderID}] > @oldOrderID
      AND [${LearningContextConnectionFields.ParentContextID}] = @parentID
    `)

    await request.query(`
      UPDATE [${LearningObjectContextsTableName}]
      SET [${LearningObjectContextFields.OrderID}] = [${LearningObjectContextFields.OrderID}] - 1
      WHERE [${LearningObjectContextFields.OrderID}] > @oldOrderID
      AND [${LearningObjectContextFields.LearningContextID}] = @parentID
    `)

    context.fields.OrderID = null
    context.fields.ParentContextID = null

    const updated = await updateRow<LearningContext>(transaction.request(), context, { ID: context.fields.ID })

    await transaction.commit()

    return new LearningContextModel(undefined, updated[0])
  } catch (error) {
    if (!rolledBack) await transaction.rollback()
    throw error
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}
