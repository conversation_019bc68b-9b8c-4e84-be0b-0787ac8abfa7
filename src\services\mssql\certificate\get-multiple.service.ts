import mssql from '@lcs/mssql-utility'
import { Certificate } from '@tess-f/lms/dist/common/certificate.js'
import { LearningObjectsTableName, LearningObjectFields } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearnerProgressTableName, LearnerProgressFields } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningContextTableName, LearningContextFields } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextSessionsTableName, LearningContextSessionFields } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { UserCompletedLearningContextsTableName, UserCompletedLearningContextFields } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'

export default async function (userID: string): Promise<Certificate[]> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('userID', userID)

  const records = await request.query<Certificate>(`
    SELECT 'Learning Object' AS [EntityType], NULL AS [${LearningContextFields.Label}], [${LearningObjectsTableName}].[${LearningObjectFields.Title}], [${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}],
      [${LearningObjectsTableName}].[${LearningObjectFields.ContentID}], [${LearningObjectsTableName}].[${LearningObjectFields.Image}], [${LearnerProgressTableName}].[${LearnerProgressFields.Certificate}], [${LearnerProgressTableName}].[${LearnerProgressFields.LessonStatusID}], [${LearnerProgressTableName}].[${LearnerProgressFields.RawScore}],
      [${LearnerProgressTableName}].[${LearnerProgressFields.CompletedDate}], [${LearningObjectsTableName}].[${LearningObjectFields.ID}]
    FROM [${LearnerProgressTableName}]
      JOIN [${LearningObjectsTableName}] ON [${LearnerProgressTableName}].[${LearnerProgressFields.LearningObjectID}] = [${LearningObjectsTableName}].[${LearningObjectFields.ID}]
    WHERE [${LearnerProgressFields.Certificate}] IS NOT NULL
    AND [${LearnerProgressTableName}].[${LearnerProgressFields.UserID}] = @userID

    UNION ALL

    SELECT 'Learning Context' AS [EntityType], [${LearningContextTableName}].[${LearningContextFields.Label}], [${LearningContextTableName}].[${LearningContextFields.Title}], NULL AS [${LearningObjectFields.LearningObjectTypeID}],
      NULL AS [ContentID], [${LearningContextTableName}].[${LearningContextFields.Image}], [${LearnerProgressTableName}].[${LearnerProgressFields.Certificate}], [${LearnerProgressTableName}].[${LearnerProgressFields.LessonStatusID}], [${LearnerProgressTableName}].[${LearnerProgressFields.RawScore}],
      COALESCE([${LearnerProgressTableName}].[${LearnerProgressFields.CompletedDate}], [${LearnerProgressTableName}].[${LearnerProgressFields.CreatedOn}]) AS [${LearnerProgressFields.CompletedDate}], [${LearningContextTableName}].[${LearningContextFields.ID}]
    FROM [${LearnerProgressTableName}]
      JOIN [${LearningContextSessionsTableName}] ON [${LearningContextSessionsTableName}].[${LearningContextSessionFields.ID}] = [${LearnerProgressTableName}].[${LearnerProgressFields.LearningContextSessionID}]
      JOIN [${LearningContextTableName}] ON [${LearningContextSessionsTableName}].[${LearningContextSessionFields.LearningContextID}] = [${LearningContextTableName}].[${LearningContextFields.ID}]
    WHERE [${LearnerProgressFields.Certificate}] IS NOT NULL
    AND [${LearnerProgressTableName}].[${LearnerProgressFields.UserID}] = @userID

    UNION ALL

    SELECT 'Learning Context' AS [EntityType], [${LearningContextTableName}].[${LearningContextFields.Label}], [${LearningContextTableName}].[${LearningContextFields.Title}], NULL AS [${LearningObjectFields.LearningObjectTypeID}],
      NULL AS [ContentID], [${LearningContextTableName}].[${LearningContextFields.Image}], [${UserCompletedLearningContextsTableName}].[${UserCompletedLearningContextFields.Certificate}], [${UserCompletedLearningContextsTableName}].[${UserCompletedLearningContextFields.LessonStatusID}],
      [${UserCompletedLearningContextsTableName}].[${UserCompletedLearningContextFields.RawScore}], [${UserCompletedLearningContextsTableName}].[${UserCompletedLearningContextFields.CompletedOn}] AS [${LearnerProgressFields.CompletedDate}], [${LearningContextTableName}].[${LearningContextFields.ID}]
    FROM [${UserCompletedLearningContextsTableName}]
      JOIN [${LearningContextTableName}] ON [${LearningContextTableName}].[${LearningContextFields.ID}] = [${UserCompletedLearningContextsTableName}].[${UserCompletedLearningContextFields.LearningContextID}]
    WHERE [${UserCompletedLearningContextFields.Certificate}] IS NOT NULL
    AND [${UserCompletedLearningContextFields.Certificate}] NOT IN (
      SELECT [${LearnerProgressFields.Certificate}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.UserID}] = @userID
      AND [${LearnerProgressFields.Certificate}] IS NOT NULL
    )
    AND [${UserCompletedLearningContextsTableName}].[${UserCompletedLearningContextFields.UserID}] = @userID
  `)

  return records.recordset
}
