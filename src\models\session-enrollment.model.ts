import { Table } from '@lcs/mssql-utility'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { SessionEnrollment, SessionEnrollmentFields, SessionEnrollmentsTableName } from '@tess-f/sql-tables/dist/lms/session-enrollment.js'
import { z } from 'zod'

export const sessionEnrollmentSchema = z.object({
  [SessionEnrollmentFields.SessionID]: zodGUID,
  [SessionEnrollmentFields.UserID]: zodGUID,
  [SessionEnrollmentFields.Waitlisted]: z.boolean().optional().default(false)
})

export default class SessionEnrollmentModel extends Table<SessionEnrollment, SessionEnrollment> {
  public fields: SessionEnrollment

  constructor (fields?: SessionEnrollment) {
    super(SessionEnrollmentsTableName, [
      SessionEnrollmentFields.SessionID,
      SessionEnrollmentFields.UserID,
      SessionEnrollmentFields.CreatedBy,
      SessionEnrollmentFields.CreatedOn
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: SessionEnrollment): void {
    this.fields = record
  }

  public exportJsonToDatabase (): SessionEnrollment {
    return this.fields
  }
}
