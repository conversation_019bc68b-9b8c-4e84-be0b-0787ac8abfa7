import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { CoursePrerequisiteFields, CoursePrerequisitesTableName } from '@tess-f/sql-tables/dist/lms/course-prerequisite.js'

// TODO: Update this to ensure that a context cannot be listed as prereq if
// 1) it's hidden
// 2) it's a cmi5 block

/**
 * Fetches a paginated list of valid courses that can be used as a prereq for the given course
 * @param {string} contextID ID of the context you need available courses for
 * @param {number} offset number of records to offset the query by
 * @param {number} limit number of records to limit the query to
 * @param {string} search term to search for (Title, Description)
 * @returns {{ totalRecords: number, contexts: LearningContextModel[] }}
 */
export default async function (contextID?: string, offset: number = 0, limit: number = 10, search?: string, labels?: string[]): Promise<{ totalRecords: number, contexts: LearningContextModel[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  let query

  if (contextID) {
    query = `
            WITH prereqs AS (
                SELECT *
                FROM [${CoursePrerequisitesTableName}] WHERE [${CoursePrerequisiteFields.CourseID}] = @contextID
                UNION ALL
                    SELECT [${CoursePrerequisitesTableName}].*
                    FROM [${CoursePrerequisitesTableName}]
                    JOIN prereqs ON [${CoursePrerequisitesTableName}].[${CoursePrerequisiteFields.CourseID}] = [prereqs].[${CoursePrerequisiteFields.PrereqCourseID}]
            )
            SELECT [${LearningContextTableName}].*, TotalRecords = COUNT(*) OVER()
            FROM [${LearningContextTableName}]
            WHERE [${LearningContextFields.ID}] NOT IN (
                SELECT [${CoursePrerequisiteFields.PrereqCourseID}]
                FROM [prereqs]
            )
            AND [${LearningContextFields.ID}] != @contextID
            AND [${LearningContextFields.ID}] NOT IN (
                SELECT [${CoursePrerequisiteFields.CourseID}]
                FROM [${CoursePrerequisitesTableName}]
                WHERE [${CoursePrerequisiteFields.PrereqCourseID}] = @contextID
            )
        `

    request.input('contextID', contextID)
  } else {
    query = `
            SELECT [${LearningContextTableName}].*, TotalRecords = COUNT(*) OVER()
            FROM [${LearningContextTableName}]
            WHERE 1 = 1
        `
  }

  // query += `AND ([ContextTypeID] = ${LearningContextTypes.course} OR [ContextTypeID] = ${LearningContextTypes.section})`

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [LearningContextFields.Title, LearningContextFields.Description, LearningContextFields.CourseID], 'any')}) `
  }

  if (labels && labels.length > 0) {
    query += 'AND ('
    const conds = labels.map((label, index) => {
      request.input(`label_${index}`, label)
      return `@label_${index}`
    })
    query += `[${LearningContextFields.Label}] IN (${conds.join(', ')}))`
  }

  query += `
        ORDER BY
            [${LearningContextFields.CreatedOn}] ASC, [${LearningContextFields.Title}]
        OFFSET @offset ROWS
        FETCH FIRST @limit ROWS ONLY
    `

  request.input('offset', offset)
  request.input('limit', limit)

  const results = await request.query<LearningContext & {TotalRecords: number}>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    contexts: results.recordset.map(record => new LearningContextModel(record))
  }
}
