import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'


describe('HTTP get-completion controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-completion.controller', {
            '../../../services/mssql/learning-context/get-completion.service.js': {
                default: Sinon.stub().returns(Promise.resolve({completion: 1, numberOfObjects: 1, numberOfCompletedObjects: 1}))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                contextID: uuid(),
                userID: uuid(),
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-completion.controller', {
            '../../../services/mssql/learning-context/get-completion.service.js': {
                default: Sinon.stub().returns(Promise.resolve({completion: 1, numberOfObjects: 1, numberOfCompletedObjects: 1}))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                contextID: false,
                userID: false,
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request parameter data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('contextID')
        expect(mocks.res._getData()).include('userID')

    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-completion.controller', {
            '../../../services/mssql/learning-context/get-completion.service.js': {
                default: Sinon.stub().rejects(Promise.resolve({completion: 1, numberOfObjects: 1, numberOfCompletedObjects: 1}))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                contextID: uuid(),
                userID: uuid(),
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })




})