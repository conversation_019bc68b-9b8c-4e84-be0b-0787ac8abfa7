import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'

/**
 * Searches learning contexts
 */
export default async function (search: string, offset: number = 0, limit: number = 10): Promise<{ totalRecords: number, items: LearningContextModel[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  // build query
  let query = `SELECT *, TotalRecords = COUNT(*) OVER() FROM [${LearningContextTableName}] WHERE 1 = 1 `
  query += `AND (${parseSearchTerms(request, search, [LearningContextFields.Title, LearningContextFields.Label], 'all')}) `
  query += `AND ([${LearningContextFields.ContextTypeID}] = 2 OR [${LearningContextFields.ContextTypeID}] = 6)`

  request.input('offset', offset)
  request.input('limit', limit)

  query += `ORDER BY [${LearningContextFields.CreatedOn}] DESC, [${LearningContextFields.Title}] ASC`
  query += `
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<LearningContext & {TotalRecords: number}>(query)

  // return the results
  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    items: results.recordset.map(record => new LearningContextModel(undefined, record))
  }
}
