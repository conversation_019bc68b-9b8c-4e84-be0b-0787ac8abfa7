import mssql, { getRows } from '@lcs/mssql-utility'
import { LearningContextRating, LearningContextRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-rating.js'
import LearningContextRatingModel from '../../../models/learning-context-rating.model.js'

export default async function getUserRatingByID (learningContextID: string): Promise<LearningContextRatingModel> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextRating>(LearningContextRatingsTableName, pool.request(), { ID: learningContextID })
  return new LearningContextRatingModel(records[0])
}

export async function getUserRatingForContext (learningContextID: string, userID: string): Promise<LearningContextRatingModel> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextRating>(LearningContextRatingsTableName, pool.request(), { LearningContextID: learningContextID, UserID: userID })
  return new LearningContextRatingModel(records[0])
}

export async function getAllRatingsForContext (learningContextID: string): Promise<LearningContextRatingModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextRating>(LearningContextRatingsTableName, pool.request(), { LearningContextID: learningContextID })
  return records.map(record => new LearningContextRatingModel(record))
}

export async function getAllContextRatingsForUser (userID: string): Promise<LearningContextRatingModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextRating>(LearningContextRatingsTableName, pool.request(), { UserID: userID })
  return records.map(record => new LearningContextRatingModel(record))
}
