import LearningContextSession, { updateLearningContextSessionSchema } from '../../../models/learning-context-session.model.js'
import logger from '@lcs/logger'
import updateSession from '../../../services/mssql/learning-context-sessions/update.service.js'
import deleteInstructors from '../../../services/mssql/learning-context-session-instructors/delete.service.js'
import createInstructor from '../../../services/mssql/learning-context-session-instructors/create.service.js'
import LearningContextSessionInstructor from '../../../models/learning-context-session-instructor.model.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const { INTERNAL_SERVER_ERROR, BAD_REQUEST } = httpStatus
const log = logger.create('Controller-HTTP.update-learning-context-session', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, while creating a new course, enter a Course Title and Course Description and then click in another field. Note the time. Check the LMS API log for the 'http-update-learning-context' label."

  try {
    let sessionInstructors: LearningContextSessionInstructor[] = []

    const learningContextSession = new LearningContextSession(updateLearningContextSessionSchema.parse(req.body))
    learningContextSession.fields.ModifiedOn = new Date()
    learningContextSession.fields.ModifiedBy = req.session.userId
    
    const data = z.object({
      Instructors: z.array(z.object({ ID: zodGUID })).optional(),
      ID: zodGUID // FIXME: the id parameter should come from the request params and not the body
    }).parse(req.body)
    
    learningContextSession.fields.ID = data.ID

    if (data.Instructors) {
      sessionInstructors = data.Instructors.map((_instructor: any) => {
        const instructor = new LearningContextSessionInstructor({
          SessionID: data.ID,
          UserID: _instructor.ID
        })
        return instructor
      })
    }

    try {
      await deleteInstructors(data.ID)
    } catch (error) {
      log('error', 'Failed to remove previous instructors for session', { error, req, sessionId: data.ID, success: false })
      throw error
    }

    await Promise.all(sessionInstructors.map(async _sessionInstructor => {
      await createInstructor(_sessionInstructor)
    }))

    const updatedSession = await updateSession(learningContextSession)

    updatedSession.fields.Instructors = data.Instructors

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, while creating a new course, enter a Course Title and Course Description and then click in another field. Note the time. Check the LMS API log for the 'http-update-learning-context' label and message indicating a successful update."
    log('info', 'Successfully updated learning context session', { id: updatedSession.fields.ID, success: true, req })

    res.json(updatedSession.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update learning context session: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to update learning context session.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
