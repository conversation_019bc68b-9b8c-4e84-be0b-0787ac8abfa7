import { RequestHand<PERSON>, Router } from 'express'
import createSystemWidgetController from './create.controller.js'
import deleteSystemWidgetController from './delete.controller.js'
import getMultipleSystemWidgetsController from './get-multiple.controller.js'
import getSystemWidgetController from './get.controller.js'
import updateSystemWidgetController from './update.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.post('/system-widget', checkClaims([Claims.CREATE_SYSTEM_WIDGETS]), createSystemWidgetController as RequestHandler)
router.delete('/system-widget/:id', checkClaims([Claims.DELETE_SYSTEM_WIDGETS]), deleteSystemWidgetController as RequestHandler)
router.post('/system-widgets', checkClaims([Claims.VIEW_SYSTEM_WIDGETS, Claims.CREATE_SYSTEM_WIDGETS, Claims.MODIFY_SYSTEM_WIDGETS, Claims.PUBLISH_SYSTEM_WIDGETS]), getMultipleSystemWidgetsController as RequestHandler)
router.get('/system-widget/:id', checkClaims([Claims.VIEW_SYSTEM_WIDGETS, Claims.CREATE_SYSTEM_WIDGETS, Claims.MODIFY_SYSTEM_WIDGETS, Claims.PUBLISH_SYSTEM_WIDGETS]), getSystemWidgetController as RequestHandler)
router.put('/system-widget', checkClaims([Claims.CREATE_SYSTEM_WIDGETS, Claims.MODIFY_SYSTEM_WIDGETS, Claims.PUBLISH_SYSTEM_WIDGETS]), updateSystemWidgetController as RequestHandler)

export default router
