import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import UserPreference, { userPreferenceSchema } from '../../../models/user-preference.model.js'
import UserPreferencesModel from '../../../models/user-preference.model.js'

describe('HTTP update controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/user-preferences/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new UserPreferencesModel))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid()
            },
            body: {
                UserID: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/user-preferences/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new UserPreferencesModel))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid()
            },
            body: {
                UserID: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('UserID')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/user-preferences/update.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new UserPreferencesModel))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid()
            },
            body: {
                UserID: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

})