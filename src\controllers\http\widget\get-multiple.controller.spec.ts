import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import Widget, { createWidgetSchema } from '../../../models/widget.model.js'




describe('HTTP get-multiple controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-multiple.controller', {
            '../../../services/mssql/groups/get-ids-of-group-for-user.service.js': {
                default: Sinon.stub().returns(Promise.resolve(['']))
            },
            '../../../services/mssql/widgets/get-for-user.service.js': {
                default: Sinon.stub().returns(Promise.resolve({adminWidgets: [new Widget()], userWidgets: [new Widget()]}))
            },
            '../../../services/mssql/widget-user-overrides/get-multiple.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: uuid()
            },
            body:{
                groupIDs: ['']
            }


        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })


    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-multiple.controller', {
            '../../../services/mssql/groups/get-ids-of-group-for-user.service.js': {
                default: Sinon.stub().returns(Promise.reject(['']))
            },
            '../../../services/mssql/widgets/get-for-user.service.js': {
                default: Sinon.stub().returns(Promise.reject({adminWidgets: [new Widget()], userWidgets: [new Widget()]}))
            },
            '../../../services/mssql/widget-user-overrides/get-multiple.service.js': {
                default: Sinon.stub().returns(Promise.reject([]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: uuid()
            },
            body:{
                groupIDs: ['']
            }


        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })


})