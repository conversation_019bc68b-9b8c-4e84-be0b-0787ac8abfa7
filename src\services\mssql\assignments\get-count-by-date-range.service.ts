import mssql from '@lcs/mssql-utility'
import { AssignmentFields, AssignmentsTableName } from '@tess-f/sql-tables/dist/lms/assignment.js'
import { Request } from 'mssql'

// gets learning object uploads by type
export default async function (from?: Date, to?: Date): Promise<number> {
  const pool = mssql.getPool()
  return await getAssignments(pool.request(), from, to)
}

async function getAssignments (request: Request, from?: Date, to?: Date): Promise<number> {
  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }
  const query = `
    SELECT COUNT([${AssignmentFields.ID}]) as AssignmentCount
    FROM [${AssignmentsTableName}]
    WHERE ([${AssignmentFields.DeletedOn}] IS NULL OR [${AssignmentFields.DeletedOn}] > GETDATE())
    ${from && to ? `AND [${AssignmentFields.CreatedOn}] BETWEEN @from AND @to` : ''}
  `

  const res = await request.query<{ AssignmentCount: number }>(query)
  return res.recordset[0].AssignmentCount
}
