import mssql from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { ConnectionPool, Request } from 'mssql'
import getAssignmentContentForUserService from './get-assignment-content-for-user.service.js'
import calculateAssignmentContentStatus from '@tess-f/lms/dist/shared/services/assignment/grade-user-assignment-content.service.js'
import { getOverallStatus } from '@tess-f/lms/dist/shared/services/assignment/context-grading/utils.js'
import { UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'
import { UserAssignedMultiSessionCoursesFields, UserAssignedMultiSessionCoursesTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-multi-session-course.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { AssignmentStatus } from '../../../utils/assignment-status.js'

const log = logger.create('Service.Assignment-Optional-Content-Grade')

export async function gradeForLearningObject (objectID: string, userID: string): Promise<void> {
  const pool = mssql.getPool()
  const request = pool.request()
  // fetch assignment ID's that use this object
  const query = `
    SELECT [${UserAssignedLearningObjectFields.AssignmentID}]
    FROM [${UserAssignedLearningObjectsTableName}]
    WHERE [${UserAssignedLearningObjectFields.UserID}] = @userID
    AND [${UserAssignedLearningObjectFields.LearningObjectID}] = @objectID
  `

  request.input('objectID', objectID)
  request.input('userID', userID)

  const assignmentIDs = (await request.query<{ AssignmentID: string }>(query)).recordset.map(record => record.AssignmentID)
  await checkAssignments(pool, assignmentIDs, userID)
}

export async function gradeForMultiSessionCourse (sessionID: string, userID: string) {
  const pool = mssql.getPool()
  const request = pool.request()
  // fetch assignment ID's that use this course
  const query = `
    SELECT [${UserAssignedMultiSessionCoursesFields.AssignmentID}]
    FROM [${UserAssignedMultiSessionCoursesTableName}]
    WHERE [${UserAssignedMultiSessionCoursesFields.UserID}] = @userID
    AND [${UserAssignedMultiSessionCoursesFields.LearningContextID}] IN (
      SELECT [${LearningContextSessionFields.LearningContextID}]
      FROM [${LearningContextSessionsTableName}]
      WHERE [${LearningContextSessionFields.ID}] = @sessionID
    )
  `

  request.input('sessionID', sessionID)
  request.input('userID', userID)

  const assignmentIDs = (await request.query<{ AssignmentID: string }>(query)).recordset.map(record => record.AssignmentID)
  await checkAssignments(pool, assignmentIDs, userID)
}

async function checkAssignments (pool: ConnectionPool, assignmentIDs: string[], userID: string): Promise<void> {
  await Promise.all(assignmentIDs.map(async assignmentID => {
    // get all the content for the assignment
    const assignedItems = await getAssignmentContentForUserService(assignmentID, userID)
    const gradedContent = calculateAssignmentContentStatus(assignedItems)
    const status = getOverallStatus(gradedContent.map(item => item.Status))
    // if the content status is completed we need to make sure that all the content is marked complete
    log('verbose', 'Graded assignment', { status, assignmentID })
    if (status === AssignmentStatus.Completed) {
      await markAssignmentComplete(pool.request(), assignmentID, userID)
    }
  }))
}

async function markAssignmentComplete (request: Request, assignmentID: string, userID: string) {
  const query = `
    UPDATE [${UserAssignedLearningObjectsTableName}]
    SET [${UserAssignedLearningObjectFields.Completed}] = 1
    WHERE [${UserAssignedLearningObjectFields.AssignmentID}] = @assignmentID
    AND [${UserAssignedLearningObjectFields.UserID}] = @userID
  `

  request.input('userID', userID)
  request.input('assignmentID', assignmentID)

  const res = await request.query(query)
  log('info', 'Successfully updated user assignment', { userID, assignmentID, success: true, count: res.rowsAffected[0] })
}
