import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextModel from '../../../models/learning-context.model.js'


describe('HTTP get-available-course-prereqs controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-available-course-prereqs.controller', {
            '../../../services/mssql/learning-context/get-available-course-prereqs.service.js': {
                default: Sinon.stub().returns(Promise.resolve({totalRecords: 0, contexts: [new LearningContextModel({ Title: 'prereqs', ContextTypeID: 1, CreatedBy: uuid(), CreatedOn: new Date()})]}))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                limit: 10,
                offset: 0,
                search: '',
                contextID: uuid(),
                labels: ''

            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-available-course-prereqs.controller', {
            '../../../services/mssql/learning-context/get-available-course-prereqs.service.js': {
                default: Sinon.stub().returns(Promise.resolve({totalRecords: 0, contexts: [new LearningContextModel({ Title: 'prereqs', ContextTypeID: 1, CreatedBy: uuid(), CreatedOn: new Date()})]}))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                limit: false,
                offset: false,
                search: false,
                contextID: false,
                labels: false

            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request query parameter')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('search')
        expect(mocks.res._getData()).include('contextID')
        expect(mocks.res._getData()).include('labels')

    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-available-course-prereqs.controller', {
            '../../../services/mssql/learning-context/get-available-course-prereqs.service.js': {
                default: Sinon.stub().returns(Promise.reject({totalRecords: 0, contexts: [new LearningContextModel({ Title: 'prereqs', ContextTypeID: 1, CreatedBy: uuid(), CreatedOn: new Date()})]}))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                limit: 10,
                offset: 0,
                search: '',
                contextID: uuid(),
                labels: ''

            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })




})