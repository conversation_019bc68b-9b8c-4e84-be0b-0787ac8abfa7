import mssql from '@lcs/mssql-utility'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { AssignmentLearningContextFields, AssignmentLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-context.js'
import { LearningContextsWithoutUpcomingILTSessionsViewFields, LearningContextsWithoutUpcomingILTSessionsViewName } from '@tess-f/sql-tables/dist/lms/learning-contexts-without-upcoming-ilt-sessions-view.js'

export default async function (assignmentID: string): Promise<LearningContextModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('assignmentID', assignmentID)

  const results = await request.query<LearningContext>(`
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ID}] IN (
      SELECT [${AssignmentLearningContextFields.LearningContextID}]
      FROM [${AssignmentLearningContextsTableName}]
      WHERE [${AssignmentLearningContextFields.AssignmentID}] = @assignmentID
      AND [${AssignmentLearningContextFields.LearningContextID}] IN (
        SELECT [${LearningContextsWithoutUpcomingILTSessionsViewFields.ID}]
        FROM [${LearningContextsWithoutUpcomingILTSessionsViewName}]
      )
    )
  `)

  return results.recordset.map(record => new LearningContextModel(undefined, record))
}
