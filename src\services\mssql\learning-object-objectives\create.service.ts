import logger from '@lcs/logger'
import mssql, { addRow } from '@lcs/mssql-utility'
import LearningObjectObjectiveModel from '../../../models/learning-object-objective.model.js'
import { LearningObjectObjective } from '@tess-f/sql-tables/dist/lms/learning-object-objective.js'
import { createObjectiveConnection } from '@tess-f/objectives/dist/amqp/create-connection.js'
import settings from '../../../config/settings.js'
import { getLearningObjectType } from '../../../utils/statics-to-names.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import { Request } from 'mssql'

const log = logger.create('Service-MSSQL.create-learning-object-objective')

export default async function createLearningObjectObjective (objectObjective: LearningObjectObjectiveModel, objectTitle: string, objectType: number, request?: Request): Promise<LearningObjectObjectiveModel> {
  if (request === undefined) {
    request = mssql.getPool().request()
  }

  const record = await addRow<LearningObjectObjective>(request, objectObjective)
  objectObjective.importFromDatabase(record)

  // create the connection with the objective app
  try {
    await createObjectiveConnection(
      settings.amqp.service_queues.objectiveConnect,
      {
        ObjectiveId: objectObjective.fields.ObjectiveId!,
        ReferenceId: objectObjective.fields.LearningObjectId!,
        ReferenceLink: `/browse/view-content/${objectObjective.fields.LearningObjectId}`,
        ReferenceText: objectTitle,
        ReferenceType: getLearningObjectType(objectType),
        SystemId: 'lms'
      },
      settings.amqp.rpc_timeout
    )
  } catch (error) {
    // log the failure, but continue on
    log('error', 'Failed to create connection between learning object and objective.', { errorMessage: getErrorMessage(error), success: false })
  }

  return objectObjective
}
