import logger from '@lcs/logger'
import httpStatus from 'http-status'
import Sinon from 'sinon'
import { expect } from 'chai'
import httpMock from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: delete group claim', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('should return bad request when the claims array is invalid', async () => {
    const controller = await esmock('./delete.controller.js')
    const mocks = httpMock.createMocks({ body: ['claim', 2, true] })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('claims.1: Expected string, received number')
    expect(data).to.include('claims.2: Expected string, received boolean')
  })

  
  it('returns bad request when the claims array is empty', async () => {
    const controller = await esmock('./delete.controller.js')
    const mocks = httpMock.createMocks({ body: [] })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('claims: Array must contain at least 1 element(s)')
  })

  it('returns bad request when the id parameter is missing', async () => {
    const controller = await esmock('./delete.controller.js')
    const mocks = httpMock.createMocks({ body: ['test'] })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Required')
  })

  it('returns bad request when the id parameter is not a valid uuid', async () => {
    const controller = await esmock('./delete.controller.js')
    const mocks = httpMock.createMocks({ body: ['test'], params: { id: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Invalid')
  })

  it('returns bad request when the id parameter is a number and not a valid uuid', async () => {
    const controller = await esmock('./delete.controller.js')
    const mocks = httpMock.createMocks({ body: ['test'], params: { id: 123 } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Expected string, received number')
  })

  it('should delete the group claim', async () => {
    const controller = await esmock('./delete.controller.js', {
      '../../../../services/mssql/claims/group/delete.service.js': {
        default: Sinon.stub().resolves(1)
      }
    })
    const mocks = httpMock.createMocks({ body: ['test'], params: { id: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.NO_CONTENT)
  })
})
