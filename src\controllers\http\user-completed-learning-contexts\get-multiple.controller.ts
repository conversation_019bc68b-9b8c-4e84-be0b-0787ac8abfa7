/* eslint-disable camelcase */
import logger from '@lcs/logger'
import { getAllRecordsForUserAndContext } from '../../../services/mssql/user-completed-learning-contexts/get-for-user.service.js'
import { Request, Response } from 'express'
import { DB_Errors } from '@lcs/mssql-utility'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.Get-User-Learning-Contexts-Grades', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { userID, contextID } = z.object({ userID: zodGUID, contextID: zodGUID }).parse(req.params)
    const grades = await getAllRecordsForUserAndContext(userID, contextID)
    grades.sort((a, b) => {
      if (a.fields.RawScore! < b.fields.RawScore!) {
        return 1
      } else if (a.fields.RawScore! > b.fields.RawScore!) {
        return -1
      } else if (a.fields.LessonStatusID! < b.fields.LessonStatusID!) {
        return 1
      } else if (a.fields.LessonStatusID! > b.fields.LessonStatusID!) {
        return -1
      } else if (a.fields.CompletedOn! < b.fields.CompletedOn!) {
        return 1
      }
      return -1
    })

    log('info', `Successfully retrieved grades for the users context completions`, { userID, contextID, count: grades.length, success: true, req })

    res.json(grades.map(record => record.fields))
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get user completed learning context grades: input validation error', { success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter, '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get user completed learning context grades because none where found in the database', { success: false, req })
      res.json([])
    } else {
      log('error', 'Failed to get user completed learning context grades', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
