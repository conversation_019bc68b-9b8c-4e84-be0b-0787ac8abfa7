import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import del from '../../../services/mssql/assignments/delete.service.js'
import { httpLogTransformer } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.delete-assignment', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    await del(id)
    log('info', 'Successfully deleted assignment', { assignmentId: id, success: true, req })
    res.sendStatus(httpStatus.NO_CONTENT)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Invalid assignment ID', { assignmentID: req.params.id, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send('Invalid assignment ID in request parameters')
    } else {
      log('error', 'Failed to delete assignment.', { error, req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
