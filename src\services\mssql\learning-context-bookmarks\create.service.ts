import mssql, { addRow } from '@lcs/mssql-utility'
import getContext from '../learning-context/get.service.js'
import createActivity from '../activity-stream/create.service.js'
import ActivityStream from '../../../models/activity-stream.model.js'
import LearningContextBookmarkModel from '../../../models/learning-context-user-bookmark.model.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { LearningContextUserBookmark } from '@tess-f/sql-tables/dist/lms/learning-context-user-bookmark.js'

export default async function (contextBookmark: LearningContextBookmarkModel): Promise<LearningContextBookmarkModel> {
  const pool = mssql.getPool()
  // create activity stream record
  const context = await getContext(contextBookmark.fields.LearningContextID!, undefined)
  const activity = new ActivityStream({
    UserID: contextBookmark.fields.UserID,
    LinkText: context.fields.Label + ': ' + context.fields.Title,
    LinkID: contextBookmark.fields.LearningContextID,
    ActivityID: Activities.BookmarkedContext,
    CreatedOn: new Date()
  })
  await createActivity(activity)

  const record = await addRow<LearningContextUserBookmark>(pool.request(), contextBookmark)
  return new LearningContextBookmarkModel(record)
}
