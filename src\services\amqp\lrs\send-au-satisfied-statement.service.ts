import { CMI5Verbs } from '@tess-f/sql-tables/dist/lrs/verb.js'
import { getSystemConfig } from '../system/get-system-config.service.js'
import createStatementService from './create-statement.service.js'
import { Agent<PERSON><PERSON> } from '../../../models/amqp/lrs/statement.model.js'

export default async function sendAuSatisfiedStatement (learningObjectId: string, agent: AgentJson, registrationId: string, auIri: string, sessionId: string) {
  const config = await getSystemConfig()
  const lmsConfig = config.Apps.find(app => app.Id === 'lms')
  await createStatementService({
    verb: { id: CMI5Verbs.Satisfied },
    object: {
      objectType: 'Activity',
      id: `${config.Domain}${lmsConfig?.Address ?? '/lms'}/learning-object/${learningObjectId}`
    },
    actor: agent,
    context: {
      registration: registrationId,
      contextActivities: {
        grouping: [
          {
            id: auIri
          }
        ]
      },
      extensions: {
        'https://w3id.org/xapi/cmi5/context/extensions/sessionid': sessionId
      }
    },
    timestamp: (new Date()).toISOString(),
    authority: agent
  })
}
