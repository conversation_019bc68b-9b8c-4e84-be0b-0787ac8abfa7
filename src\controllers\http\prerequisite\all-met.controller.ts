import type { Request, Response } from 'express'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
import getContextPrerequisites from '../../../services/mssql/course-prerequisite/get-for-context.service.js'
import getPrerequisiteStatus from '../../../services/mssql/course-prerequisite/get-status.service.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.all-prerequisites-met', httpLogTransformer)

export default async function (req: Request, res: Response): Promise<void> {
  try {
    const { contextId } = z.object({ contextId: zodGUID }).parse(req.params)
    // 1. Get all prerequisite IDs for the context
    const prerequisites = await getContextPrerequisites(contextId)
    log('info', 'Fetched prerequisites for context', { contextId, count: prerequisites.length, success: true, req })

    // If there are no prerequisites then return true
    if (prerequisites.length === 0) {
      log('info', 'No prerequisites found for context', { contextId, success: true, req })
      res.json(true)
      return
    }

    // 2. Get the status of each prerequisite
    const status = await Promise.all(prerequisites.map(async (prerequisite) => getPrerequisiteStatus(prerequisite.fields.ID ?? '', req.session.userId)))

    // 3. Determine if all prerequisites are met
    const allMet = status.every(status => status === 'Met')
    log('info', 'Successfully determined if all prerequisites are met', { contextId, allMet, success: true, req })

    res.json(allMet)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to retrieve prerequisite status: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter, '))
    } else {
      log('error', 'Failed to retrieve prerequisite status', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
