import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import { AssignmentTypes } from '@tess-f/sql-tables/dist/lms/assignment-type.js'
import create from './create.service.js'
import get from './get.service.js'
import getAssignmentContentForUser from './get-assignment-content-for-user.service.js'
import getContentTypeCounts from './get-content-type-counts.service.js'
import getContextAssignmentCount from './get-context-assignment-count.service.js'
import getContextDueDate from './get-context-due-date.service.js'
import getCountByDateRange from './get-count-by-date-range.service.js'
import getCountHistory from './get-count-history.service.js'
import getCurrentCounts from './get-current-counts.service.js'
import getObjectAssignmentCount from './get-object-assignment-count.service.js'
import getObjectDueDate from './get-object-due-date.service.js'
import getTeamsActiveAssignmentCount from './get-teams-active-assignment-count.service.js'
import getTeamsOverdueAssignmentCount from './get-teams-overdue-assignment-count.service.js'
import getUserContextAssignmentStatus from './get-user-context-assignment-status.service.js'
import getUserObjectAssignmentStatus from './get-user-object-assignment-status.service.js'
import getUserOverdueAssignmentCount from './get-user-overdue-assignment-count.service.js'
import getUsersActiveAssignmentCount from './get-users-active-assignment-count.service.js'
import searchContextUserAssignments from './search-context-user-assignments.service.js'
import searchObjectUserAssignments from './search-object-user-assignments.service.js'
import searchUserActiveAssignments from './search-user-active-assignments.service.js'
import { gradeForLearningObject, gradeForMultiSessionCourse } from './update-user-assignment-completion.service.js'
import update from './update.service.js'
import remove from './delete.service.js'
import AssignmentModel from '../../../models/assignment.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { AssignmentCountsHistoryTableName } from '@tess-f/sql-tables/dist/lms/assignment-counts-history.js'

let assignment: AssignmentModel

describe('MSSQL Assignments Service', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
  })

  it('creates an assignment', async () => {
    const records = await create(new AssignmentModel({
      Title: 'Running Assignments MSSQL Unit Test',
      TypeID: AssignmentTypes.Manual,
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    }), [], [], [], [])
    expect(records.assignment.fields).to.exist
    expect(records.assignment.fields.ID).to.exist
    expect(records.assignment.fields.TypeID).to.equal(AssignmentTypes.Manual)
    expect(records.assignment.fields.CreatedBy).to.equal(AdminUserId)
    assignment = records.assignment
  })

  it('gets an assignment by ID', async () => {
    try {
      const results = await get(assignment.fields.ID!)
      expect(results.fields.ID!).to.equal(assignment.fields.ID)
    } catch (error) {
      expect(error).to.exist
    }
  })

  it('gets assignment content for user', async () => {
    const results = await getAssignmentContentForUser(assignment.fields.ID!, AdminUserId)
    expect(results.length).to.gte(0)
  })

  it('gets content type counts', async () => {
    const results = await getContentTypeCounts()
    expect(results.length).to.gte(0)
  })

  it('gets context assignment count', async () => {
    const results = await getContextAssignmentCount(assignment.fields.ID!)
    expect(results).to.gte(0)
  })

  it('gets context due date', async () => {
    try {
      const results = await getContextDueDate(assignment.fields.ID!, AdminUserId)
      expect(results).to.exist
    } catch (error) {
      expect(error).to.exist
    }
  })

  it('gets count by date range', async () => {
    const results = await getCountByDateRange()
    expect(results).to.gte(0)
  })

  it('gets count history', async () => {
    const results = await getCountHistory()
    expect(results.length).to.gte(0)
  })

  it('gets current counts', async () => {
    const results = await getCurrentCounts()
    expect(results).to.exist
  })

  it('gets object assignment count', async () => {
    const results = await getObjectAssignmentCount(assignment.fields.ID!)
    expect(results).to.gte(0)
  })

  it('gets object due date', async () => {
    try {
      const results = await getObjectDueDate(assignment.fields.ID!, AdminUserId)
      expect(results).to.exist
    } catch (error) {
      expect(error).to.exist
    }
  })

  it('gets teams active assignment count', async () => {
    const results = await getTeamsActiveAssignmentCount(AdminUserId)
    expect(results).to.gte(0)
  })

  it('gets teams overdue assignment count', async () => {
    const results = await getTeamsOverdueAssignmentCount(AdminUserId)
    expect(results).to.gte(0)
  })

  it('gets user context assignment status', async () => {
    const results = await getUserContextAssignmentStatus(assignment.fields.ID!, assignment.fields.ID!, AdminUserId)
    expect(results).to.gte(0)
  })

  it('gets user object assignment status', async () => {
    const results = await getUserObjectAssignmentStatus(assignment.fields.ID!, assignment.fields.ID!, AdminUserId)
    expect(results).to.gte(0)
  })

  it('gets user overdue assignment count', async () => {
    const results = await getUserOverdueAssignmentCount(AdminUserId)
    expect(results).to.gte(0)
  })

  it('gets users active assignment count', async () => {
    const results = await getUsersActiveAssignmentCount(AdminUserId)
    expect(results).to.gte(0)
  })

  it('searches context user assignments', async () => {
    const results = await searchContextUserAssignments(assignment.fields.ID!, 1, 150)
    expect(results.totalRecords).to.gte(0)
  })

  it('searches object user assignments', async () => {
    const results = await searchObjectUserAssignments(assignment.fields.ID!, 1, 150)
    expect(results.totalRecords).to.gte(0)
  })

  it('searches user active assignments', async () => {
    const results = await searchUserActiveAssignments(AdminUserId)
    expect(results.TotalRecords).to.gte(0)
  })

  it('updates grade for learning object', async () => {
    const results = await gradeForLearningObject(assignment.fields.ID!, AdminUserId)
    if (results === undefined) {
      expect(results).to.eq(undefined)
    } else {
      expect(results).to.exist
    }
  })

  it('updates grade for multi session course', async () => {
    const results = await gradeForMultiSessionCourse(assignment.fields.ID!, AdminUserId)
    if (results === undefined) {
      expect(results).to.eq(undefined)
    } else {
      expect(results).to.exist
    }
  })

  it('updates an assignment', async () => {
    assignment.fields.TypeID = 1
    const res = await update(assignment, [], [])
    expect(res.assignment.fields.TypeID).to.equal(1)
  })

  it('deletes an assignment', async () => {
    await remove(assignment.fields.ID!)
  })

  after(async () => {
    await mssql.getPool().request().query(`
      DELETE FROM [${AssignmentCountsHistoryTableName}]
    `)
  })
})
