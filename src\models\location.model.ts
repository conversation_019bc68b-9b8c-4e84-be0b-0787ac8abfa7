import { Table } from '@lcs/mssql-utility'
import { Location, LocationFields, LocationsTableName } from '@tess-f/sql-tables/dist/lms/location.js'
import { z } from 'zod'

const requiredFields = z.object({
  [LocationFields.AddressLine1]: z.string().max(150),
  [LocationFields.City]: z.string().max(50),
  [LocationFields.State]: z.string().max(50),
  [LocationFields.Zip]: z.string().max(15),
  [LocationFields.Title]: z.string().max(50),
  [LocationFields.Country]: z.string().max(100)
})

const optionalFields = z.object({
  [LocationFields.AddressLine2]: z.string().max(150).optional(),
  [LocationFields.Building]: z.string().max(50).optional(),
  [LocationFields.Room]: z.string().max(50).optional(),
})

export const createLocationSchema = requiredFields.merge(optionalFields)
export const updateLocationSchema = createLocationSchema.partial()

export default class LocationModel extends Table<Location, Location> {
  public fields: Location

  constructor (fields?: Location) {
    super(LocationsTableName, [
      LocationFields.AddressLine1,
      LocationFields.City,
      LocationFields.State,
      LocationFields.Zip,
      LocationFields.Title,
      LocationFields.CreatedBy,
      LocationFields.CreatedOn,
      LocationFields.Country
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: Location): void {
    this.fields = record
  }

  public exportJsonToDatabase (): Location {
    return this.fields
  }
}
