import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import getMultiple from './get-multiple.service.js'
import getPaginated from './get-paginated.service.js'
import getTeamCertificateCount from './get-team-certificate-count.service.js'
import getTeamsCertification from './get-teams-certificates.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'

describe('MSSQL Certificate', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
  })

  it('gets available parent nodes', async () => {
    const contexts = await getMultiple(AdminUserId)
    expect(contexts.length).to.be.gte(0)
  })

  it('gets paginated data', async () => {
    const contexts = await getPaginated(AdminUserId)
    expect(contexts.TotalRecords).to.be.gte(0)
  })

  it('gets team certification count', async () => {
    const contexts = await getTeamCertificateCount(AdminUserId)
    expect(contexts).to.be.gte(0)
  })

  it('gets team certification object', async () => {
    const contexts = await getTeamsCertification(AdminUserId, 1, 150)
    expect(contexts.TotalRecords).to.be.gte(0)
  })
})
