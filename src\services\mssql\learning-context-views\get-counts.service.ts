import mssql from '@lcs/mssql-utility'
import { LearningContextUserViewFields, LearningContextUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-view.js'

export default async function getContextViewCount (learningContextUserViewId: string, from?: Date, to?: Date): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('contextID', learningContextUserViewId)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const results = await request.query<{Views: number}>(`
    SELECT COUNT(*) AS Views
    FROM [${LearningContextUserViewsTableName}]
    WHERE [${LearningContextUserViewFields.LearningContextID}] = @contextID
    ${from && to ? `AND [${LearningContextUserViewFields.CreatedOn}] BETWEEN @from AND @to` : ''}
  `)

  return results.recordset[0].Views
}
