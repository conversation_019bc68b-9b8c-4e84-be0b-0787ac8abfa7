import get from '../../../services/mssql/learner-progress/get.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'
const { INTERNAL_SERVER_ERROR, BAD_REQUEST } = httpStatus

const log = logger.create('Controller-HTTP.get-learner-progress', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-get-learner-progress' label."

  try {
    const { userID, objectID } = z.object({
      userID: zodGUID,
      objectID: zodGUID
    }).parse(req.params)

    const learnerProgress = await get(userID, objectID)

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-get-learner-progress' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved learner progress', { id: learnerProgress.fields.ID, success: true, req })

    res.json(learnerProgress.fields)
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learner progress: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learner progress of user', { userId: req.params.userID, objectId: req.params.objectID, success: false, req })
      res.json({})
    } else {
      log('error', 'Failed to get learner progress.', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
