import type { Request, Response } from 'express'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
import getPrerequisiteStatusService from '../../../services/mssql/course-prerequisite/get-status.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-prerequisite-status', httpLogTransformer)

export default async function (req: Request, res: Response): Promise<void> {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const status = await getPrerequisiteStatusService(id, req.session.userId)
    log('info', 'Successfully retrieved prerequisite status', { prerequisiteId: id, status, success: true, req })
    res.json(status)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to retrieve prerequisite status: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter, '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', `Failed to retrieve prerequisite status: prerequisite not found`, { prerequisiteId: req.params.id, success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      log('error', 'Failed to retrieve prerequisite status', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
