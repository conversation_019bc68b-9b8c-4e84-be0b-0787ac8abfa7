import del from '../../../services/mssql/learning-context-favorites/delete.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NO_CONTENT } = httpStatus

const log = logger.create('Controller-HTTP.delete-learning-context-favorites', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, unfavorite a course. Note the time. Check the LMS API log for the 'http-delete-learning-context-favorites' label."

  try {
    const { contextID, userID } = z.object({
      contextID: zodGUID,
      userID: zodGUID
    }).parse(req.params)

    await del(contextID, userID)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, unfavorite a course. Note the time. Check the LMS API log for the 'http-delete-learning-context-favorites' label and message indicating successful deletion."
    log('info', 'Successfully deleted learning context favorite', { contextID, userID, success: true, req })

    res.sendStatus(NO_CONTENT)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete learning context favorite: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else {
      log('error', 'Failed to delete learning context favorite.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
