import getIds from '../../../services/mssql/learning-context/get-parent-context-ids.service.js'
import logger from '@lcs/logger'
import { getErrorMessage, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.Get-Parent-Context-IDs', getErrorMessage)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const ids = await getIds(id)
    log('info', 'Successfully retrieved parent ids for context', { count: ids.length, forContext: id, success: true, req })
    res.json(ids)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to retrieve parent ids for context: input validation error', { contextID: req.params.id, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else {
      log('error', 'Failed to retrieve parent ids for context', { forContext: req.params.id, success: false, error, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
