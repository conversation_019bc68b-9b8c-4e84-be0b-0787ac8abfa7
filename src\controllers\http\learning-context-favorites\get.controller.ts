import { Request, Response } from 'express'
import get, { getContextFavoritesForUser, getFortiesForContext } from '../../../services/mssql/learning-context-favorites/get.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NOT_FOUND } = httpStatus
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-learning-context-favorites', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { contextID, userID } = z.object({
      contextID: zodGUID.optional(),
      userID: zodGUID.optional()
    }).parse(req.query)

    if (contextID && userID) {
      const result = await get(contextID, userID)
      log('info', 'Successfully retrieved learning context favorite', { success: true, req })
      res.json(result.fields)
    } else if (contextID) {
      const results = await getFortiesForContext(contextID)
      log('info', 'Successfully retrieved learning context favorite', { success: true, req })
      res.json(results.map(favorite => favorite.fields))
    } else if (userID) {
      const results = await getContextFavoritesForUser(userID)
      log('info', 'Successfully retrieved learning context favorite', { success: true, req })
      res.json(results.map(favorite => favorite.fields))
    } else {
      log('warn', 'Failed to get learning context favorites, invalid args', { success: false, req })
      res.status(BAD_REQUEST).send('Invalid args')
    }
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning context favorites: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', `Failed to get favorites for learning context: because they were not found in the database.`, { userId: req.query.userID, contextId: req.query.contextID, success: false, req })
      res.sendStatus(NOT_FOUND)
    } else {
      log('error', 'Failed to get learning context favorites.', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
