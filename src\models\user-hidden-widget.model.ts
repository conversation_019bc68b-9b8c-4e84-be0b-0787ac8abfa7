import { Table } from '@lcs/mssql-utility'
import { UserHiddenWidget, UserHiddenWidgetFields, UserHiddenWidgetsTableName } from '@tess-f/sql-tables/dist/lms/user-hidden-widget.js'

export default class UserHiddenWidgetModel extends Table<UserHiddenWidget, UserHiddenWidget> {
  public fields: UserHiddenWidget

  constructor (fields?: UserHiddenWidget) {
    super(UserHiddenWidgetsTableName, [
      UserHiddenWidgetFields.UserID,
      UserHiddenWidgetFields.WidgetID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: UserHiddenWidget): void {
    this.fields = record
  }

  public exportJsonToDatabase (): UserHiddenWidget {
    return this.fields
  }
}
