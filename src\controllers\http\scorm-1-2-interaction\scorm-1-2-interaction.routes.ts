import { Request<PERSON><PERSON><PERSON>, Router } from 'express'
import createInteractions from './create-multiple.controller.js'
import getForObjectController from './get-for-object.controller.js'
import downloadAllController from './download-all.controller.js'

const router = Router()

router.post('/scorm-1-2-interactions', createInteractions as RequestHandler)
router.get('/scorm-1-2-interactions/:id', getForObjectController as RequestHandler)
router.get('/all-scorm-1-2-interactions', downloadAllController as RequestHandler)

export default router
