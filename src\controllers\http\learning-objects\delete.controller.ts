import del from '../../../services/mssql/learning-objects/delete.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.delete-learning-object', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, as an admin delete a learning object file. Note the time. Check the LMS API log for the 'http-delete-learning-object' label."

  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    await del(id)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, as an admin delete a learning object file. Note the time. Check the LMS API log for the 'http-delete-learning-object' label and message indicating successful deletion."
    log('info', 'Successfully deleted learning object', { id, success: true, req })

    res.sendStatus(httpStatus.NO_CONTENT)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete learning object: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      // STIG V-69427 changing data (failure)
      log('warn', 'Failed to delete learning object because it was not found in the database.', { id: req.params.id, success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      // STIG V-69427 changing data (failure)
      log('error', 'Failed to delete learning object.', { errorMessage, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
