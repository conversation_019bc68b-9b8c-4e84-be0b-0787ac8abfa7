import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import create from './create-multiple.service.js'
import get from './get-multiple.service.js'
import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import WidgetModel from '../../../models/widget.model.js'
import UserWidgetOrderOverrideModel from '../../../models/user-widget-order-override.model.js'
import createWidgetService from '../widgets/create.service.js'
import { UserWidgetOrderOverridesTableName } from '@tess-f/sql-tables/dist/lms/user-widget-order-override.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { v4 as uuidv4 } from 'uuid'

let widget: WidgetModel
let userWidgetOrderOverride: UserWidgetOrderOverrideModel[]

describe('MSSQL Widget User Overrides Service', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    widget = await createWidgetService(new WidgetModel({
      Title: 'Running widget user overrides MSSQL unit test',
      Carousel: true,
      Filter: false,
      IsAdmin: true,
      CreatedBy: uuidv4(),
      OrderID: 1150,
      TypeID: 1
    }))
  })

  it('Unit test to create user widget order override', async () => {
    userWidgetOrderOverride = await create([new UserWidgetOrderOverrideModel({
      UserID: AdminUserId,
      WidgetID: widget.fields.ID,
      OrderID: widget.fields.OrderID,
      ColumnID: widget.fields.ColumnID
    })])
    console.log(`New claim created with string ${userWidgetOrderOverride[0].fields.WidgetID}`)
    expect(userWidgetOrderOverride.length).to.be.eq(1)
  })

  it('Unit test to get user widget order override', async () => {
    const results = await get(userWidgetOrderOverride[0].fields.UserID!)
    expect(results.length).to.be.eq(1)
  })

  after('Delete created objects in SQL', async () => {
    await mssql.getPool().request().query(`
        DELETE FROM [${UserWidgetOrderOverridesTableName}]
      `)
  })
})
