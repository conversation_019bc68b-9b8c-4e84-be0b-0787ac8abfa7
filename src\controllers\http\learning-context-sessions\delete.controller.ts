import del from '../../../services/mssql/learning-context-sessions/delete.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NOT_FOUND, NO_CONTENT } = httpStatus
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.delete-learning-context-session', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, un-bookmark a course. Note the time. Check the LMS API log for the 'http-delete-learning-context-bookmark' label."

  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const numRowsAffected = await del(id)

    if (numRowsAffected > 0) {
      // STIG V-69427 changing data (success)
      log('info', 'Successfully deleted learning context session', { id, success: true, req })

      res.sendStatus(NO_CONTENT)
    } else {
      // STIG V-69427 changing data (failure)
      log('warn', 'Failed to delete learning context session because it was not found in the database.', { id, success: false, req })

      res.sendStatus(NOT_FOUND)
    }
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete learning context session: input validation error', { success: false, req, error })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter: '))
    } else {
      log('error', 'Failed to delete learning context session.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
