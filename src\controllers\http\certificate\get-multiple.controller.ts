import logger from '@lcs/logger'
import getCertificates from '../../../services/mssql/certificate/get-multiple.service.js'
import { Request, Response } from 'express'
import { getKeywordsForContext, getKeywordsForObject } from '../../../services/mssql/keywords/get-multiple.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-user-certificates', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    let userID: string
    const forUserId = zodGUID.safeParse(req.query.for)
    if (forUserId.success) {
      userID = forUserId.data
    } else {
      userID = req.session.userId
    }

    const certs = await Promise.all((await getCertificates(userID)).map(async cert => {
      if (cert.EntityType === 'Learning Context') {
        try {
          cert.Keywords = await getKeywordsForContext(cert.ID!)
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get keywords for learning context', { contextId: cert.ID, errorMessage, success: false, req })
            throw error
          }
        }
      } else {
        try {
          cert.Keywords = await getKeywordsForObject(cert.ID!)
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get keywords for learning object', { objectId: cert.ID, errorMessage, success: false, req })
            throw error
          }
        }
      }
      return cert
    }))

    log('info', 'Successfully fetched certificate records for user', { certificateCount: certs.length, userID, success: true, req })

    res.json(certs)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to fetch certificate records for user', { errorMessage, success: false, req })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
