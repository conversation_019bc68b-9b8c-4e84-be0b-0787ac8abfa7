import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'

describe('HTTP all-met controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./all-met.controller', {
            '../../../services/mssql/course-prerequisite/get-for-context.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            }            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                contextId: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./all-met.controller', {
            '../../../services/mssql/course-prerequisite/get-for-context.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            }            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                contextId: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request parameter')
        expect(mocks.res._getData()).include('contextId')
        expect(mocks.res._getData()).include('Expected string, received boolean')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./all-met.controller', {
            '../../../services/mssql/course-prerequisite/get-for-context.service.js': {
                default: Sinon.stub().rejects(Promise.resolve([]))
            }            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                contextId: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })


})