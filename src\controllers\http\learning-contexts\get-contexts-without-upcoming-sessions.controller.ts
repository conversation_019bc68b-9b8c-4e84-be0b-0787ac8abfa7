import get from '../../../services/mssql/learning-context/get-ILT-contexts-without-upcoming-sessions.service.js'
import logger from '@lcs/logger'
import { DB_Errors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'
const { INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.get-ilt-contexts-without-sessions', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const contexts = await get(id)
    log('info', 'Successfully retrieved ILT courses without upcoming sessions', { count: contexts.length, success: true, req })
    res.json(contexts.map(context => context.fields))
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Unable to retrieve ILT courses without upcoming sessions: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Unable to retrieve ILT courses without upcoming sessions, because all contexts have sessions', { success: false, req })
      res.json([])
    } else {
      log('error', 'Unable to get ILT courses without upcoming sessions.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
