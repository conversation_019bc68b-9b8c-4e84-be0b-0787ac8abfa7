import logger from '@lcs/logger'
import { DB_Errors } from '@lcs/mssql-utility'
import LearningObjectModel from '../models/learning-object.model.js'
import getObjectUserFavorite from '../services/mssql/learning-object-favorites/get.service.js'
import getObjectUserBookmark from '../services/mssql/learning-object-bookmarks/get.service.js'
import { getObjectRatingsForUser } from '../services/mssql/learning-object-ratings/get.service.js'
import getObjectCompletion from '../services/mssql/learning-objects/get-completion.service.js'
import { getKeywordsForObject } from '../services/mssql/keywords/get-multiple.service.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import getObjectiveIdsForLearningObject from '../services/mssql/learning-object-objectives/get-multi.service.js'
import getRatingForObject from '../services/mssql/learning-object-ratings/get-average-for-object.service.js'

const log = logger.create('Mapper.Learning-object-extras')

export default async function (data: LearningObjectModel[], userID: string, opts: {
  rating?: boolean
  bookmark?: boolean
  favorite?: boolean
  completion?: boolean
  keywords?: boolean
}): Promise<void[]> {
  return await Promise.all(data.map(async obj => {
    if (opts.bookmark) {
      try {
        obj.fields.UserBookmark = (await getObjectUserBookmark(obj.fields.ID!, userID)).fields
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
          log('error', 'Failed to get user bookmark', { id: obj.fields.ID, errorMessage, success: false })
          throw error
        }
      }
    }

    if (opts.rating) {
      try {
        obj.fields.UserRating = (await getObjectRatingsForUser(obj.fields.ID!, userID)).fields
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
          log('error', 'Failed to get user rating=', { id: obj.fields.ID, errorMessage, success: false })
          throw error
        }
      }
    }

    if (opts.favorite) {
      try {
        obj.fields.UserFavorite = (await getObjectUserFavorite(obj.fields.ID!, userID)).fields
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
          log('error', 'Failed to get user favorite', { id: obj.fields.ID, errorMessage, success: false })
          throw error
        }
      }
    }

    if (opts.completion) {
      try {
        obj.fields.PercentComplete = await getObjectCompletion(obj.fields.ID!, userID)
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        log('error', 'Failed to get learning object completion', { id: obj.fields.ID, errorMessage, success: false })
      }
    }

    if (opts.keywords) {
      try {
        obj.fields.Keywords = await getKeywordsForObject(obj.fields.ID!)
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
          log('error', 'Failed to get keywords', { id: obj.fields.ID, errorMessage, success: false })
        } else {
          log('debug', 'Failed to get keywords for object', { id: obj.fields.ID, errorMessage, success: false })
        }
      }
    }

    try {
      obj.fields.ObjectiveIds = await getObjectiveIdsForLearningObject(obj.fields.ID ?? '')
    } catch (error) {
      log('error', 'Failed to get objective IDs for learning object', { id: obj.fields.ID, errorMessage: getErrorMessage(error), success: false })
      obj.fields.ObjectiveIds = []
    }

    try {
      const objectRating = await getRatingForObject(obj.fields.ID!)
      obj.fields.Rating = objectRating.average
      obj.fields.RatingCount = objectRating.count
    } catch (error) {
      if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
        log('error', 'Failed to get average rating for object ID ', { objectId: obj.fields.ID, error, success: false })
      }
    }
  }))
}
