const { expect } = require('chai')
const httpMocks = require('node-mocks-http')
const logger = require('../api/../shared_libraries/libs/logger')
const utils = require('../api/utils/test-agent.utils')
const settings = require('../api/config/settings')

const claimsGetMulti = require('../api/controllers/http/claims/get-multiple.controller')
const contentGetPopular = require('../api/controllers/http/content/get-popular.controller')
const contentGetRecent = require('../api/controllers/http/content/get-recent.controller')
const dashboardsGetAdminOveriew = require('../api/controllers/http/dashboards/get-admin-overview.controller')
const dashboardsGetUserActivity = require('../api/controllers/http/dashboards/get-user-activity-dashboard.controller')
const keywordsGetMultiple = require('../api/controllers/http/keywords/get-multiple.controller')
const learningObjectsGetMulti = require('../api/controllers/http/learning-objects/get-multiple.controller')
const skillLevelsGetMulti = require('../api/controllers/http/skill-levels/get-multiple.controller')
const systemInfoGet = require('../api/controllers/http/system-info/get.controller')
const usersGetMulti = require('../api/controllers/http/users/get-multiple.controller')

describe('STIG logging for catastrophic failure conditions', () => {
  before(async () => {
    await logger.init({
      name: 'test-lms-api',
      level: 'silly',
      use_console: true,
      use_elasticsearch: false
    })

    // Force an invalid database password to force failures
    settings.db.password = 'thisisthewrongpassword'
  })

  it('should log failed http-get-multi-claims', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    claimsGetMulti(req, res)
  })

  it('should log failed http-get-popular-content', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    contentGetPopular(req, res)
  })

  it('should log failed http-get-popular-content', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    contentGetPopular(req, res)
  })

  it('should log failed http-get-recent-content', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    contentGetRecent(req, res)
  })

  it('should log failed http-get-admin-overview-dashboard', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    dashboardsGetAdminOveriew(req, res)
  })

  it('should log failed http-get-user-activity-dashboard', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    dashboardsGetUserActivity(req, res)
  })

  it('should log failed http-get-multi-keywords', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    keywordsGetMultiple(req, res)
  })

  it('should log failed http-get-multi-learning-objects', done => {
    const req = utils.createRequestWithHeaders({
      query: { contextID: 0 }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    learningObjectsGetMulti(req, res)
  })

  it('should log failed http-get-multi-skill-level', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    skillLevelsGetMulti(req, res)
  })

  it('should log failed http-get-system-info', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    systemInfoGet(req, res)
  })

  it('should log failed http-get-multi-users', done => {
    const req = utils.createRequestWithHeaders({
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(500)
      done()
    })

    usersGetMulti(req, res)
  })

  after(done => {
    logger.close()
    done()
  })
})
