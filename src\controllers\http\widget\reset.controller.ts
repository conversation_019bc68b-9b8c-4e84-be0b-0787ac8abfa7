import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR, NO_CONTENT } = httpStatus
import resetWidgetService from '../../../services/mssql/widgets/reset.service.js'
import { httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('Controller-HTTP.reset-widgets', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    await resetWidgetService(req.session.userId)
    log('info', 'Successfully reset the widgets for user', { userID: req.session.userId, success: true, req })
    res.sendStatus(NO_CONTENT)
  } catch (error) {
    log('error', 'Failed to reset users widgets', { userID: req.session.userId, error, success: false, req })
    res.sendStatus(INTERNAL_SERVER_ERROR)
  }
}
