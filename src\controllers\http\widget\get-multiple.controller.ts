import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getUserWidgetsService from '../../../services/mssql/widgets/get-for-user.service.js'
import getUserWidgetOrderOverrides from '../../../services/mssql/widget-user-overrides/get-multiple.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import getGroupIdsForUser from '../../../services/mssql/groups/get-ids-of-group-for-user.service.js'
import httpStatus from 'http-status'
import { httpLogTransformer } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-widgets', httpLogTransformer)

export default async function getWidgets (req: Request, res: Response) {
  try {
    let userID: string | undefined
    let groupIDs: string[]
    const bodyGroupIds = z.object({ groupIds: z.array(zodGUID) }).safeParse(req.body)

    if (bodyGroupIds.success) {
      groupIDs = bodyGroupIds.data.groupIds
    } else {
      userID = req.session.userId
      // get the users group IDs
      groupIDs = await getGroupIdsForUser(userID)
    }

    const widgets = await getUserWidgetsService(userID, groupIDs)

    // set orderID overrides
    if (userID) {
      try {
        const overrides = await getUserWidgetOrderOverrides(userID)
        widgets.adminWidgets.forEach(widget => {
          const override = overrides.find(userOverride => userOverride.fields.WidgetID === widget.fields.ID)
          if (override) {
            widget.fields.OrderID = override.fields.OrderID
            widget.fields.ColumnID = override.fields.ColumnID
          }
        })
      } catch (error) {
        // if we didn't find any overrides that's fine
        if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
          throw error
        }
      }
    }

    log('info', 'Successfully retrieved home page widgets', { count: widgets.adminWidgets.length + widgets.userWidgets.length, success: true, req })

    res.json({
      userWidgets: widgets.userWidgets.map(widget => widget.fields),
      adminWidgets: widgets.adminWidgets.map(widget => widget.fields)
    })
  } catch (error) {
    log('error', 'Failed to get widgets for home page', { error, success: false, req })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
