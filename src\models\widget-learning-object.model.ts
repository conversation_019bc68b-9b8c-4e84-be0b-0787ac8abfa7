import { Table } from '@lcs/mssql-utility'
import { WidgetLearningObject, WidgetLearningObjectFields, WidgetLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/widget-learning-object.js'

export default class WidgetLearningObjectModel extends Table<WidgetLearningObject, WidgetLearningObject> {
  public fields: WidgetLearningObject

  constructor (fields?: WidgetLearningObject) {
    super(WidgetLearningObjectsTableName, [
      WidgetLearningObjectFields.WidgetID,
      WidgetLearningObjectFields.LearningObjectID,
      WidgetLearningObjectFields.OrderID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: WidgetLearningObject): void {
    this.fields = record
  }

  public exportJsonToDatabase (): WidgetLearningObject {
    return this.fields
  }
}
