import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { User, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { getBasicUserFields } from './utils.js'

export default async function getUserById (id: string): Promise<User> {
  const request = mssql.getPool().request()
  request.input('id', id)

  const results = await request.query<User>(`
    SELECT ${getBasicUserFields()}
    FROM ${UserTableName}
    WHERE [${UserFields.ID}] = @id
  `)

  if (results.recordset.length > 0) {
    return results.recordset[0]
  }
  throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
}
