import { expect } from 'chai'
import mssql, { addRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import updateStatus from './update-user-context-session-status.service.js'
import createCourse from '../learning-context/create.service.js'
import createSession from '../learning-context-sessions/create.service.js'

import AssignedMultiSessionCourse from '../../../models/user-assigned-multi-session-course.model.js'
import LearningContext from '../../../models/learning-context.model.js'
import LearningContextSession from '../../../models/learning-context-session.model.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { SessionStatuses } from '@tess-f/sql-tables/dist/lms/session-status.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearnerProgress } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { Assignment } from '@tess-f/sql-tables/dist/lms/assignment.js'
import AssignmentsModel from '../../../models/assignment.model.js'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import { AssignmentTypes } from '@tess-f/sql-tables/dist/lms/assignment-type.js'

let assignment: Assignment
let assignedCourse: AssignedMultiSessionCourse
let learningContext: LearningContext
let learningContextSession: LearningContextSession
let learnerProgress: LearnerProgress

// FIXME: need to update the set up for these tests
xdescribe('MSSQL User Assigned Multi Session Courses', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    assignment = await addRow<Assignment>(pool.request(), new AssignmentsModel({
      Title: 'Running user assigned multi session courses MSSQL unit test',
      TypeID: AssignmentTypes.Manual,
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    }))
    // create course and session.
    learningContext = (await createCourse(new LearningContext({
      Title: 'Test multi session courses',
      Description: `Running user-assigned-multi-session-courses on ${new Date()}`,
      ContextTypeID: LearningContextTypes.Course,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false
    })))
    assignedCourse = new AssignedMultiSessionCourse({
      AssignmentID: assignment.ID,
      UserID: AdminUserId,
      CreatedOn: new Date(),
      LearningContextID: learningContext.fields.ID
    })
    learningContextSession = await createSession(new LearningContextSession({
      SessionID: 'TestSession3',
      DisplayEnrollmentsOnHomePage: false,
      EnableWaitlist: false,
      SessionStatusID: SessionStatuses.Hidden,
      StartDate: new Date(),
      EndDate: new Date(),
      CreatedOn: new Date(),
      CreatedBy: AdminUserId,
      LearningContextID: learningContext.fields.ID,
      Timezone: 'UTC'
    }))
    learnerProgress = await addRow<LearnerProgress>(pool.request(), new LearnerProgressModel({
      LessonStatusID: 1,
      UserID: AdminUserId,
      CreatedOn: new Date(),
      LearningContextSessionID: learningContextSession.fields.ID
    }))
  })

  it('can update an assigned multi session courses status', async () => {
    const results = await updateStatus(AdminUserId, learningContext.fields.ID!, LessonStatuses.passed, learningContextSession.fields.ID!, learnerProgress.ID!)
    expect(results).to.gte(1)
  })

  after('Delete created objects in SQL', async () => {
    await mssql.getPool().request().query(`
        DELETE FROM [${LearningContextTableName}]
      `)
  })
})
