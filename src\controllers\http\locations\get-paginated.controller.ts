import getAllLocations from '../../../services/mssql/locations/get-paginated.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodLimit, zodOffset } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-multiple-locations', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { offset, limit, search } = z.object({
      offset: zodOffset,
      limit: zodLimit,
      search: z.coerce.string().optional()
    }).parse(req.query)

    const locations = await getAllLocations(offset, limit, search)

    // STIG V-69425 data access (success)
    log('info', 'Successfully retrieved location(s).', { success: true, req })

    res.json({
      totalRecords: locations.totalRecords,
      locations: locations.locations.map(location => location.fields)
    })
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get locations due to invalid data in the request.', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request query data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('info', 'Failed to get locations because none were found in the database.', { error, success: false, req })
      res.json([])
    } else {
      log('error', 'Failed to retrieve locations.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
