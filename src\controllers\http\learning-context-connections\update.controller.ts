import update from '../../../services/mssql/learning-context-connections/update.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import LearningContextConnection, { updateLearningContextConnectionSchema } from '../../../models/learning-context-connection.model.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NOT_FOUND } = httpStatus

const log = logger.create('Controller-HTTP.update-learning-context-connection', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const learningContextConnection = new LearningContextConnection(updateLearningContextConnectionSchema.parse(req.body))
    const { parentContextID, connectedContextID } = z.object({
      parentContextID: zodGUID,
      connectedContextID: zodGUID
    }).parse(req.params)
  
    learningContextConnection.fields.ParentContextID = parentContextID
    learningContextConnection.fields.ConnectedContextID = connectedContextID
    learningContextConnection.fields.ModifiedBy = req.session.userId
    learningContextConnection.fields.ModifiedOn = new Date()

    const updated = await update(learningContextConnection)

    log('info', 'Successfully updated learning context connection', { parentContextID, connectedContextID, success: true, req })
    res.json(updated.fields)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update learning context connection: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to update learning context connection because it was not found in the database.', { parentContextId: req.params.parentContextID, connectedContextId: req.params.connectedContextID, success: false, req })
      res.sendStatus(NOT_FOUND)
    } else {
      log('error', 'Failed to update the learning context connection.', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
