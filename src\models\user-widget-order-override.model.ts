import { Table } from '@lcs/mssql-utility'
import { UserWidgetOrderOverride, UserWidgetOrderOverrideFields, UserWidgetOrderOverridesTableName } from '@tess-f/sql-tables/dist/lms/user-widget-order-override.js'

export default class UserWidgetOrderOverrideModel extends Table<UserWidgetOrderOverride, UserWidgetOrderOverride> {
  public fields: UserWidgetOrderOverride

  constructor (fields?: UserWidgetOrderOverride) {
    super(UserWidgetOrderOverridesTableName, [
      UserWidgetOrderOverrideFields.UserID,
      UserWidgetOrderOverrideFields.WidgetID,
      UserWidgetOrderOverrideFields.OrderID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: UserWidgetOrderOverride): void {
    this.fields = record
  }

  public exportJsonToDatabase (): UserWidgetOrderOverride {
    return this.fields
  }
}
