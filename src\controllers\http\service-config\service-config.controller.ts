import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR } = httpStatus
import settings from '../../../config/settings.js'
import { httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('Controller-HTTP.service-config', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    log('verbose', 'sending service config', { success: true, req })
    res.json({
      requireAssignmentDueDate: settings.assignments.requireDueDates,
      chartTheme: settings.appConfig.chartTheme,
      homeImage: settings.appConfig.homeImage
    })
  } catch (error) {
    log('error', 'Failed to get service config', { error, req, success: false })
    res.sendStatus(INTERNAL_SERVER_ERROR)
  }
}
