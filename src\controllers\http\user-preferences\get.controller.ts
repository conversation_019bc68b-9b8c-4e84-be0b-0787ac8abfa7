import { Request, Response } from 'express'
import getService from '../../../services/mssql/user-preferences/get.service.js'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.get-user-preferences', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "Launch the LMS application and log in. Note the time. Check the LMS API log for the 'http-get-user-preferences' label."
  try {
    const { userID } = z.object({ userID: zodGUID }).parse(req.params)
    const result = await getService(userID)

    // STIG V-69425 data access (success)
    // STIGTEST "Launch the LMS application and log in. Note the time. Check the LMS API log for the 'http-get-user-preferences' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved user preferences for user', { success: true, userID, req })

    res.json(result.fields)
  } catch (error) {
    // STIG V-69425 data access (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get user preferences due to invalid data in the request.', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else {
      log('error', 'Failed to get user preferences.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
