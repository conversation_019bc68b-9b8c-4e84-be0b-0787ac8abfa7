import mssql, { addRow } from '@lcs/mssql-utility'
import { SystemWidget } from '@tess-f/sql-tables/dist/lms/system-widget.js'
import SystemWidgetModel from '../../../models/system-widget.model.js'

export default async function createSystemWidget (widget: SystemWidgetModel): Promise<SystemWidgetModel> {
  const pool = mssql.getPool()
  const record = await addRow<SystemWidget>(pool.request(), widget)
  return new SystemWidgetModel(record)
}
