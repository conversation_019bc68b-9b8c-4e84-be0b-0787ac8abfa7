import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { UserGroupFields, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'

export async function getInProgressUserIDs (offset = 0, limit = 10, objectID: string, from?: Date, to?: Date, search?: string, groupIDs?: string[]): Promise<{totalRecords: number, ids: string[]}> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('objectID', objectID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  let groupConditions: string[] = []
  if (groupIDs && groupIDs.length > 0) {
    groupConditions = groupIDs.map((id, index) => {
      request.input(`group_${index}`, id)
      return `@group_${index}`
    })
  }

  let query = `
    SELECT [${UserFields.ID}], TotalRecords = COUNT(*) OVER()
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] IN (
      SELECT [${LearnerProgressFields.UserID}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.LearningObjectID}] = @objectID
      AND [${LearnerProgressFields.LessonStatusID}] < ${LessonStatuses.fail}
      ${from && to ? `AND [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to` : ''}
    ) AND [${UserFields.ID}] NOT IN (
      SELECT [${LearnerProgressFields.UserID}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.LearningObjectID}] = @objectID
      AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.fail}
      ${from && to ? `AND [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to` : ''}
    )
  `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [UserFields.FirstName, UserFields.LastName], 'any')}) `
  }

  if (groupConditions.length > 0) {
    query += `
      AND [${UserFields.ID}] IN (
        SELECT [${UserGroupFields.UserID}]
        FROM [${UserGroupTableName}]
        WHERE [${UserGroupFields.GroupID}] IN (${groupConditions.join(', ')})
      )
    `
  }

  request.input('offset', offset)
  request.input('limit', limit)

  query += `
    ORDER BY [${UserFields.FirstName}], [${UserFields.LastName}]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<{ID: string, TotalRecords: number}>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    ids: results.recordset.map(record => record.ID)
  }
}

export async function getCompletedUserIDs (offset = 0, limit = 10, objectID: string, from?: Date, to?: Date, search?: string, groupIDs?: string[]): Promise<{totalRecords: number, ids: string[]}> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('objectID', objectID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  let groupConditions: string[] = []
  if (groupIDs && groupIDs.length > 0) {
    groupConditions = groupIDs.map((id, index) => {
      request.input(`group_${index}`, id)
      return `@group_${index}`
    })
  }

  let query = `
    SELECT [${UserFields.ID}], TotalRecords = COUNT(*) OVER()
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] IN (
      SELECT [${LearnerProgressFields.UserID}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.LearningObjectID}] = @objectID
      AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.fail}
      ${from && to ? `AND [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to` : ''}
    )
  `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [UserFields.FirstName, UserFields.LastName], 'any')}) `
  }

  if (groupConditions.length > 0) {
    query += `
      AND [${UserFields.ID}] IN (
        SELECT [${UserGroupFields.UserID}]
        FROM [${UserGroupTableName}]
        WHERE [${UserGroupFields.GroupID}] IN (${groupConditions.join(', ')})
      )
    `
  }

  request.input('offset', offset)
  request.input('limit', limit)

  query += `
    ORDER BY [${UserFields.FirstName}], [${UserFields.LastName}]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<{ID: string, TotalRecords: number}>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    ids: results.recordset.map(record => record.ID)
  }
}
