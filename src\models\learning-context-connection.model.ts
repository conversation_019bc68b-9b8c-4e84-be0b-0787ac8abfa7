import { Table } from '@lcs/mssql-utility'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { LearningContextConnection, LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'
import { z } from 'zod'

export const createLearningContextConnectionSchema = z.object({
  [LearningContextConnectionFields.ParentContextID]: zodGUID,
  [LearningContextConnectionFields.ConnectedContextID]: zodGUID,
  [LearningContextConnectionFields.OrderID]: z.number().int()
})

export const updateLearningContextConnectionSchema = z.object({
  [LearningContextConnectionFields.OrderID]: z.number().int()
})

export default class LearningContextConnectionModel extends Table<LearningContextConnection, LearningContextConnection> {
  public fields: LearningContextConnection

  constructor (fields?: LearningContextConnection) {
    super(LearningContextConnectionsTableName, [
      LearningContextConnectionFields.ParentContextID,
      LearningContextConnectionFields.ConnectedContextID,
      LearningContextConnectionFields.OrderID,
      LearningContextConnectionFields.CreatedBy,
      LearningContextConnectionFields.CreatedOn
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearningContextConnection): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningContextConnection {
    return this.fields
  }
}
