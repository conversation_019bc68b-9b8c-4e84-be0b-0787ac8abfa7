import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import reorderService from '../../../services/mssql/context-learning-matrix/reorder.service.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.ReOrder-Learning-Matrix', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { oldOrderID, newOrderID, parentID, itemID } = z.object({
      oldOrderID: z.coerce.number().int(),
      newOrderID: z.coerce.number().int(),
      parentID: zodGUID,
      itemID: zodGUID
    }).parse(req.params)

    await reorderService(parentID, itemID, oldOrderID, newOrderID)
    log('info', 'Successfully reordered the learning matrix', { success: true, req })
    res.sendStatus(httpStatus.NO_CONTENT)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Invalid request parameters', { orderID: req.params, success: false, req, error })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter(s), '))
    } else {
      log('error', 'Failed to reorder the learning matrix', { error, req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
