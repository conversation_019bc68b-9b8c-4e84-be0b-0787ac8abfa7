/**
 * This services get's the active registration for a learning context or one does not exist it creates one
 */
import mssql, { addRow } from '@lcs/mssql-utility'
import LearningContextRegistrationModel from '../../../models/learning-context-registration.model.js'
import { LearningContextRegistration, LearningContextRegistrationFields, LearningContextRegistrationsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-registration.js'

export default async function getOrCreateLearningContextRegistration (learningContextId: string, userId: string): Promise<{ registration: LearningContextRegistrationModel, created: boolean }> {
  const request = mssql.getPool().request()
  request.input('contextId', learningContextId)
  request.input('userId', userId)

  const results = await request.query<LearningContextRegistration>(`
    SELECT *
    FROM [${LearningContextRegistrationsTableName}]
    WHERE [${LearningContextRegistrationFields.LearningContextId}] = @contextId
    AND [${LearningContextRegistrationFields.UserId}] = @userId
    AND [${LearningContextRegistrationFields.CompletedOn}] IS NULL
  `)

  if (results.recordset.length > 0) {
    return {
      created: false,
      registration: new LearningContextRegistrationModel(results.recordset[0])
    }
  } else {
    const record = await addRow<LearningContextRegistration>(
      mssql.getPool().request(),
      new LearningContextRegistrationModel({
        LearningContextId: learningContextId,
        UserId: userId,
        CreatedOn: new Date()
      })
    )
    return {
      registration: new LearningContextRegistrationModel(record),
      created: true
    }
  }
}
