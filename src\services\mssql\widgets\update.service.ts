import mssql, { updateRow, deleteRow, addRow } from '@lcs/mssql-utility'
import WidgetModel from '../../../models/widget.model.js'
import WidgetKeywordModel from '../../../models/widget-keyword.model.js'
import WidgetLearningContextModel from '../../../models/widget-learning-context.model.js'
import WidgetLearningObjectModel from '../../../models/widget-learning-object.model.js'
import { WidgetKeyword, WidgetKeywordsTableName } from '@tess-f/sql-tables/dist/lms/widget-keyword.js'
import { WidgetLearningContext, WidgetLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/widget-learning-context.js'
import { WidgetLearningObject, WidgetLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/widget-learning-object.js'
import { Widget } from '@tess-f/sql-tables/dist/lms/widget.js'

export default async function updateWidgetService (widget: WidgetModel): Promise<WidgetModel> {
  const pool = mssql.getPool()

  // first we need to delete out all of the old keywords, learning context, and learning objects if they are being updated
  if (widget.fields.Keywords) {
    await deleteRow(pool.request(), WidgetKeywordsTableName, { WidgetID: widget.fields.ID })
  }
  if (!widget.fields.Filter && ((widget.fields.LearningContexts && widget.fields.LearningContexts.length > 0) || (widget.fields.LearningObjects && widget.fields.LearningObjects.length > 0))) {
    await deleteRow(pool.request(), WidgetLearningContextsTableName, { WidgetID: widget.fields.ID })
    await deleteRow(pool.request(), WidgetLearningObjectsTableName, { WidgetID: widget.fields.ID })
  }
  const record = await updateRow<Widget>(pool.request(), widget, { ID: widget.fields.ID })
  const updated = new WidgetModel(undefined, record[0])

  // if this widget is filtered we need to add the keywords if any are present
  if (updated.fields.Filter && widget.fields.Keywords) {
    const keywords = await Promise.all(widget.fields.Keywords.map(async key => {
      const keywordDBrecord = await addRow<WidgetKeyword>(pool.request(), new WidgetKeywordModel({
        WidgetID: updated.fields.ID,
        Keyword: key
      }))
      return new WidgetKeywordModel(keywordDBrecord)
    }))

    updated.fields.Keywords = keywords.map(key => key.fields.Keyword!)
  }

  // if this widget is not filtered we need to add the specified content
  if (!updated.fields.Filter) {
    if (widget.fields.LearningObjects && widget.fields.LearningObjects.length > 0) {
      await Promise.all(widget.fields.LearningObjects.map(async obj => {
        await addRow<WidgetLearningObject>(pool.request(), new WidgetLearningObjectModel({
          WidgetID: updated.fields.ID,
          LearningObjectID: obj.ID,
          OrderID: obj.OrderID!
        }))
      }))

      updated.fields.LearningObjects = widget.fields.LearningObjects
    }

    if (widget.fields.LearningContexts && widget.fields.LearningContexts.length > 0) {
      await Promise.all(widget.fields.LearningContexts.map(async context => {
        await addRow<WidgetLearningContext>(pool.request(), new WidgetLearningContextModel({
          WidgetID: updated.fields.ID,
          LearningContextID: context.ID,
          OrderID: context.OrderID!
        }))
      }))

      updated.fields.LearningContexts = widget.fields.LearningContexts
    }
  }

  return updated
}
