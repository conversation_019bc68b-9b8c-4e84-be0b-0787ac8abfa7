import logger from '@lcs/logger'
import httpStatus from 'http-status'
import Sinon from 'sinon'
import { expect } from 'chai'
import httpMock from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: get paginated certificates', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns bad request when the request body is invalid', async () => {
    const controller = await esmock('./get-paginated.controller.js')
    const mocks = httpMock.createMocks({
      session: { userId: uuid() },
      body: {
        search: 1,
        labels: [2],
        objectTypeIds: true,
        keywords: [3],
        from: 'today',
        to: 'tomorrow'
      }
    })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('search: Expected string, received number')
    expect(data).to.include('labels.0: Expected string, received number')
    expect(data).to.include('objectTypeIds: Expected array, received boolean')
    expect(data).to.include('keywords.0: Expected string, received number')
    expect(data).to.include('from: Invalid date')
    expect(data).to.include('to: Invalid date')
  })

  it('returns ok status when the request body is valid', async () => {
    const controller = await esmock('./get-paginated.controller.js', {
      '../../../services/mssql/certificate/get-paginated.service.js': {
        default: Sinon.stub().resolves({ TotalRecords: 0, items: [] })
      }
    })
    const mocks = httpMock.createMocks({
      session: { userId: uuid() },
      body: {
        search: '1',
        labels: ['2'],
        objectTypeIds: [1],
        keywords: ['3'],
        from: new Date().toISOString(),
        to: new Date().toISOString()
      }
    })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const data = JSON.parse(mocks.res._getData())
    expect(data).to.not.be.undefined
    expect(data.items).to.be.an('array')
    expect(data.items.length).to.equal(0)
    expect(data.totalRecords).to.be.a('number')
    expect(data.totalRecords).to.equal(0)
  })
})
