import logger from '@lcs/logger'
import mssql from '@lcs/mssql-utility'
import LearnerProgressModel from '../../models/learner-progress.model.js'
import LearningContextModel from '../../models/learning-context.model.js'
import { getAllContextsThatUseIltSession, getAllContextsThatUseObject, getAllContextThatUseExam, GetNestedContexts } from '../mssql/learning-context/utils.service.js'
import gradeContextCompletion from './context-grading-services/grade-context-completion.service.js'
import gradeContextPassFail from './context-grading-services/grade-context-pass-fail.service.js'
import gradeContextPercentage from './context-grading-services/grade-percentage.service.js'
import gradeContextExam from './context-grading-services/grade-exam.service.js'
import UserCompletedLearningContextModel from '../../models/user-completed-learning-context.model.js'
import { issueCertificate, getLastCompletedDate, issueCmi5ContextSatisfactionStatement, completeCmi5CourseRegistration } from './context-grading-services/utils.js'
import create from '../mssql/user-completed-learning-contexts/create.service.js'
import updateProgress from '../mssql/learner-progress/update.service.js'
import { LearnerProgress, LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { GradeTypes } from '@tess-f/sql-tables/dist/lms/grade-type.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Background-Service.Grade-Contexts')

export default async function (learnerProgress: LearnerProgressModel, notifyUser: boolean = true): Promise<void> {
  const pool = mssql.getPool()
  // determine the max age our learning progress records can be
  let maxAge: Date = learnerProgress.fields.CreatedOn!
  if (learnerProgress.fields.CompletedDate && learnerProgress.fields.CompletedDate > maxAge) {
    maxAge = learnerProgress.fields.CompletedDate
  }
  if (learnerProgress.fields.ModifiedOn && learnerProgress.fields.ModifiedOn > maxAge) {
    maxAge = learnerProgress.fields.ModifiedOn
  }

  // get the learning contexts that this learner progress affects
  let learningContexts: LearningContextModel[] = []

  if (learnerProgress.fields.LearningObjectID) {
    learningContexts = await getAllContextsThatUseObject(pool.request(), learnerProgress.fields.LearningObjectID)
  } else if (learnerProgress.fields.LearningContextSessionID) {
    learningContexts = await getAllContextsThatUseIltSession(pool.request(), learnerProgress.fields.LearningContextSessionID)
  }

  // Loop through contexts and determine how to grade
  for (const context of learningContexts) {
    // get the date this context was last completed
    const lastCompleted = await getLastCompletedDate(pool.request(), context.fields.ID!, learnerProgress.fields.UserID!, maxAge)
    context.fields.Contexts = await GetNestedContexts(pool.request(), context.fields.ID!)

    try {
      let grade!: { LessonStatusID: number, RawScore: number, CompletionDate: Date | null }
      if (!context.fields.GradeTypeID) {
        // context has no grade type, we will just check if it's completed
        grade = await gradeContextCompletion(context, learnerProgress.fields.UserID!, lastCompleted, maxAge)
      } else if (context.fields.GradeTypeID === GradeTypes.CompleteIncomplete || context.fields.GradeTypeID === GradeTypes.PassFail) {
        // context has pass/fail complete/incomplete grade type
        grade = await gradeContextPassFail(context, learnerProgress.fields.UserID!, lastCompleted, maxAge)
      } else if (context.fields.GradeTypeID === GradeTypes.Percentage) {
        // context has percentage grade type
        grade = await gradeContextPercentage(context, learnerProgress.fields.UserID!, lastCompleted, maxAge)
      } else if (context.fields.GradeTypeID === GradeTypes.Exam) {
        grade = await gradeContextExam(context, learnerProgress.fields.UserID!, lastCompleted, maxAge)
      }
      await saveGrade(grade, learnerProgress, context, notifyUser)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      // silent failure
      if (error instanceof Error && error.message.indexOf('Violation of UNIQUE KEY') < 0) {
        log('error', 'Failed to grade context', { errorMessage, contextID: context.fields.ID, userID: learnerProgress.fields.UserID })
      }
    }
  }

  // next we need to grade the contexts that use the given object as an exam)
  if (learnerProgress.fields.LearningObjectID) {
    const contexts = await getAllContextThatUseExam(pool.request(), learnerProgress.fields.LearningObjectID)
    for (const context of contexts) {
      context.fields.Contexts = await GetNestedContexts(pool.request(), context.fields.ID!)
      const lastCompleted = await getLastCompletedDate(pool.request(), context.fields.ID!, learnerProgress.fields.UserID!, maxAge)
      try {
        const grade = await gradeContextExam(context, learnerProgress.fields.UserID!, lastCompleted, maxAge)
        log('debug', 'Successfully graded context', { contextID: context.fields.ID, userID: learnerProgress.fields.UserID, lastCompleted: lastCompleted?.toISOString(), maxAge: maxAge.toISOString(), status: grade.LessonStatusID, success: true })
        await saveGrade(grade, learnerProgress, context, notifyUser)
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        // silent failure
        if (error instanceof Error && error.message.indexOf('Violation of UNIQUE KEY') < 0) {
          log('error', 'Failed to grade context', { errorMessage, contextID: context.fields.ID, userID: learnerProgress.fields.UserID })
        }
      }
    }
  }
}

async function saveGrade (
  grade: { LessonStatusID: number, RawScore: number, CompletionDate: Date | null },
  learnerProgress: LearnerProgressModel,
  context: LearningContextModel,
  notifyUser: boolean
) {
  // only save the grade if it's completed for non grade types
  // or if it's status is higher than browsed for any other grade type
  // Do not grade ILT, that logic is in the next section
  if (
    (
      (!context.fields.GradeTypeID && grade.LessonStatusID > LessonStatuses.fail) ||
      (context.fields.GradeTypeID && grade.LessonStatusID > LessonStatuses.browsed)
    ) &&
    grade.CompletionDate !== null &&
    (
      context.fields.ContextTypeID !== LearningContextTypes.Course ||
      (
        context.fields.ContextTypeID === LearningContextTypes.Course &&
        context.fields.CourseTypeID === CourseTypes.InstructorLed &&
        context.fields.GradeTypeID !== GradeTypes.Exam
      )
    )
  ) {
    const completion = new UserCompletedLearningContextModel({
      LearningContextID: context.fields.ID,
      LessonStatusID: grade.LessonStatusID,
      UserID: learnerProgress.fields.UserID,
      CompletedOn: grade.CompletionDate || learnerProgress.fields.CreatedOn!,
      GradeTypeID: context.fields.GradeTypeID,
      Credits: context.fields.Credits,
      MinScore: context.fields.MinScore,
      MaxScore: context.fields.MaxScore,
      RawScore: grade.RawScore,
      Certificate: learnerProgress.fields.Certificate
    })

    // certificates are only issued when the context type is section, cmi5 course, or cmi5 block
    if (context.fields.EnableCertificates && grade.LessonStatusID >= LessonStatuses.passed && (
      context.fields.ContextTypeID === LearningContextTypes.Section || context.fields.ContextTypeID === LearningContextTypes.CMI5Block || context.fields.ContextTypeID === LearningContextTypes.CMI5Course
    )) {
      // passed or completed and certificate needs to be issued
      await issueCertificate(context, completion, notifyUser)
    } else {
      await create(completion)
    }

    // handle cmi5 satisfaction
    if (context.fields.ContextTypeID === LearningContextTypes.CMI5Block && grade.LessonStatusID >= LessonStatuses.passed) {
      // this is a cmi5 block let's notify the LRS that the user has satisfied the block
      await issueCmi5ContextSatisfactionStatement(learnerProgress.fields.UserID!, context, learnerProgress.fields.ID!)
    } else if (context.fields.ContextTypeID === LearningContextTypes.CMI5Course && grade.LessonStatusID >= LessonStatuses.passed) {
      // this is a cmi5 course let's notify the LRS that the user has satisfied the block and close the registration
      await completeCmi5CourseRegistration(context, learnerProgress.fields.UserID!, learnerProgress.fields.ID!)
    }
  } else if (context.fields.GradeTypeID === GradeTypes.Exam &&
    context.fields.ContextTypeID === LearningContextTypes.Course &&
    context.fields.CourseTypeID === CourseTypes.InstructorLed &&
    grade.LessonStatusID > LessonStatuses.browsed) {
    // This is an ILT course, lets get sessions that need to be updated with a grade
    // Sessions can only be graded if the user has a learner progress that has a status > NOT_ATTEMPTED
    const sessionProgress = await getSessionProgressThatNeedToBeUpdated(learnerProgress.fields.UserID!, context.fields.ID!)
    let contextPassed = false
    const sessionThatNeedCertificates: LearnerProgressModel[] = []
    for (const progress of sessionProgress) {
      // check if this progress is better than the last progress we had for this session
      if (progress.fields.LessonStatusID! < grade.LessonStatusID || (progress.fields.RawScore && progress.fields.RawScore < grade.RawScore)) {
        log('debug', 'Updating learner progress', { progressID: progress.fields.ID, userID: learnerProgress.fields.UserID, newStatusId: grade.LessonStatusID, newRawScore: grade.RawScore })
        progress.fields.LessonStatusID = grade.LessonStatusID
        progress.fields.RawScore = grade.RawScore
        const updated = await updateProgress(progress)
        log('debug', 'Updated learner progress status', { statusId: updated.fields.LessonStatusID })
        if (progress.fields.LessonStatusID >= LessonStatuses.passed) {
          // this context is now passed
          contextPassed = true
          if (context.fields.EnableCertificates && !progress.fields.Certificate) {
            // we need to issue a certificate for this session
            sessionThatNeedCertificates.push(updated)
          }
        }
      }
    }
    if (contextPassed) {
      let completedContextRecord!: UserCompletedLearningContextModel
      const completion = new UserCompletedLearningContextModel({
        LearningContextID: context.fields.ID,
        LessonStatusID: grade.LessonStatusID,
        UserID: learnerProgress.fields.UserID,
        CompletedOn: grade.CompletionDate ?? learnerProgress.fields.CreatedOn!,
        GradeTypeID: context.fields.GradeTypeID,
        Credits: context.fields.Credits,
        MinScore: context.fields.MinScore,
        MaxScore: context.fields.MaxScore,
        RawScore: grade.RawScore
      })
      if (context.fields.EnableCertificates) {
        completedContextRecord = await issueCertificate(context, completion, notifyUser)
      } else {
        completedContextRecord = await create(completion)
      }

      // attach the certificate to the session records
      await Promise.all(sessionThatNeedCertificates.map(async sessionProg => {
        sessionProg.fields.Certificate = completedContextRecord.fields.Certificate
        await updateProgress(sessionProg)
      }))
    }
  }
}

async function getSessionProgressThatNeedToBeUpdated (userID: string, contextID: string): Promise<LearnerProgressModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('userID', userID)
  request.input('contextID', contextID)
  const res = await request.query<LearnerProgress>(`
    SELECT *
    FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @userID
    AND [${LearnerProgressFields.LearningContextSessionID}] IN (
      SELECT [${LearningContextSessionFields.ID}]
      FROM [${LearningContextSessionsTableName}]
      WHERE [${LearningContextSessionFields.LearningContextID}] = @contextID
    ) AND [${LearnerProgressFields.LessonStatusID}] > ${LessonStatuses.notAttempted}
  `)
  return res.recordset.map(record => new LearnerProgressModel(record))
}
