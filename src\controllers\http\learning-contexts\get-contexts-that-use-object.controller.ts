import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import getContextsThatUseObjectService from '../../../services/mssql/learning-context/get-contexts-that-use-object.service.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-contexts-that-use-object', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const contexts = await getContextsThatUseObjectService(id)
    log('info', 'Successfully retrieved learning contexts that use object', { objectId: id, count: contexts.length, success: true, req })
    res.json(contexts.map(context => context.fields))
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning contexts that use learning object: validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else {
      log('error', 'Failed to get learning contexts that use learning object', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
