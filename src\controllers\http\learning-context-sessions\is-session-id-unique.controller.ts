import isValid from '../../../services/mssql/learning-context-sessions/is-session-id-unique.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'

const { INTERNAL_SERVER_ERROR, BAD_REQUEST } = httpStatus
const log = logger.create('Controller-HTTP.is-learning-context-session-id-unique', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { SessionID, GUID } = z.object({
      SessionID: z.string(),
      GUID: zodGUID.optional()
    }).parse(req.body)

    const result = await isValid(SessionID, GUID)
    log('info', `Successfully determined that session Id ${result ? 'is' : 'is not'} valid`, { success: true, req })
    res.json(result)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to determine if learning context session id is unique: input validation error', { success: false, req, error })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to determine if learning context session id is unique.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
