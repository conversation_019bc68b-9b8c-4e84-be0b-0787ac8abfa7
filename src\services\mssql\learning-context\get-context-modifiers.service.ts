import mssql from '@lcs/mssql-utility'
import { User, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'

/**
 * Gets a list user user IDs for users that have created learning contexts
 * @param {number} learningContextTypeId context type ID to filter results by
 * @returns {User[]}
 */
export default async function (learningContextTypeId?: number): Promise<User[]> {
  const request = mssql.getPool().request()
  const query = `
    SELECT [${UserFields.ID}], [${UserFields.Avatar}], [${UserFields.FirstName}], [${UserFields.LastName}], [${UserFields.MiddleInitial}]
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] IN (
      SELECT [${LearningContextFields.ModifiedBy}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ModifiedBy}] IS NOT NULL
      ${learningContextTypeId ? `AND [${LearningContextFields.ContextTypeID}] = @typeID` : ''}
    )
  `
  if (learningContextTypeId) {
    request.input('typeID', learningContextTypeId)
  }
  const results = await request.query<User>(query)
  return results.recordset
}
