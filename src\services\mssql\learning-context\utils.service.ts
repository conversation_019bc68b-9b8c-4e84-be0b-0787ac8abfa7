import { Request } from 'mssql'
import logger from '@lcs/logger'
import LearningContextModel from '../../../models/learning-context.model.js'
import UserAssignedLearningObjectModel from '../../../models/user-assigned-learning-object.model.js'
import UserAssignedMultiSessionCourseModel from '../../../models/user-assigned-multi-session-course.model.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { UserAssignedLearningObject, UserAssignedLearningObjectFields } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'
import { UserAssignedMultiSessionCourses, UserAssignedMultiSessionCoursesFields } from '@tess-f/sql-tables/dist/lms/user-assigned-multi-session-course.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { GradeTypes } from '@tess-f/sql-tables/dist/lms/grade-type.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import { LearningContextRatingFields, LearningContextRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-rating.js'
import { LearningContextJson } from '@tess-f/lms/dist/common/learning-context.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import { FlatLearningContextTreeFields, FlatLearningContextTreeViewName } from '@tess-f/sql-tables/dist/lms/flat-learning-context-view.js'
import getChildContextsService from '../catalog/get-child-contexts.service.js'
import getLearningObjectsService from '../learning-objects/get-multiple.service.js'
import { LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'

const log = logger.create('Utils.learning-context-query-utils')

type LearningContextContentMatrixItem = {
  Id: string
  type: 'object' | 'context'
  OrderId: number,
  ContextTypeId?: number,
  CourseTypeId?: number
}

/**
 * Gets all the nested contexts for the given parent context
 * Will not return any hidden nodes
 */
export async function GetNestedContexts (request: Request, parentContextID: string): Promise<LearningContextJson[] | undefined> {
  const query = `
    WITH Child_Nodes AS (
      SELECT *
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] = @id
      
      UNION ALL
      
      SELECT [${FlatLearningContextTreeViewName}].*
      FROM [${FlatLearningContextTreeViewName}]
      INNER JOIN [Child_Nodes] ON [Child_Nodes].[${LearningContextFields.ID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}] AND [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}] != ${Visibilities.Hidden}
    )
    SELECT *
    FROM [Child_Nodes]
    WHERE [${LearningContextFields.ID}] != @id
  `
  request.input('id', parentContextID)
  const res = await request.query<LearningContextJson>(query)
  return buildContextTree(parentContextID, res.recordset)
}

function buildContextTree (id: string, contexts: LearningContextJson[]): LearningContext[] | undefined {
  if (!contexts || contexts.length === 0) {
    return undefined
  }

  const children: LearningContextJson[] = []
  const other: LearningContext[] = []

  for (const context of contexts) {
    if (context.ParentContextID === id) {
      children.push(context)
    } else {
      other.push(context)
    }
  }

  if (children.length < contexts.length) {
    for (const context of children) {
      context.Contexts = buildContextTree(context.ID!, other)
    }
  }

  return children
}

/**
 * Gets all the elective contexts for the given parent context
 * Will not return any hidden nodes
 */
export async function getElectiveContexts (request: Request, parentContextId: string): Promise<LearningContextModel[]> {
  request.input('contextId', parentContextId)
  const response = await request.query<LearningContext>(`
    WITH Child_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}], [${LearningContextFields.VisibilityID}], [${LearningContextFields.ContextTypeID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] = @contextId

      UNION ALL

      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}],
             [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ContextTypeID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Child_Nodes] ON [Child_Nodes].[${LearningContextFields.ID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}] AND [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}] != ${Visibilities.Hidden}
    )

    SELECT *
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ID}] IN (
      SELECT [${LearningContextFields.ID}]
      FROM [Child_Nodes]
      WHERE [${LearningContextFields.ContextTypeID}] = ${LearningContextTypes.ElectiveOptional}
    )
  `)

  return response.recordset.map(record => new LearningContextModel(undefined, record))
}

function GetContextIDs (context: LearningContextJson): string[] {
  const ids: string[] = []
  ids.push(context.ID!)
  if (context.Contexts && context.Contexts.length > 0) {
    for (const learningContext of context.Contexts) {
      const nestedIDs = getContextIDs(learningContext)
      nestedIDs.forEach(id => ids.push(id))
    }
  }
  return ids
}

function getContextIDs (context: LearningContext) {
  return GetContextIDs(context)
}

export async function getAverageRatingMap (request: Request, ids: string[]): Promise<{ [key: string]: { count: number, average: number } }> {
  if (ids.length === 0) return {}

  const conditions = ids.map((id, index) => {
    request.input('id_' + index, id)
    let condition = `[${LearningObjectContextFields.LearningContextID}] = @id_${index}`

    if (index < ids.length - 1) {
      condition += ' OR '
    }

    return condition
  })

  const query = `
    SELECT AVG([${LearningContextRatingFields.Rating}]) AS average, Count([${LearningContextRatingFields.ID}]) AS count, [${LearningContextRatingFields.LearningContextID}]
    FROM [${LearningContextRatingsTableName}]
    WHERE ${conditions.join('')}
    GROUP BY [${LearningContextRatingFields.LearningContextID}]
  `

  const res = await request.query<{average: number, count: number, LearningContextID: string}>(query)
  const map: { [key: string]: { count: number, average: number } } = {}

  for (const rating of res.recordset) {
    map[rating.LearningContextID] = {
      count: rating.count,
      average: rating.average
    }
  }

  return map
}

/**
 * Returns a list of UserAssignedLearningObjects for a given learning context
 * Includes exams for the parent and any sub context
 */
export async function getAssignmentContentForLearningContext (request: Request, contextID: string): Promise<UserAssignedLearningObjectModel[]> {
  request.input('contextID', contextID)
  const query = `
    WITH Child_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}], [${LearningContextFields.VisibilityID}], [${LearningContextFields.OrderID}], [${LearningContextFields.Title}],
             [${LearningContextFields.Label}], [${LearningContextFields.GradeTypeID}], [${LearningContextFields.ExamID}], [${LearningContextFields.RequiredContentCount}], [${LearningContextFields.ContextTypeID}],
             [${LearningContextFields.MinScore}], [${LearningContextFields.MaxScore}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] = @contextID
      
      UNION ALL
      
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}],
             [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.OrderID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.Title}],
             [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.Label}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.GradeTypeID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ExamID}],
             [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.RequiredContentCount}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ContextTypeID}],
             [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.MinScore}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.MaxScore}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Child_Nodes] ON [Child_Nodes].[${LearningContextFields.ID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}] AND [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}] != ${Visibilities.Hidden}
    )
    SELECT [${LearningObjectsTableName}].[${LearningObjectFields.ID}] AS [${UserAssignedLearningObjectFields.LearningObjectID}], [ForContext].[${LearningContextFields.ID}] AS [${UserAssignedLearningObjectFields.ForContextID}], [ForContext].[${LearningContextFields.Title}] AS [${UserAssignedLearningObjectFields.ForContextTitle}],
      [ForContext].[${LearningContextFields.Label}] AS [${UserAssignedMultiSessionCoursesFields.ForContextLabel}], [ForContext].[${LearningContextFields.GradeTypeID}] AS [${UserAssignedMultiSessionCoursesFields.ForContextGradeTypeID}], [ForContext].[${LearningContextFields.ContextTypeID}] AS [${UserAssignedMultiSessionCoursesFields.ForContextTypeID}],
      [ForContext].[${LearningContextFields.RequiredContentCount}] AS [${UserAssignedMultiSessionCoursesFields.ForContextRequiredContentCount}], [ForContext].[${LearningContextFields.ExamID}] AS [${UserAssignedLearningObjectFields.ForContextExamID}], [Child_Nodes].[${LearningContextFields.ID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextID}],
      [Child_Nodes].[${LearningContextFields.Title}] AS [${UserAssignedMultiSessionCoursesFields.SubContextTitle}], [Child_Nodes].[${LearningContextFields.Label}] AS [${UserAssignedMultiSessionCoursesFields.SubContextLabel}], [Child_Nodes].[${LearningContextFields.GradeTypeID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextGradeTypeID}],
      [Child_Nodes].[${LearningContextFields.ContextTypeID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextTypeID}], [Child_Nodes].[${LearningContextFields.RequiredContentCount}] AS [${UserAssignedMultiSessionCoursesFields.SubContextRequiredContentCount}], [Child_Nodes].[${LearningContextFields.ExamID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextExamID}],
      [Child_Nodes].[${LearningContextFields.ParentContextID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextParentID}], [Child_Nodes].[${LearningContextFields.OrderID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextOrderID}], NULL AS [${UserAssignedMultiSessionCoursesFields.ContentOrderID}],
      [${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}] AS [${LearningObjectFields.LearningObjectTypeID}], [ForContext].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.ForContextMinScore}], [ForContext].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.ForContextMaxScore}], [Child_Nodes].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.SubContextMaxScore}],
      [Child_Nodes].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.SubContextMinScore}],
      CASE
        WHEN [Child_Nodes].[${LearningContextFields.ContextTypeID}] = ${LearningContextTypes.ElectiveOptional} AND ([Child_Nodes].[${LearningContextFields.RequiredContentCount}] IS NULL OR [Child_Nodes].[${LearningContextFields.RequiredContentCount}] = 0) THEN 1
        ELSE 0
      END AS [${UserAssignedMultiSessionCoursesFields.Completed}]
    FROM [Child_Nodes]
        JOIN [${LearningObjectsTableName}] ON [${LearningObjectsTableName}].[${LearningObjectFields.ID}] = [Child_Nodes].[${LearningContextFields.ExamID}]
        JOIN [${LearningContextTableName}] AS [ForContext] ON [ForContext].[${LearningContextFields.ID}] = @contextID
    WHERE [Child_Nodes].[${LearningContextFields.ExamID}] IS NOT NULL
    AND [Child_Nodes].[${LearningContextFields.GradeTypeID}] = ${GradeTypes.Exam}
    AND [Child_Nodes].[${LearningContextFields.ContextTypeID}] != ${LearningContextTypes.Course}
    AND [Child_Nodes].[${LearningContextFields.ID}] != @contextID
        
    UNION ALL
        
    SELECT [${LearningObjectsTableName}].[${LearningObjectFields.ID}] AS [${LearningObjectContextFields.LearningObjectID}], [Child_Nodes].[${LearningContextFields.ID}] AS [${UserAssignedLearningObjectFields.ForContextID}], [Child_Nodes].[${LearningContextFields.Title}] AS [ForContextTitle],
        [Child_Nodes].[${LearningContextFields.Label}] AS [${UserAssignedMultiSessionCoursesFields.ForContextLabel}], [Child_Nodes].[${LearningContextFields.GradeTypeID}] AS [${UserAssignedMultiSessionCoursesFields.ForContextGradeTypeID}], [Child_Nodes].[${LearningContextFields.ContextTypeID}] AS [${UserAssignedMultiSessionCoursesFields.ForContextTypeID}],
        [Child_Nodes].[${LearningContextFields.RequiredContentCount}] AS [${UserAssignedMultiSessionCoursesFields.ForContextRequiredContentCount}], [Child_Nodes].[${LearningContextFields.ExamID}] AS [${UserAssignedLearningObjectFields.ForContextExamID}], [${UserAssignedMultiSessionCoursesFields.SubContextID}] = NULL,
        [${UserAssignedMultiSessionCoursesFields.SubContextTitle}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextLabel}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextGradeTypeID}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextTypeID}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextRequiredContentCount}] = NULL,
        [${UserAssignedMultiSessionCoursesFields.SubContextExamID}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextParentID}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextOrderID}] = NULL, NULL AS [${UserAssignedMultiSessionCoursesFields.ContentOrderID}],
        [${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}] AS [${LearningObjectFields.LearningObjectTypeID}], [Child_Nodes].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.ForContextMinScore}], [Child_Nodes].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.ForContextMaxScore}],
      [${UserAssignedMultiSessionCoursesFields.SubContextMaxScore}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextMinScore}] = NULL, 0 AS [${UserAssignedMultiSessionCoursesFields.Completed}]
    FROM [Child_Nodes]
        JOIN [${LearningObjectsTableName}] ON [${LearningObjectsTableName}].[${LearningObjectFields.ID}] = [Child_Nodes].[${LearningContextFields.ExamID}]
    WHERE [Child_Nodes].[${LearningContextFields.ExamID}] IS NOT NULL
    AND [Child_Nodes].[${LearningContextFields.GradeTypeID}] = ${GradeTypes.Exam}
    AND [Child_Nodes].[${LearningContextFields.ContextTypeID}] != ${LearningContextTypes.Course}
    AND [Child_Nodes].[${LearningContextFields.ID}] = @contextID
        
    UNION ALL
        
    SELECT
        [${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningObjectID}] AS [${LearningObjectContextFields.LearningObjectID}], [ForContext].[${LearningContextFields.ID}] AS [${UserAssignedLearningObjectFields.ForContextID}], [ForContext].[${LearningContextFields.Title}] AS [ForContextTitle],
        [ForContext].[${LearningContextFields.Label}] AS [${UserAssignedMultiSessionCoursesFields.ForContextLabel}], [ForContext].[${LearningContextFields.GradeTypeID}] AS [${UserAssignedMultiSessionCoursesFields.ForContextGradeTypeID}], [ForContext].[${LearningContextFields.ContextTypeID}] AS [${UserAssignedMultiSessionCoursesFields.ForContextTypeID}],
        [ForContext].[${LearningContextFields.RequiredContentCount}] AS [${UserAssignedMultiSessionCoursesFields.ForContextRequiredContentCount}], [ForContext].[${LearningContextFields.ExamID}] AS [${UserAssignedLearningObjectFields.ForContextExamID}], [Child_Nodes].[${LearningContextFields.ID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextID}],
        [Child_Nodes].[${LearningContextFields.Title}] AS [${UserAssignedMultiSessionCoursesFields.SubContextTitle}], [Child_Nodes].[${LearningContextFields.Label}] AS [${UserAssignedMultiSessionCoursesFields.SubContextLabel}], [Child_Nodes].[${LearningContextFields.GradeTypeID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextGradeTypeID}],
        [Child_Nodes].[${LearningContextFields.ContextTypeID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextTypeID}], [Child_Nodes].[${LearningContextFields.RequiredContentCount}] AS [${UserAssignedMultiSessionCoursesFields.SubContextRequiredContentCount}], [Child_Nodes].[${LearningContextFields.ExamID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextExamID}],
        [Child_Nodes].[${LearningContextFields.ParentContextID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextParentID}], [Child_Nodes].[${LearningContextFields.OrderID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextOrderID}], [${LearningObjectContextsTableName}].[${LearningContextFields.OrderID}] AS [${UserAssignedMultiSessionCoursesFields.ContentOrderID}],
        [${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}] AS [${LearningObjectFields.LearningObjectTypeID}], [ForContext].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.ForContextMinScore}], [ForContext].[${LearningContextFields.MinScore}],
        [Child_Nodes].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.SubContextMaxScore}], [Child_Nodes].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.SubContextMinScore}],
        CASE
          WHEN [Child_Nodes].[${LearningContextFields.ContextTypeID}] = ${LearningContextTypes.ElectiveOptional} AND ([Child_Nodes].[${LearningContextFields.RequiredContentCount}] IS NULL OR [Child_Nodes].[${LearningContextFields.RequiredContentCount}] = 0) THEN 1
          ELSE 0
        END AS [${UserAssignedMultiSessionCoursesFields.Completed}]
    FROM [Child_Nodes]
        JOIN [${LearningObjectContextsTableName}] ON [${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningContextID}] = [Child_Nodes].[${LearningContextFields.ID}]
        JOIN [${LearningObjectsTableName}] ON [${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningObjectID}] = [${LearningObjectsTableName}].[${LearningObjectFields.ID}]
        JOIN [${LearningContextTableName}] AS [ForContext] ON [ForContext].[${LearningContextFields.ID}] = @contextID
    WHERE [Child_Nodes].[${LearningContextFields.ID}] != @contextID

    UNION ALL

    SELECT
        [${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningObjectID}] AS [${LearningObjectContextFields.LearningObjectID}], [Child_Nodes].[${LearningContextFields.ID}] AS [${UserAssignedLearningObjectFields.ForContextID}], [Child_Nodes].[${LearningContextFields.Title}] AS [ForContextTitle],
        [Child_Nodes].[${LearningContextFields.Label}] AS [${UserAssignedMultiSessionCoursesFields.ForContextLabel}], [Child_Nodes].[${LearningContextFields.GradeTypeID}] AS [${UserAssignedMultiSessionCoursesFields.ForContextGradeTypeID}], [Child_Nodes].[${LearningContextFields.ContextTypeID}] AS [${UserAssignedMultiSessionCoursesFields.ForContextTypeID}],
        [Child_Nodes].[${LearningContextFields.RequiredContentCount}] AS [${UserAssignedMultiSessionCoursesFields.ForContextRequiredContentCount}], [Child_Nodes].[${LearningContextFields.ExamID}] AS [${UserAssignedLearningObjectFields.ForContextExamID}], [${UserAssignedMultiSessionCoursesFields.SubContextID}] = NULL,
        [${UserAssignedMultiSessionCoursesFields.SubContextTitle}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextLabel}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextGradeTypeID}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextTypeID}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextRequiredContentCount}] = NULL,
        [${UserAssignedMultiSessionCoursesFields.SubContextExamID}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextParentID}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextOrderID}] = NULL, [${LearningObjectContextsTableName}].[${LearningContextFields.OrderID}] AS [${UserAssignedMultiSessionCoursesFields.ContentOrderID}],
        [${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}] AS [${LearningObjectFields.LearningObjectTypeID}], [Child_Nodes].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.ForContextMinScore}], [Child_Nodes].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.ForContextMaxScore}],
        [${UserAssignedMultiSessionCoursesFields.SubContextMaxScore}] = NULL, [${UserAssignedMultiSessionCoursesFields.SubContextMinScore}] = NULL,
        CASE
          WHEN [Child_Nodes].[${LearningContextFields.ContextTypeID}] = ${LearningContextTypes.ElectiveOptional} AND ([Child_Nodes].[${LearningContextFields.RequiredContentCount}] IS NULL OR [Child_Nodes].[${LearningContextFields.RequiredContentCount}] = 0) THEN 1
          ELSE 0
        END AS [${UserAssignedMultiSessionCoursesFields.Completed}]
    FROM [Child_Nodes]
        JOIN [${LearningObjectContextsTableName}] ON [${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningContextID}] = [Child_Nodes].[${LearningContextFields.ID}]
        JOIN [${LearningObjectsTableName}] ON [${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningObjectID}] = [${LearningObjectsTableName}].[${LearningObjectFields.ID}]
    WHERE [Child_Nodes].[${LearningContextFields.ID}] = @contextID
  `

  try {
    const res = await request.query<UserAssignedLearningObject>(query)
    return res.recordset.map(record => new UserAssignedLearningObjectModel(record))
  } catch (error) {
    log('error', 'Failed to get user assigned learning objects for context', { errorMessage: getErrorMessage(error) })
    throw error
  }
}

/**
 * Returns a list of UserAssignedMultiSessionCourses for a parent context
 */
export async function getMultiSessionAssignmentContentForLearningContext (request: Request, contextID: string): Promise<UserAssignedMultiSessionCourseModel[]> {
  request.input('parentID', contextID)
  const query = `
    WITH Child_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}], [${LearningContextFields.VisibilityID}], [${LearningContextFields.OrderID}], [${LearningContextFields.Title}], [${LearningContextFields.Label}],
             [${LearningContextFields.GradeTypeID}], [${LearningContextFields.ExamID}], [${LearningContextFields.RequiredContentCount}], [${LearningContextFields.ContextTypeID}], [${LearningContextFields.CourseTypeID}], [${LearningContextFields.MinScore}], [${LearningContextFields.MaxScore}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] = @parentID
      
      UNION ALL
      
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}],
             [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.OrderID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.Title}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.Label}],
             [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.GradeTypeID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ExamID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.RequiredContentCount}],
             [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ContextTypeID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.CourseTypeID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.MinScore}],
             [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.MaxScore}]
      FROM [${FlatLearningContextTreeViewName}]
      INNER JOIN [Child_Nodes] ON [Child_Nodes].[${LearningContextFields.ID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}] AND [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}] != ${Visibilities.Hidden}
    )

    SELECT [IltNode].[${LearningContextFields.ID}] AS [${UserAssignedMultiSessionCoursesFields.LearningContextID}], [ForContext].[${LearningContextFields.ID}] AS [${UserAssignedLearningObjectFields.ForContextID}], [ForContext].[${LearningContextFields.Title}] AS [ForContextTitle], [ForContext].[${LearningContextFields.Label}] AS [${UserAssignedMultiSessionCoursesFields.ForContextLabel}], [ForContext].[${LearningContextFields.GradeTypeID}] AS [${UserAssignedMultiSessionCoursesFields.ForContextGradeTypeID}],
      [ForContext].[${LearningContextFields.ExamID}] AS [${UserAssignedLearningObjectFields.ForContextExamID}], [ForContext].[${LearningContextFields.RequiredContentCount}] AS [${UserAssignedMultiSessionCoursesFields.ForContextRequiredContentCount}], [ForContext].[${LearningContextFields.ContextTypeID}] AS [${UserAssignedMultiSessionCoursesFields.ForContextTypeID}], [SubContext].[${LearningContextFields.ID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextID}],
      [SubContext].[${LearningContextFields.ParentContextID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextParentID}], [SubContext].[${LearningContextFields.OrderID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextOrderID}], [SubContext].[${LearningContextFields.ContextTypeID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextTypeID}], [SubContext].[${LearningContextFields.Title}] AS [${UserAssignedMultiSessionCoursesFields.SubContextTitle}],
      [SubContext].[${LearningContextFields.GradeTypeID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextGradeTypeID}], [SubContext].[${LearningContextFields.RequiredContentCount}] AS [${UserAssignedMultiSessionCoursesFields.SubContextRequiredContentCount}], [SubContext].[${LearningContextFields.ExamID}] AS [${UserAssignedMultiSessionCoursesFields.SubContextExamID}], [SubContext].[${LearningContextFields.Label}] AS [${UserAssignedMultiSessionCoursesFields.SubContextLabel}],
      [IltNode].[${LearningContextFields.OrderID}] AS [${UserAssignedMultiSessionCoursesFields.ContentOrderID}], [ForContext].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.ForContextMinScore}], [ForContext].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.ForContextMaxScore}], [SubContext].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.SubContextMinScore}], [SubContext].[${LearningContextFields.MinScore}] AS [${UserAssignedMultiSessionCoursesFields.SubContextMaxScore}],
      CASE
        WHEN [SubContext].[${LearningContextFields.ContextTypeID}] = ${LearningContextTypes.ElectiveOptional} AND ([SubContext].[${LearningContextFields.RequiredContentCount}] IS NULL OR [SubContext].[${LearningContextFields.RequiredContentCount}] = 0) THEN 1
        WHEN [ForContext].[${LearningContextFields.ContextTypeID}] = ${LearningContextTypes.ElectiveOptional} AND ([ForContext].[${LearningContextFields.RequiredContentCount}] IS NULL OR [ForContext].[${LearningContextFields.RequiredContentCount}] = 0) THEN 1
        ELSE 0
      END AS [${UserAssignedMultiSessionCoursesFields.Completed}]
    FROM [Child_Nodes] AS [IltNode]
      JOIN [Child_Nodes] AS [ForContext] ON [ForContext].[${LearningContextFields.ID}] = @parentID
      LEFT JOIN [Child_Nodes] AS [SubContext] ON [SubContext].[${LearningContextFields.ID}] = [IltNode].[${LearningContextFields.ParentContextID}] AND [SubContext].[${LearningContextFields.ID}] != @parentID
    WHERE [IltNode].[${LearningContextFields.ContextTypeID}] = ${LearningContextTypes.Course}
    AND [IltNode].[${LearningContextFields.CourseTypeID}] = ${CourseTypes.InstructorLed}
  `

  try {
    const res = await request.query<UserAssignedMultiSessionCourses>(query)
    return res.recordset.map(record => new UserAssignedMultiSessionCourseModel(record))
  } catch (error) {
    log('error', 'Failed to get user assigned multi session courses for learning context', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}

/**
 * Returns a list of contexts that are not hidden and have the given object somewhere in its tree
 */
export async function getAllContextsThatUseObject (request: Request, objectID: string): Promise<LearningContextModel[]> {
  request.input('objectID', objectID)
  const res = await request.query<LearningContext>(`
    WITH Parent_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] IN (
        SELECT [${LearningObjectContextFields.LearningContextID}]
        FROM [${LearningObjectContextsTableName}]
        WHERE [${LearningObjectContextFields.LearningObjectID}] = @objectID
      )

      UNION ALL

      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Parent_Nodes] ON [Parent_Nodes].[${LearningContextFields.ParentContextID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}]
    )
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ID}] IN (
      SELECT [${LearningContextFields.ID}]
      FROM [Parent_Nodes]
    ) AND [${LearningContextFields.VisibilityID}] != ${Visibilities.Hidden}
  `)
  return res.recordset.map(record => new LearningContextModel(undefined, record))
}

/**
 * Returns a list of contexts that:
 *    are not hidden
 *    have an exam or survey
 *    have the given object somewhere in its tree
 */
export async function getAllContextsThatUseObjectAndHaveExamOrSurvey (request: Request, objectID: string): Promise<LearningContextModel[]> {
  request.input('objectID', objectID)
  const res = await request.query<LearningContext>(`
    WITH Parent_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] IN (
        SELECT [${LearningObjectContextFields.LearningContextID}]
        FROM [${LearningObjectContextsTableName}]
        WHERE [${LearningObjectContextFields.LearningObjectID}] = @objectID
      )
      
      UNION ALL
      
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Parent_Nodes] ON [Parent_Nodes].[${LearningContextFields.ParentContextID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}]
    )
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ID}] IN (
      SELECT [${LearningContextFields.ID}]
      FROM [Parent_Nodes]
    ) AND [${LearningContextFields.VisibilityID}] != ${Visibilities.Hidden}
    AND ([${LearningContextFields.GradeTypeID}] = ${GradeTypes.Exam} OR [${LearningContextFields.SurveyURL}] IS NOT NULL)
  `)
  return res.recordset.map(record => new LearningContextModel(undefined, record))
}

/**
 * Returns a list of learning contexts that have the ILT course associated with the given session in its tree
 * Only returns contexts that are not hidden
 */
export async function getAllContextsThatUseIltSession (request: Request, sessionID: string): Promise<LearningContextModel[]> {
  request.input('sessionID', sessionID)
  const res = await request.query<LearningContext>(`
    WITH Parent_Nodes AS (
      SELECT [${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeFields.ParentContextID}]
      FROM [${FlatLearningContextTreeViewName}]
      WHERE [${FlatLearningContextTreeFields.ID}] IN (
        SELECT [${LearningContextSessionFields.LearningContextID}]
        FROM [${LearningContextSessionsTableName}]
        WHERE [${LearningContextSessionFields.ID}] = @sessionID
      )
      
      UNION ALL
      
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Parent_Nodes] ON [Parent_Nodes].[${LearningContextFields.ParentContextID}] = [${FlatLearningContextTreeViewName}].[${LearningContextFields.ID}]
    )
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ID}] IN (
      SELECT [${LearningContextFields.ID}]
      FROM [Parent_Nodes]
    ) AND [${LearningContextFields.VisibilityID}] != ${Visibilities.Hidden}
  `)
  return res.recordset.map(record => new LearningContextModel(undefined, record))
}

/**
 * Returns a list of learning contexts that:
 *    have the ILT course associated with the given session in its tree
 *    are not hidden
 *    have an exam or survey
 */
export async function getAllContextsThatUseIltSessionAndHaveExamOrSurvey (request: Request, sessionID: string): Promise<LearningContextModel[]> {
  request.input('sessionID', sessionID)
  const res = await request.query<LearningContext>(`
    WITH Parent_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] IN (
        SELECT [${LearningContextConnectionFields.ParentContextID}]
        FROM [${LearningContextConnectionsTableName}]
        WHERE [${LearningContextConnectionFields.ConnectedContextID}] IN (
          SELECT [${LearningContextSessionFields.LearningContextID}]
          FROM [${LearningContextSessionsTableName}]
          WHERE [${LearningContextSessionFields.ID}] = @sessionID
        )
      )
      
      UNION ALL
      
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Parent_Nodes] ON [Parent_Nodes].[${LearningContextFields.ParentContextID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}]
    )
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ID}] IN (
      SELECT [${LearningContextFields.ID}]
      FROM [Parent_Nodes]
    ) AND [${LearningContextFields.VisibilityID}] != ${Visibilities.Hidden}
    AND ([${LearningContextFields.GradeTypeID}] = ${GradeTypes.Exam} OR [${LearningContextFields.SurveyURL}] IS NOT NULL)
  `)
  return res.recordset.map(record => new LearningContextModel(undefined, record))
}

export async function getAllContextThatUseExam (request: Request, examID: string): Promise<LearningContextModel[]> {
  request.input('examID', examID)
  const res = await request.query<LearningContext>(`
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ExamID}] = @examID
  `)
  return res.recordset.map(record => new LearningContextModel(undefined, record))
}

/**
 * Retrieves the content matrix for a given learning context. Does not return the nested learning contexts.
 *
 * @param {string} contextId - The ID of the learning context.
 * @return {Promise<LearningContextContentMatrixItem[]>} A promise that resolves to an array of LearningContextContentMatrixItem objects.
 */
export async function getLearningContextContentMatrix (contextId: string): Promise<LearningContextContentMatrixItem[]> {
  const childContexts = await getChildContextsService(contextId)
  const childObjects = await getLearningObjectsService(contextId)

  const matrix: LearningContextContentMatrixItem[] = childContexts.map(context => {
    return {
      Id: context.fields.ID!,
      type: 'context',
      OrderId: context.fields.OrderID!,
      ContextTypeId: context.fields.ContextTypeID,
      CourseTypeId: context.fields.CourseTypeID
    }
  }).concat(childObjects.map(obj => {
    return {
      Id: obj.fields.ID!,
      type: 'object',
      OrderId: obj.fields.OrderID!,
      ContextTypeId: undefined,
      CourseTypeId: undefined
    }
  })) as LearningContextContentMatrixItem[]

  matrix.sort((a, b) => a.OrderId - b.OrderId)
  return matrix
}
