import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import SessionInstructorModel from '../../../models/learning-context-session-instructor.model.js'
import get from './get.service.js'
import create from './create.service.js'
import del from './delete.service.js'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContextSession, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

let learningContext: LearningContext
let sessionInstructor: SessionInstructorModel
let session: LearningContextSession

describe('MSSQL Learning Context Session Instructors', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test context session instructors',
      Description: `Running learning-context-session-instructors on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Course
    }))
    session = await addRow<LearningContextSession>(pool.request(), new LearningContextSessionModel({
      SessionID: 'Service-Test-1',
      DisplayEnrollmentsOnHomePage: false,
      SessionStatusID: 1,
      CreatedOn: new Date(),
      CreatedBy: AdminUserId,
      LearningContextID: learningContext.ID,
      Timezone: 'MST',
      StartDate: new Date(),
      EndDate: new Date()
    }))
  })

  it('creates a learning context instructor', async () => {
    sessionInstructor = await create(new SessionInstructorModel({
      SessionID: session.ID,
      UserID: AdminUserId
    }))

    expect(sessionInstructor.fields.SessionID).to.equal(session.ID)
    expect(sessionInstructor.fields.UserID).to.equal(AdminUserId)
  })

  it('gets a list of sessions the user instructs', async () => {
    const sessionInstructors = await get(undefined, AdminUserId)
    expect(sessionInstructors.length).to.be.gte(1)
    for (let i = 0; i < sessionInstructors.length; i++) {
      expect(sessionInstructors[i].fields.UserID).to.equal(AdminUserId)
    }
  })

  it('gets a list of instructors for a session', async () => {
    const sessionInstructors = await get(session.ID)
    expect(sessionInstructors.length).to.be.gte(1)
    for (let i = 0; i < sessionInstructors.length; i++) {
      expect(sessionInstructors[i].fields.SessionID).to.equal(session.ID)
    }
  })

  it('gets all instructors for all sessions', async () => {
    const sessionInstructors = await get()
    expect(sessionInstructors.length).to.be.gte(1)
  })

  it('should fail creating the same session/user combo', async () => {
    try {
      await create(sessionInstructor)
      throw new Error('Created a duplicate session instructor')
    } catch (error) {};
  })

  it('deletes all the instructors for a session', async () => {
    await del(session.ID!)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { ID: learningContext.ID })
    await deleteRow<LearningContextSession>(pool.request(), LearningContextSessionsTableName, { ID: session.ID })
  })
})
