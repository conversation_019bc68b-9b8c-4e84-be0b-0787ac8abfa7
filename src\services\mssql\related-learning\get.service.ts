import mssql from '@lcs/mssql-utility'
import LearningObjectModel from '../../../models/learning-object.model.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { getKeywordsForObject } from '../learning-objects/get.service.js'
import { Request } from 'mssql'
import { LearningObjectKeywordFields, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import { LearningObjectsTableName, LearningObjectFields, LearningObject } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextKeywordFields, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'
import { LearningObjectRatingFields, LearningObjectRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-rating.js'
import { LearningContextRatingFields, LearningContextRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-rating.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

export default async function (id: string, limit: number = 10): Promise<{ learningObjects: LearningObjectModel[], learningContexts: LearningContextModel[] }> {
  const pool = mssql.getPool()

  const keywords = await getKeywordsForObject(pool.request(), id)
  const learningObjects = await getRelatedLearningObjects(pool.request(), keywords, id)
  const learningContexts = await getRelatedLearningContexts(pool.request(), keywords)

  const selectedObjects: LearningObjectModel[] = []
  const selectedContexts: LearningContextModel[] = []

  let selected = 0
  let available = learningObjects.length + learningContexts.length
  do {
    if (learningObjects.length > 0) {
      const obj = learningObjects.shift()
      if (obj) {
        selectedObjects.push(obj)
        selected++
        available--
      }
    }
    if (learningContexts.length > 0 && selected < limit) {
      const context = learningContexts.shift()
      if (context) {
        selectedContexts.push(context)
        selected++
        available--
      }
    }
  } while (selected < limit && available > 0)

  const objectIDs = selectedObjects.map(obj => obj.fields.ID!)
  const contextIDs = selectedContexts.map(context => context.fields.ID!)

  const objectRatings = await getLearningObjectAverageRatingMap(pool.request(), objectIDs)
  const contextRatings = await getLearningContextRatingMap(pool.request(), contextIDs)

  selectedObjects.forEach(obj => {
    const ratings = objectRatings[obj.fields.ID!]
    if (ratings) {
      obj.fields.Rating = ratings.average
      obj.fields.RatingCount = ratings.count
    }
  })

  selectedContexts.forEach(context => {
    const ratings = contextRatings[context.fields.ID!]
    if (ratings) {
      context.fields.RatingCount = ratings.count
      context.fields.Rating = ratings.average
    }
  })

  return {
    learningContexts: selectedContexts,
    learningObjects: selectedObjects
  }
}

async function getRelatedLearningObjects (request: Request, keywords: string[], id: string): Promise<LearningObjectModel[]> {
  let query: string

  request.input('id', id)

  if (keywords.length > 0) {
    const conds = keywords.map((keyword, index) => {
      request.input('keyword_' + index, keyword)
      return '@keyword_' + index
    })

    query = `
      SELECT COUNT(*) as [MatchStrength], [${LearningObjectsTableName}].[${LearningObjectFields.ID}], [${LearningObjectsTableName}].[${LearningObjectFields.Title}], [${LearningObjectsTableName}].[${LearningObjectFields.Description}],
        [${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}], [${LearningObjectsTableName}].[${LearningObjectFields.MinutesToComplete}], [${LearningObjectsTableName}].[${LearningObjectFields.ContentID}],
        [${LearningObjectsTableName}].[${LearningObjectFields.Image}], [${LearningObjectsTableName}].[${LearningObjectFields.URL}]
      FROM [${LearningObjectKeywordsTableName}]
      JOIN [${LearningObjectsTableName}] ON [${LearningObjectsTableName}].[${LearningObjectFields.ID}] = [${LearningObjectKeywordsTableName}].[${LearningObjectKeywordFields.LearningObjectID}]
      WHERE [${LearningObjectKeywordsTableName}].[${LearningObjectKeywordFields.Keyword}] IN (${conds.join(', ')})
      AND [${LearningObjectsTableName}].[${LearningObjectFields.ID}] != @id
      AND ([${LearningObjectsTableName}].[${LearningObjectFields.VisibilityID}] = ${Visibilities.Browsable} OR [${LearningObjectsTableName}].[${LearningObjectFields.VisibilityID}] = ${Visibilities.Obsolete})
      GROUP BY [${LearningObjectsTableName}].[${LearningObjectFields.ID}], [${LearningObjectsTableName}].[${LearningObjectFields.Title}], [${LearningObjectsTableName}].[${LearningObjectFields.Description}],
        [${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}], [${LearningObjectsTableName}].[${LearningObjectFields.MinutesToComplete}], [${LearningObjectsTableName}].[${LearningObjectFields.ContentID}],
        [${LearningObjectsTableName}].[${LearningObjectFields.Image}], [${LearningObjectsTableName}].[${LearningObjectFields.URL}]
      ORDER BY [MatchStrength] DESC
    `
  } else {
    query = `SELECT * FROM [${LearningObjectsTableName}] WHERE [${LearningObjectFields.ID}] != @id AND ([${LearningObjectFields.VisibilityID}] = ${Visibilities.Browsable} OR [${LearningObjectFields.VisibilityID}] = ${Visibilities.Obsolete})`
  }

  const result = await request.query<LearningObject>(query)
  return result.recordset.map(record => new LearningObjectModel(record))
}

async function getRelatedLearningContexts (request: Request, keywords: string[]) {
  let query
  if (keywords.length > 0) {
    const conds = keywords.map((keyword, index) => {
      request.input('keyword_' + index, keyword)
      return '@keyword_' + index
    })

    query = `
      SELECT COUNT(*) as [matchStrength], [${LearningContextTableName}].*
      FROM [${LearningContextKeywordsTableName}]
      JOIN [${LearningContextTableName}] ON [${LearningContextTableName}].[${LearningContextFields.ID}] = [${LearningContextKeywordsTableName}].[${LearningContextKeywordFields.LearningContextID}] 
      WHERE [${LearningContextKeywordFields.Keyword}] IN (${(conds.join(', '))})
      AND [${LearningContextTableName}].[${LearningContextFields.ContextTypeID}] IN (${LearningContextTypes.Course}, ${LearningContextTypes.Section}) 
      AND ([${LearningContextFields.VisibilityID}] = ${Visibilities.Browsable} OR [${LearningContextFields.VisibilityID}] = ${Visibilities.Obsolete})
      GROUP BY [${LearningContextFields.ID}], [${LearningContextFields.Title}], [${LearningContextFields.Description}], [${LearningContextFields.ContextTypeID}], [${LearningContextFields.CourseTypeID}],
        [${LearningContextFields.CreatedBy}], [${LearningContextFields.CreatedOn}], [${LearningContextFields.Image}], [${LearningContextFields.ModifiedBy}], [${LearningContextFields.ModifiedOn}],
        [${LearningContextFields.Credits}], [${LearningContextFields.Label}], [${LearningContextFields.OrderID}], [${LearningContextFields.ParentContextID}],
        [${LearningContextFields.SkillLevelID}], [${LearningContextFields.MinScore}], [${LearningContextFields.CourseID}], [${LearningContextFields.GradeTypeID}],
        [${LearningContextFields.MaxScore}], [${LearningContextFields.EnableCertificates}], [${LearningContextFields.LayoutTypeID}], [${LearningContextFields.VisibilityID}], [${LearningContextFields.SurveyURL}],
        [${LearningContextFields.ExamID}], [${LearningContextFields.RequiredContentCount}], [${LearningContextFields.SpecialInstructions}], [${LearningContextFields.IRI}], [${LearningContextFields.EnforceContentSequencing}]
      ORDER BY matchStrength DESC
    `
  } else {
    query = `
      SELECT *
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ContextTypeID}] IN (${LearningContextTypes.Course}, ${LearningContextTypes.Section})
      AND ([${LearningContextFields.VisibilityID}] = ${Visibilities.Browsable} OR [${LearningContextFields.VisibilityID}] = ${Visibilities.Obsolete})
      ORDER BY [${LearningContextFields.CreatedOn}] DESC
    `
  }

  const result = await request.query<LearningContext>(query)
  return result.recordset.map(record => new LearningContextModel(undefined, record))
}

async function getLearningObjectAverageRatingMap (request: Request, ids: string[]): Promise<{ [key: string]: { count: number, average: number } }> {
  if (ids.length <= 0) {
    return {}
  }

  const conditions = ids.map((id, index) => {
    request.input('id_' + index, id)
    let condition = `[${LearningObjectKeywordFields.LearningObjectID}] = @id_${index}`

    if (index < ids.length - 1) {
      condition += ' OR '
    }

    return condition
  })

  const query = `
    SELECT AVG([${LearningObjectRatingFields.Rating}]) as average, COUNT([${LearningObjectRatingFields.ID}]) as count, [${LearningObjectRatingFields.LearningObjectID}]
    FROM [${LearningObjectRatingsTableName}]
    WHERE ${conditions.join('')}
    GROUP BY [${LearningObjectRatingFields.LearningObjectID}]
  `
  const res = await request.query<{ count: number, average: number, LearningObjectID: string }>(query)
  const map: { [key: string]: { count: number, average: number } } = {}

  for (const rating of res.recordset) {
    map[rating.LearningObjectID] = {
      count: rating.count,
      average: rating.average
    }
  }

  return map
}

async function getLearningContextRatingMap (request: Request, ids: string[]): Promise<{ [key: string]: { count: number, average: number } }> {
  if (ids.length <= 0) {
    return {}
  }

  const conditions = ids.map((id, index) => {
    request.input('id_' + index, id)
    let condition = `[${LearningContextKeywordFields.LearningContextID}] = @id_${index}`

    if (index < ids.length - 1) {
      condition += ' OR '
    }

    return condition
  })

  const query = `
    SELECT AVG([${LearningContextRatingFields.Rating}]) as average, COUNT([${LearningContextRatingFields.ID}]) as count, [${LearningContextRatingFields.LearningContextID}]
    FROM [${LearningContextRatingsTableName}] 
    WHERE ${conditions.join('')}
    GROUP BY [${LearningContextRatingFields.LearningContextID}]
  `

  const res = await request.query<{ count: number, average: number, LearningContextID: string }>(query)
  const map: { [key: string]: { count: number, average: number } } = {}

  for (const rating of res.recordset) {
    map[rating.LearningContextID] = {
      count: rating.count,
      average: rating.average
    }
  }

  return map
}
