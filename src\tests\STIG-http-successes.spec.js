const { expect } = require('chai')
const httpMocks = require('node-mocks-http')
const uuidv4 = require('uuid/v4')
const logger = require('../api/../shared_libraries/libs/logger')
const utils = require('../api/utils/test-agent.utils')
const rabbitMQ = require('../api/../shared_libraries/libs/rabbitmq')
const settings = require('../api/config/settings')

const getActivityStream = require('../api/controllers/http/activity-stream/get.controller')

const getClaimsUserMulti = require('../api/controllers/http/claims/user/get-multiple.controller')
const createClaims = require('../api/controllers/http/claims/user/create.controller')
const updateClaims = require('../api/controllers/http/claims/user/update.controller')
const deleteClaims = require('../api/controllers/http/claims/user/delete.controller')

const keywordsCreate = require('../api/controllers/http/keywords/create.controller')
const keywordsDelete = require('../api/controllers/http/keywords/delete.controller')

const learnerProgressCreate = require('../api/controllers/http/learner-progress/create.controller')
const learnerProgressDelete = require('../api/controllers/http/learner-progress/delete.controller')
const learnerProgressGetMulti = require('../api/controllers/http/learner-progress/get-multiple.controller')
const learnerProgressGet = require('../api/controllers/http/learner-progress/get.controller')
const learnerProgressResume = require('../api/controllers/http/learner-progress/resume-learing.controller')
const learnerProgressUpdate = require('../api/controllers/http/learner-progress/update.controller')

const learningContextRatingsCreate = require('../api/controllers/http/learning-context-ratings/create.controller')
const learningContextRatingsDelete = require('../api/controllers/http/learning-context-ratings/delete.controller')
const learningContextRatingsGet = require('../api/controllers/http/learning-context-ratings/get.controller')
const learningContextRatingsUpdate = require('../api/controllers/http/learning-context-ratings/update.controller')

const learningObjectContextsCreate = require('../api/controllers/http/learning-object-contexts/create.controller')
const learningObjectContextsDelete = require('../api/controllers/http/learning-object-contexts/delete.controller')
const learningObjectContextsUpdate = require('../api/controllers/http/learning-object-contexts/update.controller')

const learningObjectRatingsCreate = require('../api/controllers/http/learning-object-ratings/create.controller')
const learningObjectRatingsDelete = require('../api/controllers/http/learning-object-ratings/delete.controller')

const learningObjectsGetViewsKeyword = require('../api/controllers/http/learning-objects/get-user-views-by-keyword.controller')
const learningObjectsGetViewsType = require('../api/controllers/http/learning-objects/get-user-views-by-type.controller')
const learningObjectsGetViewHistory = require('../api/controllers/http/learning-objects/get-view-history.controller')

const myLearningGetLearningMeta = require('../api/controllers/http/my-learning/get-learning-metadata.controller')
const myLearningGetMyMeta = require('../api/controllers/http/my-learning/get-my-metadata.controller')

const skillLevelsCreate = require('../api/controllers/http/skill-levels/create.controller')
const skillLevelsDelete = require('../api/controllers/http/skill-levels/delete.controller')
const skillLevelsUpdate = require('../api/controllers/http/skill-levels/update.controller')

const userPrefCreate = require('../api/controllers/http/user-preferences/create.controller')
const userPrefDelete = require('../api/controllers/http/user-preferences/delete.controller')
const userPrefUpdate = require('../api/controllers/http/user-preferences/update.controller')

const existingUserID = 'E870AED1-3C41-4738-A92F-C574881F7CF7'
let tmpUserID

describe('STIG logging for success conditions unreachable by existing client code', () => {
  before(async () => {
    await logger.init({
      name: 'test-lms-api',
      level: 'silly',
      use_console: true,
      use_elasticsearch: false
    })
    await rabbitMQ.connect(settings.amqp.connection_config)
  })

  // -------- activity-stream --------

  it('should log successful http-get-activity-stream', async () => {
    const mocks = httpMocks.createMocks({
      params: { id: existingUserID }
    })
    utils.fillRequestHeaders(mocks.req)

    await getActivityStream(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(200)
  })

  // -------- claims --------

  const claimsUserID = uuidv4()
  it('should log successful http-create-user-claims', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: claimsUserID },
      body: ['Can_View_Admin_Dashboard', 'Can_Manage_Users']
    })

    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(201)
      done()
    })

    createClaims(req, res)
  })

  it('should log successful http-get-multi-user-claims', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: claimsUserID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      done()
    })

    getClaimsUserMulti(req, res)
  })

  it('should log successful http-update-user-claims', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: claimsUserID },
      body: ['Can_View_Admin_Dashboard', 'Can_Manage_Users', 'Can_Modify_Content']
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(201)
      done()
    })

    updateClaims(req, res)
  })

  it('should log successful http-delete-user-claims', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: claimsUserID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(204)
      done()
    })

    deleteClaims(req, res)
  })

  // -------- keywords --------

  const testKeyword = uuidv4()
  it('should log successful http-create-keyword', done => {
    const req = utils.createRequestWithHeaders({
      body: { keyword: { Name: testKeyword } }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      done()
    })

    keywordsCreate(req, res)
  })

  it('should log successful http-delete-keyword', done => {
    const req = utils.createRequestWithHeaders({
      params: { name: testKeyword }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(204)
      done()
    })

    keywordsDelete(req, res)
  })

  // -------- learner-progress --------

  const learnerProgressUserID = uuidv4()
  const learnerProgresslearningObjectID = 'EAF1DA0B-9B3D-4709-8830-D7219F925AD1' // Learning Object 1

  let learnerProgress = {
    LessonStatusID: 1,
    UserID: learnerProgressUserID,
    LearningObjectID: learnerProgresslearningObjectID
  }

  it('should log successful http-create-learner-progress', done => {
    const req = utils.createRequestWithHeaders({
      body: learnerProgress
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      const _progress = JSON.parse(res._getData())
      expect(_progress.ID).to.exist
      learnerProgress = _progress

      done()
    })

    learnerProgressCreate(req, res)
  })

  it('should log successful http-get-multi-learner-progress', done => {
    const req = utils.createRequestWithHeaders({
      params: {
        userID: learnerProgressUserID,
        objectID: learnerProgresslearningObjectID
      }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      done()
    })

    learnerProgressGetMulti(req, res)
  })

  it('should log successful http-get-learner-progress', done => {
    const req = utils.createRequestWithHeaders({
      params: {
        userID: learnerProgressUserID,
        objectID: learnerProgresslearningObjectID
      }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      done()
    })

    learnerProgressGet(req, res)
  })

  it('should log successful http-resume-learning', done => {
    const req = utils.createRequestWithHeaders({
      params: { userID: learnerProgressUserID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      done()
    })

    learnerProgressResume(req, res)
  })

  it('should log successful http-update-learner-progress', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: learnerProgress.ID },
      body: { LessonLocation: 'test-updated' }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      done()
    })

    learnerProgressUpdate(req, res)
  })

  it('should log successful http-delete-learner-progress', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: learnerProgress.ID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(204)
      done()
    })

    learnerProgressDelete(req, res)
  })

  // -------- learning context ratings --------

  const learningContextRatingsUserID = uuidv4()
  const learningContextRatingsContextID = 'AD5C499B-6AD5-4C5E-BC48-938527CEA049'
  let createdLearningContextRating

  it('should log successful http-create-learning-context-ratings', done => {
    const req = utils.createRequestWithHeaders({
      body: {
        UserID: learningContextRatingsUserID,
        Rating: 3,
        LearningContextID: learningContextRatingsContextID
      }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      const result = JSON.parse(res._getData())
      createdLearningContextRating = result
      expect(result.ID).to.exist
      done()
    })

    learningContextRatingsCreate(req, res)
  })

  it('should log successful http-get-learning-context-ratings', done => {
    const req = utils.createRequestWithHeaders({
      query: {
        id: createdLearningContextRating.ID,
        context: createdLearningContextRating.LearningContextID,
        user: learningContextRatingsUserID
      }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      done()
    })

    learningContextRatingsGet(req, res)
  })

  it('should log successful http-update-learning-context-ratings', done => {
    const req = utils.createRequestWithHeaders({
      params: {
        id: createdLearningContextRating.ID
      },
      body: {
        Rating: 1,
        LearningContextID: createdLearningContextRating.LearningContextID,
        UserID: createdLearningContextRating.UserID
      }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      done()
    })

    learningContextRatingsUpdate(req, res)
  })

  it('should log successful http-delete-learning-context-ratings', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: createdLearningContextRating.ID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(204)
      done()
    })

    learningContextRatingsDelete(req, res)
  })

  // -------- learning object contexts --------

  const learningObjContextsUserID = uuidv4()
  const learningObjContextsContextID = 'BF003B1F-CC06-46D5-A948-D3D5C44A03D4' // Course 4
  const learningObjContextsObjectID = 'FA9C921C-67AC-4700-A6C2-452D4F962483' // Learning Object 4

  it('should log successful http-create-learning-object-contexts', done => {
    const req = utils.createRequestWithHeaders(
      {
        body: {
          LearningObjectID: learningObjContextsObjectID,
          LearningContextID: learningObjContextsContextID
        },
        decoded_token: {
          id: learningObjContextsUserID
        }
      }
    )

    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      done()
    })

    learningObjectContextsCreate(req, res)
  })

  it('should log successful http-update-learning-object-contexts', done => {
    const req = utils.createRequestWithHeaders(
      {
        params: {
          contextID: learningObjContextsContextID,
          objectID: learningObjContextsObjectID
        },
        body: {
          OrderID: 2
        },
        decoded_token: {
          id: learningObjContextsUserID
        }
      }
    )

    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      try {
        expect(res.statusCode).to.equal(200)
        const updated = JSON.parse(res._getData())
        expect(updated.OrderID).to.equal(2)
        done()
      } catch (error) {
        done(error)
      }
    })

    learningObjectContextsUpdate(req, res)
  })

  it('should log successful http-delete-learning-object-contexts', done => {
    const req = utils.createRequestWithHeaders({
      params: {
        contextID: learningObjContextsContextID,
        learningObjectID: learningObjContextsObjectID
      }
    })

    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(204)
      done()
    })

    learningObjectContextsDelete(req, res)
  })

  // -------- learning object ratings --------

  tmpUserID = uuidv4()
  const learningObjectRatingsObjectID = 'EAF1DA0B-9B3D-4709-8830-D7219F925AD1'
  let createdLearningObjectRating

  it('should log successful http-create-learning-object-rating', done => {
    const req = utils.createRequestWithHeaders({
      body: {
        UserID: tmpUserID,
        Rating: 5,
        LearningObjectID: learningObjectRatingsObjectID
      }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      const result = JSON.parse(res._getData())
      createdLearningObjectRating = result
      expect(result.ID).to.exist

      done()
    })

    learningObjectRatingsCreate(req, res)
  })

  it('should log successful http-delete-learning-object-rating', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: createdLearningObjectRating.ID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(204)
      done()
    })

    learningObjectRatingsDelete(req, res)
  })

  // -------- learning objects --------

  const viewsUserID = '795EE66B-5FBA-4469-BE8D-124A33624FC0'

  it('gets learning object user views by type', async () => {
    const mocks = httpMocks.createMocks({ params: { id: viewsUserID } })
    utils.fillRequestHeaders(mocks.req)

    await learningObjectsGetViewsType(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
  })

  it('gets learning object views by keyword for a given user', async () => {
    const mocks = httpMocks.createMocks({ params: { id: viewsUserID } })
    utils.fillRequestHeaders(mocks.req)

    await learningObjectsGetViewsKeyword(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
  })

  it('gets learning object view history for a given user', async () => {
    const mocks = httpMocks.createMocks({ params: { id: viewsUserID } })
    utils.fillRequestHeaders(mocks.req)

    await learningObjectsGetViewHistory(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
  })

  // -------- my learning --------

  tmpUserID = 'E977EF82-DCA6-46CC-BBD1-B4690D754B10'
  it('should log successful http-get-learning-metadata', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: tmpUserID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      done()
    })

    myLearningGetLearningMeta(req, res)
  })

  it('should log successful http-get-my-metadata', done => {
    const req = utils.createRequestWithHeaders({
      decoded_token: { id: tmpUserID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      done()
    })

    myLearningGetMyMeta(req, res)
  })

  // -------- skill levels --------

  let skillLevel = { Name: uuidv4() }
  const skillLevelUpdated = { Name: uuidv4() }

  it('should log successful http-create-skill-level', done => {
    const req = utils.createRequestWithHeaders({
      body: { Name: 'Test', OrderID: 3 }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      const created = JSON.parse(res._getData())
      expect(created.ID).to.exist
      skillLevel = created
      done()
    })

    skillLevelsCreate(req, res)
  })

  it('should log successful http-update-skill-level', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: skillLevel.ID },
      body: skillLevelUpdated
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      done()
    })

    skillLevelsUpdate(req, res)
  })

  it('should log successful http-delete-skill-level', done => {
    const req = utils.createRequestWithHeaders({
      params: { id: skillLevel.ID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(204)
      done()
    })

    skillLevelsDelete(req, res)
  })

  // -------- user preferences --------

  tmpUserID = uuidv4()
  it('should log successful http-create-user-preferences', done => {
    const req = utils.createRequestWithHeaders({
      body: {
        UserID: tmpUserID,
        GettingStarted: true
      }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      done()
    })

    userPrefCreate(req, res)
  })

  it('should log successful http-update-user-preferences', done => {
    const req = utils.createRequestWithHeaders({
      params: {
        userID: tmpUserID
      },
      body: {
        UserID: tmpUserID,
        GettingStarted: false
      }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(200)
      done()
    })

    userPrefUpdate(req, res)
  })

  it('should log successful http-delete-user-preferences', done => {
    const req = utils.createRequestWithHeaders({
      params: { userID: tmpUserID }
    })
    const res = httpMocks.createResponse({
      eventEmitter: require('events').EventEmitter
    })

    res.on('end', () => {
      expect(res.statusCode).to.equal(204)
      done()
    })

    userPrefDelete(req, res)
  })

  after(done => {
    rabbitMQ.close()
    logger.close()
    done()
  })
})
