import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningObjectUserBookmarkModel from '../../../models/learning-object-user-bookmark.model'

describe('HTTP get controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-object-bookmarks/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve({LearningObjectUserBookmarkModel})),
                getUserBookmarksForObject: Sinon.stub().returns(Promise.resolve([new LearningObjectUserBookmarkModel({})])),
                getBookmarksForUser: Sinon.stub().returns(Promise.resolve([new LearningObjectUserBookmarkModel({})]))
            }

            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                userID: uuid(), 
                objectID: uuid()
            }
            
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-object-bookmarks/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve({LearningObjectUserBookmarkModel})),
                getUserBookmarksForObject: Sinon.stub().returns(Promise.resolve([new LearningObjectUserBookmarkModel({})])),
                getBookmarksForUser: Sinon.stub().returns(Promise.resolve([new LearningObjectUserBookmarkModel({})]))
            }

            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                userID: false, 
                objectID: false
            }
            
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request query parameter data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('userID')
        expect(mocks.res._getData()).include('objectID')

    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-object-bookmarks/get.service.js': {
                default: Sinon.stub().returns(Promise.reject({LearningObjectUserBookmarkModel})),
                getUserBookmarksForObject: Sinon.stub().returns(Promise.reject([new LearningObjectUserBookmarkModel({})])),
                getBookmarksForUser: Sinon.stub().returns(Promise.reject([new LearningObjectUserBookmarkModel({})]))
            }

            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                userID: uuid(), 
                objectID: uuid()
            }
            
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })


})