import logger from '@lcs/logger'
import { ConnectionPool } from 'mssql'
import { addRow } from '@lcs/mssql-utility'
import createStream from '../activity-stream/create.service.js'
import getObjectService from '../learning-objects/get.service.js'
import getContextService from '../learning-context/get.service.js'
import ActivityStreamModel from '../../../models/activity-stream.model.js'
import AssignmentUserModel from '../../../models/assignment-users.model.js'
import { getAssignmentContentForLearningContext } from '../learning-context/utils.service.js'
import AssignmentLearningObjectModel from '../../../models/assignment-learning-object.model.js'
import AssignmentLearningContextModel from '../../../models/assignment-learning-context.model.js'
import UserAssignedLearningObjectModel from '../../../models/user-assigned-learning-object.model.js'
import { UserAssignedLearningObject } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service.Create-Multiple-User-Assigned-Learning-Objects')

export default async function (pool: ConnectionPool, assignmentGUID: string, users: AssignmentUserModel[], contexts: AssignmentLearningContextModel[], objs: AssignmentLearningObjectModel[], groupUserIDs: string[], assignedByGUID: string, dueDate?: Date): Promise<void> {
  // get the user IDs
  let userIDs: string[] = []
  users.forEach(user => {
    if (user.fields.UserID && !userIDs.includes(user.fields.UserID)) {
      userIDs.push(user.fields.UserID)
    }
  })
  userIDs = userIDs.concat(groupUserIDs)

  const userAssignedObjects: UserAssignedLearningObjectModel[] = []
  // create a user assigned object for each user and learning object
  if (objs && objs.length > 0) {
    for (const obj of objs) {
      const assignmentObject = await getObjectService(obj.fields.LearningObjectID!)
      for (const id of userIDs) {
        userAssignedObjects.push(new UserAssignedLearningObjectModel({
          AssignmentID: assignmentGUID,
          UserID: id,
          LearningObjectID: assignmentObject.fields.ID,
          DueDate: dueDate,
          CreatedOn: new Date(),
          LearningObjectTypeID: assignmentObject.fields.LearningObjectTypeID
        }))
        try {
          await createStream(new ActivityStreamModel({
            UserID: assignedByGUID,
            LinkText: assignmentObject.fields.Title,
            LinkID: assignmentObject.fields.ID,
            ActivityID: Activities.AssignedLearningObject,
            CreatedOn: new Date(),
            ToUserID: id
          }))
        } catch {}
      }
    }
  }

  // Next lets create an assigned object for each user and learning context.
  if (contexts && contexts.length > 0) {
    for (const assignmentContext of contexts) {
      // get the learning object assignments for the context
      const contextObjectAssignments = await getAssignmentContentForLearningContext(pool.request(), assignmentContext.fields.LearningContextID!)
      userIDs.forEach(userId => {
        contextObjectAssignments.forEach(assignment => {
          const userAssignment = new UserAssignedLearningObjectModel({ ...assignment.exportJsonToDatabase() })
          userAssignment.fields.AssignmentID = assignmentGUID
          userAssignment.fields.UserID = userId
          userAssignment.fields.CreatedOn = new Date()
          userAssignment.fields.DueDate = dueDate
          userAssignedObjects.push(userAssignment)
        })
      })

      // get the context
      const context = await getContextService(assignmentContext.fields.LearningContextID!, undefined, { nestedContexts: true })

      // create activity stream records
      for (const userId of userIDs) {
        try {
          let text
          if (context.fields.ContextTypeID === LearningContextTypes.Course && context.fields.CourseTypeID === CourseTypes.InstructorLed) {
            text = `${context.fields.Label} Training: ${context.fields.Title}`
          } else {
            text = `${context.fields.Label}: ${context.fields.Title}`
          }
          await createStream(new ActivityStreamModel({
            UserID: assignedByGUID,
            LinkText: text,
            LinkID: context.fields.ID,
            ActivityID: Activities.AssignedContext,
            CreatedOn: new Date(),
            ToUserID: userId
          }))
        } catch {}
      }
    }
  }

  // now lets commit the records to the database
  for (const assignment of userAssignedObjects) {
    try {
      await addRow<UserAssignedLearningObject>(pool.request(), assignment)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      // if the insert fails because the record exists just ignore otherwise throw the error
      if (errorMessage.toLowerCase().includes('violation of unique key constraint')) {
        // user already has assignment
        log('warn', 'Failed to create user assigned learning object record: user has assignment', {
          success: false,
          assignmentId: assignment.fields.AssignmentID,
          userId: assignment.fields.UserID,
          learningObjectId: assignment.fields.LearningObjectID,
          forContextId: assignment.fields.ForContextID,
          subContextId: assignment.fields.SubContextID
        })
      } else {
        log('error', 'Failed to create user assigned learning object record.', { errorMessage, success: false })
        throw error
      }
    }
  }

  log('info', 'Successfully created user assigned learning object records.', {
    success: true,
    objectCount: objs.length,
    contextCount: contexts.length,
    userCount: userIDs.length
  })
}
