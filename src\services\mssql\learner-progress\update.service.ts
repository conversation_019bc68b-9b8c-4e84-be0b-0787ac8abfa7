import mssql, { <PERSON>_Errors, getRows, updateRow } from '@lcs/mssql-utility'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import updateAssignments from '../user-assigned-learning-objects/update-user-learning-object-status.service.js'
import updateMultiSessionAssignments from '../user-assigned-multi-session-courses/update-user-context-session-status.service.js'
import errors from '../../../config/errors.js'
import getContextSession from '../learning-context-sessions/get.service.js'
import { getAllContextThatUseExam, getAllContextsThatUseObjectAndHaveExamOrSurvey, getAllContextsThatUseIltSessionAndHaveExamOrSurvey } from '../../../services/mssql/learning-context/utils.service.js'
import getContextCompletion from '../../../services/mssql/learning-context/get-completion.service.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearnerProgress } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { GradeTypes } from '@tess-f/sql-tables/dist/lms/grade-type.js'
import { User, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { sendFormattedMessage } from '@tess-f/email/dist/amqp/send-formatted-message.js'
import settings from '../../../config/settings.js'
import sendNotification from '../../../services/amqp/notification/send-notification.service.js'
import { getSystemConfig } from '../../amqp/system/get-system-config.service.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

/**
 *
 * @param {LearnerProgressModel} updateModel
 */
export default async function (updateModel: LearnerProgressModel): Promise<LearnerProgressModel> {
  updateModel.fields.ModifiedOn = new Date()

  const pool = mssql.getPool()

  // check that we are updating the progress for a learning object
  if (updateModel.fields.LearningObjectID && updateModel.fields.LearningObjectID !== null) {
    try {
      await updateAssignments(updateModel.fields.UserID!, updateModel.fields.LearningObjectID, updateModel.fields.LessonStatusID!, updateModel.fields.ID!)
    } catch (error) {
      // If there is no assignment for this learning move on
      if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
        throw error
      }
    }
  } else if (updateModel.fields.LearningContextSessionID !== null) {
    try {
      let session
      try {
        session = await getContextSession(updateModel.fields.LearningContextSessionID!)
      } catch (err) {
        if (err instanceof Error && err.message === DB_Errors.default.NOT_FOUND_IN_DB) {
          throw new Error(errors.INVALID_ARGS)
        } else {
          throw err
        }
      }
      await updateMultiSessionAssignments(updateModel.fields.UserID!, session.fields.LearningContextID!, updateModel.fields.LessonStatusID!, updateModel.fields.LearningContextSessionID!, updateModel.fields.ID!)
    } catch (error) {
      if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
        throw error // if there is no assignment for this learning move on
      }
    }
  }

  // context IDs for items that use the exam / learning object / ILT session
  let contexts: LearningContextModel[] = []
  // context IDS for items that are incomplete before save
  const incompleteContexts: LearningContextModel[] = []

  if (updateModel.fields.LearningObjectID) {
    contexts = await getAllContextsThatUseObjectAndHaveExamOrSurvey(pool.request(), updateModel.fields.LearningObjectID);
    (await getAllContextThatUseExam(pool.request(), updateModel.fields.LearningObjectID)).forEach(context => {
      if (!contexts.some(c => c.fields.ID === context.fields.ID)) {
        contexts.push(context)
      }
    })
  } else if (updateModel.fields.LearningContextSessionID) {
    contexts = await getAllContextsThatUseIltSessionAndHaveExamOrSurvey(pool.request(), updateModel.fields.LearningContextSessionID)
  }

  await Promise.all(contexts.map(async context => {
    const completion = await getContextCompletion(context.fields.ID!, updateModel.fields.UserID!, undefined, undefined, false)
    if (completion.completion < 100) {
      incompleteContexts.push(context)
    }
  }))

  const records = await updateRow<LearnerProgress>(pool.request(), updateModel, { ID: updateModel.fields.ID })

  // check status of each context
  const user = (await getRows<User>(UserTableName, pool.request(), { ID: updateModel.fields.UserID! }))[0]
  const config = await getSystemConfig()
  const lmsConfig = config.Apps.find(app => app.Id === 'lms')

  await Promise.all(incompleteContexts.map(async context => {
    // only send exam available emails if this context is graded by the exam
    if (context.fields.GradeTypeID === GradeTypes.Exam && context.fields.ExamID !== updateModel.fields.LearningObjectID) {
      const completion = (await getContextCompletion(context.fields.ID!, updateModel.fields.UserID!, undefined, undefined, false)).completion
      if (completion >= 100) {
        // this was incomplete but now the exam is unlocked
        let contextAddress = `${config.Domain}${lmsConfig?.Address ?? '/lms'}/browse/`
        if (context.fields.ContextTypeID === LearningContextTypes.Course) {
          contextAddress += `course/${context.fields.ID}`
        } else {
          contextAddress += `view-context/${context.fields.ID}`
        }
        const bodyMsg = `<p>The exam for <a href="${contextAddress}">${context.fields.Label}: ${context.fields.Title}</a> is now available.</p>`
        await sendFormattedMessage(
          settings.amqp.service_queues.email, {
            to: [user.Email!],
            body: bodyMsg,
            text: `The exam for ${context.fields.Label}: ${context.fields.Title} is now available.`,
            header: 'Exam Ready',
            subject: `${context.fields.Title} Exam`
          },
          settings.amqp.command_timeout
        )
        
        // send notification
        await sendNotification({
          Title: `${context.fields.Title} Exam`,
          Message: bodyMsg,
          PublishDate: new Date(),
          Everyone: false,
          SystemID: 'lms',
          Priority: 1,
          UserIDs: [user.ID!]
        })
      }
    }
    if (context.fields.SurveyURL) {
      const completion = (await getContextCompletion(context.fields.ID!, updateModel.fields.UserID!, undefined, undefined, true)).completion
      if (completion >= 100) {
        // this was incomplete but now it's done and the survey is ready
        const bodyMsg = `
            <p>We want to hear from you. Please fill out the below survey and tell us about your learning experience.</p>
            <p><a href="${context.fields.SurveyURL}">Take our survey</a></p>
          `
        await sendFormattedMessage(
          settings.amqp.service_queues.email, {
            to: [user.Email!],
            body: bodyMsg,
            text: `
            We want to hear from you. Please fill out the below survey and tell us about your learning experience.\n
            ${context.fields.SurveyURL}
          `,
            header: 'We want to hear from you',
            subject: `${context.fields.Title} Survey`
          },
          settings.amqp.command_timeout
        )

        // send notification
        await sendNotification({
          Title: `${context.fields.Title} Survey`,
          Message: bodyMsg,
          PublishDate: new Date(),
          Everyone: false,
          SystemID: 'lms',
          Priority: 1,
          UserIDs: [user.ID!]
        })
      }
    }
  }))

  return new LearnerProgressModel(records[0])
}
