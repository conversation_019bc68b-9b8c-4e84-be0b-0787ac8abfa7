import logger from '@lcs/logger'
import { Request, Response } from 'express'
import searchUserActiveAssignments from '../../../services/mssql/assignments/search-user-active-assignments.service.js'
import getAssignmentContentForUserService from '../../../services/mssql/assignments/get-assignment-content-for-user.service.js'
import calculateAssignmentContentStatus from '@tess-f/lms/dist/shared/services/assignment/grade-user-assignment-content.service.js'
import { getOverallStatus } from '@tess-f/lms/dist/shared/services/assignment/context-grading/utils.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID, zodLimit, zodOffset, zodString } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-users-assignment-details', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { limit, offset, search, keywords } = z.object({
      limit: zodLimit,
      offset: zodOffset,
      search: zodString.optional(),
      keywords: z.string().optional().transform(value => (value ?? '').split(','))
    }).parse(req.query)
    const userId = z.object({ id: zodGUID }).parse(req.params).id
    
    const assignments = await searchUserActiveAssignments(userId, offset, limit, search, keywords)

    const details = await Promise.all(assignments.Assignments.map(async assignment => {
      // get all the data about each assignment
      const assignedItems = await getAssignmentContentForUserService(assignment.AssignmentID, userId)
      const gradedContent = calculateAssignmentContentStatus(assignedItems)

      return {
        AssignmentID: assignment.AssignmentID,
        Title: assignment.Title,
        Status: getOverallStatus(gradedContent.map(item => item.Status)),
        Progress: getAverage(gradedContent.map(item => item.Progress)),
        Started: getStartedDate(gradedContent.map(item => item.Started).filter(item => item !== undefined && item !== null) as Date[]),
        LastAccessed: getLastAccessedDate(gradedContent.map(item => item.LastAccessed).filter(item => item !== undefined && item !== null) as Date[]),
        DueDate: assignment.DueDate,
        Content: gradedContent
      }
    }))

    log('info', 'Successfully retrieved assignment details for user', { count: details.length, totalItems: assignments.TotalRecords, success: true, req })

    res.json({
      totalRecords: assignments.TotalRecords,
      items: details
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get assignment details for user: validation error', { error, req, success: false })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to get assignment details for user', { error, req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}

function getAverage (numbers: number[]): number {
  let total = 0
  numbers.forEach(prog => { total += prog })
  return total / numbers.length
}

function getStartedDate (dates: Date[]): Date | undefined {
  let started: Date | undefined
  dates.forEach(date => {
    if (!started || started.getTime() > date.getTime()) {
      started = date
    }
  })
  return started
}

function getLastAccessedDate (dates: Date[]): Date | undefined {
  let lastAccessed: Date | undefined
  dates.forEach(date => {
    if (!lastAccessed || lastAccessed.getTime() < date.getTime()) {
      lastAccessed = date
    }
  })
  return lastAccessed
}
