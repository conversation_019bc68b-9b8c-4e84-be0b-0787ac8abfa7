import mssql, { addRow } from '@lcs/mssql-utility'
import getService from '../learning-objects/get.service.js'
import createService from '../activity-stream/create.service.js'
import ActivityStreamModel from '../../../models/activity-stream.model.js'
import LearningObjectUserFavoritesModel from '../../../models/learning-object-user-favorite.model.js'
import { LearningObjectUserFavorite } from '@tess-f/sql-tables/dist/lms/learning-object-user-favorite.js'

export default async function (objectFavorite: LearningObjectUserFavoritesModel): Promise<LearningObjectUserFavoritesModel> {
  const pool = mssql.getPool()
  // create an activity stream record
  const lo = await getService(objectFavorite.fields.LearningObjectID!)
  const activity = new ActivityStreamModel({
    UserID: objectFavorite.fields.UserID,
    LinkText: lo.fields.Title,
    LinkID: objectFavorite.fields.LearningObjectID,
    ActivityID: 2,
    CreatedOn: new Date()
  })
  await createService(activity)
  const record = await addRow<LearningObjectUserFavorite>(pool.request(), objectFavorite)
  return new LearningObjectUserFavoritesModel(record)
}
