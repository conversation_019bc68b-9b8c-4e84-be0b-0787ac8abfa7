import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import { SystemConfig } from '@tess-f/system-config/dist/http/system-config.js'
import exp from 'constants'


describe('HTTP request controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./request.controller', {
            '../../../services/mssql/users/get-by-id.service.js': {
                default: Sinon.stub().returns(Promise.resolve({ User: uuid() }))
            },
            '../../../services/amqp/system/get-system-config.service.js': {
                getSystemConfig: Sinon.stub().returns(Promise.resolve(<SystemConfig>{ Apps: [], Services: [], SessionTimeout: 0, Id: 0, DisplayName: '', Description: '', AdminEmails: '', SupportEmail: '', Theme: '', Logo: '', Domain: '', DarkLogo: '', Version: '' }))
            },
            '@tess-f/email/dist/amqp/request-ilt-session.js': {
                sendILTSessionRequestMessage: Sinon.stub().returns(Promise.resolve({success: true}))
            },
            '../../../services/amqp/notification/send-notification.service.js': {
                default: Sinon.stub().returns(Promise.resolve(true))
            },
            '../../../services/mssql/learning-context/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            body: {
                contexts: [{ ID: uuid()}],
                message: 'test'
            },
            session: { userId: uuid() }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.NO_CONTENT)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./request.controller', {
            '../../../services/mssql/users/get-by-id.service.js': {
                default: Sinon.stub().returns(Promise.resolve({ User: uuid() }))
            },
            '../../../services/amqp/system/get-system-config.service.js': {
                getSystemConfig: Sinon.stub().returns(Promise.resolve(<SystemConfig>{ Apps: [], Services: [], SessionTimeout: 0, Id: 0, DisplayName: '', Description: '', AdminEmails: '', SupportEmail: '', Theme: '', Logo: '', Domain: '', DarkLogo: '', Version: '' }))
            },
            '@tess-f/email/dist/amqp/request-ilt-session.js': {
                sendILTSessionRequestMessage: Sinon.stub().returns(Promise.resolve({success: true}))
            },
            '../../../services/amqp/notification/send-notification.service.js': {
                default: Sinon.stub().returns(Promise.resolve(true))
            },
            '../../../services/mssql/learning-context/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            body: {
                contexts: false,
                message: false
            },
            session: { userId: uuid() }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected array, received boolean')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('contexts')
        expect(mocks.res._getData()).include('message')

    })

    it('returns success if the request data is valid and there is no message in the body', async () => {
        const controller = await esmock('./request.controller', {
            '../../../services/mssql/users/get-by-id.service.js': {
                default: Sinon.stub().returns(Promise.resolve({ User: uuid() }))
            },
            '../../../services/amqp/system/get-system-config.service.js': {
                getSystemConfig: Sinon.stub().returns(Promise.resolve(<SystemConfig>{ Apps: [], Services: [], SessionTimeout: 0, Id: 0, DisplayName: '', Description: '', AdminEmails: '', SupportEmail: '', Theme: '', Logo: '', Domain: '', DarkLogo: '', Version: '' }))
            },
            '@tess-f/email/dist/amqp/request-ilt-session.js': {
                sendILTSessionRequestMessage: Sinon.stub().returns(Promise.resolve({success: true}))
            },
            '../../../services/amqp/notification/send-notification.service.js': {
                default: Sinon.stub().returns(Promise.resolve(true))
            },
            '../../../services/mssql/learning-context/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            body: {
                contexts: [{ ID: uuid()}]
            },
            session: { userId: uuid() }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.NO_CONTENT)

    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./request.controller', {
            '../../../services/mssql/users/get-by-id.service.js': {
                default: Sinon.stub().returns(Promise.resolve({ User: uuid() }))
            },
            '../../../services/amqp/system/get-system-config.service.js': {
                getSystemConfig: Sinon.stub().returns(Promise.reject(<SystemConfig>{ Apps: [], Services: [], SessionTimeout: 0, Id: 0, DisplayName: '', Description: '', AdminEmails: '', SupportEmail: '', Theme: '', Logo: '', Domain: '', DarkLogo: '', Version: '' }))
            },
            '@tess-f/email/dist/amqp/request-ilt-session.js': {
                sendILTSessionRequestMessage: Sinon.stub().rejects(Promise.resolve({success: true}))
            },
            '../../../services/amqp/notification/send-notification.service.js': {
                default: Sinon.stub().returns(Promise.reject(true))
            },
            '../../../services/mssql/learning-context/get.service.js': {
                default: Sinon.stub().returns(Promise.reject(new LearningContextSessionModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            body: {
                contexts: [{ ID: uuid()}],
                message: 'test'
            },
            session: { userId: uuid() }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })


    

})