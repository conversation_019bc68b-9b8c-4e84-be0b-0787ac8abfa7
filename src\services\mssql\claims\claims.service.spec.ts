import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'

import getForUser from './get-for-user.service.js'
import getMultiple from './get-multiple.service.js'
import hasClaim from './has-claim.service.js'
import ClaimModel from '../../../models/claim.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

let claim: ClaimModel

describe('MSSQL Claims Service', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
  })

  it('should get all claims for user', async () => {
    const claims = await getForUser(AdminUserId)
    expect(claims.length).to.be.gte(1)
  })

  it('should get all claims', async () => {
    const claims = await getMultiple()
    expect(claims.length).to.be.gte(1)
  })

  it('should check if user has claim', async () => {
    const claims = await hasClaim(AdminUserId, Claims.BROWSE_CONTENT)
    expect(claims).to.be.eq(true)
  })
})
