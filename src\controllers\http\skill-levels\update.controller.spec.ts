import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextSessionModel from '../../../models/learning-context-session.model'
import SkillLevelModel from '../../../models/skill-level.model'

describe('HTTP update controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/skill-levels/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SkillLevelModel({})))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            },
            body: {
                Name: "Test",
                OrderID: 1
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })


    it('returns an error if the request params are invalid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/skill-levels/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SkillLevelModel({})))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: false
            },
            body: {
                Name: "Test",
                OrderID: 1
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('id: Expected string, received boolean')
    })

    it('returns an error if the request body is invalid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/skill-levels/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SkillLevelModel({})))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            },
            body: {
                Name: false,
                OrderID: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Name: Expected string, received boolean')
        expect(mocks.res._getData()).include('OrderID: Expected number, received boolean')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/skill-levels/update.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new SkillLevelModel({})))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            },
            body: {
                Name: "Test",
                OrderID: 1
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })
 

})