import getDuration from '../../../services/mssql/learning-context/get-duration.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.get-learning-context-duration', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, click on a single course. Note the time. Check the LMS API log for the 'http-get-learning-context-duration' label."

  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const duration = await getDuration(id)

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, click on a single course. Note the time. Check the LMS API log for the 'http-get-learning-context-duration' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved duration for learning context', { contextId: id, duration, success: true, req })

    res.json({
      duration
    })
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning context duration: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learning context duration for the given context because it was not found in the database', { contextId: req.params.id, success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      log('error', 'Failed to get learning context duration', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
