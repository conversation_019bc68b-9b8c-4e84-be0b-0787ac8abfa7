import logger from '@lcs/logger'
import Sinon from 'sinon'
import { expect } from 'chai'
import LearningContextModel from '../../../models/learning-context.model.js'
import { v4 as uuid } from 'uuid'
import esmock from 'esmock'

describe('Controller.AMQP: create learning context', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns unsuccessful when data is missing from request', async () => {
    const controller = await esmock('./create.js')
    const response = await controller({ command: 'test', data: undefined })
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('Required')
  })

  it('returns unsuccess when the message is missing required fields', async () => {
    const controller = await esmock('./create.js')
    const response = await controller({ command: 'test', data: { Label: 'Test', CourseID: 'Test' } })
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('Title: Required')
    expect(response.message).to.include('ContextTypeID: Required')
  })

  it('creates the given learning context', async () => {
    const controller = await esmock('./create.js', {
      '../../../services/mssql/learning-context/create.service.js': {
        default: Sinon.stub().returns(Promise.resolve(new LearningContextModel({
          ID: uuid(),
          Title: 'Test'
        })))
      }
    })
    
    const response = await controller({
      command: 'test',
      data: {
        Title: 'Test',
        ContextTypeID: 1,
        CreatedBy: uuid()
      }
    })
    expect(response.success).to.be.true
    expect(response.data).to.exist
    expect(response.data?.ID).to.exist
  })

  it('returns unsuccessful with an error message when the service fails', async () => {
    const controller = await esmock('./create.js', {
      '../../../services/mssql/learning-context/create.service.js': {
        default: Sinon.stub().returns(Promise.reject(new Error('Service Error')))
      }
    })
    const response = await controller({
      command: 'test',
      data: {
        Title: 'Test',
        ContextTypeID: 1,
        CreatedBy: uuid()
      }
    })
    expect(response.success).to.be.false
    expect(response.message).to.equal('Service Error')
    expect(response.error).to.exist
  })
})
