import { Table } from '@lcs/mssql-utility'
import { SystemWidget, SystemWidgetFields, SystemWidgetsTableName } from '@tess-f/sql-tables/dist/lms/system-widget.js'
import { SystemWidgetJson } from '@tess-f/lms/dist/common/system-widget.js'
import { z } from 'zod'
import { createWidgetSchema } from './widget.model.js'
import { zodGUID } from '@tess-f/backend-utils/validators'

const systemWidgetSchema = z.object({
  [SystemWidgetFields.Title]: z.string().max(500),
  [SystemWidgetFields.Everyone]: z.boolean().optional().default(true),
  [SystemWidgetFields.Published]: z.boolean().optional().default(false),
  [SystemWidgetFields.Priority]: z.number().int().positive().optional().default(1),
  Widgets: z.array(createWidgetSchema).min(1),
  Groups: z.array(z.object({ ID: zodGUID })).optional()
})

export const createSystemWidgetSchema = systemWidgetSchema
.superRefine(({ Everyone, Groups }, ctx) => {
  if (!Everyone && (!Groups || Groups.length <= 0)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'No audience defined',
      path: ['Everyone']
    })
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'No audience defined',
      path: ['Groups']
    })
  }
})

export const updateSystemWidgetSchema = systemWidgetSchema.partial().merge(z.object({
  [SystemWidgetFields.ID]: zodGUID
}))

export default class SystemWidgetModel extends Table<SystemWidgetJson, SystemWidget> {
  public fields: SystemWidgetJson

  constructor (fields?: SystemWidgetJson, record?: SystemWidget) {
    super(SystemWidgetsTableName, [
      SystemWidgetFields.Title,
      SystemWidgetFields.Everyone,
      SystemWidgetFields.Published
    ])
    if (fields) this.fields = fields
    else this.fields = {}

    if (record) this.importFromDatabase(record)
  }

  public importFromDatabase (record: SystemWidget): void {
    this.fields = record
  }

  public exportJsonToDatabase (): SystemWidget {
    return {
      ID: this.fields.ID,
      Title: this.fields.Title,
      Everyone: this.fields.Everyone,
      Published: this.fields.Published,
      Priority: this.fields.Priority
    }
  }
}
