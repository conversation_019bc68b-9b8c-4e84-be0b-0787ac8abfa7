import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: get my teams learning metadata', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => sinon.restore())

  it('returns bad request when the request query data is invalid', async () => {
    const controller = await esmock('./get-my-teams-learning-metadata.controller.js')
    const mocks = httpMocks.createMocks({ query: { from: 'tomorrow', to: 'yesterday', search: 1 }, session: { userId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request query data:')
    expect(data).to.include('search: Expected string, received number')
    expect(data).to.include('from: Invalid date')
    expect(data).to.include('to: Invalid date')
  })

  it('returns the learning metadata for the users team', async () => {
    const controller = await esmock('./get-my-teams-learning-metadata.controller.js', {
      '../../../services/mssql/users/get-paginated-team.service.js': { default: sinon.stub().resolves({ totalRecords: 20, users: [] }) }
    })
    const mocks = httpMocks.createMocks({ session: { userId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
    expect(data.TotalRecords).to.be.a('number')
    expect(data.UserLearningMetadata).to.be.an('array')
    expect(data.UserLearningMetadata.length).to.equal(0)
    expect(data.TotalRecords).to.equal(20)
  })

  it('gracefully handles an error', async () => {
    const controller = await esmock('./get-my-teams-learning-metadata.controller.js', {
      '../../../services/mssql/users/get-paginated-team.service.js': { default: sinon.stub().rejects(new Error('Service Error')) }
    })
    const mocks = httpMocks.createMocks({ session: { userId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })
})
