import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NOT_FOUND } = httpStatus
import getCmi5CourseContextForAu from '../../../services/mssql/learning-context/get-id-of-cmi5-course-by-au-id.service.js'
import getOrCreateLearningContextRegistration from '../../../services/mssql/learning-context-registrations/get-or-create.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import markNaAusSatisfied from '../../../services/mssql/cmi5-au/mark-na-aus-satisfied.service.js'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-or-create-cmi5-course-registration', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { auId } = z.object({ auId: zodGUID }).parse(req.params)
    const course = await getCmi5CourseContextForAu(auId)
    const { registration, created } = await getOrCreateLearningContextRegistration(course.fields.ID!, req.session.userId)

    log('info', 'Successfully retrieved registration', { courseId: course.fields.ID, userId: registration.fields.UserId, success: true, created, req })

    if (created) {
      // this is a new registration!
      // we need to gather all the AU's for this course that have a move on of 'not applicable' and mark them satisfied
      await markNaAusSatisfied(course.fields.ID!, req.session.userId, registration.fields.Id!)
    }

    res.json(registration.fields)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get or create cmi5 course registration: validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get cmi5 course id for au', { auId: req.params.auId, success: false, req })
      res.sendStatus(NOT_FOUND)
    } else {
      log('error', 'Failed to get or create cmi5 course registration', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
