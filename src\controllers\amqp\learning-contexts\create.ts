import logger from '@lcs/logger'
import LearningContext, { createLearningContextSchema } from '../../../models/learning-context.model.js'
import create from '../../../services/mssql/learning-context/create.service.js'
import { RpcMessage } from '@tess-f/shared-config/dist/types/rpc-message.js'
import { RpcResponse } from '@tess-f/shared-config/dist/types/rpc-response.js'
import { LearningContextJson } from '@tess-f/lms/dist/common/learning-context.js'
import { getErrorMessage, zodErrorToMessage } from '@tess-f/backend-utils'
import { ZodError } from 'zod'

const log = logger.create('Controller-AMQP.create-learning-contexts')

export default async function (req: RpcMessage<LearningContextJson>): Promise<RpcResponse<LearningContextJson>> {
  try {
    const learningContext = new LearningContext(createLearningContextSchema.parse(req.data))
    learningContext.fields.CreatedOn = new Date()

    const created = await create(learningContext)

    log('info', 'Successfully created learning context', { id: created.fields.ID, success: true })

    return {
      success: true,
      data: created.fields
    }
  } catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Failed to created learning context: input validation error', { errorMessage: error.message, success: false })
      return {
        success: false,
        message: zodErrorToMessage(error, 'Invalid request data:')
      }
    }
    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to create learning context', { errorMessage, success: false })

    return {
      success: false,
      message: errorMessage,
      error
    }
  }
}
