import get from '../../../services/mssql/learning-objects/get.service.js'
import logger from '@lcs/logger'
import { RpcMessage } from '@tess-f/shared-config/dist/types/rpc-message.js'
import { GetLearningObjectRequest } from '@tess-f/lms/dist/amqp/learning-object.js'
import { RpcResponse } from '@tess-f/shared-config/dist/types/rpc-response.js'
import { LearningObjectJson } from '@tess-f/lms/dist/common/learning-object.js'
import { getErrorMessage, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-AMQP.get-learning-object')

export default async function (req: RpcMessage<GetLearningObjectRequest>): Promise<RpcResponse<LearningObjectJson>> {
  try {
    const { ID } = z.object({ ID: zodGUID }).parse(req.data) 
    const res = await get(ID)

    log('info', 'Successfully retrieved learner object', { success: true })

    return {
      success: true,
      data: res.fields
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning object: input validation error', { errorMessage: error.message, success: false })
      return {
        success: false,
        message: zodErrorToMessage(error, 'Invalid request data: ')
      }
    }

    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to get learning object.', { errorMessage, success: false })

    return {
      success: false,
      message: errorMessage
    }
  }
}
