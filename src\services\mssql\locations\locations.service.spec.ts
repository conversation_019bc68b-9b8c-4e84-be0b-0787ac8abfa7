import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import Location from '../../../models/location.model.js'
import get from './get.service.js'
import getPaginated from './get-paginated.service.js'
import create from './create.service.js'
import remove from './delete.service.js'
import update from './update.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'

let locID1: string, locID2: string

const location1 = new Location({
  AddressLine1: 'Test Address 1',
  City: 'Santa Fe',
  State: 'NM',
  Zip: '87501',
  Title: 'Location Title 1',
  Building: 'Location Building 1',
  Room: 'LocationRoom 1',
  CreatedOn: new Date(),
  CreatedBy: AdminUserId,
  Country: 'USA'
})

let location2 = new Location({
  AddressLine1: 'Test Address 2',
  City: 'Santa Fe',
  State: 'NM',
  Zip: '87505',
  Title: 'Location Title 2',
  Building: 'Location Building 2',
  Room: 'LocationRoom 2',
  CreatedOn: new Date(),
  CreatedBy: AdminUserId,
  Country: 'USA'
})

describe('MSSQL Location', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
  })

  it('creates locations', async () => {
    const res1 = await create(location1)
    const res2 = await create(location2)
    locID1 = res1.fields.ID!
    locID2 = res2.fields.ID!
    location2 = res2
    expect(res1.fields.ID).to.exist
    expect(res2.fields.ID).to.exist
  })

  it('gets a location', async () => {
    const res = await get(locID1)
    expect(res.fields.ID).to.equal(locID1)
  })

  it('gets a location', async () => {
    const res = await getPaginated(0, 150)
    expect(res.totalRecords).to.gte(1)
  })

  it('updates a location', async () => {
    location2.fields.AddressLine1 = 'Test Address updated'
    const res = await update(location2)
    expect(res.fields.ID).to.exist
    expect(res.fields.AddressLine1).to.equal('Test Address updated')
  })

  it('deletes locations', async () => {
    await remove(locID1)
    await remove(locID2)
  })
})
