import { Table } from '@lcs/mssql-utility'
import { AssignmentLearningContext, AssignmentLearningContextFields, AssignmentLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-context.js'

export default class AssignmentLearningContextsModel extends Table<AssignmentLearningContext, AssignmentLearningContext> {
  public fields: AssignmentLearningContext

  constructor (fields?: AssignmentLearningContext) {
    super(AssignmentLearningContextsTableName, [
      AssignmentLearningContextFields.AssignmentID,
      AssignmentLearningContextFields.LearningContextID,
      AssignmentLearningContextFields.OrderID,
      AssignmentLearningContextFields.CreatedBy,
      AssignmentLearningContextFields.CreatedOn
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: AssignmentLearningContext): void {
    this.fields = record
  }

  public exportJsonToDatabase (): AssignmentLearningContext {
    return this.fields
  }
}
