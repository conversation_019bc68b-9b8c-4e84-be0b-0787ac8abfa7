import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import CoursePreqModel from '../../../models/course-prerequisite.model.js'
import getForContext from './get-for-context.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

let coursePrereq: CoursePreqModel
let course: LearningContext
let prerequisiteCourse: LearningContext

// FIXME: need to update the set up for these tests
xdescribe('MSSQL Course Prerequisite Service', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    course = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test Course PreReq Child',
      Description: `Running course prerequisite on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Course
    }))
    prerequisiteCourse = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test Course PreReq Parent',
      Description: `Running course prerequisite on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Course
    }))
  })

  it('should get for context', async () => {
    try {
      const indices = await getForContext(coursePrereq.fields.CourseID!)
      expect(indices.length).be.gte(0)
    } catch (error) {
      expect(error).to.exist
    }
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { CreatedBy: AdminUserId })
  })
})
