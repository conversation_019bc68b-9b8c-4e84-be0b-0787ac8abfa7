import logger from '@lcs/logger'
import getSession from '../../../services/mssql/learning-context-sessions/get.service.js'
import getContext from '../../../services/mssql/learning-context/get.service.js'
import getLearnerProgress from '../../../services/mssql/learner-progress/get.service.js'
import saveCert from '../../../services/amqp/file/save-certificate.service.js'
import updateLearnerProgress from '../../../services/mssql/learner-progress/update.service.js'
import { sendGenericMessage } from '@tess-f/email/dist/amqp/send-generic-message.js'
import { Request, Response } from 'express'
import LearningContextSession from '../../../models/learning-context-session.model.js'
import LearningContext from '../../../models/learning-context.model.js'
import LearnerProgress from '../../../models/learner-progress.model.js'
import createCertificateService from '../../../services/file/create-certificate.service.js'
import getUserById from '../../../services/mssql/users/get-by-id.service.js'
import settings from '../../../config/settings.js'
import sendNotification from '../../../services/amqp/notification/send-notification.service.js'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import httpStatus from 'http-status'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('http-issue-certificate', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // get the session information
  const params = z.object({
    sessionId: zodGUID,
    userId: zodGUID
  }).safeParse(req.params)

  if (!params.success) {
    log('warn', 'Failed to save PDF certificate: input validation error', { success: false, req })
    res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(params.error, 'Invalid request parameter(s), '))
    return
  }

  const { sessionId, userId } = params.data

  let session: LearningContextSession

  try {
    session = await getSession(sessionId)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to issue certificate for the given user and session. An error occurred while retrieving the session.', { userId: userId, errorMessage, success: false, req })
    res.status(httpStatus.INTERNAL_SERVER_ERROR).send('Failed to retrieve session info')
    return
  }

  // get the learning context info
  let course: LearningContext

  try {
    course = await getContext(session.fields.LearningContextID!, undefined)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to issue certificate for the given user and session. An error occurred while retrieving the session.', { userId: userId, errorMessage, success: false, req })
    res.status(httpStatus.INTERNAL_SERVER_ERROR).send('Failed to get course for session')
    return
  }

  // get the users info
  const user = await getUserById(userId)

  // get the learner progress so we can update it with the cert
  let learnerProgress: LearnerProgress

  try {
    learnerProgress = await getLearnerProgress(userId, sessionId)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to issue certificate for the given user and session. An error occurred while retrieving the session.', { userId: userId, errorMessage, success: false, req })
    res.status(httpStatus.INTERNAL_SERVER_ERROR).send('Failed to retrieve the learners progress')
    return
  }

  // check if the user already has a certificate
  if (learnerProgress.fields.Certificate) {
    log('info', 'Failed to issue certificate for the given user and session. The user already has a certificate for this session.', { userId: userId, success: false, req })
    res.status(httpStatus.BAD_REQUEST).send('User already has certificate for this session')
    return
  }

  let completedDate = new Date()

  if (learnerProgress.fields.CompletedDate) {
    completedDate = learnerProgress.fields.CompletedDate
  }

  const pdf = await createCertificateService(`${user.FirstName} ${user.LastName}`, `Course: ${course.fields.Title}`, completedDate)
  // send the cert to the CDS to be saved
  try {
    const contentID = await saveCert(pdf, `${user.FirstName}_${user.LastName}_ILT_${course.fields.Title?.split(' ').join('_')}_certificate`)

    // save the id of the cert
    learnerProgress.fields.Certificate = contentID
    await updateLearnerProgress(learnerProgress)

    // notify the user of the new certificate
    const msg = `Congratulations your certificate of completion for ${course.fields.Title} is now ready to view.`
    const hdr = `${course.fields.Title} Certificate of Completion`
    const sbj = 'Certificate of Completion'

    if (user.Email) {
      await sendGenericMessage(
        settings.amqp.service_queues.email, {
          to: [user.Email],
          message: msg,
          header: hdr,
          subject: sbj
        },
        settings.amqp.command_timeout
      )
    }

    // send notification
    await sendNotification({
      Title: hdr,
      Message: msg,
      PublishDate: new Date(),
      Everyone: false,
      SystemID: 'lms',
      Priority: 1,
      UserIDs: [user.ID!],
      CreatedBy: req.session.userId
    })

    // return the cert id to the callee
    log('info', 'Successfully issued certificate for the given user and session', { userId: userId, newContentId: contentID, success: true, req })
    res.json(contentID)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to save PDF certificate', { errorMessage, success: false, req })
    res.status(httpStatus.INTERNAL_SERVER_ERROR).send('Failed to save PDF certificate')
  }
}
