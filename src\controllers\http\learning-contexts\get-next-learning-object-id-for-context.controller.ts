import { Request, Response } from 'express'
import logger from '@lcs/logger'
import get from '../../../services/mssql/learning-objects/get-next-learning-object-id-for-context.service.js'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.Get-Next-Learning-Element-ID', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // Finds the next learning object ID for a given learning context ID
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const data = await get(id, req.session.userId)
    log('info', 'Successfully found the next learning object id for the context', { contextId: id, id: data, userId: req.session.userId, success: true, req })
    res.json({
      success: true,
      objectID: data.objectID,
      contextID: data.contextID
    })
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get the ID of the learning object for the context: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === 'Context has no objects') {
      log('warn', 'Failed to ge the ID of the learning object for the context, because it does not have any learning objects', { contextId: req.params.id, success: false, req })
      res.json({
        success: false
      })
    } else {
      log('error', 'Failed to get the ID of the next learning object for context', { contextId: req.params.id, success: false, error, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
