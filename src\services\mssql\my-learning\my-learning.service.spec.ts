import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import getCompletedContexts from './get-completed-contexts.service.js'
import getCompletedObjects from './get-completed-objects.service.js'
import getInprogressContexts from './get-inprogress-context.service.js'
import getInprogressObjects from './get-inprogress-objects.service.js'
import getMetadata from './get-metadata.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
// TODO Uncomment once attempting to figure out why queries are not working
// import createLearningObject from '../../../services/mssql/learning-objects/create.service.js'
// import createLearnerProgress from '../../../services/mssql/learner-progress/create.service.js'
// import { LearningContext } from '@tess-f/sql-tables/dist/lms/learning-context.js'
// import LearnerProgressModel from '../../../models/learner-progress.model.js'
// import AssignmentsModel from '../../../models/assignment.model.js'
// import LearningObjectModel from '../../../models/learning-object.model.js'
// import deleteAssignment from '../assignments/delete.service.js'
// import deleteLearningContext from '../learning-context/delete.service.js'
// import deleteLearningObject from '../learning-objects/delete.service.js'
// import deleteLearnerProgress from '../learner-progress/delete.service.js'
// import LearningContextModel from '../../../models/learning-context.model.js'
// import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
// import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
// import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
// import { v4 as uuidv4 } from 'uuid'
// import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
// import createAssignment from '../../../services/mssql/assignments/create.service.js'
// import AssignmentUserModel from '../../../models/assignment-users.model.js'
// import AssignmentLearningObjectModel from '../../../models/assignment-learning-object.model.js'
// import { ActivityStreamTableName } from '@tess-f/sql-tables/dist/lms/activity-stream.js'

// let learningContext: LearningContext
// let learningObject1: LearningObjectModel
// let learningObject2: LearningObjectModel
// let learnerProgressComplete: LearnerProgressModel
// let learnerProgressInProgress: LearnerProgressModel
// let learnerProgressBrowsed: LearnerProgressModel
// let learnerProgressPassed: LearnerProgressModel
// let assignment: AssignmentsModel

describe('MSSQL My Learning', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    // const pool = mssql.getPool()
    // learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
    //   Title: 'Test my learning',
    //   Description: 'Running my learning',
    //   CreatedBy: AdminUserId,
    //   CreatedOn: new Date(),
    //   EnableCertificates: false,
    //   ContextTypeID: LearningContextTypes.Collection,
    //   VisibilityID: Visibilities.Browsable
    // }))
    // learningObject1 = await createLearningObject(new LearningObjectModel({
    //   Title: 'Test Object',
    //   Description: 'Test Object for My Learning service tests',
    //   CreatedBy: AdminUserId,
    //   CreatedOn: new Date(),
    //   LearningObjectTypeID: LearningObjectTypes.Text,
    //   ContentID: uuidv4()
    // }))
    // learningObject2 = await createLearningObject(new LearningObjectModel({
    //   Title: 'Test Object',
    //   Description: 'Test Object for My Learning service Tests',
    //   CreatedBy: AdminUserId,
    //   CreatedOn: new Date(),
    //   LearningObjectTypeID: LearningObjectTypes.Text,
    //   ContentID: uuidv4()
    // }))
    // learnerProgressComplete = await createLearnerProgress(new LearnerProgressModel({
    //   LessonStatusID: LessonStatuses.completed,
    //   UserID: AdminUserId,
    //   CreatedOn: new Date()
    // }))
    // learnerProgressInProgress = await createLearnerProgress(new LearnerProgressModel({
    //   LessonStatusID: LessonStatuses.incomplete,
    //   UserID: AdminUserId,
    //   CreatedOn: new Date()
    // }))
    // learnerProgressBrowsed = await createLearnerProgress(new LearnerProgressModel({
    //   LessonStatusID: LessonStatuses.browsed,
    //   UserID: AdminUserId,
    //   CreatedOn: new Date()
    // }))
    // learnerProgressPassed = await createLearnerProgress(new LearnerProgressModel({
    //   LessonStatusID: LessonStatuses.passed,
    //   UserID: AdminUserId,
    //   CreatedOn: new Date()
    // }))
    // assignment = (await createAssignment(new AssignmentsModel({
    //   Title: 'Test for my learning service',
    //   TypeID: 1,
    //   Everyone: false,
    //   CreatedBy: AdminUserId,
    //   CreatedOn: new Date()
    // }), [new AssignmentUserModel({
    //   UserID: AdminUserId
    // })], [], [new AssignmentLearningObjectModel({
    //   LearningObjectID: learningObject2.fields.ID,
    //   CreatedBy: AdminUserId,
    //   CreatedOn: new Date()
    // })], [])).assignment
  })

  it('gets a list of completed learning contexts', async () => {
    const result = await getCompletedContexts(AdminUserId)
    expect(result.length).to.be.gte(0)
  })

  it('gets a list of completed learning objects for a user', async () => {
    const result = await getCompletedObjects(AdminUserId)
    expect(result.length).to.be.gte(0)
  })

  it('gets inprogress learning contexts', async () => {
    const result = await getInprogressContexts(AdminUserId)
    expect(result.length).to.be.gte(0)
  })

  it('gets a list of inprogress learning objects for a user', async () => {
    const result = await getInprogressObjects(AdminUserId)
    expect(result.length).to.gte(0)
  })

  it('gets my learning metadata', async () => {
    const result = await getMetadata(AdminUserId)
    expect(result.InProgress).to.be.gte(0)
    expect(result.Completed).to.be.gte(0)
  })

  it('gets metadata of a user with assignments', async () => {
    const result = await getMetadata(AdminUserId)
    expect(result.Assigned).to.be.gte(0)
    expect(result.Overdue).to.be.gte(0)
  })

  after('Delete created objects in SQL', async () => {
    // await deleteLearnerProgress(learnerProgressInProgress.fields.ID!)
    // await deleteLearnerProgress(learnerProgressComplete.fields.ID!)
    // await deleteLearnerProgress(learnerProgressBrowsed.fields.ID!)
    // await deleteLearnerProgress(learnerProgressPassed.fields.ID!)
    // await deleteAssignment(assignment.fields.ID!)
    // await deleteLearningObject(learningObject1.fields.ID!)
    // await deleteLearningObject(learningObject2.fields.ID!)
    // await deleteLearningContext(learningContext.ID!)
    // await mssql.getPool().request().query(`
    //   DELETE FROM [${ActivityStreamTableName}]
    // `)
  })
})
