import getPaginated from '../../../services/mssql/learning-objects/get-paginated.service.js'
import logger from '@lcs/logger'
import getUserFavorite from '../../../services/mssql/learning-object-favorites/get.service.js'
import getUserBookmark from '../../../services/mssql/learning-object-bookmarks/get.service.js'
import { getObjectRatingsForUser as getUserRating } from '../../../services/mssql/learning-object-ratings/get.service.js'
import getRatingForObject from '../../../services/mssql/learning-object-ratings/get-average-for-object.service.js'
import getLearningObjectKeywords from '../../../services/mssql/learning-object-keywords/get.service.js'
import { Request, Response } from 'express'
import { DB_Errors } from '@lcs/mssql-utility'
import httpStatus from 'http-status'
import getObjectiveIdsForLearningObject from '../../../services/mssql/learning-object-objectives/get-multi.service.js'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import learningObjectExtrasMapper from '../../../mappers/learning-object-extras.mapper.js'

const log = logger.create('Controller-HTTP.get-paginated-learning-objects', httpLogTransformer)

/*
    Query options
        types: number[]
        rating: boolean
        offset: number
        limit: number
        search: string
        keywords: string[]
        visibilities: number[]
        contextCounts: boolean
        createdByIds: string[]
        modifiedByIds: string[]
        sortColumn: string
        sortDirection: string (ASC || DESC)
*/
export default async function (req: Request, res: Response) {
  try {
    // #region query params
    const { types, rating, offset, limit, search, keywords, visibilities, sortColumn, sortDirection, createdByIds, modifiedByIds, isReferenced } = z.object({
      types: z.array(z.nativeEnum(LearningObjectTypes)).optional(),
      rating: z.boolean().optional(),
      offset: zodOffset,
      limit: zodLimit,
      search: z.string().optional(),
      keywords: z.array(z.string()).optional(),
      visibilities: z.array(z.nativeEnum(Visibilities)).optional(),
      sortColumn: z.string().optional(),
      sortDirection: z.string().toUpperCase().or(z.literal('ASC')).or(z.literal('DESC')).optional(),
      modifiedByIds: z.array(zodGUID).optional(),
      createdByIds: z.array(zodGUID).optional(),
      isReferenced: z.boolean().optional()
    }).parse(req.body)

    // #endregion

    // #region get objects

    const results = await getPaginated(offset, limit, search, {
      typeIds: types, visibilities, keywords, createdByIds, modifiedByIds, isReferenced
    }, sortColumn && sortDirection ? { column: sortColumn, direction: sortDirection } : undefined)

    // #endregion

    // #region map objects and add extras

    await learningObjectExtrasMapper(results.objects, req.session.userId, {
      keywords: true,
      rating: true,
      bookmark: true,
      favorite: true
    })

    log('info', `Successfully retrieved ${results.objects.length} of ${results.totalRecords} learning objects`, { success: true, req })

    res.json({
      totalRecords: results.totalRecords,
      objects: results.objects.map(obj => obj.fields)
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get paginated learning objects because of input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to get paginated learning objects', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
