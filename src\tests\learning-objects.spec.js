const chai = require('chai');
const expect = chai.expect;
const uuidv4 = require('uuid/v4');
const tester = require('../api/utils/test-agent.utils');
const settings = require('../api/config/settings');

const learningObjectID = "EAF1DA0B-9B3D-4709-8830-D7219F925AD1"; // Learning Object 1
const lesson = "83C4BF97-BBE6-4C1A-83A2-EEC762461AFA"; // Course 1 / Module 1/ Topic 1 / Lesson 1
const userID = uuidv4();

let learningObject = {
    Title: uuidv4(),
    Description: uuidv4(),
    LearningObjectTypeID: 1
};

let learningObjectModified = {
    Title: uuidv4(),
    Description: uuidv4(),
    LearningObjectTypeID: 5
}

let isolated = false;

describe("E2E: Learning Objects", () => {

    before( done => {
        if(tester.agent == null) {
            isolated = true;
            tester.create()
            .then( agent => done())
            .catch( done );

        } else {
            done();
        }
    });

    it('creates a learning object', done => {
        tester.agent
        .post(settings.server.root + 'learning-object')
        .send(learningObject)
        .end((err, res) => {
            expect( res.statusCode ).to.equal( 200 );
            expect( res.body.ID ).to.exist;
            learningObject = res.body;
            learningObjectModified.ID = res.body.ID;
            done();
        });
    });

    it('updates a learning object', done => {
        tester.agent
        .put(settings.server.root + 'learning-object')
        .send(learningObjectModified)
        .end((err, res) => {
            expect( res.statusCode ).to.equal( 200 );
            done();
        });
    });

    it('deletes a learning object', done => {
        tester.agent
        .delete(settings.server.root + 'learning-object/' + learningObject.ID )
        .send( learningObject )
        .end((err, res) => {
            expect( res.statusCode ).to.equal( 204 );
            done();
        });
    });

    it('gets a learning object', done => {
        tester.agent
        .get(settings.server.root + 'learning-object/' + learningObjectID )
        .send( learningObject )
        .end((err, res) => {
            expect( res.statusCode ).to.equal( 200 );
            expect( res.body.ID ).to.equal( learningObjectID );
            done();
        });
    });

    it('gets multiple learning objects', done => {
        tester.agent
        .get(settings.server.root + 'learning-objects' )
        .end((err, res) => {
            expect( res.statusCode ).to.equal( 200 );
            expect( res.body.length ).to.be.gt(1);
            done();
        });
    });

    it('gets multiple learning objects linked to a context', done => {
        tester.agent
        .get(settings.server.root + 'learning-objects?contextID=' + lesson )
        .end((err, res) => {
            expect( res.statusCode ).to.equal( 200 );
            expect( res.body.length ).to.be.gt(0);
            done();
        });
    });

    after((done)=>{
        if(isolated) {
            tester.close();
        } 
        done();
    });

})