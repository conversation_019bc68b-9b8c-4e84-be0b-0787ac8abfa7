import mssql, { deleteRow } from '@lcs/mssql-utility'
import { LearningObjectUserBookmarksTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-bookmark.js'

export default async function (objectID: string, userID: string): Promise<number> {
  const pool = mssql.getPool()
  return await deleteRow(pool.request(), LearningObjectUserBookmarksTableName, { LearningObjectID: objectID, UserID: userID })
}
