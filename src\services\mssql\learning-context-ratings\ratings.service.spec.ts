import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import create from './create.service.js'
import getUserRatingByID, { getUserRatingForContext, getAllRatingsForContext, getAllContextRatingsForUser } from './get.service.js'
import getAverageForContext from './get-average-for-context.service.js'
import update from './update.service.js'
import remove from './delete.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import LearningContextRatingModel from '../../../models/learning-context-rating.model.js'

let learningContext: LearningContext
let learningContextRating: LearningContextRatingModel

describe('MSSQL Learning Context Ratings', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test learning context rating',
      Description: 'Running learning context rating unit test',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Course
    }))
  })

  it('Should create a new learning object rating object', async () => {
    learningContextRating = await create(new LearningContextRatingModel({
      UserID: AdminUserId,
      Rating: 1,
      LearningContextID: learningContext.ID
    }))
    expect(learningContextRating).to.not.be.eq(undefined)
  })

  it('Should get user rating by ID', async () => {
    const results = await getUserRatingByID(learningContextRating.fields.ID!)
    expect(results.fields.Rating).to.be.eq(learningContextRating.fields.Rating)
    expect(results.fields.UserID).to.be.eq(learningContextRating.fields.UserID)
  })

  it('Should get user rating for context', async () => {
    const results = await getUserRatingForContext(learningContext.ID!, AdminUserId)
    expect(results.fields.Rating).to.be.eq(learningContextRating.fields.Rating)
    expect(results.fields.UserID).to.be.eq(learningContextRating.fields.UserID)
  })

  it('Should get all ratings for context', async () => {
    const results = await getAllRatingsForContext(learningContext.ID!)
    expect(results.length).to.be.gte(0)
  })

  it('Should get all context rating for user', async () => {
    const results = await getAllContextRatingsForUser(AdminUserId)
    expect(results.length).to.be.gte(0)
  })

  it('Should get average for context', async () => {
    const results = await getAverageForContext(learningContext.ID!)
    expect(results.count).to.be.gte(0)
  })

  it('Should update the learning context object', async () => {
    learningContextRating.fields.Rating = 2
    const results = await update(learningContextRating)
    expect(results.fields.Rating).to.be.eq(2)
  })

  it('Should remove the learning context object from DB', async () => {
    const results = await remove(learningContext.ID!)
    expect(results).to.be.eq(undefined)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { ID: learningContext.ID })
  })
})
