import mssql from '@lcs/mssql-utility'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'

/**
 * @param allLabels Should the service return all labels or just the browseable labels
 * @returns {Promise<string[]>} unique list of learning context labels
 */
export default async function (allLabels = false, contextType?: number): Promise<string[]> {
  const pool = mssql.getPool()
  const request = pool.request()
  let query = `
    SELECT DISTINCT [${LearningContextFields.Label}]
    FROM [${LearningContextTableName}]
    WHERE 1=1`

  if (!allLabels) {
    query += ` AND [${LearningContextFields.VisibilityID}] = ${Visibilities.Browsable}`
  }

  if (contextType) {
    request.input('contextType', contextType)
    query += ` AND [${LearningContextFields.ContextTypeID}] = @contextType`
  }
  const results = await request.query<LearningContext>(query)

  return results.recordset.map(record => record.Label!)
}
