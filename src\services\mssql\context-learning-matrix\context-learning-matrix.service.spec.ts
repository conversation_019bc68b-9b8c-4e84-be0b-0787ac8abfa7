import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import ActivityStreamModel from '../../../models/activity-stream.model.js'
import reorder from './reorder.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'

describe('MSSQL Context Learning Matrix', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
  })

  it('reorders', async () => {
    // const response = await reorder()
    // expect(response).to.exist
  })

  it('updates', async () => {
    // const response = await update()
    // expect(response).to.exist
  })
})
