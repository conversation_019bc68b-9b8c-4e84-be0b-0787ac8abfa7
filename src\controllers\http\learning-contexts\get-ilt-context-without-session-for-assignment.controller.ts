import getContexts from '../../../services/mssql/learning-context/get-ILT-contexts-without-sessions-for-assignment.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-contexts-without-sessions-for-assignment', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const contexts = await getContexts(id)
    log('info', 'Successfully retrieved ILT contexts without sessions for assignment', { count: contexts.length, assignmentId: id, success: true, req })
    res.json(contexts.map(context => context.fields))
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get ILT contexts missing sessions for assignment: validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else {
      log('error', 'Failed to get ILT contexts missing sessions for assignment', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
