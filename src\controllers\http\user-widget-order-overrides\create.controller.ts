import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import UserWidgetOrderOverride from '../../../models/user-widget-order-override.model.js'
import createUserWidgetOrderOverridesService from '../../../services/mssql/widget-user-overrides/create-multiple.service.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.create-user-widget-order-overrides', httpLogTransformer)

export default async function createWidgetOrderOverridesController (req: Request, res: Response) {
  try {
    const overrides = z.array(z.object({
      ID: zodGUID,
      OrderID: z.number().int().positive(),
      ColumnID: z.number().int().nonnegative()
    })).parse(req.body)
      .map(widgetOverride => new UserWidgetOrderOverride({
        UserID: req.session.userId,
        WidgetID: widgetOverride.ID,
        OrderID: widgetOverride.OrderID,
        ColumnID: widgetOverride.ColumnID
      }))

    const created = await createUserWidgetOrderOverridesService(overrides)

    log('info', 'Successfully created widget order overrides', { count: created.length, success: true, req })

    res.json(created.map(override => override.fields))
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to create user widget order overrides: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to create user widget order overrides', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
