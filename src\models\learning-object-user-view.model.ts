import { Table } from '@lcs/mssql-utility'
import { LearningObjectUserView, LearningObjectUserViewFields, LearningObjectUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-view.js'

export default class LearningObjectUserViewModel extends Table<LearningObjectUserView, LearningObjectUserView> {
  public fields: LearningObjectUserView

  constructor (fields?: LearningObjectUserView) {
    super(LearningObjectUserViewsTableName, [
      LearningObjectUserViewFields.UserID,
      LearningObjectUserViewFields.LearningObjectID,
      LearningObjectUserViewFields.CreatedOn
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearningObjectUserView): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningObjectUserView {
    return this.fields
  }
}
