import mssql, { DB_Errors, getRows } from '@lcs/mssql-utility'
import { UserCompletedLearningContext, UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import UserCompletedLearningContextModel from '../../../models/user-completed-learning-context.model.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'

export async function getAllRecordsForUserAndContext (userID: string, contextID: string): Promise<UserCompletedLearningContextModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<UserCompletedLearningContext>(UserCompletedLearningContextsTableName, pool.request(), { UserID: userID, LearningContextID: contextID })
  return records.map(record => new UserCompletedLearningContextModel(record))
}

export async function getMostRecentCompletion (userID: string, contextID: string, to?: Date): Promise<UserCompletedLearningContextModel> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('contextID', contextID)
  request.input('userID', userID)

  if (to) {
    request.input('to', to)
  }

  const query = `
    SELECT TOP (1) *
    FROM [${UserCompletedLearningContextsTableName}]
    WHERE [${UserCompletedLearningContextFields.UserID}] = @userID
    AND [${UserCompletedLearningContextFields.LearningContextID}] = @contextID
    AND [${UserCompletedLearningContextFields.LessonStatusID}] >= ${LessonStatuses.fail}
    ${to ? `AND [${UserCompletedLearningContextFields.CompletedOn}] <= @to` : ''}
    ORDER BY [${UserCompletedLearningContextFields.CompletedOn}] DESC    
  `

  const results = await request.query(query)

  if (results.recordset.length <= 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }

  return new UserCompletedLearningContextModel(results.recordset[0])
}
