import logger from '@lcs/logger'
import { Request, Response } from 'express'
import searchContextUserAssignmentsService from '../../../services/mssql/assignments/search-context-user-assignments.service.js'
import getUserContextAssignmentStatusService from '../../../services/mssql/assignments/get-user-context-assignment-status.service.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodDates, zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-context-assigned-users', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { limit, offset, search, groupIDs } = z.object({
      limit: zodLimit,
      offset: zodOffset,
      search: z.string().optional(),
      groupIDs: z.array(zodGUID).optional()
    }).parse(req.body)

    const { from, to } = z.object({
      from: zodDates.optional(),
      to: zodDates.optional()
    }).parse(req.query)

    const { id } = z.object({ id: zodGUID }).parse(req.params)

    const assignedUsers = await searchContextUserAssignmentsService(id, offset, limit, search, groupIDs, from, to)

    const users = await Promise.all(assignedUsers.users.map(async user => {
      const status = await getUserContextAssignmentStatusService(id, user.AssignmentID, user.UserID, from, to)
      return {
        UserID: user.UserID,
        AssignmentTitle: user.Title,
        AssignedBy: user.CreatedBy,
        AssignmentStatus: status,
        AssignedOn: user.CreatedOn,
        DueDate: user.DueDate,
        AssignmentID: user.AssignmentID
      }
    }))

    log('info', 'Successfully fetched learning context assigned users', {
      search,
      groupIDs,
      from,
      to,
      contextID: id,
      count: users.length,
      totalRecords: assignedUsers.totalRecords,
      success: true,
      req
    })

    res.json({
      totalRecords: assignedUsers.totalRecords,
      users
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning context assigned users: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to get learning context assigned users.', { error, req, success: false, contextId: req.params.id })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
