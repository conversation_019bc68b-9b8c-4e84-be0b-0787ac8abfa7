import mssql, { deleteRow } from '@lcs/mssql-utility'
import { LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { Request, Transaction } from 'mssql'

export default async function (parentContextID: string, connectedContextID: string, resetOrderIds: boolean = true): Promise<number> {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    const rowsDeleted = await removeContextConnection(transaction, parentContextID, connectedContextID, resetOrderIds)

    await transaction.commit()

    return rowsDeleted
  } catch (error) {
    if (!rolledBack) await transaction.rollback()
    throw error
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}

async function removeContextConnection (transaction: Transaction, parentContextID: string, connectedContextID: string, resetOrderIds: boolean = true): Promise<number> {
  if (resetOrderIds) {
    await reorderContexts(transaction.request(), parentContextID, connectedContextID)
    await reorderObjects(transaction.request(), parentContextID, connectedContextID)
  }
  return await deleteRow(transaction.request(), LearningContextConnectionsTableName, { ParentContextID: parentContextID, ConnectedContextID: connectedContextID })
}

async function reorderContexts (request: Request, parentContextID: string, connectedContextID: string): Promise<void> {
  request.input('parentContextID', parentContextID)
  request.input('connectedContextID', connectedContextID)

  const query = `
    UPDATE [${LearningContextConnectionsTableName}]
    SET [${LearningContextConnectionFields.OrderID}] = [${LearningContextConnectionFields.OrderID}] - 1
    WHERE [${LearningContextConnectionFields.OrderID}] > (
        SELECT TOP(1) [${LearningContextConnectionFields.OrderID}]
            FROM [${LearningContextConnectionsTableName}]
            WHERE [${LearningContextConnectionFields.ParentContextID}] = @parentContextID
            AND [${LearningContextConnectionFields.ConnectedContextID}] = @connectedContextID
    ) AND [${LearningContextConnectionFields.ParentContextID}] = @parentContextID
  `

  await request.query(query)

  await request.query(`
      UPDATE [${LearningContextTableName}]
      SET [${LearningContextFields.OrderID}] = [${LearningContextFields.OrderID}] - 1
      WHERE [${LearningContextFields.OrderID}] > (
        SELECT TOP(1) [${LearningContextConnectionFields.OrderID}]
        FROM [${LearningContextConnectionsTableName}]
        WHERE [${LearningContextConnectionFields.ParentContextID}] = @parentContextID
        AND [${LearningContextConnectionFields.ConnectedContextID}] = @connectedContextID
      )
      AND [${LearningContextFields.ParentContextID}] = @parentContextID
  `)
}

async function reorderObjects (request: Request, parentContextID: string, connectedContextID: string): Promise<void> {
  request.input('parentContextID', parentContextID)
  request.input('connectedContextID', connectedContextID)

  const query = `
    UPDATE [${LearningObjectContextsTableName}]
    SET [${LearningObjectContextFields.OrderID}] = [${LearningObjectContextFields.OrderID}] - 1
    WHERE [${LearningObjectContextFields.OrderID}] > (
        SELECT TOP(1) [${LearningContextConnectionFields.OrderID}]
        FROM [${LearningContextConnectionsTableName}]
        WHERE [${LearningContextConnectionFields.ParentContextID}] = @parentContextID
        AND [${LearningContextConnectionFields.ConnectedContextID}] = @connectedContextID
    ) AND [${LearningObjectContextFields.LearningContextID}] = @connectedContextID
  `

  await request.query(query)
}
