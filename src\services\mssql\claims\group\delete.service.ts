import mssql, { deleteRow } from '@lcs/mssql-utility'
import { GroupClaimsTableName } from '@tess-f/sql-tables/dist/lms/group-claim.js'

/**
 * Delete claims associated with a particular group. If no claim input is
 * provided, it will delete ALL the claims associated with the given group ID.
 *
 * @param { String } groupID - remove claim associated with this group ID
 * @param { String } claim
 * @returns { Number } number of affected rows (i.e. number of claims removed)
 */
export default async function (groupID: string, claim?: string): Promise<number> {
  const pool = mssql.getPool()
  if (claim) {
    return await deleteRow(pool.request(), GroupClaimsTableName, { GroupID: groupID, Claim: claim })
  } else {
    return await deleteRow(pool.request(), GroupClaimsTableName, { GroupID: groupID })
  }
}
