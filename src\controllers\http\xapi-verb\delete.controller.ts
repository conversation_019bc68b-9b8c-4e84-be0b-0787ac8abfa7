import { Request, Response } from 'express'
import logger from '@lcs/logger'
import del from '../../../services/mssql/xapi-verb/delete.service.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.delete-xapi-verb', httpLogTransformer)

export default async function (req: Request, res: Response): Promise<void> {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    await del(id)
    log('info', 'Successfully deleted verb', { success: true, id, req })
    res.sendStatus(httpStatus.NO_CONTENT)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete verb: input validation error', { error, success: false, id: req.params.id, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    }
    else{
    log('error', 'Failed to delete verb', { error, success: false, id: req.params.id, req })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
}
