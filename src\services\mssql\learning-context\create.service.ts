import mssql, { addRow } from '@lcs/mssql-utility'
import createActivity from '../activity-stream/create.service.js'
import ActivityStream from '../../../models/activity-stream.model.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { ConnectionPool, Request } from 'mssql'
import LearningContextKeywordModel from '../../../models/learning-context-keyword.model.js'
import CoursePrerequisiteModel from '../../../models/course-prerequisite.model.js'
import logger from '@lcs/logger'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { CoursePrerequisite } from '@tess-f/sql-tables/dist/lms/course-prerequisite.js'
import { LearningContextKeyword } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'
import { LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import LearningContextObjectiveModel from '../../../models/learning-context-objective.model.js'
import createLearningContextObjective from '../learning-context-objectives/create.service.js'
import { CoursePrerequisiteJson } from '@tess-f/lms/dist/common/course-prerequisite.js'
import { CoursePrerequisiteAlternative } from '@tess-f/sql-tables/dist/lms/course-prerequisite-alternative.js'
import { CoursePrerequisiteAlternativeModel } from '../../../models/course-prerequisite-alternative.model.js'

const log = logger.create('Service-MSSQL.create-learning-context')

export default async function (learningContext: LearningContextModel): Promise<LearningContextModel> {
  try {
    const pool = mssql.getPool()

    // if this learning context is being created with a parent and an order ID,
    // we need to update the order ID's of the other content before we add this context
    if (learningContext.fields.ParentContextID && learningContext.fields.OrderID !== null && learningContext.fields.OrderID !== undefined && !isNaN(learningContext.fields.OrderID)) {
      await updateOrderIDs(pool.request(), learningContext.fields.ParentContextID, learningContext.fields.OrderID)
    }

    // if this learning context is being created with a parent and an order ID,
    // we need to update the order ID's of the other content before we add this context
    if (learningContext.fields.ParentContextID && learningContext.fields.OrderID !== null && learningContext.fields.OrderID !== undefined && !isNaN(learningContext.fields.OrderID)) {
      await updateOrderIDs(pool.request(), learningContext.fields.ParentContextID, learningContext.fields.OrderID)
    }

    // Create the context
    const record = await addRow<LearningContext>(pool.request(), learningContext)
    log('info', 'Successfully added context to the database', { contextID: record.ID, success: true })
    const created = new LearningContextModel(undefined, record)

    // Create the prereqs
    created.fields.Prerequisites = await createPrerequisites(pool, created.fields.ID!, learningContext.fields.Prerequisites)
    log('info', 'Successfully added context Prerequisites to the database', { count: created.fields.Prerequisites ? created.fields.Prerequisites.length : 0, success: true, contextID: record.ID })

    // Create the keywords
    created.fields.Keywords = await createKeywords(pool, created.fields.ID!, learningContext.fields.Keywords)
    log('info', 'Successfully added context keywords to the database', { count: created.fields.Keywords ? created.fields.Keywords.length : 0, contextID: record.ID, success: true })

    // create objective connections
    created.fields.ObjectiveIds = await createObjectives(pool, created.fields, learningContext.fields.ObjectiveIds)
    log('info', 'Successfully added objectives to context', { count: created.fields.ObjectiveIds ? created.fields.ObjectiveIds.length : 0, contextID: record.ID, success: true })

    // create an activity stream record
    const activity = new ActivityStream({
      UserID: created.fields.CreatedBy,
      LinkID: created.fields.ID,
      LinkText: created.fields.Label + ': ' + created.fields.Title,
      ActivityID: Activities.CreatedContext,
      CreatedOn: new Date()
    })
    await createActivity(activity)
    return created
  } catch (error) {
    const errorMsg = getErrorMessage(error)
    // Unexpected error
    log('error', 'Unexpected error', { errorMessage: errorMsg, success: false })
    throw error
  }
}

// Creates course prereqs attached to a given context
async function createPrerequisites (pool: ConnectionPool, contextID: string, prereqs?: CoursePrerequisiteJson[]): Promise<CoursePrerequisiteJson[] | undefined> {
  if (!prereqs) return undefined
  const prerequisites: CoursePrerequisiteJson[] = []

  for (const prerequisite of prereqs) {
    const prereq = new CoursePrerequisiteModel(prerequisite)
    prereq.fields.CourseID = contextID
    prereq.fields.Alternatives = []
    const record = await addRow<CoursePrerequisite>(pool.request(), prereq)
    prereq.importFromDatabase(record)

    // if the prerequisite is enforced and has alternatives, add those as well
    if (prereq.fields.Enforce && prerequisite.Alternatives.length > 0) {
      for (const alternative of prerequisite.Alternatives) {
        const alt = new CoursePrerequisiteAlternativeModel(alternative)
        alt.fields.ForPrerequisiteId = prereq.fields.ID
        const newAlt = await addRow<CoursePrerequisiteAlternative>(pool.request(), alt)
        prereq.fields.Alternatives.push(newAlt)
      }
    }
    prerequisites.push(prereq.fields)
  }

  return prerequisites
}

// Create course keywords
async function createKeywords (pool: ConnectionPool, contextID: string, keywords?: string[]): Promise<string[] | undefined> {
  if (!keywords) return undefined
  const output: string[] = []

  for (const word of keywords) {
    const keyword = new LearningContextKeywordModel({
      Keyword: word,
      LearningContextID: contextID
    })
    const record = await addRow<LearningContextKeyword>(pool.request(), keyword)
    output.push(record.Keyword!)
  }

  return output
}

// Create objectives
async function createObjectives(pool: ConnectionPool, context: LearningContext, objectiveIds?: string[]): Promise<string[] | undefined> {
  if (!objectiveIds) return undefined
  const output: string[] = []

  for (let i = 0; i < objectiveIds.length; i++) {
    const objective = new LearningContextObjectiveModel({
      LearningContextId: context.ID ?? '',
      ObjectiveId: objectiveIds[i],
      OrderId: i + 1
    })

    const created = await createLearningContextObjective(objective, context.Title ?? 'Course', context.Label ?? 'Course', context.CourseTypeID ?? -1, pool.request())
    output.push(created.fields.ObjectiveId!)
  }

  return output
}

// update the order ID's of this contexts parent, it's being created as a child in a given position
async function updateOrderIDs (request: Request, parentID: string, orderID: number) {
  request.input('orderID', orderID)
  request.input('parentID', parentID)

  await request.query(`
    UPDATE [${LearningContextTableName}]
    SET [${LearningContextFields.OrderID}] = [${LearningContextFields.OrderID}] + 1
    WHERE [${LearningContextFields.OrderID}] >= @orderID
    AND [${LearningContextFields.ParentContextID}] = @parentID
  `)

  await request.query(`
    UPDATE [${LearningContextConnectionsTableName}]
    SET [${LearningContextConnectionFields.OrderID}] = [${LearningContextConnectionFields.OrderID}] + 1
    WHERE [${LearningContextConnectionFields.OrderID}] >= @orderID
    AND [${LearningContextConnectionFields.ParentContextID}] = @parentID
  `)

  await request.query(`
    UPDATE [${LearningObjectContextsTableName}]
    SET [${LearningObjectContextFields.OrderID}] = [${LearningObjectContextFields.OrderID}] + 1
    WHERE [${LearningObjectContextFields.OrderID}] >= @orderID
    AND [${LearningObjectContextFields.LearningContextID}] = @parentID
  `)
}
