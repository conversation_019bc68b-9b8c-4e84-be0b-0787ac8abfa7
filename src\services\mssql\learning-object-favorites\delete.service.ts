import mssql, { deleteRow } from '@lcs/mssql-utility'
import { LearningObjectUserFavoritesTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-favorite.js'

export default async function (objectID: string, userID: string): Promise<number> {
  const pool = mssql.getPool()
  return await deleteRow(pool.request(), LearningObjectUserFavoritesTableName, { UserID: userID, LearningObjectID: objectID })
}
