import { RequestHand<PERSON>, Router } from 'express'
import getTopLevelController from './get-top-level.controller.js'
import getContextChildrenController from './get-children.controller.js'
import searchCatalog from './search.controller.js'
import getAvailableParentNodesController from './get-available-parent-nodes.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.get('/catalog-top', checkClaims([Claims.VIEW_CATALOG, Claims.CREATE_CATALOG_ITEMS, Claims.MODIFY_CATALOG_ITEMS, Claims.PUBLISH_CATALOG_ITEMS]), getTopLevelController as RequestHandler)
router.get('/context-children/:id', checkClaims([Claims.VIEW_CATALOG, Claims.CREATE_CATALOG_ITEMS, Claims.MODIFY_CATALOG_ITEMS]), getContextChildrenController as RequestHandler)
router.post('/search-catalog', checkClaims([Claims.VIEW_CATALOG, Claims.CREATE_CATALOG_ITEMS, Claims.MODIFY_CATALOG_ITEMS]), searchCatalog as RequestHandler)
router.get('/available-parent-nodes/:id', checkClaims([Claims.CREATE_CATALOG_ITEMS, Claims.MODIFY_CATALOG_ITEMS]), getAvailableParentNodesController as RequestHandler)

export default router
