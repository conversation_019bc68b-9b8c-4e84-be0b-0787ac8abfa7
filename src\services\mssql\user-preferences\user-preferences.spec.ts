import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import get from './get.service.js'
import create from './create.service.js'
import update from './update.service.js'
import del from './delete.service.js'
import UserPreference from '../../../models/user-preference.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'

describe('MSSQL User Preferences', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
  })

  it('creates a user preference', async () => {
    await create(new UserPreference({
      UserID: AdminUserId,
      GettingStarted: true
    }))
  })

  it('updates a user preference', async () => {
    const result = await update(new UserPreference({
      UserID: AdminUserId,
      GettingStarted: false
    }))

    expect(result.fields.GettingStarted).to.equal(false)
  })

  it('gets a user preference', async () => {
    const result = await get(AdminUserId)
    expect(result.fields.UserID!.toLowerCase()).to.equal(AdminUserId.toLowerCase())
  })

  it('deletes a user preference', async () => {
    await del(AdminUserId)
  })
})
