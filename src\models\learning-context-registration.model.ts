import { Table } from '@lcs/mssql-utility'
import { LearningContextRegistration, LearningContextRegistrationFields, LearningContextRegistrationsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-registration.js'

export default class LearningContextRegistrationModel extends Table<LearningContextRegistration, LearningContextRegistration> {
  public fields: LearningContextRegistration

  constructor (fields?: LearningContextRegistration) {
    super(LearningContextRegistrationsTableName, [
      LearningContextRegistrationFields.LearningContextId,
      LearningContextRegistrationFields.UserId
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearningContextRegistration): void {
    this.fields = record
  }

  exportJsonToDatabase (): LearningContextRegistration {
    return this.fields
  }
}
