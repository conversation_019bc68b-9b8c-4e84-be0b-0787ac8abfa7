import mssql, { addRow } from '@lcs/mssql-utility'
import { UserHiddenWidget } from '@tess-f/sql-tables/dist/lms/user-hidden-widget.js'
import UserHiddenWidgetModel from '../../../models/user-hidden-widget.model.js'

export default async function createUserHiddenWidgetService (hiddenWidget: UserHiddenWidgetModel): Promise<UserHiddenWidgetModel> {
  const pool = mssql.getPool()
  const record = await addRow<UserHiddenWidget>(pool.request(), hiddenWidget)
  return new UserHiddenWidgetModel(record)
}
