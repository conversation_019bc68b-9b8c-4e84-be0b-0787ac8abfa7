import logger from '@lcs/logger'
import Statement from '../../../models/amqp/lrs/statement.model.js'
import { getByID as getLearnerProgress } from '../../../services/mssql/learner-progress/get.service.js'
import updateLearnerProgress from '../../../services/mssql/learner-progress/update.service.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import { CMI5Verbs } from '@tess-f/sql-tables/dist/lrs/verb.js'
import getLearningObject from '../../../services/mssql/learning-objects/get.service.js'
import getStatementsFromLRS from '../../../services/amqp/lrs/get-statements.service.js'
import sendAuSatisfiedStatement from '../../../services/amqp/lrs/send-au-satisfied-statement.service.js'
import gradeContextsService from '../../../services/background/grade-contexts.service.js'

const log = logger.create('Controller-AMQP.process-cmi5-statement')

export default async function (statement: Statement) {
  if (
    statement.verb.id === CMI5Verbs.Completed ||
    statement.verb.id === CMI5Verbs.Passed ||
    statement.verb.id === CMI5Verbs.Failed
  ) {
    try {
      // get the learner progress record that we will update
      const learnerProgress = await getLearnerProgress(statement.context!.extensions['https://w3id.org/xapi/cmi5/context/extensions/sessionid'])
      learnerProgress.fields.LessonStatusID = getStatusId(statement.verb.id)
      learnerProgress.fields.RawScore = getScore(statement)

      // set the completed date if the au is satisfied (move on criteria has been met)
      const learningObject = await getLearningObject(learnerProgress.fields.LearningObjectID!)

      if (await isAuSatisfied(learningObject.fields.MoveOn ?? 'NotApplicable', statement)) {
        learnerProgress.fields.CompletedDate = new Date()
        await sendAuSatisfiedStatement(learningObject.fields.ID!, statement.actor, statement.context!.registration!, learningObject.fields.IRI!, learnerProgress.fields.ID!)
      }

      // save the updated status
      await updateLearnerProgress(learnerProgress)
      // grade the contexts around this record
      await gradeContextsService(learnerProgress, true)
    } catch (error) {
      log('error', 'Failed to update learner progress for xAPI statement', { errorMessage: getErrorMessage(error), success: false })
    }
  }
}

function getScore (statement: Statement): number | undefined {
  if (statement.result?.score) {
    if (statement.result.score.raw !== null && statement.result.score.raw !== undefined) {
      return statement.result.score.raw
    } else if (statement.result.score.scaled !== null && statement.result.score.scaled !== undefined) {
      return statement.result.score.scaled * 100
    }
  }
  return undefined
}

function getStatusId (verbId: string): number {
  switch (verbId) {
    case CMI5Verbs.Completed:
      return LessonStatuses.completed
    case CMI5Verbs.Passed:
      return LessonStatuses.passed
    default:
      return LessonStatuses.fail
  }
}

async function isAuSatisfied (moveOn: string, statement: Statement): Promise<boolean> {
  if (
    (moveOn.toLowerCase() === 'passed' && statement.verb.id === CMI5Verbs.Passed) ||
    (moveOn.toLowerCase() === 'completed' && statement.verb.id === CMI5Verbs.Completed) ||
    (moveOn.toLowerCase() === 'CompletedOrPassed'.toLowerCase() && (statement.verb.id === CMI5Verbs.Passed || statement.verb.id === CMI5Verbs.Completed)) ||
    moveOn.toLowerCase() === 'NotApplicable'.toLowerCase()
  ) {
    return true
  } else if (moveOn.toLowerCase() === 'CompletedAndPassed'.toLowerCase() && (statement.verb.id === CMI5Verbs.Passed || statement.verb.id === CMI5Verbs.Completed)) {
    // we need to check that the user has gotten both a completed and passed statement for this lp
    let statements = []
    if (statement.verb.id === CMI5Verbs.Passed) {
      statements = (await getStatementsFromLRS({ format: 'ids', registration: statement.context!.registration, verb: CMI5Verbs.Completed, agent: statement.actor, activity: statement.object.id })).statements
    } else {
      statements = (await getStatementsFromLRS({ format: 'ids', registration: statement.context!.registration, verb: CMI5Verbs.Passed, agent: statement.actor, activity: statement.object.id })).statements
    }

    if (statements.length >= 1) {
      return true
    }
  }
  return false
}
