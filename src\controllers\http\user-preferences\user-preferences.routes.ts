import { Request<PERSON><PERSON><PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getController from './get.controller.js'
import updateController from './update.controller.js'

const router = Router()

// TODO: User Preferences needs to be moved to the system config services user preferences

router.get('/user-preference/:userID', getController as RequestHandler)
router.put('/user-preference/:userID', updateController as RequestHandler)
router.delete('/user-preference/:userID', deleteController as RequestHandler)
router.post('/user-preference', createController as RequestHandler)

export default router
