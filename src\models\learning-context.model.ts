import { Table } from '@lcs/mssql-utility'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextJson } from '@tess-f/lms/dist/common/learning-context.js'
import z from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { GradeTypes } from '@tess-f/sql-tables/dist/lms/grade-type.js'
import { LearningContextLayouts } from '@tess-f/sql-tables/dist/lms/learning-context-layout.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { createPrerequisiteSchema } from './course-prerequisite.model.js'

const requiredFields = z.object({
  [LearningContextFields.Title]: z.string().max(500),
  [LearningContextFields.ContextTypeID]: z.nativeEnum(LearningContextTypes)
})

const optionalFields = z.object({
  [LearningContextFields.Description]: z.string().optional(),
  [LearningContextFields.Credits]: z.string().or(z.number()).transform(val => isNaN(Number(val)) ? 0 : Number(val)).nullish(),
  [LearningContextFields.Image]: zodGUID.nullable().optional(),
  [LearningContextFields.CourseTypeID]: z.nativeEnum(CourseTypes).nullable().optional(),
  [LearningContextFields.OrderID]: z.number().nullable().optional(),
  [LearningContextFields.ParentContextID]: zodGUID.nullable().optional(),
  [LearningContextFields.SkillLevelID]: zodGUID.nullable().optional(),
  [LearningContextFields.MinScore]: z.number().nullable().optional(),
  [LearningContextFields.Label]: z.string().nullable().optional(),
  [LearningContextFields.CourseID]: z.string().nullable().optional(),
  [LearningContextFields.GradeTypeID]: z.nativeEnum(GradeTypes).nullable().optional(),
  [LearningContextFields.MaxScore]: z.number().nullable().optional(),
  [LearningContextFields.EnableCertificates]: z.boolean().optional(),
  [LearningContextFields.LayoutTypeID]: z.nativeEnum(LearningContextLayouts).nullable().optional(),
  [LearningContextFields.VisibilityID]: z.nativeEnum(Visibilities).nullable().optional(),
  [LearningContextFields.SurveyURL]: z.string().url().nullable().optional(),
  [LearningContextFields.ExamID]: zodGUID.nullable().optional(),
  [LearningContextFields.RequiredContentCount]: z.number().nullable().optional(),
  [LearningContextFields.SpecialInstructions]: z.string().nullable().optional(),
  [LearningContextFields.IRI]: z.string().nullable().optional(),
  [LearningContextFields.EnforceContentSequencing]: z.boolean().optional()
})

const extraFields = z.object({
  Keywords: z.array(z.string()).optional(),
  ObjectiveIds: z.array(zodGUID).optional(),
  Prerequisites: z.array(createPrerequisiteSchema).optional()
})

export const createLearningContextSchema = requiredFields.merge(optionalFields).merge(extraFields)
export const updateLearningContextSchema = createLearningContextSchema.partial()

export default class LearningContextModel extends Table<LearningContextJson, LearningContext> {
  public fields: LearningContextJson

  constructor (fields?: LearningContextJson, record?: LearningContext) {
    super(LearningContextTableName, [
      LearningContextFields.Title,
      LearningContextFields.ContextTypeID,
      LearningContextFields.CreatedBy,
      LearningContextFields.CreatedOn
    ])
    if (fields) this.fields = fields
    else this.fields = {}

    if (record) this.importFromDatabase(record)
  }

  public importFromDatabase (record: LearningContext): void {
    this.fields = {
      ID: record.ID,
      Title: record.Title,
      Description: record.Description,
      Credits: record.Credits,
      Image: record.Image,
      CourseTypeID: record.CourseTypeID,
      OrderID: record.OrderID,
      ParentContextID: record.ParentContextID,
      SkillLevelID: record.SkillLevelID,
      ContextTypeID: record.ContextTypeID,
      CreatedBy: record.CreatedBy,
      CreatedOn: record.CreatedOn,
      ModifiedBy: record.ModifiedBy,
      ModifiedOn: record.ModifiedOn,
      MinScore: record.MinScore,
      Label: record.Label,
      CourseID: record.CourseID,
      GradeTypeID: record.GradeTypeID,
      MaxScore: record.MaxScore,
      EnableCertificates: record.EnableCertificates,
      LayoutTypeID: record.LayoutTypeID,
      VisibilityID: record.VisibilityID,
      SurveyURL: record.SurveyURL,
      ExamID: record.ExamID,
      RequiredContentCount: record.RequiredContentCount,
      SpecialInstructions: record.SpecialInstructions, 
      IRI: record.IRI,
      EnforceContentSequencing: record.EnforceContentSequencing
    }
  }

  public exportJsonToDatabase (): LearningContext {
    return {
      ID: this.fields.ID,
      Title: this.fields.Title,
      Description: this.fields.Description,
      Credits: this.fields.Credits,
      Image: this.fields.Image,
      CourseTypeID: this.fields.CourseTypeID,
      OrderID: this.fields.OrderID,
      ParentContextID: this.fields.ParentContextID,
      SkillLevelID: this.fields.SkillLevelID,
      ContextTypeID: this.fields.ContextTypeID,
      CreatedBy: this.fields.CreatedBy,
      CreatedOn: this.fields.CreatedOn,
      ModifiedBy: this.fields.ModifiedBy,
      ModifiedOn: this.fields.ModifiedOn,
      MinScore: this.fields.MinScore,
      Label: this.fields.Label,
      CourseID: this.fields.CourseID,
      GradeTypeID: this.fields.GradeTypeID,
      MaxScore: this.fields.MaxScore,
      EnableCertificates: this.fields.EnableCertificates,
      LayoutTypeID: this.fields.LayoutTypeID,
      VisibilityID: this.fields.VisibilityID,
      SurveyURL: this.fields.SurveyURL,
      ExamID: this.fields.ExamID,
      RequiredContentCount: this.fields.RequiredContentCount,
      SpecialInstructions: this.fields.SpecialInstructions,
      EnforceContentSequencing: this.fields.EnforceContentSequencing
    }
  }
}
