import { Request, Response } from 'express'
import logger from '@lcs/logger'
import update from '../../../services/mssql/xapi-verb/update.service.js'
import httpStatus from 'http-status'
import XAPIVerbModel, { xapiVerbSchema } from '../../../models/xapi-verb.model.js'
import { DB_Errors } from '@lcs/mssql-utility'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.update-xapi-verb', httpLogTransformer)

export default async function (req: Request, res: Response): Promise<void> {
  try {
    const verb = new XAPIVerbModel(xapiVerbSchema.parse(req.body))
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    verb.fields.Id = id
    const updated = await update(verb)
    log('info', 'Successfully updated xapi verb', { verbID: updated.fields.Id, success: true, req })
    res.json(updated.fields)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update xapi verb: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to update xapi verb because it was not found in the database.', { id: req.params.id, success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      log('error', 'Failed to update xapi verb', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
