import getMultipleService from '../../../services/mssql/skill-levels/get-multiple.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer } from '@tess-f/backend-utils'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.get-multi-skill-level', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, go to Course Management and click on a single course. Note the time. Check the LMS API log for the 'http-get-multi-skill-level' label."

  try {
    const skillLevels = (await getMultipleService()).map(skillLevel => skillLevel.fields)

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, go to Course Management and click on a single course. Note the time. Check the LMS API log for the 'http-get-multi-skill-level' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved skill levels.', { success: true, count: skillLevels.length, req })

    res.json(skillLevels)
  } catch (error) {
    // STIG V-69425 data access (failure)
    log('error', 'Failed to get multi skill levels.', { error, success: false, req })

    res.sendStatus(INTERNAL_SERVER_ERROR)
  }
}
