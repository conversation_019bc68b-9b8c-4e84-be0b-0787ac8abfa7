import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'

describe('HTTP Controller: get admin overview dashboard', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => sinon.restore())

  it('can retrieve data even when the date query params are invalid', async () => {
    const controller = await esmock('./get-admin-overview.controller.js', {
      '../../../services/mssql/users/get-count-of-all-users.service.js': { default: sinon.stub().resolves(10) },
      '../../../services/mssql/learning-objects/get-top-rated.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/learning-objects/get-top-viewed.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/learning-objects/get-uploads-by-type.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/assignments/get-count-by-date-range.service.js': { default: sinon.stub().resolves(0) },
      '../../../services/mssql/learning-object-views/get-views-by-type.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/keywords/get-view-count.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/learning-objects/get-multiple.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/learning-context-views/get-views-by-type.service.js': { default: sinon.stub().resolves([]) }
    })
    const mocks = httpMocks.createMocks({ query: { from: 'yesterday', to: 'tomorrow' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
  })

  it('can retrieve data with the given date query params', async () => {
    const controller = await esmock('./get-admin-overview.controller.js', {
      '../../../services/mssql/users/get-count-of-all-users.service.js': { default: sinon.stub().resolves(10) },
      '../../../services/mssql/learning-objects/get-top-rated.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/learning-objects/get-top-viewed.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/learning-objects/get-uploads-by-type.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/assignments/get-count-by-date-range.service.js': { default: sinon.stub().resolves(0) },
      '../../../services/mssql/learning-object-views/get-views-by-type.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/keywords/get-view-count.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/learning-objects/get-multiple.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/learning-context-views/get-views-by-type.service.js': { default: sinon.stub().resolves([]) }
    })
    const mocks = httpMocks.createMocks({ query: { from: new Date().toISOString(), to: new Date().toISOString() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
  })
})
