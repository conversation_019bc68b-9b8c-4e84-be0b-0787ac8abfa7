import mssql, { getRows } from '@lcs/mssql-utility'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'

export default async function getLearningObjectByIRI (iri: string): Promise<LearningObjectModel> {
  const request = mssql.getPool().request()
  const rows = await getRows<LearningObject>(LearningObjectsTableName, request, { IRI: iri })
  return new LearningObjectModel(undefined, rows[0])
}
