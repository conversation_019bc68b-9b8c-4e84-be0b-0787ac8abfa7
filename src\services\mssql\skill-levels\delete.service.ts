import mssql, { deleteRow } from '@lcs/mssql-utility'
import { SkillLevelsTableName } from '@tess-f/sql-tables/dist/lms/skill-level.js'

export default async function (skillLevelID: string): Promise<number> {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    // delete the skill level
    const numRows = await deleteRow(transaction.request(), SkillLevelsTableName, { ID: skillLevelID })

    await transaction.commit()
    return numRows
  } catch (error) {
    if (!rolledBack) transaction.rollback()
    throw error
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}
