import mssql from '@lcs/mssql-utility'
import { UserGroup, UserGroupFields, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'

export default async function getGroupIdsForUser (userId: string): Promise<string[]> {
  const request = mssql.getPool().request()
  request.input('userId', userId)
  const results = await request.query<UserGroup>(`
    SELECT *
    FROM [${UserGroupTableName}]
    WHERE [${UserGroupFields.UserID}] = @userId
  `)
  return results.recordset.map(record => record.GroupID!)
}
