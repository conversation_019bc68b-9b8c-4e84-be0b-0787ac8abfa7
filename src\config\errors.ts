export default {
  MISSING_ARGS: 'Missing Argument(s)',
  INVALID_ARGS: 'Invalid Argument(s)',
  UNKNOWN_USER: 'Unknown user',
  INVALID_LOGIN: 'Incorrect username or password',
  MISSING_TOKEN: 'Missing token',
  INVALID_TOKEN: 'Invalid token',
  USERNAME_EXISTS: 'Username already exists',
  UNKNOWN_ERROR: 'Unknown error',
  UNKNOWN_COMMAND: 'Unknown command (100)',
  NOT_UNIQUE_RECORD: 'More than one course found with that GUID. There should only be one (1001)',
  REQUIRED_FIELDS_MISSING: 'Service does not have required fields to perform operation (2000)',
  PROMISE_TIMEOUT: 'Promise timed out',
  AMQP_COMMAND_TIMEOUT: 'rpc timeout',
  SQL_FOREIGN_KEY: 'The INSERT statement conflicted with the FOREIGN KEY constraint',
  INVALID_CLAIM: 'Invalid claim',
  CONNECTION_NOT_ENCRYPTED: 'connection not encrypted',
  MISSING_CONNECTION_CONFIG: 'missing connection config'
}
