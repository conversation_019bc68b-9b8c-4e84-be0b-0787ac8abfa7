import mssql, { getRows } from '@lcs/mssql-utility'
import { UserWidgetOrderOverride, UserWidgetOrderOverridesTableName } from '@tess-f/sql-tables/dist/lms/user-widget-order-override.js'
import UserWidgetOrderOverrideModel from '../../../models/user-widget-order-override.model.js'

export default async function getUserWidgetOrderOverrides (userID: string): Promise<UserWidgetOrderOverrideModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<UserWidgetOrderOverride>(UserWidgetOrderOverridesTableName, pool.request(), { UserID: userID })
  return records.map(record => new UserWidgetOrderOverrideModel(record))
}
