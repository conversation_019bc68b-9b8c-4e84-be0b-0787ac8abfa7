import { Table } from '@lcs/mssql-utility'
import { LearningContextSessionInstructor, LearningContextSessionInstructorFields, LearningContextSessionInstructorsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session-instructor.js'

export default class LearningContextSessionInstructorModel extends Table<LearningContextSessionInstructor, LearningContextSessionInstructor> {
  public fields: LearningContextSessionInstructor

  constructor (fields?: LearningContextSessionInstructor) {
    super(LearningContextSessionInstructorsTableName, [
      LearningContextSessionInstructorFields.SessionID,
      LearningContextSessionInstructorFields.UserID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearningContextSessionInstructor): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningContextSessionInstructor {
    return this.fields
  }
}
