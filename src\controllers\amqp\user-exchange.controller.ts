import rabbitMQ from '@lcs/rabbitmq'
import logger from '@lcs/logger'

// import controllers
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Controller-AMQP.user-exchange')

export default class UserExchange {
  private consumer!: Function

  constructor (exchangeName: string, subscriptionQueue: string, routes: string[] | '' | undefined) {
    this.connect(exchangeName, subscriptionQueue, routes)
  }

  private async connect (exchangeName: string, subscriptionQueue: string, routes: string[] | '' | undefined) {
    // Setup user exchange consumer on the subscription queue
    this.consumer = await rabbitMQ.createExchangeConsumer(
      exchangeName,
      subscriptionQueue,
      routes,
      (message, route, resolve, reject) => { this.onMessage(message, route, resolve, reject) },
      (error) => { this.onReceivedError(error) }
    )

    log('info', 'Consumer is active')
  }

  private async onMessage (message: any, route: string, resolve: Function, reject: Function) {
    // Handle the message and resolve if successful
    try {
      await this.routeMessage(message, route)
      resolve()
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      // Send back the message
      reject()
      // Log the error
      log('error', 'Unable to handle event', { errorMessage, success: false })
    }
  }

  private async routeMessage (message: any, route: string) {
    // route to the services
    switch (route) {
      case 'created':
        break
      case 'modified':
        break
      case 'user_added_to_group':
        break
      case 'user_removed_from_group':
        break
      case 'deleted':
        break
      case 'group_created':
        // for now we are ignoring this route
        break
      case 'group_deleted':
        // for now we are ignoring this route
        break
      default:
        log('warn', 'Received an unknown routing key', { route, success: false})
        break
    }
  }

  async close () {
    if (!this.consumer) { throw new Error('Cannot close consumer because it does not exist') }
    await this.consumer()
  }

  private async onReceivedError (error: unknown) {
    const errorMessage = getErrorMessage(error)
    log('error', 'Incoming message error', { errorMessage, success: false })
  }
}
