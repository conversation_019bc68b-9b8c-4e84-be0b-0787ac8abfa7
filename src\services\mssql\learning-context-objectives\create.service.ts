import logger from '@lcs/logger'
import mssql, { addRow } from '@lcs/mssql-utility'
import { LearningContextObjective } from '@tess-f/sql-tables/dist/lms/learning-context-objective.js'
import LearningContextObjectiveModel from '../../../models/learning-context-objective.model.js'
import { createObjectiveConnection } from '@tess-f/objectives/dist/amqp/create-connection.js'
import settings from '../../../config/settings.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import { type Request } from 'mssql'

const log = logger.create('Service-MSSQL.create-learning-context-objective')

export default async function createLearningContextObjective (contextObjective: LearningContextObjectiveModel, contextTitle: string, contextLabel: string, courseType: number, request?: Request): Promise<LearningContextObjectiveModel> {
  if (request === undefined) {
    request = mssql.getPool().request()
  }
  const record = await addRow<LearningContextObjective>(request, contextObjective)
  contextObjective.importFromDatabase(record)

  try {
    await createObjectiveConnection(
      settings.amqp.service_queues.objectiveConnect,
      {
        ObjectiveId: contextObjective.fields.ObjectiveId!,
        ReferenceId: contextObjective.fields.LearningContextId!,
        ReferenceLink: courseType === CourseTypes.InstructorLed ? `/browse/course/${contextObjective.fields.LearningContextId}` : `/browse/view-context/${contextObjective.fields.LearningContextId}`,
        ReferenceText: contextTitle,
        ReferenceType: contextLabel,
        SystemId: 'lms'
      },
      settings.amqp.rpc_timeout
    )
  } catch (error) {
    log('error', 'Failed to create connection between learning context and objective.', { errorMessage: getErrorMessage(error), success: false })
  }

  return contextObjective
}
