kind: Deployment
apiVersion: apps/v1
metadata:
  name: {{ .Values.name }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.name }}
  template:
    metadata:
      labels:
        app: {{ .Values.name }}
    spec:
      volumes:
        - name: config
          configMap:
            name: {{ .Values.configMapName }}
      imagePullSecrets:
        - name: {{ .Values.imagePullSecretName }}
      containers:
        - name: {{ .Values.name }}
          image: {{ .Values.imageRepository }}
          imagePullPolicy: {{ .Values.imagePullPolicy }}
          volumeMounts:
            - name: config
              mountPath: {{ .Values.configMountPath }}
              subPath: {{ .Values.configMapSubPath }}
          ports:
            - containerPort: {{ .Values.containerPort }}
              protocol: TCP
          env:
            - name: CONFIG_PATHS
              value: {{ .Values.configMapSubPath }}
            - name: SERVER_BASE_PATH
              value: '{{ .Values.serverBasePath }}'
            - name: SERVER_PORT
              value: '{{ .Values.containerPort }}'
            - name: ROOT_API_PATH
              value: '{{ .Values.rootApiPath }}'
            {{ if .Values.mssqlEncrypt }}
            - name: MSSQL_ENCRYPT
              value: 'true'
            {{ end }}
                