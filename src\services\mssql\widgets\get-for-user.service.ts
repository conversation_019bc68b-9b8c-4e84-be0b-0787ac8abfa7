import mssql, { DB_Errors, getRows } from '@lcs/mssql-utility'
import { SystemWidgetGroupFields, SystemWidgetGroupsTableName } from '@tess-f/sql-tables/dist/lms/system-widget-group.js'
import { SystemWidgetFields, SystemWidgetsTableName } from '@tess-f/sql-tables/dist/lms/system-widget.js'
import { UserHiddenWidgetFields, UserHiddenWidgetsTableName } from '@tess-f/sql-tables/dist/lms/user-hidden-widget.js'
import { Widget, WidgetFields, WidgetsTableName } from '@tess-f/sql-tables/dist/lms/widget.js'
import WidgetModel from '../../../models/widget.model.js'

export default async function getUserWidgetsService (userID?: string, groupIDs?: string[]): Promise<{ adminWidgets: WidgetModel[], userWidgets: WidgetModel[] }> {
  const pool = mssql.getPool()

  let userWidgets: Widget[]
  if (userID) {
    try {
      userWidgets = await getRows<Widget>(WidgetsTableName, pool.request(), { CreatedBy: userID })
    } catch (error) {
      if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
        throw error
      } else {
        userWidgets = []
      }
    }
  } else {
    userWidgets = []
  }

  const request = pool.request()
  const conds: string[] = []
  if (groupIDs && groupIDs.length > 0) {
    groupIDs.forEach((id, index) => {
      request.input(`id_${index}`, id)
      conds.push(`@id_${index}`)
    })
  }

  let query = `
    SELECT [${WidgetsTableName}].*, [${SystemWidgetsTableName}].[${SystemWidgetFields.Priority}]
    FROM [${WidgetsTableName}]
      JOIN [${SystemWidgetsTableName}] ON [${WidgetsTableName}].[${WidgetFields.CreatedBy}] = [${SystemWidgetsTableName}].[${SystemWidgetFields.ID}]
    WHERE 1 = 1
  `

  if (groupIDs && groupIDs.length > 0) {
    query += `
      AND ([${WidgetFields.CreatedBy}] IN (
        SELECT [${SystemWidgetGroupFields.SystemWidgetID}]
        FROM [${SystemWidgetGroupsTableName}]
        JOIN [${SystemWidgetsTableName}] ON [${SystemWidgetsTableName}].[${SystemWidgetFields.ID}] = [${SystemWidgetGroupsTableName}].[${SystemWidgetGroupFields.SystemWidgetID}] AND [${SystemWidgetsTableName}].[${SystemWidgetFields.Published}] = 1
        WHERE [${SystemWidgetGroupFields.GroupID}] IN (${conds.join(', ')})
      ) OR [${WidgetFields.CreatedBy}] IN (
        SELECT [${SystemWidgetFields.ID}]
        FROM [${SystemWidgetsTableName}]
        WHERE [${SystemWidgetFields.Everyone}] = 1
        AND [${SystemWidgetFields.Published}] = 1
      ))
    `
  } else {
    query += `
      AND [${WidgetFields.CreatedBy}] IN (
        SELECT [${SystemWidgetFields.ID}]
        FROM [${SystemWidgetsTableName}]
        WHERE [${SystemWidgetFields.Everyone}] = 1
        AND [${SystemWidgetFields.Published}] = 1
      )
    `
  }

  if (userID) {
    request.input('userID', userID)
    query += ` AND [${WidgetsTableName}].[${WidgetFields.ID}] NOT IN (
      SELECT [${UserHiddenWidgetFields.WidgetID}]
      FROM [${UserHiddenWidgetsTableName}]
      WHERE [${UserHiddenWidgetFields.UserID}] = @userID
    )`
  }

  const adminWidgets = await request.query<Widget>(query)

  return {
    userWidgets: userWidgets.map(widget => new WidgetModel(undefined, widget)),
    adminWidgets: adminWidgets.recordset.map(widget => new WidgetModel(undefined, widget))
  }
}
