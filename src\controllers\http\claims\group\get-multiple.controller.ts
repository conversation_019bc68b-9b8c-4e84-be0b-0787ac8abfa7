import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import get from '../../../../services/mssql/claims/group/get-multiple.service.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-multi-group-claims', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-get-multi-group-claims' label."

  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const claims = await get(id)

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-get-multi-group-claims' label and message indicating successful claim retrieval."
    log('info', 'Successfully retrieved claims for group.', { success: true, count: claims.length, req })

    res.json(claims.map(claim => claim.fields))
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to retrieve group claims: input validation error', { success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameters,'))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to retrieve group claims for group', { success: false, groupId: req.params.id, req })
      res.json([])
    } else {
      log('error', 'Failed to retrieve group claims.', { errorMessage, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
