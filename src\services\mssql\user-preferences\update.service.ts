import mssql, { updateRow, DB_Errors } from '@lcs/mssql-utility'
import UserPreference from '../../../models/user-preference.model.js'
import { UserPreferences } from '@tess-f/sql-tables/dist/lms/user-preference.js'

export default async function (model: UserPreference): Promise<UserPreference> {
  const pool = mssql.getPool()
  const updated = await updateRow<UserPreferences>(pool.request(), model, { UserID: model.fields.UserID })

  if (updated.length <= 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }

  return new UserPreference(updated[0])
}
