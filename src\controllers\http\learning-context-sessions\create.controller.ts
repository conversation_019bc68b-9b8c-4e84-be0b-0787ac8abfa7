import LearningContextSession, { createLearningContextSessionSchema } from '../../../models/learning-context-session.model.js'
import LearningContextSessionInstructor from '../../../models/learning-context-session-instructor.model.js'
import createSession from '../../../services/mssql/learning-context-sessions/create.service.js'
import createSessionInstructor from '../../../services/mssql/learning-context-session-instructors/create.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus
import getMultipleUsersByIds from '../../../services/mssql/users/get-multiple-by-ids.service.js'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.create-learning-context-sessions', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers

  try {
    const session = new LearningContextSession(createLearningContextSessionSchema.parse(req.body))

    session.fields.CreatedBy = req.session.userId
    session.fields.CreatedOn = new Date()
    
    const sessionData = z.object({
      Instructors: z.array(z.object({ ID: zodGUID }))
    }).parse(req.body)

    const createdSession = await createSession(session)

    const createdSessionInstructors = await Promise.all(sessionData.Instructors.map(async instructor => {
      const sessionInstructor = new LearningContextSessionInstructor({
        SessionID: createdSession.fields.ID,
        UserID: instructor.ID
      })
      const created = await createSessionInstructor(sessionInstructor)
      return created
    }))

    createdSession.fields.Instructors = await getMultipleUsersByIds(createdSessionInstructors.map(instructor => instructor.fields.UserID!))

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, bookmark a course. Note the time. Check the LMS API log for the 'http-create-learning-context-bookmark' label and message indicating successful creation."
    log('info', 'Successfully created learning context session', { contextSessionId: createdSession.fields.ID, success: true, req })

    res.json(createdSession.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to create learning context session: input validation error', { success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else if (errorMessage.startsWith('Violation of UNIQUE KEY constraint')) {
      log('warn', 'Failed to create learning context session for learning context because that learning context session already exists in the database.', { contextId: req.body.LearningContextID, success: false, req })
      res.sendStatus(BAD_REQUEST).send('Session already exists')
    } else {
      log('error', 'Failed to create learning context session.', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
