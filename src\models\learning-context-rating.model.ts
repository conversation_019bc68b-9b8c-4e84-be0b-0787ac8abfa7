import { Table } from '@lcs/mssql-utility'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { LearningContextRating, LearningContextRatingFields, LearningContextRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-rating.js'
import { z } from 'zod'

export const learningContextRatingSchema = z.object({
  [LearningContextRatingFields.UserID]: zodGUID,
  [LearningContextRatingFields.LearningContextID]: zodGUID,
  [LearningContextRatingFields.Rating]: z.number().int().min(1).max(5)
})

export default class LearningContextRatingModel extends Table<LearningContextRating, LearningContextRating> {
  public fields: LearningContextRating

  constructor (fields?: LearningContextRating) {
    super(LearningContextRatingsTableName, [
      LearningContextRatingFields.UserID,
      LearningContextRatingFields.Rating,
      LearningContextRatingFields.LearningContextID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearningContextRating): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningContextRating {
    return this.fields
  }
}
