import mssql, { getRows } from '@lcs/mssql-utility'
import { Location, LocationsTableName } from '@tess-f/sql-tables/dist/lms/location.js'
import LocationModel from '../../../models/location.model.js'

export default async function (id: string): Promise<LocationModel> {
  const pool = mssql.getPool()
  const records = await getRows<Location>(LocationsTableName, pool.request(), { ID: id })
  return new LocationModel(records[0])
}
