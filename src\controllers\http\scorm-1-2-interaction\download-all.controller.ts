import logger from '@lcs/logger'
import { Request, Response } from 'express'
import { Workbook } from 'excel4node'
import getAllObjectsThatHaveScormInteractions from '../../../services/mssql/learning-objects/get-all-that-have-scorm-1-2-interactions.service.js'
import getInteractionsForLearningObject, { InteractionData } from '../../../services/mssql/scorm-1-2-interaction/get-all-for-learning-object.service.js'
import httpStatus from 'http-status'
import { httpLogTransformer } from '@tess-f/backend-utils'
const { INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.download-all-scorm-interaction-data', httpLogTransformer)

/**
 * Gets all SCORM 1.2 interaction data
 * Data is mapped with learning object name, user names, and session (learner progress) data
 * Returns data as a XLSX file with each learning objects interactions on a separate sheet and all interactions on one sheet
 */
export default async function (req: Request, res: Response): Promise<void> {
  try {
    log('debug', 'Building interaction report workbook')
    const workbook = new Workbook()

    /**
     * Data worksheet
     * Columns: Session ID, User ID, Content ID, Content Title, Completion Date, Question ID, Type, Weighting, Student Response, Result, Latency, Correct Response
     */
    const dataSheet = workbook.addWorksheet('Interaction Data')
    dataSheet.cell(1, 1).string('Session ID')
    dataSheet.cell(1, 2).string('User ID')
    dataSheet.cell(1, 3).string('Content ID')
    dataSheet.cell(1, 4).string('Content Title')
    dataSheet.cell(1, 5).string('Completion Date')
    dataSheet.cell(1, 6).string('Question ID')
    dataSheet.cell(1, 7).string('Question Type')
    dataSheet.cell(1, 8).string('Question Weighting')
    dataSheet.cell(1, 9).string('Student Response')
    dataSheet.cell(1, 10).string('Result')
    dataSheet.cell(1, 11).string('Latency')
    dataSheet.cell(1, 12).string('Correct Response')
    dataSheet.row(1).freeze()
    let nextDataRow = 2

    // fetch all of the learning objects that have SCORM 1.2 interaction data
    const learningObjects = await getAllObjectsThatHaveScormInteractions()
    log('info', 'Found learning objects that have SCORM 1.2 interaction data', { count: learningObjects.length, success: true, req })

    await Promise.all(learningObjects.map(async learningObject => {
      log('debug', 'Getting interaction data for learning object', { id: learningObject.fields.ID, req })
      // next we need to get the interaction data for this learning object
      const interactionData = await getInteractionsForLearningObject(learningObject.fields.ID ?? '')

      // create a worksheet for this learning objects data
      const interactionSheet = workbook.addWorksheet(learningObject.fields.Title?.replace('\\', '').replace('/', '').replace('?', '').replace('*', '').replace('[', '').replace(']', '').substring(0, 30) ?? `Content ${learningObjects.indexOf(learningObject) + 1}`)
      // Add the initial columns
      interactionSheet.cell(1, 1).string('Session ID')
      interactionSheet.cell(1, 2).string('Completion Date')
      interactionSheet.cell(1, 3).string('Max Score')
      interactionSheet.cell(1, 4).string('Min Score')
      interactionSheet.cell(1, 5).string('Score')
      interactionSheet.cell(1, 6).string('Total Time')
      interactionSheet.cell(1, 7).string('First Name')
      interactionSheet.cell(1, 8).string('Last Name')
      interactionSheet.cell(1, 9).string('User ID')
      interactionSheet.cell(1, 10).string('Status')
      // now we need to loop through the data adding each row to the data sheet
      // we also need to map the data by session grouping all of the questions together
      let nextColumn = 11
      const columnMap = new Map<string, number>()
      const sessionMap = new Map<string, InteractionData[]>()

      for (const data of interactionData) {
        if (!sessionMap.has(data.LearnerProgressID)) {
          sessionMap.set(data.LearnerProgressID, [])
        }
        sessionMap.get(data.LearnerProgressID)?.push(data)
        // add the question column if it doesn't exist
        if (!columnMap.has(data.ID)) {
          columnMap.set(data.ID, nextColumn)
          interactionSheet.cell(1, nextColumn).string('Question ID')
          nextColumn++
          interactionSheet.cell(1, nextColumn).string('Student Response')
          nextColumn++
          interactionSheet.cell(1, nextColumn).string('Result')
          nextColumn++
          interactionSheet.cell(1, nextColumn).string('Correct Response')
          nextColumn++
          interactionSheet.cell(1, nextColumn).string('Latency')
          nextColumn++
          interactionSheet.cell(1, nextColumn).string('Weighting')
          nextColumn++
        }

        dataSheet.cell(nextDataRow, 1).string(data.LearnerProgressID)
        dataSheet.cell(nextDataRow, 2).string(data.UserID)
        dataSheet.cell(nextDataRow, 3).string(data.LearningObjectId)
        dataSheet.cell(nextDataRow, 4).string(data.Title)
        dataSheet.cell(nextDataRow, 5).string(data.CompletedDate?.toISOString() ?? '')
        dataSheet.cell(nextDataRow, 6).string(data.ID)
        dataSheet.cell(nextDataRow, 7).string(data.Type ?? 'unknown')
        dataSheet.cell(nextDataRow, 8).string(data.Weighting ?? 'unknown')
        dataSheet.cell(nextDataRow, 9).string(data.StudentResponse ?? 'unknown')
        dataSheet.cell(nextDataRow, 10).string(data.Result ?? 'unknown')
        dataSheet.cell(nextDataRow, 11).string(data.Latency ?? 'unknown')
        dataSheet.cell(nextDataRow, 12).string(data.CorrectResponses ?? 'unknown')
        nextDataRow++
      }

      let nextInteractionRow = 2
      // now we need to map in each session to the interaction data row
      for (const sessionId of sessionMap.keys()) {
        sessionMap.get(sessionId)?.forEach((data, index) => {
          if (index === 0) {
            // first item add the base info for the session
            interactionSheet.cell(nextInteractionRow, 1).string(sessionId)
            interactionSheet.cell(nextInteractionRow, 2).string(data.CompletedDate?.toISOString() ?? '')
            interactionSheet.cell(nextInteractionRow, 3).number(data.MaxScore ?? 0)
            interactionSheet.cell(nextInteractionRow, 4).number(data.MinScore ?? 0)
            interactionSheet.cell(nextInteractionRow, 5).number(data.RawScore ?? 0)
            interactionSheet.cell(nextInteractionRow, 6).string(data.TotalTime ?? '')
            interactionSheet.cell(nextInteractionRow, 7).string(data.FirstName)
            interactionSheet.cell(nextInteractionRow, 8).string(data.LastName)
            interactionSheet.cell(nextInteractionRow, 9).string(data.UserID)
            interactionSheet.cell(nextInteractionRow, 10).string(data.LessonStatus)
          }

          // map in the question data
          let questionIndex = columnMap.get(data.ID) ?? 11
          interactionSheet.cell(nextInteractionRow, questionIndex).string(data.ID)
          questionIndex++
          interactionSheet.cell(nextInteractionRow, questionIndex).string(data.StudentResponse ?? 'unknown')
          questionIndex++
          interactionSheet.cell(nextInteractionRow, questionIndex).string(data.Result ?? 'unknown')
          questionIndex++
          interactionSheet.cell(nextInteractionRow, questionIndex).string(data.CorrectResponses ?? 'unknown')
          questionIndex++
          interactionSheet.cell(nextInteractionRow, questionIndex).string(data.Latency ?? 'unknown')
          questionIndex++
          interactionSheet.cell(nextInteractionRow, questionIndex).string(data.Weighting ?? 'unknown')
        })

        nextInteractionRow++
      }
    }))

    const today = new Date()
    workbook.write(`${today.getMonth() + 1}-${today.getDate()}-${today.getFullYear()}-interaction-data.xlsx`, res)
  } catch (error) {
    log('error', 'Failed to generate interaction data excel report', { error, success: false, req })
    res.sendStatus(INTERNAL_SERVER_ERROR)
  }
}
