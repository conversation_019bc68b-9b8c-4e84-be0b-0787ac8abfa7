import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'

describe('HTTP search-context-session-enrollments controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./search-context-session-enrollments.controller', {
            '../../../services/mssql/session-enrollments/search-context-session-enrollments.service.js': {
                default: Sinon.stub().returns(Promise.resolve({totalRecords: 1, enrollments: [{UserID: uuid(), CreatedOn: new Date(), SessionID: uuid(), SessionTitle: 'test'}]}))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                offset: 1,
                limit: 10
            },
            query: {
                from: new Date(),
                to: new Date()
            },
            params: {
                id: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./search-context-session-enrollments.controller', {
            '../../../services/mssql/session-enrollments/search-context-session-enrollments.service.js': {
                default: Sinon.stub().returns(Promise.resolve({totalRecords: 1, enrollments: [{UserID: uuid(), CreatedOn: new Date(), SessionID: uuid(), SessionTitle: 'test'}]}))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                offset: 1,
                limit: 10
            },
            query: {
                from: new Date(),
                to: new Date()
            },
            params: {
                id: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('id')
        expect(mocks.res._getData()).include('Expected string, received boolean')

    })


    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./search-context-session-enrollments.controller', {
            '../../../services/mssql/session-enrollments/search-context-session-enrollments.service.js': {
                default: Sinon.stub().returns(Promise.reject({totalRecords: 1, enrollments: [{UserID: uuid(), CreatedOn: new Date(), SessionID: uuid(), SessionTitle: 'test'}]}))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                offset: 1,
                limit: 10
            },
            query: {
                from: new Date(),
                to: new Date()
            },
            params: {
                id: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

})