import { Table } from '@lcs/mssql-utility'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { CoursePrerequisiteAlternative, CoursePrerequisiteAlternativeFields, CoursePrerequisiteAlternativesTableName } from '@tess-f/sql-tables/dist/lms/course-prerequisite-alternative.js'
import z from 'zod'

export const createPrerequisiteAlternativeSchema = z.object({
  PrerequisiteContextId: zodGUID.nullish(),
  PrerequisiteObjectId: zodGUID.nullish()
}).superRefine(({ PrerequisiteContextId, PrerequisiteObjectId }, ctx) => {
  if (!!PrerequisiteContextId && !!PrerequisiteObjectId) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Cannot set both PrerequisiteContextId and PrerequisiteObjectId'
    })
  } else if (!PrerequisiteContextId && !PrerequisiteObjectId) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Must set either PrerequisiteContextId or PrerequisiteObjectId'
    })
  }
})

export class CoursePrerequisiteAlternativeModel extends Table<CoursePrerequisiteAlternative, CoursePrerequisiteAlternative> {
  fields: CoursePrerequisiteAlternative

  constructor(fields?: CoursePrerequisiteAlternative) {
    super(
      CoursePrerequisiteAlternativesTableName,
      [
        CoursePrerequisiteAlternativeFields.ForPrerequisiteId
      ]
    )

    if (fields) this.fields = fields
    else this.fields = {}
  }

  importFromDatabase (record: CoursePrerequisiteAlternative): void {
    this.fields = {
      Id: record.Id,
      ForPrerequisiteId: record.ForPrerequisiteId,
      PrerequisiteContextId: record.PrerequisiteContextId,
      PrerequisiteObjectId: record.PrerequisiteObjectId
    }
  }

  exportJsonToDatabase (): CoursePrerequisiteAlternative {
    return {
      Id: this.fields.Id,
      ForPrerequisiteId: this.fields.ForPrerequisiteId,
      PrerequisiteContextId: this.fields.PrerequisiteContextId,
      PrerequisiteObjectId: this.fields.PrerequisiteObjectId
    }
  }
}
