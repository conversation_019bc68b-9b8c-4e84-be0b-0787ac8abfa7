import mssql from '@lcs/mssql-utility'
import { User, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { getBasicUserFields } from './utils.js'

export default async function getMultipleUsersByIds (userIds: string[]): Promise<User[]> {
  if (userIds.length <= 0) return []

  const request = mssql.getPool().request()

  const conditions = userIds.map((id, index) => {
    request.input(`id_${index}`, id)
    return `@id_${index}`
  })

  const results = await request.query<User>(`
    SELECT ${getBasicUserFields()}
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] IN (${conditions.join(', ')})
  `)

  return results.recordset
}
