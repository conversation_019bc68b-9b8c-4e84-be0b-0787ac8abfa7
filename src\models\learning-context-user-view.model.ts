import { Table } from '@lcs/mssql-utility'
import { LearningContextUserView, LearningContextUserViewFields, LearningContextUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-view.js'

export default class LearningContextUserViewModel extends Table<LearningContextUserView, LearningContextUserView> {
  public fields: LearningContextUserView

  constructor (fields?: LearningContextUserView) {
    super(LearningContextUserViewsTableName, [
      LearningContextUserViewFields.UserID,
      LearningContextUserViewFields.LearningContextID,
      LearningContextUserViewFields.CreatedOn
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearningContextUserView): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningContextUserView {
    return this.fields
  }
}
