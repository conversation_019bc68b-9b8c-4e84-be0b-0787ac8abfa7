import mssql from '@lcs/mssql-utility'
import { UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'

export async function getObjectInProgressCount (objectID: string, from?: Date, to?: Date): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('objectID', objectID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const results = await request.query<{InProgress: number}>(`
    SELECT COUNT ([${UserFields.ID}]) AS InProgress
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] IN (
      SELECT [${LearnerProgressFields.UserID}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.LearningObjectID}] = @objectID
      AND [${LearnerProgressFields.LessonStatusID}] < ${LessonStatuses.fail}
      ${from && to ? `AND [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to` : ''}
    ) AND [${UserFields.ID}] NOT IN (
      SELECT [${LearnerProgressFields.UserID}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.LearningObjectID}] = @objectID
      AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.fail}
      ${from && to ? `AND [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to` : ''}
    )
  `)

  return results.recordset[0].InProgress
}

export async function getObjectCompletedCount (objectID: string, from?: Date, to?: Date): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('objectID', objectID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const results = await request.query<{Completed: number}>(`
    SELECT COUNT ([${UserFields.ID}]) AS Completed
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] IN (
      SELECT [${LearnerProgressFields.UserID}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.LearningObjectID}] = @objectID
      AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.fail}
      ${from && to ? `AND [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to` : ''}
    )
  `)

  return results.recordset[0].Completed
}
