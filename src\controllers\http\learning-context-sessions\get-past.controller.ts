import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getPastSessions from '../../../services/mssql/learning-context-sessions/get-past-for-context.service.js'
import getEnrollments from '../../../services/mssql/session-enrollments/get.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'
const { INTERNAL_SERVER_ERROR, BAD_REQUEST } = httpStatus

const log = logger.create('Controller-HTTP.get-past-sessions-for-context', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { offset, limit } = z.object({
      offset: zodOffset,
      limit: zodLimit
    }).parse(req.query)

    const { id } = z.object({ id: zodGUID }).parse(req.params)

    const _sessions = await getPastSessions(id, offset, limit)

    const sessions = await Promise.all(_sessions.sessions.map(async session => {
      try {
        session.fields.Enrollments = (await getEnrollments(session.fields.ID, undefined)).map(sessionEnrollment => sessionEnrollment.fields)
      } catch (error) {
        if (error instanceof Error && error.message === dbErrors.default.NOT_FOUND_IN_DB) {
          session.fields.Enrollments = []
        } else {
          throw error
        }
      }

      return session.fields
    }))

    log('info', 'Successfully retrieved past sessions for context', { totalRecords: _sessions.totalRecords, count: sessions.length, success: true, req })

    res.json({
      totalRecords: _sessions.totalRecords,
      sessions
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get past sessions for context: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to get past sessions for context', { contextID: req.params.id, error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
