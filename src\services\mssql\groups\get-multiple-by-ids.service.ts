import mssql from '@lcs/mssql-utility'
import { Group, GroupFields, GroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/group.js'

export default async function getMultipleGroupsByIds (ids: string[]): Promise<Group[]> {
  if (ids.length <= 0) return []

  const request = mssql.getPool().request()
  const conditions = ids.map((id, index) => {
    request.input(`id_${index}`, id)
    return `@id_${index}`
  })

  const results = await request.query<Group>(`
    SELECT *
    FROM [${GroupTableName}]
    WHERE [${GroupFields.ID}] IN (${conditions.join(', ')})
  `)

  return results.recordset
}
