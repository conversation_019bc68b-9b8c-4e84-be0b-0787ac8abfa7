import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import getAssignment from '../../../services/mssql/assignments/get.service.js'
import getAssignmentObjects from '../../../services/mssql/assignment-learning-objects/get-multiple.service.js'
import getAssignmentContexts from '../../../services/mssql/assignment-learning-contexts/get-multiple.service.js'
import getLearningObject from '../../../services/mssql/learning-objects/get.service.js'
import getLearningContext from '../../../services/mssql/learning-context/get.service.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import getUsersAndGroupsForAssignment from '../../../services/mssql/assignment-users/get-users-and-groups-for-assignment.service.js'
import { httpLogTransformer } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-assignment', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    // get the assignment
    const assignment = await getAssignment(id)

    // get the learning objects
    try {
      const assignmentObjects = await getAssignmentObjects(id)
      assignment.fields.LearningObjects = await Promise.all(assignmentObjects.map(async assignmentObject => {
        const obj = await getLearningObject(assignmentObject.fields.LearningObjectID!)
        const learningObject = obj.fields
        learningObject.OrderID = assignmentObject.fields.OrderID
        return learningObject
      }))
    } catch (error) {
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        log('warn', 'Unable to retrieve learning objects for assignment because none where found in the database.', { assignmentId: assignment.fields.ID, success: false, req })
      } else {
        throw error
      }
    }

    // get the learning contexts
    try {
      const assignmentContexts = await getAssignmentContexts(id)
      assignment.fields.LearningContexts = await Promise.all(assignmentContexts.map(async assignmentContext => {
        const context = await getLearningContext(assignmentContext.fields.LearningContextID!, undefined, { prerequisites: true })
        context.fields.OrderID = assignmentContext.fields.OrderID
        return context.fields
      }))
    } catch (error) {
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        log('warn', 'Unable to retrieve learning contexts for assignment because none where found in the database.', { error, req, assignmentId: id, success: false })
      } else {
        throw error
      }
    }

    // map the assignment users to actual users and groups
    const { users, groups } = await getUsersAndGroupsForAssignment(assignment)
    assignment.fields.Users = users
    assignment.fields.Groups = groups

    log('info', 'Successfully retrieved assignment', { success: true })

    res.json(assignment.fields)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Invalid assignment ID', { assignmentID: req.params.id, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send('Invalid assignment ID in request parameters')
    } else {
      log('error', 'Failed to retrieve assignment.', { error, req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
