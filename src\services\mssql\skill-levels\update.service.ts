import mssql, { DB_Errors, updateRow } from '@lcs/mssql-utility'
import SkillLevelModel from '../../../models/skill-level.model.js'
import { SkillLevel } from '@tess-f/sql-tables/dist/lms/skill-level.js'

export default async function (model: SkillLevelModel): Promise<SkillLevelModel> {
  const pool = mssql.getPool()
  const updated = await updateRow<SkillLevel>(pool.request(), model, { ID: model.fields.ID })

  if (updated.length <= 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }

  return new SkillLevelModel(updated[0])
}
