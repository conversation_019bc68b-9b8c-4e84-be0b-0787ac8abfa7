import { Request<PERSON>and<PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getMultipleController from './get-multiple.controller.js'
import getPaginatedController from './get-paginated.controller.js'
import getController from './get.controller.js'
import updateController from './update.controller.js'
import getByCmi5ContextIdController from './get-by-cmi5-context-id.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.get('/learning-object/:id', getController as RequestHandler)
router.get('/learning-objects', getMultipleController as RequestHandler)
router.post('/learning-object', checkClaims([Claims.CREATE_FILE]), createController as RequestHandler)
router.put('/learning-object', checkClaims([Claims.CREATE_FILE, Claims.MODIFY_FILE]), updateController as RequestHandler)
router.delete('/learning-object/:id', checkClaims([Claims.DELETE_FILE]), deleteController as RequestHandler)
router.post('/paginated-learning-objects', checkClaims([Claims.VIEW_FILES]), getPaginatedController as RequestHandler)
router.get('/learning-object-for-cmi5-course/:id', checkClaims([Claims.VIEW_FILES, Claims.VIEW_CATALOG, Claims.CREATE_FILE, Claims.MODIFY_FILE, Claims.DELETE_FILE, Claims.CREATE_CATALOG_ITEMS, Claims.MODIFY_CATALOG_ITEMS, Claims.DELETE_CATALOG_ITEMS]), getByCmi5ContextIdController as RequestHandler)

export default router
