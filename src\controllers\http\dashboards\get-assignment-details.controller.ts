import logger from '@lcs/logger'
import { Request, Response } from 'express'
import searchActiveAssignmentUsers from '../../../services/mssql/assignment-users/search-active-assignment-users.service.js'
import getAssignmentContentForUser from '../../../services/mssql/assignments/get-assignment-content-for-user.service.js'
import calculateAssignmentContentStatus from '@tess-f/lms/dist/shared/services/assignment/grade-user-assignment-content.service.js'
import { getOverallStatus } from '@tess-f/lms/dist/shared/services/assignment/context-grading/utils.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-assignment-details-dashboard', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { limit, offset, search, groups } = z.object({
      limit: zodLimit,
      offset: zodOffset,
      search: z.string().optional(),
      groups: z.array(zodGUID).optional()
    }).parse(req.body)
    const { id } = z.object({ id: zodGUID }).parse(req.params)

    const userIDs = await searchActiveAssignmentUsers(id, offset, limit, search, groups)

    const details = await Promise.all(userIDs.userIDs.map(async UserID => {
      // we need to get all the data about each user
      const assignedItems = await getAssignmentContentForUser(id, UserID)
      const gradedContent = calculateAssignmentContentStatus(assignedItems)

      return {
        UserID,
        Status: getOverallStatus(gradedContent.map(item => item.Status)),
        Progress: getAverage(gradedContent.map(item => item.Progress)),
        Started: getStartedDate(gradedContent.map(item => item.Started).filter(item => item !== undefined)),
        LastAccessed: getLastAccessedDate(gradedContent.map(item => item.LastAccessed).filter(item => item !== undefined)),
        Content: gradedContent
      }
    }))

    log('info', 'Successfully retrieved assignment details report', { count: details.length, totalItems: userIDs.TotalRecords, success: true, req })
    res.json({
      totalRecords: userIDs.TotalRecords,
      items: details
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get assignment details report: validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data:'))
    } else {
      log('error', 'Failed to get assignment details report', { error, assignmentID: req.params.id, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}

function getAverage (numbers: number[]): number {
  let total = 0
  numbers.forEach(prog => { total += prog })
  return total / numbers.length
}

function getStartedDate (dates: Date[]): Date | undefined {
  let started: Date | undefined
  dates.forEach(date => {
    if (started === undefined || started === null) {
      started = date || undefined
    } else if (started?.getTime() > date?.getTime()) {
      started = date
    }
  })
  return started
}

function getLastAccessedDate (dates: Date[]): Date | undefined {
  let lastAccessed: Date | undefined
  dates.forEach(date => {
    if (lastAccessed === undefined || lastAccessed === null) {
      lastAccessed = date || undefined
    } else if (lastAccessed?.getTime() < date?.getTime()) {
      lastAccessed = date
    }
  })
  return lastAccessed
}
