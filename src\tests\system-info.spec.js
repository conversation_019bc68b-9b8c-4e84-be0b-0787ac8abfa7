const chai = require('chai');
const expect = chai.expect;
const uuidv4 = require('uuid/v4');
const tester = require('../api/utils/test-agent.utils');
const settings = require('../api/config/settings');

let isolated = false;

describe("System Info", () => {

    before( done => {
        if(tester.agent == null) {
            isolated = true;
            tester.create()
            .then( agent => {
                done();
            })
            .catch( err => {
                console.error(err);
                done();
            });
        } else {
            done();
        }
    });

    // GET /api/system-info
    it("gets system info", done => {
        tester.agent
        .get(settings.server.root + 'system-info')
        .end((err, res) => {
            expect(res.body.apps).to.exist;
            done();
        });
    });

    after((done)=>{
        if(isolated) {
            tester.close();
        } 
        done();
    });

})