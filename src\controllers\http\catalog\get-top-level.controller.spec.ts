import logger from '@lcs/logger'
import httpStatus from 'http-status'
import Sinon from 'sinon'
import { expect } from 'chai'
import httpMock from 'node-mocks-http'
import esmock from 'esmock'

describe('HTTP Controller: get top level of catalog', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('should return the top level of the catalog', async () => {
    const controller = await esmock('./get-top-level.controller.js', {
      '../../../services/mssql/catalog/get-top-level.service.js': {
        default: Sinon.stub().resolves([])
      }
    })
    const mocks = httpMock.createMocks()
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const data = JSON.parse(mocks.res._getData())
    expect(data).to.exist
    expect(data.length).to.be.gte(0)
  })
})
