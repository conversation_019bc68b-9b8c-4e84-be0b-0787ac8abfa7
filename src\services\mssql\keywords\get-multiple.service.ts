import mssql, { getRows } from '@lcs/mssql-utility'
import { Keyword, KeywordsTableName } from '@tess-f/sql-tables/dist/lms/keyword.js'
import { LearningContextKeyword, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'
import { LearningObjectKeyword, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import KeywordModel from '../../../models/keyword.model.js'

export default async function (): Promise<KeywordModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<Keyword>(KeywordsTableName, pool.request())
  return records.map(record => new KeywordModel(record))
}

export async function getKeywordsForContext (contextID: string): Promise<string[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextKeyword>(LearningContextKeywordsTableName, pool.request(), { LearningContextID: contextID })
  return records.map(record => record.Keyword!)
}

export async function getKeywordsForObject (objectID: string): Promise<string[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningObjectKeyword>(LearningObjectKeywordsTableName, pool.request(), { LearningObjectID: objectID })
  return records.map(record => record.Keyword!)
}
