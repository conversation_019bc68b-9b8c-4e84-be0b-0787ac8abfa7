import mssql, { addRow } from '@lcs/mssql-utility'
import LearningContextConnectionModel from '../../../models/learning-context-connection.model.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { LearningContextConnection, LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'

export default async function (connection: LearningContextConnectionModel): Promise<LearningContextConnectionModel> {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    const request = transaction.request()
    request.input('parentID', connection.fields.ParentContextID)
    request.input('orderID', connection.fields.OrderID)

    await request.query(`
      UPDATE [${LearningContextTableName}]
      SET [${LearningContextFields.OrderID}] = [${LearningContextFields.OrderID}] + 1
      WHERE [${LearningContextFields.OrderID}] >= @orderID
      AND [${LearningContextFields.ParentContextID}] = @parentID
    `)

    await request.query(`
      UPDATE [${LearningContextConnectionsTableName}]
      SET [${LearningContextConnectionFields.OrderID}] = [${LearningContextConnectionFields.OrderID}] + 1
      WHERE [${LearningContextConnectionFields.OrderID}] >= @orderID
      AND [${LearningContextConnectionFields.ParentContextID}] = @parentID
    `)

    await request.query(`
      UPDATE [${LearningObjectContextsTableName}]
      SET [${LearningObjectContextFields.OrderID}] = [${LearningObjectContextFields.OrderID}] + 1
      WHERE [${LearningObjectContextFields.OrderID}] >= @orderID
      AND [${LearningObjectContextFields.LearningContextID}] = @parentID
    `)

    const record = await addRow<LearningContextConnection>(transaction.request(), connection)

    await transaction.commit()

    return new LearningContextConnectionModel(record)
  } catch (error) {
    if (!rolledBack) await transaction.rollback()
    throw error
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}
