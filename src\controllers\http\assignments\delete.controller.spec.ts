import logger from '@lcs/logger'
import { expect } from 'chai'
import esmock from 'esmock'
import Sinon from 'sinon'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: Delete Assignment', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns a bad request status code when no assignment id is provided', async () => {
    const mocks = httpMocks.createMocks()
    const deleteAssignment = await esmock('./delete.controller.js')
    await deleteAssignment(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid assignment ID in request parameters')
  })

  it('returns a bad request status code when an invalid assignment id is provided', async () => {
    const mocks = httpMocks.createMocks({
      params: {
        id: 'invalid'
      }
    })
    const deleteAssignment = await esmock('./delete.controller.js')
    await deleteAssignment(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid assignment ID in request parameters')
  })

  it('returns a no content status code when assignment is successfully deleted', async () => {
    const mocks = httpMocks.createMocks({
      params: {
        id: uuid()
      }
    })
    const deleteAssignment = await esmock('./delete.controller.js', {
      '../../../services/mssql/assignments/delete.service.js': {
        default: Sinon.stub().returns(Promise.resolve())
      }
    })
    await deleteAssignment(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.NO_CONTENT)
  })

  it('returns an internal server error status code when an error occurs', async () => {
    const mocks = httpMocks.createMocks({
      params: {
        id: uuid()
      }
    })
    const deleteAssignment = await esmock('./delete.controller.js', {
      '../../../services/mssql/assignments/delete.service.js': {
        default: Sinon.stub().returns(Promise.reject(new Error('test error')))
      }
    })
    await deleteAssignment(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })
})