import create from '../../../services/mssql/learning-object-contexts/create.service.js'
import LearningObjectContext, { createLearningObjectContextSchema } from '../../../models/learning-object-context.model.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { ZodError } from 'zod'

const log = logger.create('Controller-HTTP.create-learning-object-contexts', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, add an existing content file to a course. Note the time. Check the LMS API log for the 'http-create-learning-object-contexts' label."
  try {
    const learningObjectContext = new LearningObjectContext(createLearningObjectContextSchema.parse(req.body))
  
    // Set the creator as the ID contained in the token
    learningObjectContext.fields.CreatedBy = req.session.userId
    learningObjectContext.fields.CreatedOn = new Date()
    const created = (await create(learningObjectContext)).fields

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, add an existing content file to a course. Note the time. Check the LMS API log for the 'http-create-learning-object-contexts' label and message indicating successful creation."
    log('info', 'Successfully created learning object contexts.', { success: true, req })

    res.json(created)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create learning object context: input validation error', { success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to create learning object context.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
