import mssql, { addRow } from '@lcs/mssql-utility'
import LearningContextSessionInstructorModel from '../../../models/learning-context-session-instructor.model.js'
import { LearningContextSessionInstructor } from '@tess-f/sql-tables/dist/lms/learning-context-session-instructor.js'

export default async function (sessionInstructor: LearningContextSessionInstructorModel): Promise<LearningContextSessionInstructorModel> {
  const pool = mssql.getPool()
  const record = await addRow<LearningContextSessionInstructor>(pool.request(), sessionInstructor)
  sessionInstructor.importFromDatabase(record)
  return sessionInstructor
}
