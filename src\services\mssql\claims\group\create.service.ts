import mssql, { addRow } from '@lcs/mssql-utility'
import errors from '../../../../config/errors.js'
import GroupClaimModel from '../../../../models/group-claim.model.js'
import { GroupClaim } from '@tess-f/sql-tables/dist/lms/group-claim.js'

/**
 * Creates GroupClaims, an association between a Group ID and a particular claim
 * value.
 *
 * @param { Array[GroupClaimModel] } groupClaims
 */
export default async function (groupClaims: GroupClaimModel[]): Promise<void> {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    for (const claim of groupClaims) {
      await addRow<GroupClaim>(pool.request(), claim)
    }

    await transaction.commit()
  } catch (error) {
    if (!rolledBack) transaction.rollback()
    const errorMessage = error instanceof Error ? error.message : ''
    if (errorMessage.indexOf(errors.SQL_FOREIGN_KEY) !== -1) {
      throw new Error(errors.INVALID_CLAIM)
    }
    throw error
  }
}
