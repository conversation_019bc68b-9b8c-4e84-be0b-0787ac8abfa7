import mssql, { deleteRow, updateRow } from '@lcs/mssql-utility'
import LearningContextModel from '../../../models/learning-context.model.js'
import { ConnectionPool, Transaction } from 'mssql'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'

export default async function (context: LearningContextModel, newParentID: string, newOrderID: number) {
  await resetParent(mssql.getPool(), context, newParentID, newOrderID)
}

export async function resetParent (connection: ConnectionPool | Transaction, context: LearningContextModel, newParentID: string, newOrderID: number) {
  // update the order ID's for all the learning contexts, context connections, and learning object contexts that have the old parent
  const request = connection.request()
  request.input('parentID', context.fields.ParentContextID)
  request.input('orderID', context.fields.OrderID)
  await request.query(`
    UPDATE [${LearningContextTableName}]
    SET [${LearningContextFields.OrderID}] = [${LearningContextFields.OrderID}] - 1
    WHERE [${LearningContextFields.OrderID}] > @orderID
    AND [${LearningContextFields.ParentContextID}] = @parentID
  `)

  await request.query(`
    UPDATE [${LearningObjectContextsTableName}]
    SET [${LearningObjectContextFields.OrderID}] = [${LearningObjectContextFields.OrderID}] - 1
    WHERE [${LearningObjectContextFields.OrderID}] > @orderID
    AND [${LearningObjectContextFields.LearningContextID}] = @parentID
  `)

  await request.query(`
    UPDATE [${LearningContextConnectionsTableName}]
    SET [${LearningContextConnectionFields.OrderID}] = [${LearningContextConnectionFields.OrderID}] - 1
    WHERE [${LearningContextConnectionFields.OrderID}] > @orderID
    AND [${LearningContextConnectionFields.ParentContextID}] = @parentID
  `)

  // next update the parent of the content to the new parent
  context.fields.ParentContextID = newParentID
  context.fields.OrderID = newOrderID
  await updateRow<LearningContext>(connection.request(), context, { ID: context.fields.ID })

  // remove the connection we just mapped
  await deleteRow(connection.request(), LearningContextConnectionsTableName, { ParentContextID: newParentID, ConnectedContextID: context.fields.ID })
}
