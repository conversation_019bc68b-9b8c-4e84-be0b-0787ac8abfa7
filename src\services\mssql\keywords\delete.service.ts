import mssql, { deleteRow } from '@lcs/mssql-utility'
import { KeywordsTableName } from '@tess-f/sql-tables/dist/lms/keyword.js'

export default async function (name: string): Promise<number> {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })
    // remove the keyword
    const numRowsAffected = await deleteRow(transaction.request(), KeywordsTableName, { Name: name })
    await transaction.commit()
    return numRowsAffected
  } catch (error) {
    if (!rolledBack) await transaction.rollback()
    throw error
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}
