import mssql from '@lcs/mssql-utility'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'

export default async function getUserInProgressDates (objectID: string, userID: string, from?: Date, to?: Date): Promise<{ startedOn: Date |undefined, LastAccessed: Date |undefined }> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('objectID', objectID)
  request.input('userID', userID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const baseQuery = `
    SELECT TOP (1) [${LearnerProgressFields.CreatedOn}]
    FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @userID
    AND [${LearnerProgressFields.LearningObjectID}] = @objectID
    ${from && to ? `AND [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to` : ''}
  `

  const started = await request.query<{CreatedOn: Date}>(baseQuery + `ORDER BY [${LearnerProgressFields.CreatedOn}] ASC`)
  const lastAccessed = await request.query<{CreatedOn: Date}>(baseQuery + `ORDER BY [${LearnerProgressFields.CreatedOn}] DESC`)

  return {
    LastAccessed: lastAccessed.recordset.length > 0 ? lastAccessed.recordset[0].CreatedOn : undefined,
    startedOn: started.recordset.length > 0 ? started.recordset[0].CreatedOn : undefined
  }
}
