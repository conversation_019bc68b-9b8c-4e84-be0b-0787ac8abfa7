import mssql, { getRows } from '@lcs/mssql-utility'
import { Widget, WidgetsTableName } from '@tess-f/sql-tables/dist/lms/widget.js'
import WidgetModel from '../../../models/widget.model.js'

export default async function getWidgetsForCreator (creatorID: string): Promise<WidgetModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<Widget>(WidgetsTableName, pool.request(), { CreatedBy: creatorID })
  return records.map(record => new WidgetModel(undefined, record))
}
