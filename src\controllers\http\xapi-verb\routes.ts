import { RequestHandler, Router } from 'express'
import getAllController from './get-all.controller.js'
import updateController from './update.controller.js'
import deleteController from './delete.controller.js'
import createController from './create.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.get('/', checkClaims([Claims.VIEW_SYSTEM_LABELS, Claims.CREATE_SYSTEM_LABELS, Claims.MODIFY_SYSTEM_LABELS, Claims.DELETE_SYSTEM_LABELS]), getAllController as RequestHandler)
router.put('/:id', checkClaims([Claims.CREATE_SYSTEM_LABELS, Claims.MODIFY_SYSTEM_LABELS]), updateController as RequestHandler)
router.delete('/:id', checkClaims([Claims.DELETE_SYSTEM_LABELS]), deleteController as RequestHandler)
router.post('/', checkClaims([Claims.CREATE_SYSTEM_LABELS]), createController as RequestHandler)

export default router
