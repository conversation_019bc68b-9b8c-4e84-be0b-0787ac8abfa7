import logger from '@lcs/logger'
import Sinon from 'sinon'
import { expect } from 'chai'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('Controller.AMQP: delete learning object', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns unsuccessful when the id is missing from the request data', async () => {
    const controller = await esmock('./delete.js')
    const message: any = { command: 'test', data: { } }
    const response = await controller(message)
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('ID: Required')
  })

  it('returns unsuccessful when the id is not a valid uuid', async () => {
    const controller = await esmock('./delete.js')
    const message: any = { command: 'test', data: { ID: 'test' } }
    const response = await controller(message)
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('ID: Invalid')
  })

  it('deletes the requested object', async () => {
    const controller = await esmock('./delete.js', {
      '../../../services/mssql/learning-objects/delete.service.js': {
        default: Sinon.stub().returns(Promise.resolve(1))
      }
    })
    const response = await controller({ command: 'test', data: { ID: uuid() } })
    expect(response.success).to.be.true
  })

  it('returns unsuccessful when the service encounters an error', async () => {
    const controller = await esmock('./delete.js', {
      '../../../services/mssql/learning-objects/delete.service.js': {
        default: Sinon.stub().returns(Promise.reject(new Error('Service Error')))
      }
    })
    const response = await controller({ command: 'test', data: { ID: uuid() } })
    expect(response.success).to.be.false
    expect(response.message).to.equal('Service Error')
  })
})
