import { Request, Response } from 'express'
import SessionEnrollment, { sessionEnrollmentSchema } from '../../../models/session-enrollment.model.js'
import getSession from '../../../services/mssql/learning-context-sessions/get.service.js'
import getSessionEnrollments from '../../../services/mssql/session-enrollments/get.service.js'
import getInstructorIDs from '../../../services/mssql/learning-context-session-instructors/get.service.js'
import getSessionLocation from '../../../services/mssql/locations/get.service.js'
import getLearningContext from '../../../services/mssql/learning-context/get.service.js'
import updateSession from '../../../services/mssql/learning-context-sessions/update.service.js'
import createSessionEnrollment from '../../../services/mssql/session-enrollments/create.service.js'
import logger from '@lcs/logger'
import moment from 'moment-timezone'
import { DB_Errors } from '@lcs/mssql-utility'
import Location from '../../../models/location.model.js'
import getMultipleUsersByIds from '../../../services/mssql/users/get-multiple-by-ids.service.js'
import httpStatus from 'http-status'
import { sendLearnerEnrollmentMessage } from '@tess-f/email/dist/amqp/send-learner-enrollment.js'
import settings from '../../../config/settings.js'
import { sendInstructorEnrollmentMessage } from '@tess-f/email/dist/amqp/send-instructor-enrollment.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import LearningContextSessionInstructorModel from '../../../models/learning-context-session-instructor.model.js'
import sendAdminSessionEnrollments from '../../../services/amqp/email/send-admin-session-enrollments.service.js'
import sendNotification from '../../../services/amqp/notification/send-notification.service.js'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { SessionStatuses } from '@tess-f/sql-tables/dist/lms/session-status.js'

const log = logger.create('Controller-HTTP.create-multiple-learning-context-session-enrollments', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    // check that there is a body  
    const data = z.array(sessionEnrollmentSchema).min(1).parse(req.body)
    const sessionID: string = data[0].SessionID

    let allEnrollmentsForSession, sessionLocation: Location | undefined, sessionLearningContext: LearningContextModel, sessionInstructors: LearningContextSessionInstructorModel[], newSessionEnrollees: string[]

    if (data.filter(enrollment => sessionID === enrollment.SessionID).length !== data.length) {
      log('info', 'Failed to create learning context session due to multiple session IDs in the request.', { req, success: true })
      res.status(httpStatus.BAD_REQUEST).send('Session ID miss match, all enrollments must be for the same session')
    }

    const createdSessionEnrollments = await Promise.all(data.map(async enrollment => {
      const sessionEnrollment = new SessionEnrollment(enrollment)
      sessionEnrollment.fields.CreatedBy = req.session.userId
      sessionEnrollment.fields.CreatedOn = new Date()

      const createdSessionEnrollment = await createSessionEnrollment(sessionEnrollment)
      return createdSessionEnrollment
    }))

    try {
      allEnrollmentsForSession = await getSessionEnrollments(sessionID, undefined)
    } catch (error) {
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        allEnrollmentsForSession = []
      } else {
        throw error
      }
    }

    const session = await getSession(sessionID)
    const startTime = moment.tz(session.fields.StartDate!.toISOString(), session.fields.Timezone!)
    const endTime = moment.tz(session.fields.EndDate!.toISOString(), session.fields.Timezone!)
    const now = moment.tz(Date.now(), session.fields.Timezone!)

    // check if the session has already happened, if so don't send an email
    if (now.isSameOrBefore(startTime)) {
      if (session.fields.LocationID) {
        try {
          sessionLocation = await getSessionLocation(session.fields.LocationID)
        } catch (error) {
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            throw error
          }
        }
      }

      sessionLearningContext = await getLearningContext(session.fields.LearningContextID!, undefined)
      sessionInstructors = await getInstructorIDs(sessionID)
      newSessionEnrollees = createdSessionEnrollments.map(enrollment => enrollment.fields.UserID!)

      const newLearners = await getMultipleUsersByIds(newSessionEnrollees)
      const instructors = await getMultipleUsersByIds(sessionInstructors.map(instructor => instructor.fields.UserID!))
      const instructorEmails = instructors.map(instructor => instructor.Email!).filter(email => email)
      if (instructorEmails.length > 0) {
        try {
          // send emails to each instructor
          await sendInstructorEnrollmentMessage(
            settings.amqp.service_queues.email, {
              to: instructorEmails,
              subject: 'Enrollment Confirmation',
              header: 'Enrollment Confirmation',
              message: {
                sessionTitle: sessionLearningContext.fields.Title!,
                sessionID: session.fields.SessionID!,
                sessionLearnerNames: newLearners.filter(learner => learner.Email).map(record => { return { name: record.FirstName + ' ' + record.LastName, email: record.Email! } }),
                sessionStartDate: startTime.format('dddd, MMMM Do'),
                sessionTime: `${startTime.format('h:mm')} - ${endTime.format('h:mm')} (${endTime.format('z')})`,
                sessionLocation: sessionLocation ? sessionLocation.fields : undefined,
                sessionWebinarInfo:
                {
                  webinarUrl: (session.fields.WebinarURL ?? undefined) as string | undefined,
                  webinarID: (session.fields.WebinarID ?? undefined) as string | undefined,
                  webinarDialInNumber: (session.fields.WebinarDialInNumber ?? undefined) as string | undefined
                }
              }
            },
            settings.amqp.command_timeout
          )
          log('info', 'Successfully emailed instructors about session enrollment', { instructorIds: instructors.map(instructor => instructor.ID), req, count: instructorEmails.length })
        } catch (error) {
          log('error', 'Failed to send enrollment emails to instructors for session', { sessionId: sessionID, errorMessage: getErrorMessage(error), success: false, req })
        }
      } else {
        log('warn', 'No instructor emails found to notify or session enrollment', { instructorIds: instructors.map(instructor => instructor.ID), req })
      }

      const instructorIDs = instructors.map(instructor => instructor.ID!).filter(ID => ID)
      let instructorMessage = `
        <p>The following learners have registered for: ${sessionLearningContext.fields.Title} - ${session.fields.SessionID}.</p>
        <h3>User(s) Registered</h3>
        <ul>${newLearners.map(learner => learner.Email ? `<li><a href="${learner.Email}">${learner.FirstName} ${learner.LastName}</a></li>` : `<li>${learner.FirstName} ${learner.LastName}</li>`)}</ul>
        <h3>Date/Time Information</h3>
        <p>
          <strong>${startTime.format('dddd, MMMM Do')}</strong><br>
          ${startTime.format('h:mm')} - ${endTime.format('h:mm')} (${endTime.format('z')})
        </p>
      `

      if (sessionLocation !== undefined) {
        instructorMessage += `
          <h3>Location Information</h3>
          <p>
            <strong>${sessionLocation.fields.Title}</strong><br>
            ${sessionLocation.fields.AddressLine1}
            ${sessionLocation.fields.AddressLine2 ? sessionLocation.fields.AddressLine2 + '<br>' : ''}
            ${sessionLocation.fields.City}, ${sessionLocation.fields.State} ${sessionLocation.fields.Zip}<br>
            ${sessionLocation.fields.Country}
          </p>
        `
      }

      if (session.fields.WebinarDialInNumber !== undefined || session.fields.WebinarID !== undefined || session.fields.WebinarURL !== undefined) {
        instructorMessage += `
          <h3>Webinar Information</h3>
          <p>
            ${session.fields.WebinarURL ? 'Webinar URL: ' + session.fields.WebinarURL + '<br>' : ''}
            ${session.fields.WebinarID ? 'Webinar/conference ID: ' + session.fields.WebinarID + '<br>' : ''}
            ${session.fields.WebinarDialInNumber ? 'Dial-In Number: ' + session.fields.WebinarDialInNumber : ''}
          </p>
        `
      }
      // send notification to each instructor
      await sendNotification({
        Title: 'Enrollment Confirmation',
        Message: instructorMessage,
        PublishDate: new Date(),
        Everyone: false,
        SystemID: 'lms',
        Priority: 1,
        UserIDs: instructorIDs
      })

      // send email and notifications to administrator 
      await sendAdminSessionEnrollments(sessionLearningContext, session, newLearners, startTime, endTime, sessionLocation)

      const learnerEmails = newLearners.map(user => user.Email).filter(email => email) as string[]

      if (learnerEmails.length > 0) {
        try {
          // send emails to each student
          await sendLearnerEnrollmentMessage(
            settings.amqp.service_queues.email, {
              to: learnerEmails,
              subject: 'Enrollment Confirmation',
              header: 'Enrollment Confirmation',
              message: {
                startDate: session.fields.StartDate!.toString(),
                timezone: session.fields.Timezone!,
                endDate: session.fields.EndDate!.toString(),
                title: sessionLearningContext.fields.Title!,
                location: sessionLocation ? sessionLocation.fields : undefined,
                webinarInfo: session.fields.WebinarDialInNumber && session.fields.WebinarID && session.fields.WebinarURL
                  ? {
                      url: (session.fields.WebinarURL ?? undefined) as string | undefined,
                      id: (session.fields.WebinarID ?? undefined) as string | undefined,
                      dialInNumber: (session.fields.WebinarDialInNumber ?? undefined) as string | undefined
                    }
                  : undefined,
                instructors: instructors.filter(instructor => instructor.Email).map((record) => { return { name: record.FirstName + ' ' + record.LastName, email: record.Email! } })
              }
            },
            settings.amqp.command_timeout
          )
          log('info', 'Successfully notified learner(s) of their session enrollment.', { learnerIds: newLearners.map(learner => learner.ID), req, count: learnerEmails.length })
        } catch (error) {
          log('error', 'Failed to send enrollment emails to newly enrolled users for session', { sessionId: sessionID, errorMessage: getErrorMessage(error), success: false, req })
        }
      } else {
        log('warn', 'Unable to send enrollment notification to learners for session because no email address found', { learners: newLearners.map(learner => learner.ID), sessionID, req })
      }

      const learnerIDs = newLearners.map(user => user.ID).filter(ID => ID) as string[]
      let learnerMessage = `
        <p>Good news! You are now enrolled in ${sessionLearningContext.fields.Title}.</p>
        <h3>Date/Time Information</h3>
        <p>
          <strong>${startTime.format('dddd, MMMM Do')}</strong><br>
          ${startTime.format('h:mm')} - ${endTime.format('h:mm')} (${endTime.format('z')})
        </p>
      `
      if (sessionLocation !== undefined) {
        learnerMessage += `
          <h3>Location Information</h3>
          <p>
            <strong>${sessionLocation.fields.Title}</strong><br>
            ${sessionLocation.fields.AddressLine1}
            ${sessionLocation.fields.AddressLine2 ? sessionLocation.fields.AddressLine2 + '<br>' : ''}
            ${sessionLocation.fields.City}, ${sessionLocation.fields.State} ${sessionLocation.fields.Zip}<br>
            ${sessionLocation.fields.Country}
          </p>
        `
      }

      if (session.fields.WebinarDialInNumber !== undefined || session.fields.WebinarID !== undefined || session.fields.WebinarURL !== undefined) {
        learnerMessage += `
          <h3>Webinar Information</h3>
          <p>
            ${session.fields.WebinarURL ? 'Webinar URL: ' + session.fields.WebinarURL + '<br>' : ''}
            ${session.fields.WebinarID ? 'Webinar/conference ID: ' + session.fields.WebinarID + '<br>' : ''}
            ${session.fields.WebinarDialInNumber ? 'Dial-In Number: ' + session.fields.WebinarDialInNumber : ''}
          </p>
        `
      }

      learnerMessage += `
        <h3>Questions?</h3>
        <p>Contact the instructor(s)</p>
        <ul>${instructors.map(instructor => instructor.Email ? '<li><a href="mailto:' + instructor.Email + '">' + instructor.FirstName + ' ' + instructor.LastName + '</a></li>' : '<li>' +instructor.FirstName + ' ' + instructor.LastName + '</li>')}</ul>
      `

      // send notifications to each student
      await sendNotification({
        Title: 'Enrollment Confirmation',
        Message: learnerMessage,
        PublishDate: new Date(),
        Everyone: false,
        SystemID: 'lms',
        Priority: 1,
        UserIDs: learnerIDs
      })

    } else {
      log('info', 'Bypassed emails and notifications, session is in the past.', { startDate: startTime.format('dddd, MMMM Do'), req })
    }

    // if the session is full mark as such
    if (session.fields.MaxEnrollments && session.fields.MaxEnrollments <= allEnrollmentsForSession.length && session.fields.SessionStatusID! < SessionStatuses.Full) {
      session.fields.SessionStatusID = SessionStatuses.Full
      await updateSession(session)
    }

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, bookmark a course. Note the time. Check the LMS API log for the 'http-create-learning-context-bookmark' label and message indicating successful creation."
    log('info', 'Successfully created learning context session enrollments for session', { success: true, sessionId: sessionID, req })

    res.json(createdSessionEnrollments.map(sessionEnrollment => sessionEnrollment.fields))
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to create learning context session enrollments for session due to invalid data in the request.', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      const errorMessage = getErrorMessage(error)
      if (errorMessage.startsWith('Violation of UNIQUE KEY constraint')) {
        log('warn', 'Failed to create learning context session enrollments for session because it already exists in the database.', { sessionId: req.body[0].SessionID, error, success: false, req })
      } else {
        log('error', 'Failed to create learning context session enrollments.', { error, success: false, req })
      }
  
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
