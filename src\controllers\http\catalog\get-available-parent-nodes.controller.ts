import logger from '@lcs/logger'
import getAvailableParentNodes from '../../../services/mssql/catalog/get-available-parent-nodes.service.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.Get-Available-Parent-Nodes', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const nodes = await getAvailableParentNodes(id)
    log('info', 'Successfully retrieved possible parent nodes for context', { nodeCount: nodes.length, forContext: id, success: true, req })
    res.json(nodes)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Invalid context ID', { contextID: req.params.id, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send('Invalid context ID in request parameters')
    } else {
      log('error', 'Failed to retrieve potential parent nodes for context', { error, req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
