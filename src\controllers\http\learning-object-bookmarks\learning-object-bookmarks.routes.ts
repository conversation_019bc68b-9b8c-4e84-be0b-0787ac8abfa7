import { Request<PERSON><PERSON><PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getController from './get.controller.js'

const router = Router()

router.post('/object-bookmark', createController as RequestHandler)
router.get('/object-bookmark', getController as RequestHandler)
router.delete('/object-bookmark/:userID/:objectID', deleteController as RequestHandler)

export default router
