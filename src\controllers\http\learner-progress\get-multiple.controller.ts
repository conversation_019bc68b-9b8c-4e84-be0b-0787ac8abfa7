import getMulti from '../../../services/mssql/learner-progress/get-multiple.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'
const { INTERNAL_SERVER_ERROR, NOT_FOUND, BAD_REQUEST } = httpStatus

const log = logger.create('Controller-HTTP.get-multi-learner-progress', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, click on a single course. Note the time. Check the LMS API log for the 'http-get-multi-learner-progress' label."

  try {
    const { userID, objectID } = z.object({
      userID: zodGUID,
      objectID: zodGUID
    }).parse(req.params)
    const progresses = await getMulti(userID, objectID)

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, click on a single course. Note the time. Check the LMS API log for the 'http-get-multi-learner-progress' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved learner progress records for user.', { count: progresses.length, userId: userID, learningObjectId: objectID, success: true, req })

    res.json(progresses.map(progress => progress.fields))
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learner progress for the given user and learning object: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learner progress for the given user and learning object because it was not found in the database.', { userId: req.params.userID, objectId: req.params.objectId, success: false, req })
      res.sendStatus(NOT_FOUND)
    } else {
      log('error', 'Failed to get learner progress for the given user and learning object.', { userId: req.params.userID, objectId: req.params.objectId, errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
