import mssql, { DB_Errors, getRows } from '@lcs/mssql-utility'
import LearningElementModel from '../../../models/learning-elements-view.model.js'
import { WidgetLearningContext, WidgetLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/widget-learning-context.js'
import { WidgetLearningObject, WidgetLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/widget-learning-object.js'
import { LearningElement, LearningElementsViewFields, LearningElementsViewName } from '@tess-f/sql-tables/dist/lms/learning-elements-view.js'

export default async function getWidgetSpecifiedContentService (widgetID: string): Promise<LearningElementModel[]> {
  const pool = mssql.getPool()
  let widgetContexts: WidgetLearningContext[] = []

  try {
    widgetContexts = await getRows<WidgetLearningContext>(WidgetLearningContextsTableName, pool.request(), { WidgetID: widgetID })
  } catch (error) {
    if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }

  let widgetObjects: WidgetLearningObject[] = []

  try {
    widgetObjects = await getRows<WidgetLearningObject>(WidgetLearningObjectsTableName, pool.request(), { WidgetID: widgetID })
  } catch (error) {
    if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
      throw error
    }
  }

  const request = pool.request()
  const ids = widgetObjects.map(obj => obj.LearningObjectID).concat(widgetContexts.map(context => context.LearningContextID))
  const conds = ids.map((id, index) => {
    request.input(`id_${index}`, id)
    return `@id_${index}`
  })

  const query = `
    SELECT *
    FROM [${LearningElementsViewName}]
    WHERE [${LearningElementsViewFields.ID}] IN (${conds.join(', ')})
  `

  const records = await request.query<LearningElement>(query)

  const elements = records.recordset.map(record => new LearningElementModel(record))

  // map the orderID's
  elements.forEach(element => {
    if (element.fields.EntityType === 'Learning Context') {
      const widgetContext = widgetContexts.find(context => context.LearningContextID === element.fields.ID)
      if (widgetContext) {
        element.fields.OrderID = widgetContext.OrderID
      }
    } else if (element.fields.EntityType === 'Learning Object') {
      const widgetObject = widgetObjects.find(obj => obj.LearningObjectID === element.fields.ID)
      if (widgetObject) {
        element.fields.OrderID = widgetObject.OrderID
      }
    }
  })

  elements.sort((a, b) => (a.fields.OrderID || 1) > (b.fields.OrderID || 1) ? 1 : -1)

  return elements
}
