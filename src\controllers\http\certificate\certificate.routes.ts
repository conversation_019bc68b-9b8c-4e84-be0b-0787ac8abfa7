import { RequestHand<PERSON>, Router } from 'express'
import issueIltController from './issue-ilt.controller.js'
import getCertificatesController from './get-multiple.controller.js'
import getContextUserCertificatesController from './get-context-user-certificates.controller.js'
import getPaginatedController from './get-paginated.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.post('/issue-ilt-certificate/:sessionID/:userID', checkClaims([Claims.MODIFY_COURSE, Claims.CREATE_COURSE]), issueIltController as RequestHandler)
router.post('/user-context-certificates/:id', checkClaims([Claims.VIEW_CONTENT_OVERVIEW]), getContextUserCertificatesController as RequestHandler)
router.post('/paginated-certificates', getPaginatedController as <PERSON><PERSON><PERSON><PERSON><PERSON>)
// route is not used:
router.get('/user-certificates', getCertificatesController as RequestHandler)

export default router
