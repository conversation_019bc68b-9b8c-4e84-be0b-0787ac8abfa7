import mssql, { updateRow } from '@lcs/mssql-utility'
import SessionEnrollmentModel from '../../../models/session-enrollment.model.js'
import { SessionEnrollment } from '@tess-f/sql-tables/dist/lms/session-enrollment.js'

export default async function (model: SessionEnrollmentModel): Promise<SessionEnrollmentModel> {
  const records = await updateRow<SessionEnrollment>(mssql.getPool().request(), model, {
    UserID: model.fields.UserID,
    SessionID: model.fields.SessionID
  })

  model.importFromDatabase(records[0])
  return model
}
