import { RequestHand<PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getMultipleController from './get-multiple.controller.js'
import getPastController from './get-past.controller.js'
import getSessionsWithProgressController from './get-sessions-with-progress.controller.js'
import getUpcomingController from './get-upcoming.controller.js'
import getController from './get.controller.js'
import isSessionIdUniqueController from './is-session-id-unique.controller.js'
import requestController from './request.controller.js'
import updateController from './update.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.post('/session-id-unique', checkClaims([Claims.CREATE_COURSE, Claims.MODIFY_COURSE]), isSessionIdUniqueController as RequestHandler)
router.post('/learning-context-session', checkClaims([Claims.CREATE_COURSE, Claims.MODIFY_COURSE]), createController as RequestHandler)
router.put('/learning-context-session', checkClaims([Claims.CREATE_COURSE, Claims.MODIFY_COURSE]), updateController as RequestHandler)
router.get('/learning-context-sessions/:id', checkClaims([Claims.CREATE_COURSE, Claims.MODIFY_COURSE, Claims.VIEW_COURSES]), getMultipleController as RequestHandler)
router.get('/learning-context-session/:id', checkClaims([Claims.CREATE_COURSE, Claims.MODIFY_COURSE, Claims.VIEW_COURSES]), getController as RequestHandler)
router.delete('/learning-context-session/:id', checkClaims([Claims.CREATE_COURSE, Claims.MODIFY_COURSE]), deleteController as RequestHandler)
router.post('/request-ilt-session', requestController as RequestHandler)
router.get('/upcoming-learning-context-sessions/:id', getUpcomingController as RequestHandler)
router.get('/past-learning-context-sessions/:id', getPastController as RequestHandler)
router.get('/learning-context-sessions-with-progress/:id', getSessionsWithProgressController as RequestHandler)

export default router
