import { Request<PERSON><PERSON><PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getController from './get.controller.js'

const router = Router()

router.post('/object-favorite', create<PERSON>ontroller as <PERSON>questHandler)
router.get('/object-favorite', getController as <PERSON>questHandler)
router.delete('/object-favorite/:userID/:objectID', deleteController as RequestHandler)

export default router
