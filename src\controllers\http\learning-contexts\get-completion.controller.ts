/* eslint-disable camelcase */
import getCompletion from '../../../services/mssql/learning-context/get-completion.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const { INTERNAL_SERVER_ERROR, NOT_FOUND, BAD_REQUEST } = httpStatus
const log = logger.create('Controller-HTTP.get-learning-context-completion', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, click on a single course. Note the time. Check the LMS API log for the 'http-get-learning-context-completion' label.

  try {
    const { contextID, userID } = z.object({
      contextID: zodGUID,
      userID: zodGUID
    }).parse(req.params)

    const results = await getCompletion(contextID, userID)

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, click on a single course. Note the time. Check the LMS API log for the 'http-get-learning-context-completion' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved learning context completion.', { success: true, req })

    res.json({
      completion: results.completion,
      numberOfObjects: results.numberOfObjects,
      numberOfCompletedObjects: results.numberOfCompletedObjects
    })
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning context completion: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learning context completion for the given user and context because it was not found in the database', { userId: req.params.userID, contextId: req.params.contextID, success: false, req })
      res.sendStatus(NOT_FOUND)
    } else {
      log('error', 'Failed to get learning context completion.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
