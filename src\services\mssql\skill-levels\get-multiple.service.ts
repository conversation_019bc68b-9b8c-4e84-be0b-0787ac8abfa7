import mssql, { getRows } from '@lcs/mssql-utility'
import SkillLevelModel from '../../../models/skill-level.model.js'
import { SkillLevel, SkillLevelsTableName } from '@tess-f/sql-tables/dist/lms/skill-level.js'

export default async function (): Promise<SkillLevelModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<SkillLevel>(SkillLevelsTableName, pool.request())
  return records.map(record => new SkillLevelModel(record))
}
