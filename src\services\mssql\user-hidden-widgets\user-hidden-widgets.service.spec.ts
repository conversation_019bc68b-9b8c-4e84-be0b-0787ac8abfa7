import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import create from './create.service.js'
import createWidgetService from '../widgets/create.service.js'
import UserHiddenWidgetModel from '../../../models/user-hidden-widget.model.js'
import WidgetModel from '../../../models/widget.model.js'
import { UserHiddenWidgetsTableName } from '@tess-f/sql-tables/dist/lms/user-hidden-widget.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'

let widget: WidgetModel

describe('MSSQL User Hidden Widgets', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    // Create a widget record to use for the test
    widget = await createWidgetService(new WidgetModel({
      Title: 'Running user hidden widgets MSSQL Unit Tests',
      Carousel: true,
      Filter: false,
      IsAdmin: true,
      CreatedBy: AdminUserId,
      OrderID: 1150,
      TypeID: 1
    }))
  })

  it('Unit test to get all user ids', async () => {
    const results = await create(new UserHiddenWidgetModel({
      UserID: AdminUserId,
      WidgetID: widget.fields.ID
    }))
    expect(results).to.not.be.eq(undefined)
  })

  after('Delete created objects in SQL', async () => {
    await mssql.getPool().request().query(`
        DELETE FROM [${UserHiddenWidgetsTableName}]
      `)
  })
})
