import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningObjectModel from '../../../models/learning-object.model'
import LearningObjectUserFavoriteModel from '../../../models/learning-object-user-favorite.model'
import LearningObjectUserBookmarkModel from '../../../models/learning-object-user-bookmark.model'
import LearningObjectRatingModel from '../../../models/learning-object-rating.model'

describe('HTTP get controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-objects/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid() })))
            },
            '../../../services/mssql/learning-object-favorites/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectUserFavoriteModel({ UserID: uuid(), LearningObjectID: uuid() })))
            },
            '../../../services/mssql/learning-object-bookmarks/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectUserBookmarkModel({ UserID: uuid(), LearningObjectID: uuid() })))
            },
            '../../../services/mssql/learning-object-ratings/get.service.js': {
                getObjectRatingsForUser: Sinon.stub().returns(Promise.resolve(new LearningObjectRatingModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                contextID: uuid(),
                visibility: 1,
                contextCount: '1',
                rating: '1'
            },
            params: {
                id: uuid()
            }
            




        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })


    it('returns an error if the request params are invalid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-objects/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid() })))
            },
            '../../../services/mssql/learning-object-favorites/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectUserFavoriteModel({ UserID: uuid(), LearningObjectID: uuid() })))
            },
            '../../../services/mssql/learning-object-bookmarks/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectUserBookmarkModel({ UserID: uuid(), LearningObjectID: uuid() })))
            },
            '../../../services/mssql/learning-object-ratings/get.service.js': {
                getObjectRatingsForUser: Sinon.stub().returns(Promise.resolve(new LearningObjectRatingModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                contextID: uuid(),
                visibility: 1,
                contextCount: '1',
                rating: '1'
            },
            params: {
                id: false
            }
            




        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request parameter')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('id')
    })

    it('returns an error if the request body is invalid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-objects/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid() })))
            },
            '../../../services/mssql/learning-object-favorites/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectUserFavoriteModel({ UserID: uuid(), LearningObjectID: uuid() })))
            },
            '../../../services/mssql/learning-object-bookmarks/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectUserBookmarkModel({ UserID: uuid(), LearningObjectID: uuid() })))
            },
            '../../../services/mssql/learning-object-ratings/get.service.js': {
                getObjectRatingsForUser: Sinon.stub().returns(Promise.resolve(new LearningObjectRatingModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                contextID: false,
                visibility: false,
                contextCount: false,
                rating: false
            },
            params: {
                id: uuid()
            }
            




        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request parameter data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('contextCount')
        expect(mocks.res._getData()).include('rating')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-objects/get.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new LearningObjectModel({ ID: uuid() })))
            },
            '../../../services/mssql/learning-object-favorites/get.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new LearningObjectUserFavoriteModel({ UserID: uuid(), LearningObjectID: uuid() })))
            },
            '../../../services/mssql/learning-object-bookmarks/get.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new LearningObjectUserBookmarkModel({ UserID: uuid(), LearningObjectID: uuid() })))
            },
            '../../../services/mssql/learning-object-ratings/get.service.js': {
                getObjectRatingsForUser: Sinon.stub().rejects(Promise.resolve(new LearningObjectRatingModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                contextID: uuid(),
                visibility: 1,
                contextCount: '1',
                rating: '1'
            },
            params: {
                id: uuid()
            }
            




        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })
   
 

})

