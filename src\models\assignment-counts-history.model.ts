import { Table } from '@lcs/mssql-utility'
import { AssignmentCountsHistory, AssignmentCountsHistoryFields, AssignmentCountsHistoryTableName } from '@tess-f/sql-tables/dist/lms/assignment-counts-history.js'

export default class AssignmentCountsHistoryModel extends Table<AssignmentCountsHistory, AssignmentCountsHistory> {
  public fields: AssignmentCountsHistory

  constructor (fields?: AssignmentCountsHistory) {
    super(AssignmentCountsHistoryTableName, [
      AssignmentCountsHistoryFields.CreatedOn,
      AssignmentCountsHistoryFields.Overdue,
      AssignmentCountsHistoryFields.InProgress,
      AssignmentCountsHistoryFields.Completed,
      AssignmentCountsHistoryFields.NotStarted,
      AssignmentCountsHistoryFields.NoContent,
      AssignmentCountsHistoryFields.Active
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: AssignmentCountsHistory): void {
    this.fields = record
  }

  public exportJsonToDatabase (): AssignmentCountsHistory {
    return this.fields
  }
}
