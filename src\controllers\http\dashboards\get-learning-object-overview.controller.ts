import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getService from '../../../services/mssql/learning-objects/get.service.js'
import getObjectViewCount from '../../../services/mssql/learning-object-views/get-counts.service.js'
import { getObjectCompletedCount, getObjectInProgressCount } from '../../../services/mssql/learning-objects/get-completion-counts.service.js'
import getViewHistoryService from '../../../services/mssql/learning-object-views/get-view-history.service.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.get-learning-object-overview', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)

    const obj = await getService(id, undefined, { rating: true })

    let from: Date | undefined, to: Date | undefined
    const filterDates = z.object({
      from: z.coerce.date().optional(),
      to: z.coerce.date().optional()
    }).safeParse(req.query)
    if (filterDates.success) {
      from = filterDates.data.from
      to = filterDates.data.to
    }

    // we need to get specific view counts with filtered dates
    obj.fields.Views = await getObjectViewCount(obj.fields.ID!, from, to)
    obj.fields.Completions = await getObjectCompletedCount(obj.fields.ID!, from, to)
    obj.fields.InProgress = await getObjectInProgressCount(obj.fields.ID!, from, to)
    const ViewHistory = await getViewHistoryService(obj.fields.ID!, from, to)

    log('info', 'Successfully retrieved learning object overview data.', { req, rating: obj.fields.Rating, views: obj.fields.Views, completions: obj.fields.Completions, inProgress: obj.fields.InProgress, viewHistoryCount: ViewHistory.length, success: true })
    res.json({
      element: obj.fields,
      ViewHistory
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning object overview: validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to get learning object overview', { id: req.params.id, success: false, error, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
