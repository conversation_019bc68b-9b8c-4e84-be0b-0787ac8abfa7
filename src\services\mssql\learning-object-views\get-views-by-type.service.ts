import mssql from '@lcs/mssql-utility'
import { LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningObjectUserViewFields, LearningObjectUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-view.js'
import { Request } from 'mssql'

export default async function (from?: Date, to?: Date) {
  const pool = mssql.getPool()
  return await getViewsByType(pool.request(), from, to)
}

async function getViewsByType (request: Request, from?: Date, to?: Date) {
  let query = `
    SELECT COUNT([${LearningObjectUserViewsTableName}].[${LearningObjectUserViewFields.LearningObjectID}]) AS Views, [${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}] 
    FROM [${LearningObjectUserViewsTableName}] INNER JOIN 
      [${LearningObjectsTableName}] ON [${LearningObjectUserViewsTableName}].[${LearningObjectUserViewFields.LearningObjectID}] = [${LearningObjectsTableName}].[${LearningObjectFields.ID}] 
  `

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
    query += `WHERE [${LearningObjectUserViewsTableName}].[${LearningObjectUserViewFields.CreatedOn}] BETWEEN @from AND @to `
  }

  query += `GROUP BY [${LearningObjectsTableName}].[${LearningObjectFields.LearningObjectTypeID}]`

  const result = await request.query<{ Views: number, LearningObjectTypeID: number }>(query)
  return result.recordset
}
