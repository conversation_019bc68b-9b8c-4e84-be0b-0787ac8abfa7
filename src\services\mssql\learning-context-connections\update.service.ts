import mssql, { updateRow } from '@lcs/mssql-utility'
import LearningContextConnectionModel from '../../../models/learning-context-connection.model.js'
import { LearningContextConnection } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'

/**
 * @param {LearningContextConnectionModel} contextConnection
 * @returns {LearningContextConnectionModel}
 */
export default async function (contextConnection: LearningContextConnectionModel): Promise<LearningContextConnectionModel> {
  const pool = mssql.getPool()
  const records = await updateRow<LearningContextConnection>(
    pool.request(),
    contextConnection,
    { ParentContextID: contextConnection.fields.ParentContextID, ConnectedContextID: contextConnection.fields.ConnectedContextID }
  )
  contextConnection.importFromDatabase(records[0])
  return contextConnection
}
