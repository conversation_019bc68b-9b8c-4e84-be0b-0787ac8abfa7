import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextSession from '../../../models/learning-context-session.model.js'
import LearningContextSessionInstructor from '../../../models/learning-context-session-instructor.model.js'


describe('HTTP Create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context-sessions/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSession({})))
            },
            '../../../services/mssql/learning-context-session-instructors/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionInstructor({ SessionID: uuid(), UserID: uuid()})))
            },
            '../../../services/mssql/users/get-multiple-by-ids.service.js': {
                 default: Sinon.stub().returns(Promise.resolve([{ ID: uuid() }]))
                 }
        })

        const mocks = httpMocks.createMocks({
            body: {
                SessionID: uuid(),
                SessionStatusID: 1,
                StartDate: new Date(),
                EndDate: new Date(),
                LearningContextID: uuid(),
                Timezone: 'MST',
                WebinarURL: 'https://example.com',
                Instructors: [{ ID: uuid() }]
            },
            session: { userId: uuid() }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
        expect(mocks.res._getData()).to.exist
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context-sessions/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSession({})))
            },
            '../../../services/mssql/learning-context-session-instructors/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionInstructor({ SessionID: uuid(), UserID: uuid()})))
            },
            '../../../services/mssql/users/get-multiple-by-ids.service.js': {
                 default: Sinon.stub().returns(Promise.resolve([{ ID: uuid() }]))
                 }
        })

        const mocks = httpMocks.createMocks({
            body: {
                SessionID: false,
                SessionStatusID: false,
                StartDate: false,
                EndDate: false,
                LearningContextID: false,
                Timezone: false,
                WebinarURL: false,
                Instructors: false
            },
            session: { userId: uuid() }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('SessionID: Expected string, received boolean')
        expect(mocks.res._getData()).include('SessionStatusID: Expected 1 | 2 | 3 | 4, received boolean')
        expect(mocks.res._getData()).include('LearningContextID: Expected string, received boolean')
        expect(mocks.res._getData()).include('Timezone: Expected string, received boolean')
        expect(mocks.res._getData()).include('WebinarURL: Expected string, received boolean')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context-sessions/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSession({})))
            },
            '../../../services/mssql/learning-context-session-instructors/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionInstructor({ SessionID: uuid(), UserID: uuid()})))
            },
            '../../../services/mssql/users/get-multiple-by-ids.service.js': {
                 default: Sinon.stub().returns(Promise.reject([{ ID: uuid() }]))
                 }
        })

        const mocks = httpMocks.createMocks({
            body: {
                SessionID: uuid(),
                SessionStatusID: 1,
                StartDate: new Date(),
                EndDate: new Date(),
                LearningContextID: uuid(),
                Timezone: 'MST',
                WebinarURL: 'https://example.com',
                Instructors: [{ ID: uuid() }]
            },
            session: { userId: uuid() }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

    


    

})