import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import getObjectViewCount from './get-counts.service.js'
import getUserViewsByType from './get-user-views-by-type.service.js'
import getViewHistory from './get-view-history.service.js'
import getViewsByType from './get-views-by-type.service.js'
import LearningObjectUserViewModel from '../../../models/learning-object-user-view.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { v4 as uuidv4 } from 'uuid'

let learningObject: LearningObject
let learningObjectUserView: LearningObjectUserViewModel

// FIXME: need to update the set up and tear down for these tests
xdescribe('MSSQL Learning Object User Views', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningObject = await addRow<LearningObject>(pool.request(), new LearningObjectModel({
      Title: 'Testing object views',
      Description: 'Testing object views unit test',
      LearningObjectTypeID: LearningObjectTypes.Audio,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      ContentID: uuidv4()
    }))
  })

  it('gets users views by type', async () => {
    const d = learningObjectUserView.fields.CreatedOn!
    d.setDate(d.getDate() - 5)
    const res = await getUserViewsByType(learningObjectUserView.fields.ID!, d, new Date())
    expect(res.length).to.be.gte(0)
  })

  it('gets users views by keyword', async () => {
    const d = learningObjectUserView.fields.CreatedOn!
    d.setDate(d.getDate() - 5)
    const res = await getObjectViewCount(learningObject.ID!, d, new Date())
    expect(res).to.be.gte(0)
  })

  it('get learning object views by type', async () => {
    const d = learningObjectUserView.fields.CreatedOn!
    d.setDate(d.getDate() - 5)
    const res = await getViewsByType(d, new Date())
    expect(res.length).to.be.gte(0)
  })

  it('get learning object views by type', async () => {
    const d = learningObjectUserView.fields.CreatedOn!
    d.setDate(d.getDate() - 5)
    const res = await getViewHistory(learningObject.ID!, d, new Date())
    expect(res.length).to.be.gte(0)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningObject>(pool.request(), LearningObjectsTableName, { ID: learningObject.ID })
  })
})
