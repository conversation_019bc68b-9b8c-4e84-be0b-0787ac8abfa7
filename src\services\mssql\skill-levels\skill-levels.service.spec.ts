import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'

import SkillLevel from '../../../models/skill-level.model.js'
import create from './create.service.js'
import del from './delete.service.js'
import update from './update.service.js'
import get from './get.service.js'
import getMulti from './get-multiple.service.js'
import { v4 as uuidv4 } from 'uuid'

let skillLevel = new SkillLevel({ Name: uuidv4() })
const skillLevelUpdated = new SkillLevel({ Name: uuidv4() })

describe('Service [MSSQL]: Skill Level', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
  })

  it('creates a skill level', async () => {
    const created = await create(skillLevel)
    expect(created.fields.ID).to.exist
    expect(created.fields.Name).to.eq(skillLevel.fields.Name)
    skillLevel = created
    skillLevelUpdated.fields.ID = created.fields.ID
  })

  it('updates a skill level', async () => {
    const updated = await update(skillLevelUpdated)
    expect(updated.fields.Name).to.eq(skillLevelUpdated.fields.Name)
  })

  it('gets a skill level', async () => {
    const _skillLevel = await get(skillLevel.fields.ID!)
    expect(_skillLevel.fields.ID).to.equal(skillLevel.fields.ID)
  })

  it('deletes a skill level', async () => {
    await del(skillLevel.fields.ID!)
  })

  it('gets multiple skill levels', async () => {
    const skillLevels = await getMulti()
    expect(skillLevels.length).to.be.gt(1)
  })
})
