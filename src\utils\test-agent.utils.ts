import httpMocks from 'node-mocks-http'
import { Request } from 'express'

export function createRequestWithHeaders (options: httpMocks.RequestOptions | undefined) {
  const req = httpMocks.createRequest(options)
  module.exports.fillRequestHeaders(req)
  return req
}

// Populates the given request headers with test content (intended to satisfy the STIG logger during unit testing).
export function fillRequestHeaders (req: Request) {
  req.headers['user-agent'] = '<test user-agent header>'
  req.headers.referer = '<test referer header>'
}
