import mssql from '@lcs/mssql-utility'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { Request } from 'mssql'

export default async function (sessionID: string): Promise<number> {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    // Delete the Sessions
    const rowsAffected = await deleteSessions(transaction.request(), sessionID)

    await transaction.commit()

    return rowsAffected
  } catch (err) {
    if (!rolledBack) await transaction.rollback()
    throw err
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}

async function deleteSessions (request: Request, sessionID: string): Promise<number> {
  request.input('ID', sessionID)

  const query = `DELETE FROM [${LearningContextSessionsTableName}] WHERE [${LearningContextSessionFields.ID}] = @ID;`

  const results = await request.query(query)

  return results.rowsAffected[0]
}
