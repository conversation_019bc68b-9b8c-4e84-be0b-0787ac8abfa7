import { RequestHand<PERSON>, Router } from 'express'
import createController from './create.controller.js'
import downloadController from './download.controller.js'
import getAllForIltController from './get-all-for-ilt.controller.js'
import getMultipleController from './get-multiple.controller.js'
import getController from './get.controller.js'
import resumeLearningController from './resume-learning.controller.js'
import updateController from './update.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.post('/learner-progress', createController as RequestHandler)
router.put('/learner-progress/:id', updateController as RequestHandler)
router.get('/latest-learner-progress/:userID/:objectID', getController as RequestHandler)
router.get('/resume-learning/:userID', resumeLearningController as RequestHandler)
router.get('/learner-progress/:userID/:objectID', getMultipleController as RequestHandler)
router.get('/download-learner-data', checkClaims([Claims.VIEW_ADMIN_OVERVIEW]), downloadController as RequestHandler)
router.get('/ilt-user-progress/:id', getAllForIltController as RequestHandler)

export default router
