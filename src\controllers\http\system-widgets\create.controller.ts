import logger from '@lcs/logger'
import { Request, Response } from 'express'
import createSystemWidget from '../../../services/mssql/system-widgets/create.service.js'
import createWidgetService from '../../../services/mssql/widgets/create.service.js'
import createSystemWidgetGroup from '../../../services/mssql/system-widget-groups/create.service.js'
import SystemWidget, { createSystemWidgetSchema } from '../../../models/system-widget.model.js'
import SystemWidgetGroup from '../../../models/system-widget-group.model.js'
import Widget from '../../../models/widget.model.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { ZodError } from 'zod'

const log = logger.create('Controller-HTTP.create-system-widgets', httpLogTransformer)

export default async function createSystemWidgetController (req: Request, res: Response) {
  try {
    const systemWidget = new SystemWidget(createSystemWidgetSchema.parse(req.body))

    const created = await createSystemWidget(systemWidget)

    log('info', 'Successfully created system widget', { id: created.fields.ID, success: true, req })

    if (systemWidget.fields.Groups && systemWidget.fields.Groups.length > 0 && !systemWidget.fields.Everyone) {
      await Promise.all(systemWidget.fields.Groups.map(async group => {
        await createSystemWidgetGroup(new SystemWidgetGroup({
          GroupID: group.ID,
          SystemWidgetID: created.fields.ID
        }))
        log('info', 'Successfully added group as audience to system widget', { groupId: group.ID, systemWidget: created.fields.ID, success: true, req })
      }))
      created.fields.Groups = systemWidget.fields.Groups
    }

    // create the widgets
    created.fields.Widgets = []
    await Promise.all(systemWidget.fields.Widgets!.map(async widget => {
      widget.CreatedBy = created.fields.ID
      const createdWidget = await createWidgetService(new Widget(widget))
      log('info', 'Successfully created system widget', { id: createdWidget.fields.ID, req })
      created.fields.Widgets?.push(createdWidget.fields)
    }))

    res.json(created.fields)
  } catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Failed to create system widget: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to create system widget', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
