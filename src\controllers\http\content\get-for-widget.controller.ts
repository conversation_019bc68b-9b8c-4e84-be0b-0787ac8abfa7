/* eslint no-unneeded-ternary: "off" */
import logger from '@lcs/logger'
import { DB_Errors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import LearningElement from '../../../models/learning-elements-view.model.js'
import getWidget from '../../../services/mssql/widgets/get.service.js'
import searchService from '../../../services/mssql/content/search.service.js'
import getWidgetKeywordsService from '../../../services/mssql/widgets/get-keywords.service.js'
import getWidgetSpecifiedContentService from '../../../services/mssql/content/get-widget-specified-content.service.js'
import extrasMapper from '../../../mappers/learning-element-extras.mapper.js'
import { WidgetTypes } from '@tess-f/sql-tables/dist/lms/widget-type.js'
import httpStatus from 'http-status'
import searchILTContent from '../../../services/mssql/content/search-content-with-upcoming-dates.service.js'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-content-for-widget', httpLogTransformer)

export default async function getContentForWidgetController (req: Request, res: Response) {
  try {
    // get the widget
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const widget = await getWidget(id)

    let content: LearningElement[] = []

    if (widget.fields.Filter) {
      // get the widgets keywords if it has any
      try {
        widget.fields.Keywords = await getWidgetKeywordsService(widget.fields.ID!)
      } catch (error) {
        if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
          widget.fields.Keywords = []
        } else {
          const errorMessage = getErrorMessage(error)
          log('error', 'Failed to get keywords for the widget', { widgetId: widget.fields.ID, errorMessage, success: false, req })
          throw error
        }
      }

      if (widget.fields.TypeID !== WidgetTypes.UpcomingDate) {
        // this is a standard content search
        content = (await searchService(
          0,
          widget.fields.MaxItemCount ? widget.fields.MaxItemCount : 25,
          widget.fields.Search ? widget.fields.Search : undefined,
          {
            labels: widget.fields.ContextLabels ? widget.fields.ContextLabels : undefined,
            objectTypeIds: widget.fields.ObjectTypes ? widget.fields.ObjectTypes : undefined,
            keywords: widget.fields.Keywords,
            myLearning: widget.fields.MyLearning ? widget.fields.MyLearning : undefined,
            userID: widget.fields.MyLearning ? req.session.userId : undefined
          }
        )).elements
      } else {
        // this is a modified search that takes into account the number of upcoming sessions a course has
        content = (await searchILTContent(
          widget.fields.MaxItemCount ? widget.fields.MaxItemCount : 25,
          widget.fields.Search ? widget.fields.Search : undefined,
          {
            labels: widget.fields.ContextLabels ? widget.fields.ContextLabels : undefined,
            sortColumn: widget.fields.SortBy ? widget.fields.SortBy : undefined,
            sortDirection: widget.fields.SortDirection ? widget.fields.SortDirection : undefined,
            keywords: widget.fields.Keywords,
            myLearning: widget.fields.MyLearning ? widget.fields.MyLearning : undefined,
            userID: widget.fields.MyLearning ? req.session.userId : undefined
          }
        )).elements
      }
    } else {
      // this widget has content specified by the user
      content = await getWidgetSpecifiedContentService(widget.fields.ID!)
    }

    if (widget.fields.TypeID === WidgetTypes.LargeCard || widget.fields.TypeID === WidgetTypes.Table) {
      // only run the extras mapper if we are adding extras
      await extrasMapper(content, req.session.userId, {
        rating: widget.fields.TypeID === WidgetTypes.LargeCard,
        bookmark: widget.fields.TypeID === WidgetTypes.LargeCard,
        favorite: widget.fields.TypeID === WidgetTypes.LargeCard,
        keywords: (widget.fields.TypeID === WidgetTypes.Table && widget.fields.Columns && widget.fields.Columns.includes('Keywords')) ?? false,
        prerequisites: (widget.fields.TypeID === WidgetTypes.Table && widget.fields.Columns && widget.fields.Columns.includes('Prerequisites')) ?? false,
        sessions: (widget.fields.TypeID === WidgetTypes.Table && widget.fields.Columns && widget.fields.Columns.includes('Upcoming Sessions')) ?? false,
        duration: widget.fields.TypeID === WidgetTypes.LargeCard || ((widget.fields.TypeID === WidgetTypes.Table && widget.fields.Columns && widget.fields.Columns.includes('Estimated Time')) ?? false),
        completion: widget.fields.TypeID === WidgetTypes.LargeCard
      })
    }

    res.json(content.map(element => element.fields))
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get content for widget: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameters, '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get content for widget, not found in the database', { success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      log('error', 'Failed to get content for widget', {  errorMessage, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
