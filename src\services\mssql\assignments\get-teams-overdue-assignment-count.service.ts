import mssql from '@lcs/mssql-utility'
import { UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { UserOverdueAssignmentFields, UserOverdueAssignmentsViewName } from '@tess-f/sql-tables/dist/lms/user-overdue-assignments-view.js'

export default async function getTeamsOverdueAssignmentCount (managerId: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('managerId', managerId)

  const result = await request.query<{ OverdueCount: number }>(`
    SELECT COUNT(*) AS [OverdueCount]
    FROM [${UserOverdueAssignmentsViewName}]
    WHERE [${UserOverdueAssignmentFields.UserID}] IN (
      SELECT [${UserFields.ID}]
      FROM [${UserTableName}]
      WHERE [${UserFields.ManagerID}] = @managerId
    )
  `)

  return result.recordset[0].OverdueCount
}
