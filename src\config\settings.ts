import { readFileSync } from 'fs'
import minimist from 'minimist'
import <PERSON><PERSON><PERSON> from 'prettyjson'
import dotenv from 'dotenv'
import path from 'path'
import tessConfigLoader from '@tess-f/shared-config'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename)

if (process.env.LOAD_DEV_ENV === 'true') {
  const devEnvPath = path.join(__dirname, '/environments/dev.env')
  dotenv.config({ path: devEnvPath })
  console.log(`dev environment loaded\t\t${devEnvPath}`)
}

// fill in defaults
const defaultEnvPath = path.join(__dirname, '/environments/defaults.env')
dotenv.config({ path: defaultEnvPath })
console.log(`default environment loaded\t\t${defaultEnvPath}`)

// Load TESS shared config
const configPaths = process.env.CONFIG_PATHS ? process.env.CONFIG_PATHS.split(',') : []
const tessConfig = tessConfigLoader.load(configPaths)
console.log(`TESS shared config loaded\t\t${configPaths}`)

const args = minimist(process.argv.slice(2))
const system = process.env.SYSTEM ?? args.s

// http://patorjk.com/software/taag/#p=display&f=Small&t=LMS (trying to save vertical space in console)
const asciiLogo = readFileSync(path.join(__dirname, '/ascii-logo.txt')).toString()

// certificate file path
const certificateBackgroundPath = process.env.CERTIFICATE_BACKGROUND_PATH ? path.join(__dirname, process.env.CERTIFICATE_BACKGROUND_PATH) : path.join(__dirname, '/../images/certificate.png')

export default {
  system,
  system_display_name: process.env.SYSTEM_DISPLAY_NAME ?? 'LMS Service',
  certificate_background: 'data:image/png;base64,' + readFileSync(path.resolve(certificateBackgroundPath)).toString('base64'),

  server: {
    version: '2.0.0',
    log_all_requests: process.env.LOG_ALL_REQUESTS === 'true',
    timeout: process.env.SERVER_TIMEOUT ? parseInt(process.env.SERVER_TIMEOUT) : 240000,
    port: process.env.SERVER_PORT ? parseInt(process.env.SERVER_PORT) : 8080,
    address: process.env.SERVER_ADDRESS ?? 'localhost',
    root: process.env.ROOT_PATH ?? '/',
    api: process.env.ROOT_API_PATH ?? '/api/'
  },

  appConfig: {
    chartTheme: process.env.CHART_THEME ?? 'picnic',
    homeImage: process.env.HOME_IMAGE ?? 'files/public/header-images/default-home.jpg'
  },

  logger: {
    level: tessConfig.logger.level,
    name: process.env.LOG_NAME,
    useConsole: tessConfig.logger.useConsole,
    useElasticsearch: tessConfig.logger.useElasticsearch,
    noColor: tessConfig.logger.noColor,
    elasticsearch: {
      index: process.env.ELASTICSEARCH_INDEX ?? 'learning-mgmt-system',
      options: {
        node: tessConfig.elasticsearch.host,
        auth: tessConfig.elasticsearch.elasticPassword
          ? {
              username: 'elastic',
              password: tessConfig.elasticsearch.elasticPassword
            }
          : undefined
      }
    }
  },
  defaultPromiseTimeout: process.env.PROMISE_TIMEOUT ? parseInt(process.env.PROMISE_TIMEOUT) : 10000, // 10 seconds
  amqp: {
    rpc_timeout: tessConfig.rabbitmq.remoteProcedureCalls.timeoutMilli,
    queue: tessConfig.rabbitmq.remoteProcedureCalls.lms.queue,
    config: {
      protocol: tessConfig.rabbitmq.protocol,
      hostname: tessConfig.rabbitmq.host,
      port: tessConfig.rabbitmq.port,
      username: tessConfig.rabbitmq.username,
      password: tessConfig.rabbitmq.password
    },
    user_exchange: {
      name: tessConfig.rabbitmq.exchanges.user.name,
      routes: tessConfig.rabbitmq.exchanges.user.routes,
      subscription_queue: process.env.USER_EXCHANGE_SUBSCRIPTION_QUEUE ?? 'lms_user-exchange'
    },
    system_exchange: {
      name: tessConfig.rabbitmq.exchanges.system.name,
      bound_routes: tessConfig.rabbitmq.exchanges.system.routes,
      subscription_queue: process.env.SYSTEM_EXCHANGE_SUBSCRIPTION_QUEUE ?? 'lms_system-exchange'
    },
    lrs_exchange: {
      name: tessConfig.rabbitmq.exchanges.learningRecordStore.name,
      bound_routes: ['created'],
      subscription_queue: process.env.LRS_EXCHANGE_SUBSCRIPTION_QUEUE ?? 'lms_LRS-exchange'
    },
    service_queues: {
      identity_mgmt: tessConfig.rabbitmq.remoteProcedureCalls.identityManagement.queue,
      fds: tessConfig.rabbitmq.remoteProcedureCalls.fileDelivery.queue,
      email: tessConfig.rabbitmq.remoteProcedureCalls.email.queue,
      system_config: tessConfig.rabbitmq.remoteProcedureCalls.systemConfig.queue,
      lrs: tessConfig.rabbitmq.remoteProcedureCalls.learningRecordStore.queue,
      system: tessConfig.rabbitmq.remoteProcedureCalls.systemConfig.queue,
      notification: tessConfig.rabbitmq.remoteProcedureCalls.notifications.queue,
      objectiveConnect: tessConfig.rabbitmq.remoteProcedureCalls.objectiveConnections.queue,
      evaluations: tessConfig.rabbitmq.remoteProcedureCalls.evaluationAuthoring.queue
    },
    service_commands: {
      evaluations: tessConfig.rabbitmq.remoteProcedureCalls.evaluationAuthoring.commands
    },
    command_timeout: 10000 // 10 seconds
  },
  mail: {
    cc_certificates_to_admin: process.env.CC_CERTS_TO_ADMIN ? Boolean(process.env.CC_CERTS_TO_ADMIN) : false,
    send_enrollments_to_admin: process.env.ADMIN_ENROLLMENT_NOTIFICATION ? Boolean(process.env.ADMIN_ENROLLMENT_NOTIFICATION) : false
  },
  redis: {
    url: tessConfig.redis.host,
    password: tessConfig.redis.password,
    lmsLearnerProgressTimerDatabase: tessConfig.redis.databases.lmsProgressTimers
  },
  sessionAuthority: {
    redisUrl: tessConfig.redis.host,
    redisPassword: tessConfig.redis.password,
    redisSessionDatabase: tessConfig.redis.databases.sessions,
    redisSessonLookupDatabase: tessConfig.redis.databases.sessionLookup,
    jwtSecret: tessConfig.security.jwtSecret,

    cookieSecret: tessConfig.security.cookieSecret,
    cookieSecure: tessConfig.security.cookieSecure,
    cookieSigned: tessConfig.security.cookieSigned,
    bypass: process.env.SESSION_AUTHORITY_BYPASS === 'true',
    options: {
      hmacSha256Path: tessConfig.security.hmacSha256Path,
      jwtDefaultExpiresIn: tessConfig.security.session.expiresInMilli,
    },
    checkIp: process.env.CHECK_SESSION_IP === 'true'
  },
  mssql: {
    forceEncrypted: tessConfig.microsoftSql.forceEncrypted,
    streamChunkSize: tessConfig.microsoftSql.streamChunkSize,
    connectionConfig: {
      user: tessConfig.microsoftSql.username,
      password: tessConfig.microsoftSql.password,
      server: tessConfig.microsoftSql.host,
      database: tessConfig.microsoftSql.database,
      port: tessConfig.microsoftSql.port,
      debug: tessConfig.microsoftSql.debug,
      requestTimeout: tessConfig.microsoftSql.requestTimeout,
      connectionTimeout: tessConfig.microsoftSql.connectionTimeout,
      options: {
        encrypt: tessConfig.microsoftSql.encrypt,
        trustedConnection: tessConfig.microsoftSql.trustedConnection,
        enableArithAbort: true,
        trustServerCertificate: tessConfig.microsoftSql.trustServerCertificate
      },
      pool: {
        max: tessConfig.microsoftSql.maxPoolSize,
        min: tessConfig.microsoftSql.minPoolSize,
        idleTimeoutMillis: tessConfig.microsoftSql.idleTimeoutMillisPool
      }
    }
  },

  assignments: {
    requireDueDates: process.env.REQUIRE_ASSIGNMENT_DUE_DATE ? Boolean(process.env.REQUIRE_ASSIGNMENT_DUE_DATE) : false
  },

  print: function () {
    const _sanitizedCopy = JSON.parse(JSON.stringify(this))

    _sanitizedCopy.sessionAuthority.jwtSecret = '<hidden>'
    _sanitizedCopy.sessionAuthority.cookieSecret = '<hidden>'
    _sanitizedCopy.mssql.connectionConfig.password = '<hidden>'
    _sanitizedCopy.certificate_background = '<hidden>'
    _sanitizedCopy.redis.password = '<hidden>'
    _sanitizedCopy.amqp.config.password = '<hidden>'
    if (this.logger.elasticsearch.options.auth) {
      _sanitizedCopy.logger.elasticsearch.options.auth!.password = '<hidden>'
    }
    console.log(asciiLogo)
    console.log('--------------------')
    console.log(prettyjson.render(_sanitizedCopy))
    console.log('--------------------')
  }

}
