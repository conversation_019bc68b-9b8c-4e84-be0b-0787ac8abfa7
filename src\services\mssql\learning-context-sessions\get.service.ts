import mssql, { getRows } from '@lcs/mssql-utility'
import { LearningContextSession, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'

export default async function (sessionID: string): Promise<LearningContextSessionModel> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextSession>(LearningContextSessionsTableName, pool.request(), { ID: sessionID })
  return new LearningContextSessionModel(records[0])
}

export async function getSessionsForContext (contextID: string): Promise<LearningContextSessionModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextSession>(LearningContextSessionsTableName, pool.request(), { LearningContextID: contextID })
  return records.map(record => new LearningContextSessionModel(record))
}
