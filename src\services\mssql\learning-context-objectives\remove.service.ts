import logger from '@lcs/logger'
import mssql, { deleteRow } from '@lcs/mssql-utility'
import { LearningContextObjective, LearningContextObjectiveTableName } from '@tess-f/sql-tables/dist/lms/learning-context-objective.js'
import removeObjectiveConnection from '@tess-f/objectives/dist/amqp/remove-connection.js'
import settings from '../../../config/settings.js'
import { Request } from 'mssql'

const log = logger.create('Service-MSSQL.remove-learning-context-objective')

export default async function removeLearningContextObjective (contextId: string, objectiveId: string, request?: Request): Promise<void> {
  if (request === undefined) {
    request = mssql.getPool().request()
  }

  const removedCount = await deleteRow<LearningContextObjective>(request, LearningContextObjectiveTableName, { LearningContextId: contextId, ObjectiveId: objectiveId })

  if (removedCount > 0) {
    try {
      await removeObjectiveConnection(
        settings.amqp.service_queues.objectiveConnect,
        {
          ObjectiveId: objectiveId,
          ReferenceId: contextId,
          SystemId: 'lms'
        },
        settings.amqp.rpc_timeout
      )
    } catch (error) {
      log('error', 'Failed to remove connection from objectives app', { success: false })
    }
  }
}
