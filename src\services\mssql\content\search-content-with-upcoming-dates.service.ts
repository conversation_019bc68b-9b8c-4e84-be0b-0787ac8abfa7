import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { LearningElement, LearningElementsViewFields, LearningElementsViewName } from '@tess-f/sql-tables/dist/lms/learning-elements-view.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import LearningElementModel from '../../../models/learning-elements-view.model.js'
import { isValidString, isSortDirectionValid } from '@tess-f/backend-utils/validators'
import getCompletedContextsService from '../my-learning/get-completed-contexts.service.js'
import getInprogressContextService from '../my-learning/get-inprogress-context.service.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextKeywordFields, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { SessionEnrollmentFields, SessionEnrollmentsTableName } from '@tess-f/sql-tables/dist/lms/session-enrollment.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningContextUserFavoriteFields, LearningContextUserFavoritesTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-favorite.js'
import { LearningContextUserBookmarkFields, LearningContextUserBookmarksTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-bookmark.js'
import { UserAssignedMultiSessionCoursesFields, UserAssignedMultiSessionCoursesTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-multi-session-course.js'
import { SessionStatuses } from '@tess-f/sql-tables/dist/lms/session-status.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'

/**
 * Searches learning elements
 *  Only returns visible contexts (not hidden)
 * @param {number} limit - the number of records to limit the query to
 * @param {string} search - the terms to search for (title, descriptions)
 * @param {{
 *  labels: string[],
 *  contextTypeIds: number[],
 *  keywords: string[],
 *  sortColumn: string,
 *  sortDirection: string
 * }} filters - query filters
 * @returns {{ totalRecords: number, elements: LearningElementModel[] }}
 */
export default async function (limit: number = 10, search?: string, filters?: { labels?: string[], keywords?: string[], sortColumn?: string, sortDirection?: string, myLearning?: string, userID?: string }): Promise<{ totalRecords: number, elements: LearningElementModel[] }> {
  const request = mssql.getPool().request()

  // build query
  let query = `
    SELECT [${LearningElementsViewName}].*, [TotalRecords] = COUNT(*) OVER(), SUM((CASE WHEN [${LearningContextSessionsTableName}].[${LearningContextSessionFields.ID}] IS NOT NULL THEN 1 ELSE 0 END)) AS [SessionCount]
    FROM [${LearningElementsViewName}]
      LEFT JOIN [${LearningContextSessionsTableName}] ON [${LearningContextSessionsTableName}].[${LearningContextSessionFields.LearningContextID}] = [${LearningElementsViewName}].[${LearningElementsViewFields.ID}] AND [${LearningContextSessionsTableName}].[${LearningContextSessionFields.SessionStatusID}] = ${SessionStatuses.Open}
    WHERE [${LearningElementsViewFields.ContextTypeID}] = ${LearningContextTypes.Course}
    AND [${LearningElementsViewFields.CourseTypeID}] = ${CourseTypes.InstructorLed}
  `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [LearningElementsViewFields.Title, LearningElementsViewFields.Description], 'any')}) `
  }

  if (filters) {
    if (filters.labels && filters.labels.length > 0) {
      const conditions = filters.labels.map((label, index) => {
        request.input(`label_${index}`, label)
        return `@label_${index}`
      })
      query += `AND ([${LearningElementsViewName}].[${LearningElementsViewFields.ID}] IN (
        SELECT [${LearningContextTableName}].[${LearningContextFields.ID}]
        FROM [${LearningContextTableName}]
        WHERE [${LearningContextFields.Label}] IN (${conditions.join(', ')})
      ))`
    }

    if (filters.keywords && filters.keywords.length > 0) {
      const conditions = filters.keywords.map((key, index) => {
        request.input(`key_${index}`, key)
        return `@key_${index}`
      })
      query += `
        AND ([${LearningElementsViewName}].[${LearningElementsViewFields.ID}] IN (
          SELECT [${LearningContextKeywordFields.LearningContextID}]
          FROM [${LearningContextKeywordsTableName}]
          WHERE [${LearningContextKeywordFields.Keyword}] IN (${conditions.join(', ')})
        ))
      `
    }
  }

  if (filters?.myLearning && filters.userID) {
    request.input('userId', filters.userID)
    switch (filters.myLearning.toLowerCase()) {
      case 'enrolled':
        // get courses that the user is enrolled in
        query += `
          AND ([${LearningElementsViewName}].[${LearningElementsViewFields.ID}] IN (
            SELECT [${LearningContextSessionFields.LearningContextID}]
            FROM [${LearningContextSessionsTableName}]
            WHERE [${LearningContextSessionsTableName}].[${LearningContextSessionFields.ID}] IN (
              SELECT [${SessionEnrollmentFields.SessionID}]
              FROM [${SessionEnrollmentsTableName}]
              WHERE [${SessionEnrollmentFields.UserID}] = @userID
            ) AND [${LearningContextSessionFields.ID}] NOT IN (
              SELECT [${LearnerProgressFields.LearningContextSessionID}]
              FROM [${LearnerProgressTableName}]
              WHERE [${LearnerProgressFields.UserID}] = @userID
              AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.fail}
              AND [${LearnerProgressFields.LearningContextSessionID}] IS NOT NULL
            )
          ))
        `
        break
      case 'favorites':
        query += `
          AND ([${LearningElementsViewName}].[${LearningElementsViewFields.ID}] IN (
            SELECT [${LearningContextUserFavoriteFields.LearningContextID}]
            FROM [${LearningContextUserFavoritesTableName}]
            WHERE [${LearningContextUserFavoriteFields.UserID}] = @userID
          ))
        `
        break
      case 'my plan':
        query += `
          AND ([${LearningElementsViewName}].[${LearningElementsViewFields.ID}] IN (
            SELECT [${LearningContextUserBookmarkFields.LearningContextID}]
            FROM [${LearningContextUserBookmarksTableName}]
            WHERE [${LearningContextUserBookmarkFields.UserID}] = @userID
          ))
        `
        break
      case 'in progress': {
        const ids = (await getInprogressContextService(filters.userID)).map(context => context.fields.ID!).map((id, index) => {
          request.input(`in_progress_${index}`, id)
          return `@in_progress_${index}`
        })
        if (ids.length <= 0) {
          // if the user is filtering for in progress an none are found return empty
          return {
            totalRecords: 0,
            elements: []
          }
        }
        query += `
          AND ([${LearningElementsViewName}].[${LearningElementsViewFields.ID}] IN (${ids.join(', ')}))
        `
        break
      }
      case 'assigned':
        query += `
          AND ([${LearningElementsViewName}].[${LearningElementsViewFields.ID}] IN (
            SELECT [${UserAssignedMultiSessionCoursesFields.ForContextExamID}]
            FROM [${UserAssignedMultiSessionCoursesTableName}]
            WHERE [${UserAssignedMultiSessionCoursesFields.UserID}] = @userID
            AND [${UserAssignedMultiSessionCoursesFields.LessonStatusID}] < ${LessonStatuses.fail}
            AND [${UserAssignedMultiSessionCoursesFields.Deleted}] = 0
            AND [${UserAssignedMultiSessionCoursesFields.Completed}] = 0
            AND [${UserAssignedMultiSessionCoursesFields.ForContextID}] IS NOT NULL
          ) OR [${LearningElementsViewName}].[${LearningElementsViewFields.ID}] IN (
            SELECT [${UserAssignedMultiSessionCoursesFields.LearningContextID}]
            FROM [${UserAssignedMultiSessionCoursesTableName}]
            WHERE [${UserAssignedMultiSessionCoursesFields.UserID}] = @userID
            AND [${UserAssignedMultiSessionCoursesFields.LessonStatusID}] < ${LessonStatuses.fail}
            AND [${UserAssignedMultiSessionCoursesFields.Deleted}] = 0
            AND [${UserAssignedMultiSessionCoursesFields.Completed}] = 0
            AND [${UserAssignedMultiSessionCoursesFields.ForContextID}] IS NULL
          ))
        `
        break
      case 'completed': {
        const ids = (await getCompletedContextsService(filters.userID)).map(context => context.fields.ID!).map((id, index) => {
          request.input(`completed_${index}`, id)
          return `@completed_${index}`
        })
        if (ids.length <= 0) {
          // if the user filtering for completed and none are found return empty
          return {
            totalRecords: 0,
            elements: []
          }
        }
        query += `
          AND ([${LearningElementsViewName}].[${LearningElementsViewFields.ID}] IN (${ids.join(', ')}))
        `
        break
      }
    }
  } else {
    query += `AND ([${LearningElementsViewFields.VisibilityID}] = ${Visibilities.Browsable} OR [${LearningElementsViewFields.VisibilityID}] = ${Visibilities.Obsolete}) `
  }

  request.input('limit', limit)

  query += `
    GROUP BY [${LearningElementsViewName}].[${LearningElementsViewFields.ID}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.Title}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.Description}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.LearningObjectTypeID}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.VisibilityID}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.EntityType}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.ContentID}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.Image}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.CourseTypeID}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.ContextTypeID}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.CreatedOn}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.Rating}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.RatingCount}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.views}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.Label}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.CreatedBy}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.ModifiedBy}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.ModifiedOn}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.Credits}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.SkillLevelID}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.MinutesToComplete}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.URL}]
      ,[${LearningElementsViewName}].[${LearningElementsViewFields.CourseID}]
  `

  if (filters && filters.sortColumn && isValidString(filters.sortColumn, [LearningElementsViewFields.CreatedOn, LearningElementsViewFields.Rating, LearningElementsViewFields.views]) &&
      filters.sortDirection && isSortDirectionValid(filters.sortDirection)) {
    query += `ORDER BY [SessionCount] DESC, [${filters.sortColumn}] ${filters.sortDirection} `
  } else {
    query += `ORDER BY [SessionCount] DESC, [${LearningElementsViewFields.CreatedOn}] DESC, [${LearningElementsViewFields.Title}] ASC `
  }

  query += `
    OFFSET 0 ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<LearningElement & { TotalRecords: number }>(query)

  // return the results
  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    elements: results.recordset.map(record => new LearningElementModel(record))
  }
}
