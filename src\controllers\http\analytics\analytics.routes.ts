import { RequestHand<PERSON>, Router } from 'express'
import getCourseReportController from './get-course-report.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.get('/download-course-completion', checkClaims([Claims.VIEW_ADMIN_OVERVIEW]), getCourseReportController as RequestHandler)

export default router
