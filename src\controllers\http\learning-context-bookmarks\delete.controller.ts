import del from '../../../services/mssql/learning-context-bookmarks/delete.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NOT_FOUND, NO_CONTENT } = httpStatus
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.delete-learning-context-bookmark', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, un-bookmark a course. Note the time. Check the LMS API log for the 'http-delete-learning-context-bookmark' label."
  try {
    const { contextID, userID } = z.object({
      contextID: zodGUID,
      userID: zodGUID
    }).parse(req.params)
 
    const numRowsAffected = await del(contextID, userID)

    if (numRowsAffected > 0) {
      // STIG V-69427 changing data (success)
      // STIGTEST "In the LMS application, un-bookmark a course. Note the time. Check the LMS API log for the 'http-delete-learning-context-bookmark' label and message indicating successful deletion."
      log('info', 'Successfully deleted bookmark for learning context', { learningContextId: contextID, userId: userID, success: true, req })

      res.sendStatus(NO_CONTENT)
    } else {
      // STIG V-69427 changing data (failure)
      log('warn', 'Failed to delete bookmark for learning context because it was not found in the database.', { contextId:contextID, userId: userID, success: false, req })
      res.sendStatus(NOT_FOUND)
    }
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete learning context bookmark: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    }
    else{
    log('error', 'Failed to delete learning context bookmark.', { error, req, success: false })
    res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
