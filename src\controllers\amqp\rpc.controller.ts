import rabbitMQ from '@lcs/rabbitmq'
import logger from '@lcs/logger'
import errors from '../../config/errors.js'

// import all of our controllers
import createContextController from './learning-contexts/create.js'
import deleteContextController from './learning-contexts/delete.js'
import getContextController from './learning-contexts/get.js'
import createLearningObject from './learning-objects/create.js'
import updateLearningObject from './learning-objects/update.js'
import deleteLearningObject from './learning-objects/delete.js'
import getLearningObject from './learning-objects/get.js'
import checkUserHasClaim from './user-claims/check-user-has-claim.js'
import { RpcCommands } from '@tess-f/lms/dist/rpc-commands.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const httpLog = logger.create('AMQP.rpc-controller')

const recognizedCommands = new Map<string, Function>([
  [RpcCommands.CheckClaim, checkUserHasClaim],
  [RpcCommands.CreateLearningContext, createContextController],
  [RpcCommands.CreateLearningObject, createLearningObject],
  [RpcCommands.DeleteLearningContext, deleteContextController],
  [RpcCommands.DeleteLearningObject, deleteLearningObject],
  [RpcCommands.GetLearningContext, getContextController],
  [RpcCommands.GetLearningObject, getLearningObject],
  [RpcCommands.UpdateLearningObject, updateLearningObject]
])

export default class RpcController {
  private consumer!: Function

  constructor (queueName: string) {
    this.connect(queueName)
  }

  private async connect (queueName: string) {
    // Start the consumer and attach listeners
    this.consumer = await rabbitMQ.createRpcConsumer(queueName,
      (message, resolve, reject) => { this.onMessage(message, resolve, reject) },
      (error) => { this.onReceiveError(error) }
    )

    httpLog('info', 'Consumer is active', { success: true })
  }

  private async onMessage (message: any, resolve: Function, reject: Function) {
    try {
      // Default command if it doesn't exist
      message.command = message.command ? message.command : ''

      if (recognizedCommands.has(message.command)) {
        // Handle the message and resolve the result
        resolve(await recognizedCommands.get(message.command)!(message))
      } else {
        resolve({
          success: false,
          error: errors.UNKNOWN_COMMAND
        })
      }
    } catch (error) {
      // Send back the message
      reject()

      // Server error occurred
      httpLog('error', 'Critical error', { errorMessage: getErrorMessage(error), success: false })

      // SHUTDOWN ON CRITICAL ERROR
      process.exit(-1)
    }
  }

  private async onReceiveError (error: unknown) {
    const errorMessage = getErrorMessage(error)
    httpLog('error', 'Incoming message error', { errorMessage, success: false })
  }

  async close () {
    if (this.consumer) { await this.consumer() }
  }
}
