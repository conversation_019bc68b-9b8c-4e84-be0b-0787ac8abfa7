import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'

describe('HTTP get-multiple controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-multiple.controller', {
            '../../../services/mssql/learning-objects/get-multiple.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                contextID: uuid(),
                visibility: 1,
                contextCount: '1',
                rating: '1'
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
        expect(mocks.res._getData()).equal('[]')

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-multiple.controller', {
            '../../../services/mssql/learning-objects/get-multiple.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                contextID: false,
                visibility: false,
                contextCount: false,
                rating: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request query data:')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('contextID')
        expect(mocks.res._getData()).include('visibility')
        expect(mocks.res._getData()).include('contextCount')
        expect(mocks.res._getData()).include('rating')

    })


    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-multiple.controller', {
            '../../../services/mssql/learning-objects/get-multiple.service.js': {
                default: Sinon.stub().rejects(Promise.resolve([]))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                contextID: uuid(),
                visibility: 1,
                contextCount: '1',
                rating: '1'
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })
 

})