import LearningContext, { createLearningContextSchema } from '../../../models/learning-context.model.js'
import logger from '@lcs/logger'
import create from '../../../services/mssql/learning-context/create.service.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { ZodError } from 'zod'

const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus
const log = logger.create('Controller-HTTP.create-learning-contexts', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, begin creating a new course. Note the time. Check the LMS API log for the 'http-create-learning-contexts' label."

  try {
    const learningContext = new LearningContext(createLearningContextSchema.parse(req.body))

    // Set the Created by and on fields
    learningContext.fields.CreatedBy = req.session.userId
    learningContext.fields.CreatedOn = new Date()

    if (learningContext.fields.Prerequisites) {
      for (const prereq of learningContext.fields.Prerequisites) {
        prereq.CreatedBy = req.session.userId
        prereq.CreatedOn = new Date()
      }
    }

    const result = await create(learningContext)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, being creating a new course. Note the time. Check the LMS API log for the 'http-create-learning-contexts' label and message indicating successful creation."
    log('info', `Successfully created learning context: ${result.fields.ID}`, { success: true, req })

    res.json(result.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create learning context: input validation error', { success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to create learning context.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
