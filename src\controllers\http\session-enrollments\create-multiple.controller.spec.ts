import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import SessionEnrollment from '../../../models/session-enrollment.model.js'
import LearningContextSession from '../../../models/learning-context-session.model.js'
import { SessionStatuses } from '@tess-f/sql-tables/dist/lms/session-status.js'

describe('HTTP create-multiple controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    const pastDate = new Date(Date.now() - 1000000000)

    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create-multiple.controller', {
            '../../../services/mssql/session-enrollments/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SessionEnrollment()))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../../../services/mssql/learning-context-sessions/get.service.js': {
                default: Sinon.stub().resolves(new LearningContextSession({ MaxEnrollments: 45, Timezone: 'MST', StartDate:pastDate, EndDate:pastDate, SessionStatusID: SessionStatuses.Full }))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: [
                {
                    SessionID: uuid(),
                    UserID: uuid()
                }
            ]

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create-multiple.controller', {
            '../../../services/mssql/session-enrollments/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SessionEnrollment()))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../../../services/mssql/learning-context-sessions/get.service.js': {
                default: Sinon.stub().resolves(new LearningContextSession({ MaxEnrollments: 45, Timezone: 'MST', StartDate:pastDate, EndDate:pastDate, SessionStatusID: SessionStatuses.Full }))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: [
                {
                    SessionID: false,
                    UserID: false
                }
            ]

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('SessionID')
        expect(mocks.res._getData()).include('UserID')

    })


    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./create-multiple.controller', {
            '../../../services/mssql/session-enrollments/create.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new SessionEnrollment()))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().rejects([])
            },
            '../../../services/mssql/learning-context-sessions/get.service.js': {
                default: Sinon.stub().rejects(new LearningContextSession({ MaxEnrollments: 45, Timezone: 'MST', StartDate:pastDate, EndDate:pastDate, SessionStatusID: SessionStatuses.Full }))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: [
                {
                    SessionID: uuid(),
                    UserID: uuid()
                }
            ]

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })
    

})