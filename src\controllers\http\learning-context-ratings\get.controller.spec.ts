import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextRatingModel from '../../../models/learning-context-rating.model'



describe('HTTP Get controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-context-ratings/get.service.js': {
                default: Sinon.stub().resolves(new LearningContextRatingModel({}))
            }
        })

        const mocks = httpMocks.createMocks({
            query: {
                id: uuid(),
                context: uuid(),
                user: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-context-ratings/get.service.js': {
                default: Sinon.stub().resolves(new LearningContextRatingModel({}))
            }
        })

        const mocks = httpMocks.createMocks({
            query: {
                id: false,
                context: false,
                user: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request query data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('id')
        expect(mocks.res._getData()).include('context')
        expect(mocks.res._getData()).include('user')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-context-ratings/get.service.js': {
                default: Sinon.stub().rejects(new LearningContextRatingModel({}))
            }
        })

        const mocks = httpMocks.createMocks({
            query: {
                id: uuid(),
                context: uuid(),
                user: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })


    it('returns an error if the request data is empty', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-context-ratings/get.service.js': {
                default: Sinon.stub().resolves(new LearningContextRatingModel({}))
            }
        })

        const mocks = httpMocks.createMocks()
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request query data')
        expect(mocks.res._getData()).include('query.id: Must provide either rating, context, or user id')
        expect(mocks.res._getData()).include('query.context: Must provide either rating, context, or user id')
        expect(mocks.res._getData()).include('query.user: Must provide either rating, context, or user id')
       
    })
   

    

})