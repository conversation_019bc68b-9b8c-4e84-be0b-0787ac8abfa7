import create from '../../../services/mssql/learning-objects/create.service.js'
import LearningObject, { createLearningObjectSchema } from '../../../models/learning-object.model.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { ZodError } from 'zod'

const log = logger.create('Controller-HTTP.create-learning-object', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, as an admin upload a new course file. Note the time. Check the LMS API log for the 'http-create-learning-object' label."  
  try {
    const learningObject = new LearningObject(createLearningObjectSchema.parse(req.body))
    learningObject.fields.CreatedBy = req.session.userId
    learningObject.fields.CreatedOn = new Date()

    const created = await create(learningObject)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, as an admin upload a new course file. Note the time. Check the LMS API log for the 'http-create-learning-object' label and message indicating successful creation."
    log('info', 'Successfully created learning object', { id: created.fields.ID, success: true, req })
    res.json(created.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create learning object: input validation error', { success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to create learning object.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
