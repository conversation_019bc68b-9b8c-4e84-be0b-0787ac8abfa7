import logger from '@lcs/logger'
import mssql, { deleteRow } from '@lcs/mssql-utility'
import { LearningObjectObjective, LearningObjectObjectiveTableName } from '@tess-f/sql-tables/dist/lms/learning-object-objective.js'
import removeObjectiveConnection from '@tess-f/objectives/dist/amqp/remove-connection.js'
import settings from '../../../config/settings.js'
import { Request } from 'mssql'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service-MSSQL.remove-learning-object-objective')

export default async function removeLearningObjectObjective (learningObjectId: string, objectiveId: string, request?: Request): Promise<void> {
  if (request === undefined) {
    request = mssql.getPool().request()
  }

  const removedCount = await deleteRow<LearningObjectObjective>(request, LearningObjectObjectiveTableName, { LearningObjectId: learningObjectId, ObjectiveId: objectiveId })

  if (removedCount > 0) {
    // remove the connection from the objective app
    try {
      await removeObjectiveConnection(
        settings.amqp.service_queues.objectiveConnect,
        {
          ObjectiveId: objectiveId,
          ReferenceId: learningObjectId,
          SystemId: 'lms'
        },
        settings.amqp.rpc_timeout
      )
    } catch (error) {
      log('error', 'Failed to remove connection from objectives app', { success: false, errorMessage: getErrorMessage(error) })
    }
  }
}
