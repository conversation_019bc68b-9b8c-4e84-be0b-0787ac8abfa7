import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import UserPreference, { userPreferenceSchema } from '../../../models/user-preference.model.js'
import UserPreferencesModel from '../../../models/user-preference.model.js'

describe('HTTP get-context-modifiers controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-context-modifiers.controller', {
            '../../../services/mssql/learning-context/get-context-modifiers.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            


        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })


    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-context-modifiers.controller', {
            '../../../services/mssql/learning-context/get-context-modifiers.service.js': {
                default: Sinon.stub().rejects(Promise.resolve([]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            


        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })



})