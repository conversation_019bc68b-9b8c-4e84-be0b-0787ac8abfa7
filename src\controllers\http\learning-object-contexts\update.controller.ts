import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import update from '../../../services/mssql/learning-object-contexts/update.service.js'
import LearningObjectContext from '../../../models/learning-object-context.model.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.update-learning-object-contexts', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-update-learning-object-contexts' label."  
  try {
    const objectContext = new LearningObjectContext(z.object({ OrderID: z.number().int() }).parse(req.body))
    const { objectID, contextID } = z.object({ objectID: zodGUID, contextID: zodGUID }).parse(req.params)
  
    // Set implicit values
    objectContext.fields.LearningObjectID = objectID
    objectContext.fields.LearningContextID = contextID
    objectContext.fields.ModifiedBy = req.session.userId
    objectContext.fields.ModifiedOn = new Date()

    const updated = await update(objectContext)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-update-learning-object-contexts' label and message indicating a successful update."
    log('info', 'Successfully updated learning context object', { contextID, objectID, success: true, req })
    res.json(updated.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update learning object context: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to update learning object context because it was not found in the database.', { contextID: req.params.contextID, objectID: req.params.objectID, success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      log('error', 'Failed to update learning object contexts.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
