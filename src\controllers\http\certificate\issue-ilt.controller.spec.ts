import logger from '@lcs/logger'
import httpStatus from 'http-status'
import Sinon from 'sinon'
import { expect } from 'chai'
import httpMock from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'
import LearningContextSession from '../../../models/learning-context-session.model.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import LearnerProgress from '../../../models/learner-progress.model.js'

describe('HTTP Controller: issue ilt certificate', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns bad request when the request params are missing', async () => {
    const controller = await esmock('./issue-ilt.controller.js')
    const mocks = httpMock.createMocks()
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request parameter(s),')
    expect(data).to.include('Required')
  })

  it('returns bad request when the request sessionId param is invalid', async () => {
    const controller = await esmock('./issue-ilt.controller.js')
    const mocks = httpMock.createMocks({ params: { sessionId: 'test', userId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request parameter(s),')
    expect(data).to.include('sessionId: Invalid')
  })

  it('returns bad request when the request userId param is invalid', async () => {
    const controller = await esmock('./issue-ilt.controller.js')
    const mocks = httpMock.createMocks({ params: { sessionId: uuid(), userId: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request parameter(s),')
    expect(data).to.include('userId: Invalid')
  })

  it('returns ok status when the request params are valid', async () => {
    const controller = await esmock('./issue-ilt.controller.js', {
      '../../../services/mssql/learning-context-sessions/get.service.js': {
        default: Sinon.stub().resolves(new LearningContextSession({ LearningContextID: uuid() }))
      },
      '../../../services/mssql/learning-context/get.service.js': {
        default: Sinon.stub().resolves(new LearningContextModel({ ID: uuid(), Title: 'Test' }))
      },
      '../../../services/mssql/users/get-by-id.service.js': {
        default: Sinon.stub().resolves({ FirstName: 'Test', LastName: 'User', ID: uuid() })
      },
      '../../../services/mssql/learner-progress/get.service.js': {
        default: Sinon.stub().resolves(new LearnerProgress({ Certificate: undefined }))
      },
      '../../../services/file/create-certificate.service.js': {
        default: Sinon.stub().resolves('test')
      },
      '../../../services/amqp/file/save-certificate.service.js': {
        default: Sinon.stub().resolves(uuid())
      },
      '../../../services/mssql/learner-progress/update.service.js': {
        default: Sinon.stub().resolves()
      },
      '../../../services/amqp/notification/send-notification.service.js': {
        default: Sinon.stub().resolves()
      }
    })
    const mocks = httpMock.createMocks({ params: { sessionId: uuid(), userId: uuid() }, session: { userId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
  })
})
