import mssql from '@lcs/mssql-utility'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObject, LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { AssignmentLearningObjectFields, AssignmentLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-object.js'

export default async function getLearningObjectsForAssignment (assignmentId: string): Promise<LearningObjectModel[]> {
  const request = mssql.getPool().request()
  request.input('assignmentId', assignmentId)
  const results = await request.query<LearningObject>(`
    SELECT *
    FROM [${LearningObjectsTableName}]
    WHERE [${LearningObjectFields.ID}] IN (
      SELECT [${AssignmentLearningObjectFields.LearningObjectID}]
      FROM [${AssignmentLearningObjectsTableName}]
      WHERE [${AssignmentLearningObjectFields.AssignmentID}] = @assignmentId
    )
  `)
  return results.recordset.map(record => new LearningObjectModel(undefined, record))
}
