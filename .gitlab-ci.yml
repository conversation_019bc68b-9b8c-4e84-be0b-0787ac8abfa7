stages:
  - sonarqube-check
  - build
  - deploy

before_script:
  - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
  - echo "//gitlab.ab.c2fse.northgrum.com/api/v4/packages/npm/:_authToken=${CI_JOB_TOKEN}" >> .npmrc

sonarqube-check:
  stage: sonarqube-check
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script: 
    - sonar-scanner
  allow_failure: true
  only:
    - development # or the name of your main branch


build_dev:
  stage: build
  only:
    - development
  script:
    - docker build $DOCKER_BUILD_ARGS -t $CI_REGISTRY_IMAGE:dev .
    - docker push $CI_REGISTRY_IMAGE:dev

build_test:
  stage: build
  only:
    - test
  script:
    - docker build $DOCKER_BUILD_ARGS -t $CI_REGISTRY_IMAGE:test .
    - docker push $CI_REGISTRY_IMAGE:test

build_master:
  stage: build
  only:
    - master
  script:
    - docker build $DOCKER_BUILD_ARGS -t $CI_REGISTRY_IMAGE .
    - docker push $CI_REGISTRY_IMAGE

deploy_development:
  stage: deploy
  only:
    - development
  script:
    - sshpass -p $AUTO_DEPLOY_PASS ssh -oStrictHostKeyChecking=no $AUTO_DEPLOY_USER@$AUTO_DEPLOY_HOST "${AUTO_DEPLOY_NODE_PATH} ${AUTO_DEPLOY_SCRIPTS_FOLDER}/update-clusters.js development learning-management-api"

deploy_wolf:
  stage: deploy
  only:
    - development
  script:
    - kubectl delete pods -l app=learning-management-system-api --kubeconfig /home/<USER>/.kube/dam-dev-config
  tags: [wolf-deployer]