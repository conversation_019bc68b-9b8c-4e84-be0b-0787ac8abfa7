import get, { getBookmarksForUser, getUserBookmarksForObject } from '../../../services/mssql/learning-object-bookmarks/get.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-learning-object-bookmarks', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { objectID, userID } = z.object({ objectID: zodGUID.optional(), userID: zodGUID.optional() }).superRefine(({ objectID, userID }, ctx) => {
      if (!objectID && !userID) {
        ctx.addIssue({ code: 'custom', message: 'must provide either objectID or userID' })
      }
    }).parse(req.query)

    if (objectID && userID) {
      const result = await get(objectID, userID)
      log('info', 'Successfully retrieved learning context bookmarks.', { success: true, req })
      res.json(result.fields)
    } else if (objectID) {
      const results = await getUserBookmarksForObject(objectID)
      log('info', 'Successfully retrieved learning context bookmarks.', { success: true, req })
      res.json(results.map(bookmark => bookmark.fields))
    } else if (userID) {
      const results = await getBookmarksForUser(userID)
      log('info', 'Successfully retrieved learning context bookmarks.', { success: true, req })
      res.json(results.map(bookmark => bookmark.fields))
    }
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning object bookmarks: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request query parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learning object bookmarks for user because they were not found in the database.', { userId: req.query.userID, learningObjectId: req.query.objectID, success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      log('error', 'Failed to get learning object bookmarks.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
