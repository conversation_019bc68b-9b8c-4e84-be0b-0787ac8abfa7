import logger from '@lcs/logger'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import Sinon from 'sinon'
import * as getIdOfCmi5CourseContextForAu from '../../../services/mssql/learning-context/get-id-of-cmi5-course-by-au-id.service.js'
import * as getOrCreateLearningContextRegistration from '../../../services/mssql/learning-context-registrations/get-or-create.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import LearningContextRegistrationModel from '../../../models/learning-context-registration.model.js'
import httpStatus from 'http-status'
const { OK, INTERNAL_SERVER_ERROR, BAD_REQUEST, NOT_FOUND } = httpStatus
import controller from './get-or-create-cmi5-registration.controller.js'
import { v4 as uuid } from 'uuid'
import { LearningContextRegistration } from '@tess-f/sql-tables/dist/lms/learning-context-registration.js'
import * as markNaAusSatisfied from '../../../services/mssql/cmi5-au/mark-na-aus-satisfied.service.js'
import LearningContextModel from '../../../models/learning-context.model.js'

xdescribe('Controller HTTP: get or create cmi5 course registration', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns bad request when the au id param is missing', async () => {
    const mocks = httpMocks.createMocks()
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(BAD_REQUEST)
  })

  it('returns bad request when the au id is not a guid', async () => {
    const mocks = httpMocks.createMocks({ params: { auId: 'test-id' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(BAD_REQUEST)
  })

  it('returns not found when the course id for the au cannot be found', async () => {
    const mocks = httpMocks.createMocks({ params: { auId: uuid() }, session: { userId: uuid() } })
    Sinon.stub(getIdOfCmi5CourseContextForAu, 'default').returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(NOT_FOUND)
  })

  it('returns the requested registration', async () => {
    const mocks = httpMocks.createMocks({ params: { auId: uuid() }, session: { userId: uuid() } })
    Sinon.stub(getIdOfCmi5CourseContextForAu, 'default').returns(Promise.resolve(new LearningContextModel({ ID: uuid() })))
    Sinon.stub(getOrCreateLearningContextRegistration, 'default').returns(Promise.resolve({
      created: false,
      registration: new LearningContextRegistrationModel({
        UserId: uuid(),
        Id: uuid(),
        LearningContextId: uuid(),
        CreatedOn: new Date()
      })
    }))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(OK)
    const data: LearningContextRegistration = JSON.parse(mocks.res._getData())
    expect(data).to.exist
    expect(data.CompletedOn).to.not.exist
    expect(data.Id).to.exist
    expect(data.LearningContextId).to.exist
    expect(data.UserId).to.exist
  })

  it('marks not applicable aus satisfied when the registration is created', async () => {
    const mocks = httpMocks.createMocks({ params: { auId: uuid() }, session: { userId: uuid() } })
    Sinon.stub(getIdOfCmi5CourseContextForAu, 'default').returns(Promise.resolve(new LearningContextModel({ ID: uuid() })))
    Sinon.stub(getOrCreateLearningContextRegistration, 'default').returns(Promise.resolve({
      created: true,
      registration: new LearningContextRegistrationModel({
        UserId: uuid(),
        Id: uuid(),
        LearningContextId: uuid(),
        CreatedOn: new Date()
      })
    }))
    const naStub = Sinon.stub(markNaAusSatisfied, 'default')
    naStub.returns(Promise.resolve())
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(OK)
    const data: LearningContextRegistration = JSON.parse(mocks.res._getData())
    expect(data).to.exist
    expect(data.CompletedOn).to.not.exist
    expect(data.Id).to.exist
    expect(data.LearningContextId).to.exist
    expect(data.UserId).to.exist
    expect(naStub.called).to.be.true
  })

  it('returns internal server error when the registration service fails', async () => {
    const mocks = httpMocks.createMocks({ params: { auId: uuid() }, session: { userId: uuid() } })
    Sinon.stub(getIdOfCmi5CourseContextForAu, 'default').returns(Promise.resolve(new LearningContextModel({ ID: uuid() })))
    Sinon.stub(getOrCreateLearningContextRegistration, 'default').returns(Promise.reject(new Error('Service Failure')))
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(INTERNAL_SERVER_ERROR)
  })
})
