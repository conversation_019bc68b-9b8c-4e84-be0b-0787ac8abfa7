import del from '../../../services/mssql/learning-context-ratings/delete.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NO_CONTENT } = httpStatus

const log = logger.create('Controller-HTTP.delete-learning-context-ratings', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-delete-learning-context-ratings' label."

  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    await del(id)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-delete-learning-context-ratings' label and message indicating successful deletion."
    log('info', 'Successfully deleted learning context rating', { id, success: true, req })

    res.sendStatus(NO_CONTENT)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete learning context rating: input validation error', { success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    }
    else{
    log('error', 'Failed to delete learning context rating.', { error, success: false, req })
    res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
