import { Table } from '@lcs/mssql-utility'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ClaimTableName } from '@tess-f/sql-tables/dist/lms/claim.js'

export default class ClaimModel extends Table<Claim, Claim> {
  public fields: Claim

  constructor (fields?: Claim) {
    super(ClaimTableName, [
      ClaimFields.Claim
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: Claim): void {
    this.fields = record
  }

  public exportJsonToDatabase (): Claim {
    return this.fields
  }
}
