import logger from '@lcs/logger'
import { RpcMessage } from '@tess-f/shared-config/dist/types/rpc-message.js'
import { RpcResponse } from '@tess-f/shared-config/dist/types/rpc-response.js'
import userHasClaim from '../../../services/mssql/claims/has-claim.service.js'
import { CheckClaimResponse, CheckClaimRequest } from '@tess-f/lms/dist/amqp/check-claim.js'
import { getErrorMessage, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-AMQP.check-user-has-claims')

export default async function (req: RpcMessage<CheckClaimRequest>): Promise<RpcResponse<CheckClaimResponse>> {
  try {
    const { userID, claim } = z.object({ userID: zodGUID, claim: z.string() }).parse(req.data)

    const hasClaim = await userHasClaim(userID, claim)

    return { success: true, data: { valid: hasClaim } }
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to check if user has claim: input validation error', { errorMessage: error.message, success: false })
      return {
        success: false,
        message: zodErrorToMessage(error, 'Invalid request data: ')
      }
    }

    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to check if user has claim', { errorMessage, success: false })
    return { success: false, message: errorMessage }
  }
}
