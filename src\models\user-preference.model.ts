import { Table } from '@lcs/mssql-utility'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { UserPreferences, UserPreferencesFields, UserPreferencesTableName } from '@tess-f/sql-tables/dist/lms/user-preference.js'
import { z } from 'zod'

export const userPreferenceSchema = z.object({
  [UserPreferencesFields.GettingStarted]: z.boolean().optional(),
  [UserPreferencesFields.UserID]: zodGUID,
  [UserPreferencesFields.HomeLayout]: z.string().max(25).optional()
})

export default class UserPreferencesModel extends Table<UserPreferences, UserPreferences> {
  public fields: UserPreferences

  constructor (fields?: UserPreferences) {
    super(UserPreferencesTableName, [
      UserPreferencesFields.GettingStarted,
      UserPreferencesFields.UserID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: UserPreferences): void {
    this.fields = record
  }

  public exportJsonToDatabase (): UserPreferences {
    return this.fields
  }
}
