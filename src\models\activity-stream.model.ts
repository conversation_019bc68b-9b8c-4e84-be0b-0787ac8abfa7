import { Table } from '@lcs/mssql-utility'
import { ActivityStream, ActivityStreamFields, ActivityStreamTableName } from '@tess-f/sql-tables/dist/lms/activity-stream.js'

export default class ActivityStreamModel extends Table<ActivityStream, ActivityStream> {
  public fields: ActivityStream

  constructor (fields?: ActivityStream) {
    super(ActivityStreamTableName, [
      ActivityStreamFields.UserID,
      ActivityStreamFields.LinkText,
      ActivityStreamFields.LinkID,
      ActivityStreamFields.ActivityID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: ActivityStream): void {
    this.fields = record
  }

  public exportJsonToDatabase (): ActivityStream {
    return this.fields
  }
}
