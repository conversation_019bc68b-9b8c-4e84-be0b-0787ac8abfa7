import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'

describe('HTTP get-context-in-progress-users controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-context-in-progress-users.controller', {
            '../../../services/mssql/learning-context/get-completion-user-ids.service.js': {
                getInProgressUserIDs: Sinon.stub().returns(Promise.resolve({ids: [''], totalRecords: 0}))
            },
            '../../../services/mssql/learning-context/get-user-completion-dates.service.js': {
                getUserInProgressDates: Sinon.stub().returns(Promise.resolve(Date.now()))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
            },
            body: {
                limit: 10,
                offset: 1
            },
            params: {
                id: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })


    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-context-in-progress-users.controller', {
            '../../../services/mssql/learning-context/get-completion-user-ids.service.js': {
                getInProgressUserIDs: Sinon.stub().returns(Promise.resolve({ids: [''], totalRecords: 0}))
            },
            '../../../services/mssql/learning-context/get-user-completion-dates.service.js': {
                getUserInProgressDates: Sinon.stub().returns(Promise.resolve(Date.now()))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
            },
            body: {
                limit: 10,
                offset: 1
            },
            params: {
                id: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('id')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-context-in-progress-users.controller', {
            '../../../services/mssql/learning-context/get-completion-user-ids.service.js': {
                getInProgressUserIDs: Sinon.stub().rejects(Promise.resolve({ids: [''], totalRecords: 0}))
            },
            '../../../services/mssql/learning-context/get-user-completion-dates.service.js': {
                getUserInProgressDates: Sinon.stub().rejects(Promise.resolve(Date.now()))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
            },
            body: {
                limit: 10,
                offset: 1
            },
            params: {
                id: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

  

})