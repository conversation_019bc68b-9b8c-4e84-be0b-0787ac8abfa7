import logger from '@lcs/logger'
import sessionAuthority from '@lcs/session-authority'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { NextFunction, Request, Response } from 'express'
import settings from '../../../config/settings.js'
import { httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('HTTP-Middleware.check-session', httpLogTransformer)

export default async function (req: Request, res: Response, next: NextFunction) {
  // for integration testing
  if (settings.sessionAuthority.bypass) {
    req.session = {
      userId: AdminUserId, // req.cookies.userId,
      sessionId: ''
    }
    next()
    return
  }

  const authResult = await sessionAuthority.authenticate(req, res)

  if (authResult.result === 'valid') {
    next()
  } else if (authResult.result === 'invalid' || authResult.result === 'missing') {
    // Invalid session! Redirect to the login page
    log('error', 'received invalid session', { authResult: authResult.result, req })
    res.redirect('/access/login')
  }
}
