import getParentContextsForContextService from '../../../services/mssql/learning-context/get-parent-contexts-for-context.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.Get-Parents-For-Context', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const parents = await getParentContextsForContextService(id)
    log('info', 'Successfully retrieved parents for context', { count: parents.length, forContext: id, success: true, req })
    res.json(parents.map(context => context.fields))
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('error', 'Failed to get parents for context', { forContext: req.params.id, success: false, error, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else {
      log('error', 'Failed to get parents for context', { forContext: req.params.id, success: false, error, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
