import mssql from '@lcs/mssql-utility'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'
import { UserAssignedMultiSessionCoursesFields, UserAssignedMultiSessionCoursesTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-multi-session-course.js'
import { FlatLearningContextTreeViewName, FlatLearningContextTreeFields } from '@tess-f/sql-tables/dist/lms/flat-learning-context-view.js'

export default async function (contextID: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('contextID', contextID)

  const assigned = await request.query<{AssignmentCount: number}>(`
    WITH Parent_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}], [${LearningContextFields.VisibilityID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] = @contextID
      UNION ALL
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Parent_Nodes] ON [Parent_Nodes].[${LearningContextFields.ParentContextID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}]
    ), Assigned AS (
      SELECT [${UserAssignedLearningObjectFields.AssignmentID}], [${UserAssignedLearningObjectFields.UserID}]
      FROM [${UserAssignedLearningObjectsTableName}]
      WHERE [${UserAssignedLearningObjectFields.ForContextID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [Parent_Nodes]
        WHERE [${LearningContextFields.VisibilityID}] != ${Visibilities.Hidden}
      )
      UNION ALL
      SELECT [${UserAssignedMultiSessionCoursesFields.AssignmentID}], [${UserAssignedMultiSessionCoursesFields.UserID}]
      FROM [${UserAssignedMultiSessionCoursesTableName}]
      WHERE [${UserAssignedMultiSessionCoursesFields.LearningContextID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [Parent_Nodes]
        WHERE [${LearningContextFields.VisibilityID}] != ${Visibilities.Hidden}
      ) OR [${UserAssignedMultiSessionCoursesFields.ForContextID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [Parent_Nodes]
        WHERE [${LearningContextFields.VisibilityID}] != ${Visibilities.Hidden}
      )
    )
    SELECT COUNT(*) AS AssignmentCount FROM (
      SELECT [${UserAssignedLearningObjectFields.AssignmentID}], [${UserAssignedLearningObjectFields.UserID}]
      FROM [Assigned]
    ) AS DistinctAssignments
  `)

  return assigned.recordset[0].AssignmentCount
}
