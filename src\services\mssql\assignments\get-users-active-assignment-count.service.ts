import mssql from '@lcs/mssql-utility'
import { UserInProgressAssignmentFields, UserInProgressAssignmentsViewName } from '@tess-f/sql-tables/dist/lms/user-in-progress-assignmnets-view.js'
import { UserNotStartedAssignmentFields, UserNotStartedAssignmentsViewName } from '@tess-f/sql-tables/dist/lms/user-not-started-assignments-view.js'
import { UserOverdueAssignmentFields, UserOverdueAssignmentsViewName } from '@tess-f/sql-tables/dist/lms/user-overdue-assignments-view.js'

export default async function getUsersActiveAssignmentCount (userId: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('userId', userId)

  const result = await request.query<{ ActiveCount: number }>(`
    SELECT COUNT(*) AS [ActiveCount]
    FROM (
      SELECT *
      FROM [${UserInProgressAssignmentsViewName}]
      WHERE [${UserInProgressAssignmentFields.UserID}] = @userId

      UNION ALL

      SELECT *
      FROM [${UserNotStartedAssignmentsViewName}]
      WHERE [${UserNotStartedAssignmentFields.UserID}] = @userId

      UNION ALL

      SELECT *
      FROM [${UserOverdueAssignmentsViewName}]
      WHERE [${UserOverdueAssignmentFields.UserID}] = @userId
    ) AS activeAssignments
  `)

  return result.recordset[0].ActiveCount
}
