import mssql, { updateRow } from '@lcs/mssql-utility'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import { LearningContextSession } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'

export default async function (updatedSession: LearningContextSessionModel): Promise<LearningContextSessionModel> {
  const pool = mssql.getPool()
  const records = await updateRow<LearningContextSession>(pool.request(), updatedSession, { ID: updatedSession.fields.ID })
  updatedSession.importFromDatabase(records[0])
  return updatedSession
}
