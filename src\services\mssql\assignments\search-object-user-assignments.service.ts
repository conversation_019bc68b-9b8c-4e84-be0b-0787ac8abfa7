import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { GhostUserId, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { UserGroupFields, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'
import { AssignmentFields, AssignmentsTableName } from '@tess-f/sql-tables/dist/lms/assignment.js'
import { UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'

export default async function (objectID: string, offset = 0, limit = 10, search?: string, groupIDs?: string[], from?: Date, to?: Date): Promise<{ totalRecords: number, users: { AssignmentID: string, UserID: string, Title: string, CreatedBy: string, CreatedOn: Date, DueDate: Date } []}> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('objectID', objectID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  let query = `
    WITH DistinctAssignments AS (
      SELECT DISTINCT [${UserAssignedLearningObjectFields.AssignmentID}], [${UserAssignedLearningObjectFields.UserID}]
      FROM [${UserAssignedLearningObjectsTableName}]
      WHERE [${UserAssignedLearningObjectFields.LearningObjectID}] = @objectID
    )
    SELECT [DA].*, [${AssignmentsTableName}].[${AssignmentFields.Title}], [${AssignmentsTableName}].[${AssignmentFields.CreatedBy}], [${AssignmentsTableName}].[${AssignmentFields.CreatedOn}], [${AssignmentsTableName}].[${AssignmentFields.DueDate}], [${UserTableName}].[${UserFields.FirstName}], [${UserTableName}].[${UserFields.LastName}], [TotalRecords] = COUNT(*) OVER()
    FROM [DistinctAssignments] AS DA
    JOIN [${AssignmentsTableName}] ON [${AssignmentsTableName}].[${AssignmentFields.ID}] = [DA].[${UserAssignedLearningObjectFields.AssignmentID}]
    JOIN [${UserTableName}] ON [${UserTableName}].[${UserFields.ID}] = [DA].[${UserAssignedLearningObjectFields.UserID}]
    WHERE 1 = 1
  `

  if (search) {
    query += `
      AND [${UserAssignedLearningObjectFields.UserID}] IN (
        SELECT [${UserFields.ID}]
        FROM [${UserTableName}]
        WHERE ${parseSearchTerms(request, search, [UserFields.FirstName, UserFields.LastName], 'any')}
        AND [${UserFields.ID}] != '${GhostUserId}'
      )
    `
  }

  if (from && to) {
    query += `AND [${UserAssignedLearningObjectFields.CreatedOn}] BETWEEN @from AND @to`
  }

  if (groupIDs && groupIDs.length > 0) {
    const conds = groupIDs.map((id, index) => {
      request.input(`group_${index}`, id)
      return `@group_${index}`
    })
    query += `
      AND [${UserAssignedLearningObjectFields.UserID}] IN (
        SELECT [${UserGroupFields.UserID}]
        FROM [${UserGroupTableName}]
        WHERE [${UserGroupFields.GroupID}] IN (
          ${conds.join(', ')}
        )
      )
    `
  }

  request.input('offset', offset)
  request.input('limit', limit)

  query += `
    ORDER BY [${UserFields.FirstName}], [${UserFields.LastName}]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<{ AssignmentID: string, UserID: string, Title: string, CreatedBy: string, CreatedOn: Date, DueDate: Date, TotalRecords: number }>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    users: results.recordset
  }
}
