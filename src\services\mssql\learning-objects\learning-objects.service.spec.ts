import { expect } from 'chai'
import mssqlConnection from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import create from './create.service.js'
import get from './get.service.js'
import { getObjectInProgressCount, getObjectCompletedCount } from './get-completion-counts.service.js'
import { getCompletedUserIDs, getInProgressUserIDs } from './get-completion-user-ids.service.js'
import getCompletion from './get-completion.service.js'
import getCreatorIds from './get-creator-ids.service.js'
import getModifierIds from './get-modifier-ids.service.js'
import getMultipleByContentId from './get-multiple-by-content-id.service.js'
import getMultiple from './get-multiple.service.js'
import getNextLearningObjectIdForContent from './get-next-learning-object-id-for-context.service.js'
import getPaginated from './get-paginated.service.js'
import getTopRated from './get-top-rated.service.js'
import getTopViewed from './get-top-viewed.service.js'
import getUploadsByType from './get-uploads-by-type.service.js'
import getUserCompletionDates from './get-user-completion-dates.service.js'
import getViewHistory from './get-view-history.service.js'
import update from './update.service.js'
import remove from './delete.service.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { v4 as uuidv4 } from 'uuid'

let learningObject: LearningObjectModel
let learningObject2: LearningObjectModel

describe('MSSQL Learning Object Unit Test', () => {
  before('Connect to SQL', async () => {
    await mssqlConnection.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
  })

  it('created learning object', async () => {
    learningObject = await create(new LearningObjectModel({
      Title: 'Test Learning Object',
      Description: `Running learning-objects on ${new Date()}`,
      ContentID: uuidv4(),
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      LearningObjectTypeID: LearningObjectTypes.PDF
    }))
    expect(learningObject.fields.CreatedBy).to.eq(AdminUserId)
    expect(learningObject.fields.LearningObjectTypeID).to.be.eq(LearningObjectTypes.PDF)
    // create a second object for testing
    learningObject2 = await create(new LearningObjectModel({
      Title: 'Test object 2',
      Description: 'Test object for Learning Object service tests',
      ContentID: uuidv4(),
      CreatedBy: AdminUserId,
      CreatedOn: new Date('2/5/2014'),
      EnableCertificates: true,
      LearningObjectTypeID: LearningObjectTypes.Image
    }))
  })

  it('gets learning object by id', async () => {
    const result = await get(learningObject.fields.ID!, AdminUserId)
    expect(result.fields.CreatedBy).to.be.eq(AdminUserId)
    expect(result.fields.LearningObjectTypeID).to.be.eq(LearningObjectTypes.PDF)
  })

  it('gets object in progress count', async () => {
    const result = await getObjectInProgressCount(learningObject.fields.ID!)
    expect(result).to.be.gte(0)
  })

  it('gets object completed count', async () => {
    const result = await getObjectCompletedCount(learningObject.fields.ID!)
    expect(result).to.be.gte(0)
  })

  it('gets completed by user ids', async () => {
    const result = await getCompletedUserIDs(undefined, undefined, learningObject.fields.ID!)
    expect(result.totalRecords).to.be.gte(0)
  })

  it('gets in progress user ids', async () => {
    const result = await getInProgressUserIDs(undefined, undefined, learningObject.fields.ID!)
    expect(result.totalRecords).to.be.gte(0)
  })

  it('gets completion', async () => {
    const result = await getCompletion(learningObject.fields.ID!, AdminUserId)
    expect(result).to.be.gte(0)
  })

  it('gets creator ids', async () => {
    const result = await getCreatorIds()
    expect(result.length).to.be.gte(0)
  })

  it('gets modifier ids', async () => {
    const result = await getModifierIds()
    expect(result.length).to.be.gte(0)
  })

  it('gets multiple by content id', async () => {
    const result = await getMultipleByContentId(learningObject.fields.ContentID!)
    expect(result.length).to.be.gte(0)
  })

  it('gets multiple', async () => {
    const result = await getMultiple(learningObject.fields.ID!)
    expect(result.length).to.be.gte(0)
  })

  it('gets next learning object id for content', async () => {
    try {
      const result = await getNextLearningObjectIdForContent(learningObject.fields.ID!, AdminUserId)
      expect(result.objectID).to.be.eq(learningObject.fields.ID)
    } catch (error) {
      expect(error).to.exist
    }
  })

  it('gets paginated', async () => {
    const result = await getPaginated(0, 150)
    expect(result.totalRecords).to.be.gte(2)
    expect(result.objects.length).to.be.gte(2)
    // check that the second record was created after the first
    expect(result.objects[0].fields.CreatedOn!.getTime()).to.be.gt(result.objects[1].fields.CreatedOn!.getTime())
  })

  it('gets top rated', async () => {
    const result = await getTopRated(150)
    expect(result.length).to.be.gte(0)
  })

  it('gets top viewed', async () => {
    const result = await getTopViewed(150)
    expect(result.length).to.be.gte(0)
  })

  it('gets uploads by type', async () => {
    const result = await getUploadsByType()
    expect(result.length).to.be.gte(0)
  })

  it('gets user completion dates', async () => {
    const result = await getUserCompletionDates(learningObject.fields.ID!, AdminUserId)
    if (result.LastAccessed === undefined || result.startedOn === undefined) {
      expect(result.LastAccessed).to.eq(undefined)
      expect(result.startedOn).to.eq(undefined)
    } else {
      expect(result.LastAccessed).to.exist
      expect(result.startedOn).to.exist
    }
  })

  it('gets view history', async () => {
    const result = await getViewHistory(AdminUserId)
    expect(result.length).to.be.gte(0)
  })

  it('updates learning object ', async () => {
    learningObject.fields.EnableCertificates = true
    learningObject.fields.LearningObjectTypeID = LearningObjectTypes.Audio
    learningObject.fields.ModifiedBy = AdminUserId
    const result = await update(learningObject)
    expect(result.fields.CreatedBy).to.be.eq(AdminUserId)
    expect(result.fields.LearningObjectTypeID).to.be.eq(LearningObjectTypes.Audio)
  })

  it('removes learning object', async () => {
    const result = await remove(learningObject.fields.ID!)
    expect(result).to.be.gte(1)
    const result2 = await remove(learningObject2.fields.ID!)
    expect(result2).to.be.gte(1)
  })
})
