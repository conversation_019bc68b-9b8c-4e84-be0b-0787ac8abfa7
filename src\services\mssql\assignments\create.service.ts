import mssql, { addRow } from '@lcs/mssql-utility'
import AssignmentLearningContextModel from '../../../models/assignment-learning-context.model.js'
import AssignmentLearningObjectModel from '../../../models/assignment-learning-object.model.js'
import AssignmentUserModel from '../../../models/assignment-users.model.js'
import AssignmentModel from '../../../models/assignment.model.js'
import createUserAssignedObjects from '../user-assigned-learning-objects/create-multiple.service.js'
import createUserAssignedMultiCourse from '../user-assigned-multi-session-courses/create-multiple.service.js'
import { ConnectionPool } from 'mssql'
import { Assignment } from '@tess-f/sql-tables/dist/lms/assignment.js'
import { AssignmentUser } from '@tess-f/sql-tables/dist/lms/assignment-user.js'
import { AssignmentLearningContext } from '@tess-f/sql-tables/dist/lms/assignment-learning-context.js'
import { AssignmentLearningObject } from '@tess-f/sql-tables/dist/lms/assignment-learning-object.js'

/**
 * @param {AssignmentModel} assignment the assignment object to create
 * @param {AssignmentUserModel[]} users the assignment users that are assigned to this assignment if not everyone
 * @param {AssignmentLearningContextModel[]} contexts the assignment learning contexts for this assignment
 * @param {AssignmentLearningObjectModel[]} learnObjs the assignment learning objects for this assignment
 * @param {string[]} groupUserIDs the ids of the group users that are not in the users array
 *
 */
export default async function (assignment: AssignmentModel, users: AssignmentUserModel[], contexts: AssignmentLearningContextModel[], learnObjs: AssignmentLearningObjectModel[], groupUserIDs: string[]): Promise<{
  assignment: AssignmentModel,
  users: AssignmentUserModel[],
  contexts: AssignmentLearningContextModel[],
  learningObjects: AssignmentLearningObjectModel[]
}> {
  const pool = mssql.getPool()
  const record = await addRow<Assignment>(pool.request(), assignment)
  const created = new AssignmentModel(record)
  const createdUser = (users && !created.fields.Everyone) ? await createUsers(pool, users, created.fields.ID!) : []
  const createdContext = contexts ? await createAssignmentContexts(pool, contexts, created.fields.ID!) : []
  const createdLearnObjects = learnObjs ? await createAssignmentObjects(pool, learnObjs, created.fields.ID!) : []

  // Create the assigned learning object records for tracking progress on this assignment
  if (users && (contexts || learnObjs)) {
    // If we have users and we have content let's make the tracking objects in the database.
    await createUserAssignedObjects(pool, created.fields.ID!, users, contexts, learnObjs, groupUserIDs, assignment.fields.CreatedBy!, created.fields.DueDate)
  }

  if (users && contexts) {
    // If we have users and we have contexts let's create records for the multi session (ILT) courses
    // the service will handle checking if the context is an ILT course
    await createUserAssignedMultiCourse(pool, created.fields.ID!, users, contexts, groupUserIDs, created.fields.CreatedBy!, created.fields.DueDate)
  }

  return {
    assignment: created,
    users: createdUser,
    contexts: createdContext,
    learningObjects: createdLearnObjects
  }
}

async function createUsers (pool: ConnectionPool, users: AssignmentUserModel[], assignmentID: string): Promise<AssignmentUserModel[]> {
  const output: AssignmentUserModel[] = []

  for (const user of users) {
    user.fields.AssignmentID = assignmentID
    const record = await addRow<AssignmentUser>(pool.request(), user)
    output.push(new AssignmentUserModel(record))
  }
  return output
}

async function createAssignmentContexts (pool: ConnectionPool, contexts: AssignmentLearningContextModel[], assignmentID: string): Promise<AssignmentLearningContextModel[]> {
  const output: AssignmentLearningContextModel[] = []
  for (const context of contexts) {
    context.fields.AssignmentID = assignmentID
    const record = await addRow<AssignmentLearningContext>(pool.request(), context)
    output.push(new AssignmentLearningContextModel(record))
  }
  return output
}

async function createAssignmentObjects (pool: ConnectionPool, learnObjs: AssignmentLearningObjectModel[], assignmentID: string): Promise<AssignmentLearningObjectModel[]> {
  const output: AssignmentLearningObjectModel[] = []

  for (const learnObj of learnObjs) {
    learnObj.fields.AssignmentID = assignmentID
    const record = await addRow<AssignmentLearningObject>(pool.request(), learnObj)
    output.push(new AssignmentLearningObjectModel(record))
  }
  return output
}
