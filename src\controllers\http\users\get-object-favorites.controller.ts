import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getFavoriteUsersService from '../../../services/mssql/learning-object-favorites/get-favorite-users.service.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-learning-object-favorite-users', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { search, offset, limit, groupIDs } = z.object({
      search: z.string().optional(),
      offset: zodOffset,
      limit: zodLimit,
      groupIDs: z.array(zodGUID).optional()
    }).parse(req.body)

    const { id } = z.object({ id: zodGUID }).parse(req.params)

    const users = await getFavoriteUsersService(id, offset, limit, search, groupIDs)

    log('info', 'Successfully fetched learning object favorite users', {
      count: users.ids.length,
      totalRecords: users.totalRecords,
      objectID: id,
      success: true,
      req
    })

    res.json({
      totalRecords: users.totalRecords,
      users: users.ids.map(id => { return { UserID: id } })
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning object favorite users: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to get learning object favorite users', { error, req, success: false, objectID: req.params.id })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
