import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { UserGroupFields, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'
import { UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'
import { UserAssignedMultiSessionCoursesFields, UserAssignedMultiSessionCoursesTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-multi-session-course.js'

export default async function (assignmentID: string, offset = 0, limit = 10, search?: string, groups?: string[]): Promise<{ TotalRecords: number, userIDs: string[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('assignmentID', assignmentID)

  let query = `
    WITH ActiveUsers AS (
      SELECT *
      FROM [${UserTableName}]
      WHERE [${UserFields.ID}] IN (
        SELECT [${UserAssignedLearningObjectFields.UserID}]
        FROM [${UserAssignedLearningObjectsTableName}]
        WHERE [${UserAssignedLearningObjectFields.AssignmentID}] = @assignmentID
        AND [${UserAssignedLearningObjectFields.Deleted}] = 0
      ) OR [${UserFields.ID}] IN (
        SELECT [${UserAssignedMultiSessionCoursesFields.UserID}]
        FROM [${UserAssignedMultiSessionCoursesTableName}]
        WHERE [${UserAssignedMultiSessionCoursesFields.AssignmentID}] = @assignmentID
        AND [${UserAssignedMultiSessionCoursesFields.Deleted}] = 0
      )
    )
    SELECT *, [TotalRecords] = COUNT(*) OVER()
    FROM ActiveUsers
    WHERE 1 = 1
  `

  if (search) {
    query += `AND ${parseSearchTerms(request, search, [UserFields.FirstName, UserFields.LastName], 'any')} `
  }

  if (groups && groups.length > 0) {
    const conditions = groups.map((groupID, index) => {
      request.input(`group_${index}`, groupID)
      return `@group_${index}`
    })
    query += `
      AND [${UserFields.ID}] IN (
        SELECT [${UserGroupFields.UserID}]
        FROM [${UserGroupTableName}]
        WHERE [${UserGroupFields.GroupID}] IN (${conditions.join(', ')})
      )
    `
  }

  request.input('offset', offset)
  request.input('limit', limit)

  query += `
    ORDER BY [${UserFields.FirstName}] ASC, [${UserFields.LastName}] ASC
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const records = await request.query<{ ID: string, TotalRecords: number }>(query)

  return {
    TotalRecords: records.recordset.length > 0 ? records.recordset[0].TotalRecords : 0,
    userIDs: records.recordset.map(user => user.ID)
  }
}
