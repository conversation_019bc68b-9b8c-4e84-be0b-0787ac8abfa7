import { Request<PERSON><PERSON><PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getController from './get.controller.js'

const router = Router()

router.post('/context-favorite', create<PERSON><PERSON>roller as <PERSON>questHandler)
router.get('/context-favorite', get<PERSON>ontroller as <PERSON>questHandler)
router.delete('/context-favorite/:userID/:contextID', deleteController as <PERSON>questHandler)

export default router
