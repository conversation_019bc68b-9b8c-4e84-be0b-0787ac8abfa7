import { RequestHandler, Router } from 'express'
import createMultipleController from './create-multiple.controller.js'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import searchContextSessionEnrollmentsController from './search-context-session-enrollments.controller.js'
import updateController from './update.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.post('/session-enrollments', checkClaims([Claims.CREATE_COURSE, Claims.MODIFY_COURSE]), createMultipleController as RequestHandler)
router.post('/session-enrollment', checkClaims([Claims.CREATE_COURSE, Claims.MODIFY_COURSE]), createController as RequestHandler)
router.put('/session-enrollment', checkClaims([Claims.CREATE_COURSE, Claims.MODIFY_COURSE]), updateController as RequestHandler)
router.delete('/session-enrollment/:sessionID/:userID', checkClaims([Claims.CREATE_COURSE, Claims.MODIFY_COURSE]), deleteController as RequestHandler)
router.post('/get-context-user-enrollments/:id', checkClaims([Claims.VIEW_COURSES, Claims.CREATE_COURSE, Claims.MODIFY_COURSE]), searchContextSessionEnrollmentsController as RequestHandler)

export default router
