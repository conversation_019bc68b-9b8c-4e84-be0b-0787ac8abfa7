import mssql from '@lcs/mssql-utility'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import { fillRequestHeaders } from '../../../utils/test-agent.utils.js'
import { startServices, shutdownServices } from '../../../utils/testing.utils.js'
import { v4 as uuidv4 } from 'uuid'
import LearningContextModel from '../../../models/learning-context.model.js'
import createContext from '../../../services/mssql/learning-context/create.service.js'
import deleteContext from '../../../services/mssql/learning-context/delete.service.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import createLearningObject from '../../../services/mssql/learning-objects/create.service.js'
import deleteLearningObject from '../../../services/mssql/learning-objects/delete.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import createKeyword from '../../../services/mssql/keywords/create.service.js'
import deleteKeyword from '../../../services/mssql/keywords/delete.service.js'
import { ActivityStreamTableName } from '@tess-f/sql-tables/dist/lms/activity-stream.js'

import getRelated from './get.controller.js'
import KeywordModel from '../../../models/keyword.model.js'

const keyword1 = uuidv4()
const keyword2 = uuidv4()
//TODO : move this test to its appropriate spec file
describe('HTTP Related Learning Controller', () => {
  let obj1: LearningObjectModel
  let obj2: LearningObjectModel
  let context1: LearningContextModel
  let context2: LearningContextModel

  before(async () => await startServices())

  before(async () => {
    await createKeyword(new KeywordModel({ Name: keyword1 }))
    await createKeyword(new KeywordModel({ Name: keyword2 }))
    obj1 = await createLearningObject(new LearningObjectModel({
      Title: 'Test Object',
      Description: 'Test Object for related learning controller test',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      LearningObjectTypeID: LearningObjectTypes.Text,
      ContentID: uuidv4(),
      Keywords: [keyword1, keyword2]
    }))
    obj2 = await createLearningObject(new LearningObjectModel({
      Title: 'Test Object',
      Description: 'Test Object for related learning controller test',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      LearningObjectTypeID: LearningObjectTypes.Text,
      ContentID: uuidv4(),
      Keywords: [keyword1]
    }))
    context1 = await createContext(new LearningContextModel({
      Title: 'Test Context',
      Description: 'Test Context for related learning controller test',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      ContextTypeID: LearningContextTypes.ElectiveOptional,
      Keywords: [keyword1, keyword2]
    }))
    context2 = await createContext(new LearningContextModel({
      Title: 'Test Context',
      Description: 'Test Context for related learning controller test',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      ContextTypeID: LearningContextTypes.ElectiveOptional
    }))
  })

  it('gets related learning for a learning object', async () => {
    const mocks = httpMocks.createMocks({
      params: { id: obj1.fields.ID },
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await getRelated(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const results = JSON.parse(mocks.res._getData())
    expect(results.learningObjects.length).to.be.gte(0)
    expect(results.learningContexts.length).to.be.gte(0)
  })

  after(async () => {
    await deleteContext(context1.fields.ID!)
    await deleteContext(context2.fields.ID!)
    await deleteLearningObject(obj1.fields.ID!)
    await deleteLearningObject(obj2.fields.ID!)
    await deleteKeyword(keyword1)
    await deleteKeyword(keyword2)
    await mssql.getPool().request().query(`
      DELETE FROM [${ActivityStreamTableName}]
    `)
    await shutdownServices()
  })
})
