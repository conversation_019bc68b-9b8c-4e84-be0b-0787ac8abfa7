﻿import express from 'express'
import settings from '../config/settings.js'
import logger from '@lcs/logger'
import rabbitMQ from '@lcs/rabbitmq'
import mssql from '@lcs/mssql-utility'
import sessionAuthority from '@lcs/session-authority'

import RpcController from '../controllers/amqp/rpc.controller.js'
import UserExchange from '../controllers/amqp/user-exchange.controller.js'
import LRSExchange from '../controllers/amqp/lrs-exchange.controller.js'

import router from './router.js'
import { RedisClient } from '../services/redis/client.service.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const app = express()
// disable app fingerprint (security requirement)
app.disable('x-powered-by')

const log = logger.create('Server')
const sessionLog = logger.create('Server.Session-Authority')

let rpcController: RpcController
let userExchange: UserExchange
let lrsExchange: LRSExchange

export default async function main () {
  try {
    settings.print()
  } catch (error) {
    console.error('Could not print settings', { error })
  }

  try {
    setupLogger()
  } catch (error) {
    console.error('Could not setup logger', error)
  }

  try {
    await setupMssql()

    await setupRabbit()

    await setupSessionAuthorityProxy()
    await setupControllers()
    
    // attach the router
    app.use(settings.server.api, router)

    // Start the server
    const server = app.listen(settings.server.port, () => {
      log('info', `listening on port :: ${settings.server.port}`)
    })

    // Set the server response timeout
    server.setTimeout(settings.server.timeout)

    // Return app and server objects for testing
    return {
      app,
      close: () => (async () => {
        await rabbitMQ.close()
        await RedisClient.shutdown()
        log('info', 'Successful server shutdown', { success: true })
        await logger.close()
        server.close(() => { process.exit(0) })
      })()
    }
  } catch (error) {
    await handleFatalError(error)
  }
}

async function setupMssql () {
  await mssql.init(settings.mssql.connectionConfig, settings.mssql.forceEncrypted, settings.mssql.streamChunkSize)

  log('info', 'mssql connection initialized')
}

async function handleFatalError (error: unknown) {
  if (rpcController) await rpcController.close()
  if (userExchange) await userExchange.close()
  if (lrsExchange) await lrsExchange.close()
  log('error', 'Critical server error', { errorMessage: getErrorMessage(error) })
  process.exit(-1)
}

function setupLogger () {
  const loggerConfig = JSON.parse(JSON.stringify(settings.logger))
  if (!loggerConfig.useElasticsearch) {
    // if we are not using elasticsearch delete the options
    delete loggerConfig.elasticsearch
  }
  logger.init(loggerConfig)
  log('info', 'logger initialized')
}

async function setupRabbit () {
  await rabbitMQ.connect(settings.amqp.config)
  log('info', 'Connected to message queue', { success: true })
}

async function setupControllers () {
  // Connect the amqp controller
  rpcController = new RpcController(settings.amqp.queue)
  // Connect to the event capture controller
  userExchange = new UserExchange(settings.amqp.user_exchange.name, settings.amqp.user_exchange.subscription_queue, Object.values(settings.amqp.user_exchange.routes))
  lrsExchange = new LRSExchange(settings.amqp.lrs_exchange.name, settings.amqp.lrs_exchange.subscription_queue, settings.amqp.lrs_exchange.bound_routes)
}

async function setupSessionAuthorityProxy () {
  if (settings.sessionAuthority) {
    sessionAuthority.events.on('verbose', message => sessionLog('verbose', message.message, { data: message.data }))
    sessionAuthority.events.on('info', message => sessionLog('info', message.message, { data: message.data }))
    sessionAuthority.events.on('error', message => sessionLog('error', message.message, { error: message.error }))
    await sessionAuthority.init(settings.sessionAuthority)
    log('info', 'Session authority initialized')
  } else {
    throw new Error('Failed to set up session authority missing config')
  }
}
