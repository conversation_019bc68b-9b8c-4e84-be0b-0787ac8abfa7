import mssql from '@lcs/mssql-utility'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'

export default async function (contextID: string): Promise<string[]> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('contextID', contextID)
  return (await request.query<{ ParentContextID: string }>(`
    SELECT [${LearningContextFields.ParentContextID}]
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ID}] = @contextID
    AND [${LearningContextFields.ParentContextID}] IS NOT NULL
    UNION
    SELECT [${LearningContextConnectionFields.ParentContextID}]
    FROM [${LearningContextConnectionsTableName}]
    WHERE [${LearningContextConnectionFields.ConnectedContextID}] = @contextID
  `)).recordset.map(record => record.ParentContextID)
}
