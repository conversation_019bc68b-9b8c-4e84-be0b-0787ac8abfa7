/**
 * Gets all the SCORM 1.2 interactions for a learning object
 * And returns them as a CSV file
 */

import { Request, Response } from 'express'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus
import getAllLearnerProgressForInteractionReport, { LearnerProgressWithUserData } from '../../../services/mssql/learner-progress/get-all-for-learning-object-interaction-report.service.js'
import getInteractionsForLearnerProgress from '../../../services/mssql/scorm-1-2-interaction/get-for-learner-progress.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { Parser } from '@json2csv/plainjs'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.get-scorm-1.2-interactions-for-object', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    // Get all Learner Progress records for learning object (with user name)
    const learnerProgressRecords = await getAllLearnerProgressForInteractionReport(id)

    // set up csv report fields
    // value = the object key
    // label = the label to use in the CSV for the header
    const fields: { label: string, value: string }[] = [
      { value: 'ID', label: 'Session ID' },
      { value: 'CompletedDate', label: 'Completion Date' },
      { value: 'MaxScore', label: 'Max Score' },
      { value: 'MinScore', label: 'Min Score' },
      { value: 'RawScore', label: 'Score' },
      { value: 'TotalTime', label: 'Total Time' },
      { value: 'FirstName', label: 'First Name' },
      { value: 'LastName', label: 'Last Name' },
      { value: 'UserID', label: 'User ID' },
      { value: 'LessonStatus', label: 'Status' }
    ]
    // for each learner progress get interaction data
    // and flatten interactions
    const report = await Promise.all(learnerProgressRecords.map(async progress => {
      // get the interactions for this record
      try {
        const interactions = await getInteractionsForLearnerProgress(progress.ID)
        const flattened: LearnerProgressWithUserData & {[key: string]: any} = progress
        interactions.forEach(interaction => {
          flattened[`${interaction.fields.ID}-ID`] = interaction.fields.ID!
          // check if fields has this key
          if (!fields.find(f => f.value === `${interaction.fields.ID}-ID`)) {
            fields.push({
              value: `${interaction.fields.ID}-ID`,
              label: 'Question ID'
            })
          }

          if (interaction.fields.StudentResponse) {
            flattened[`${interaction.fields.ID}-response`] = interaction.fields.StudentResponse
            if (!fields.find(f => f.value === `${interaction.fields.ID}-response`)) {
              fields.push({
                value: `${interaction.fields.ID}-response`,
                label: 'Student Response'
              })
            }
          }

          if (interaction.fields.Result) {
            flattened[`${interaction.fields.ID}-result`] = interaction.fields.Result
            if (!fields.find(f => f.value === `${interaction.fields.ID}-result`)) {
              fields.push({
                value: `${interaction.fields.ID}-result`,
                label: 'Result'
              })
            }
          }

          if (interaction.fields.CorrectResponses) {
            flattened[`${interaction.fields.ID}-correct-response`] = interaction.fields.CorrectResponses
            if (!fields.find(f => f.value === `${interaction.fields.ID}-correct-response`)) {
              fields.push({
                value: `${interaction.fields.ID}-correct-response`,
                label: 'Correct Response'
              })
            }
          }

          if (interaction.fields.Latency) {
            flattened[`${interaction.fields.ID}-latency`] = interaction.fields.Latency
            if (!fields.find(f => f.value === `${interaction.fields.ID}-latency`)) {
              fields.push({
                value: `${interaction.fields.ID}-latency`,
                label: 'Latency'
              })
            }
          }
        })
        return flattened
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        if (errorMessage !== dbErrors.default.NOT_FOUND_IN_DB) {
          throw error
        }
        return progress
      }
    }))

    const parser = new Parser({ fields })
    const csv = parser.parse(report)

    res.header('Content-Type', 'text/csv')
    res.attachment('learner-interaction-report.csv')
    res.send(csv)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to generate SCORM 1.2 interactions report: input validation error', { success: false, req, error })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter, '))
    } else {
      log('error', 'Failed to generate SCORM 1.2 interactions report', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
