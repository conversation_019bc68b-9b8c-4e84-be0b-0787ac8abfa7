const chai = require('chai');
const expect = chai.expect;
const tester = require('../api/utils/test-agent.utils');
const uuidv4 = require('uuid/v4');
const settings = require('../api/config/settings');

const course1 = "AD5C499B-6AD5-4C5E-BC48-938527CEA049"; // Course 1

let isolated = false;

let context = {
    Title: uuidv4(),
    ContextTypeID: 1
};

let context2 = {
    Title: uuidv4(),
    ContextTypeID: 1,
    prerequisites: [{
        PrereqCourseID: 'EA50A30B-B553-4B73-B93C-3828EA4603BD', // Course 2
        Enforce: true
    }]
};

let subContext = {
    Title: uuidv4(),
    ContextTypeID: 2
};


describe("Learning Contexts", function() {

    this.timeout(30000);

    before( done => {
        if(tester.agent == null) {
            isolated = true;
            tester.create()
            .then( agent => {
                done();
            })
            .catch( err => {
                console.error(err);
                done();
            });
        } else {
            done();
        }
    });

    it('creates a learning context', done => {
        
        tester.agent
        .post(settings.server.root + 'learning-context')
        .send( context )
        .end((err, res) => {
            expect(res.statusCode).to.equal(200);
            context = res.body;
            done();
        });
    });

    it('creates a learning context with prerequisites', done => {

        tester.agent
        .post(settings.server.root + 'learning-context')
        .send( context2 )
        .end((err, res) => {
            expect(res.statusCode).to.equal(200);
            context2 = res.body;
            done();
        });
    });

    it('creates a sub learning context', done => {

        tester.agent
        .post(settings.server.root + 'learning-context')
        .send( subContext )
        .end((err, res) => {
            expect(res.statusCode).to.equal(200);
            subContext = res.body;
            done();
        });
    });

    it('updates a learning context', done => {
        const newTitle = uuidv4();

        tester.agent
        .put(settings.server.root + 'learning-context/' + context.ID )
        .send( { Title: newTitle } )
        .end((err, res) => {
            expect(res.statusCode).to.equal(200);
            context = res.body;
            done();
        });
    });

    it('adds course prereqs to a context', done => {

        tester.agent
        .put(settings.server.root + 'learning-context/' + context2.ID )
        .send({ prerequisites: [{
            PrereqCourseID: '3B90A056-805C-427A-96CF-C1EDD5A17282', // Course 3
            Enforce: true
        }]})
        .end((err, res) => {
            expect(res.statusCode).to.equal(200);
            context2 = res.body;
            done();
        });
    });

    it('deletes created learning contexts', done => {

        tester.agent
        .delete(settings.server.root + 'learning-context/' + context.ID )
        .end((err, res) => {

            expect(res.statusCode).to.equal(204);
            
            tester.agent
            .delete(settings.server.root + 'learning-context/' + context2.ID )
            .end((err, res) => {
                expect(res.statusCode).to.equal(204);

                done();
            });
        });
    });

    it('gets a learning context', done => {

        tester.agent
        .get(settings.server.root + 'learning-context/' + course1 + '?prerequisites=true&nestedContexts=true&incViews=true&rating=true' )
        .end((err, res) => {
            expect(res.statusCode).to.equal(200);
            expect(res.body.ID).to.equal( course1 );
            done();
        });

    });

    it('gets a course duration', function (done) {

        tester.agent
        .get(settings.server.root + 'context-duration/' + course1  )
        .end((err, res) => {
            expect(res.statusCode).to.equal(200);
            expect(res.body.duration).to.exist;
            done();
        });
    });

    it('gets multiple learning contexts', done => {

        tester.agent
        .get(settings.server.root + 'learning-contexts?prerequisites=true&rating=true&nestedContexts=true')
        .end((err, res) => {
            expect(res.statusCode).to.equal(200);
            done();
        });

    });

    it('gets popular learning contexts', done => {

        tester.agent
        .get(settings.server.root + 'popular-context')
        .end((err, res) => {
            expect(res.statusCode).to.equal(200);
            done();
        });

    });

    it('gets recent learning contexts', done => {

        tester.agent
        .get(settings.server.root + 'recent-context')
        .end((err, res) => {
            expect(res.statusCode).to.equal(200);
            done();
        });
    });

    after((done)=>{
        if(isolated) {
            tester.close();
        } 
        done();
    });

});