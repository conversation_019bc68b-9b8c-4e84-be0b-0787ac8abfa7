import logger from '@lcs/logger'
import mssql, { DB_Errors as dbErrors, getRows } from '@lcs/mssql-utility'
import LearningContextModel from '../../../models/learning-context.model.js'
import LearningContextConnectionModel from '../../../models/learning-context-connection.model.js'
import { Request, Transaction } from 'mssql'
import { byConnectedContextID, ContextConnection } from '../learning-context-connections/get.service.js'
import { resetParent } from './reset-parent.service.js'
import AssignmentModel from '../../../models/assignment.model.js'
import { User, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { Assignment, AssignmentFields, AssignmentsTableName } from '@tess-f/sql-tables/dist/lms/assignment.js'
import { LearningContextConnection, LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { AssignmentLearningContextFields, AssignmentLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-context.js'
import settings from '../../../config/settings.js'
import { sendGenericMessage } from '@tess-f/email/dist/amqp/send-generic-message.js'
import { AssignmentLearningObjectFields, AssignmentLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-object.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import sendNotification from '../../../services/amqp/notification/send-notification.service.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'

const log = logger.create('Service-MSSQL.delete-learning-context')

export default async function (contextID: string): Promise<void> {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    // Get all the contexts that need to be removed ( Parent and nested )
    // We need to be careful with nested, we should only remove it if it's not used somewhere else
    let learningContexts = await getLearningContexts(transaction, contextID)
    log('debug', 'Found learning contexts', { count: learningContexts.length })

    learningContexts = (await Promise.all(learningContexts.map(async context => {
      if (context.fields.ID === contextID) {
        return context
      } else {
        // we need to check if this context is used in other places before we remove it
        let connected: ContextConnection[]
        try {
          log('debug', 'Checking if context is used in other places', { id: context.fields.ID })
          connected = (await byConnectedContextID(context.fields.ID!)).filter(c => c.ParentContextID !== contextID)
          log('debug', 'Found connections', { count: connected.length })
        } catch (err) {
          if (getErrorMessage(err) !== dbErrors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to check if context is used in other places', { id: context.fields.ID, errorMessage: getErrorMessage(err), success: false })
            throw err
          }
          connected = []
        }
        if (connected.length > 0) {
          log('debug', 'Reparenting context', { count: connected.length})
          // this context is used in other places we just need to reset the parent of this context
          await resetParent(transaction, context, connected[0].ParentContextID, connected[0].OrderID)
          return null
        }
        // we don't want to remove this context if it's a training event or cmi5 content
        if (context.fields.ContextTypeID === LearningContextTypes.CMI5Course || (context.fields.ContextTypeID === LearningContextTypes.Course && context.fields.CourseTypeID === CourseTypes.InstructorLed)) {
          // remove the parent connection
          log('debug', 'Removing context', { id: context.fields.ID })
          await removeParentFromContext(transaction.request(), context.fields.ID!)
          return null
        }
        return context
      }
    }))).filter(context => context !== null) as LearningContextModel[]

    // Update learning contexts where this context is used
    log('debug', 'Updating learning matrix order ids')
    await updateLearningMatrix(transaction.request(), contextID) // DON'T TOUCH THIS (we can't make this a trigger since there are times we don't want to update the order of these items but still remove the record)

    // get a list of assignments that will be effected by this delete
    log('debug', 'Getting effected assignments')
    const effectedAssignments = await getEffectedAssignments(transaction.request(), contextID)

    // update the effected assignments content order
    for (const assignment of effectedAssignments) {
      log('debug', 'Updating assignment content order', { assignmentId: assignment.fields.ID, contextID })
      const request = transaction.request()
      request.input('assignmentId', assignment.fields.ID)
      request.input('contextId', contextID)
  
      await request.query(`
        UPDATE [${AssignmentLearningContextsTableName}]
        SET [${AssignmentLearningContextFields.OrderID}] = [${AssignmentLearningContextFields.OrderID}] - 1
        WHERE [${AssignmentLearningContextFields.OrderID}] > (
          SELECT TOP(1) [${AssignmentLearningContextFields.OrderID}]
          FROM [${AssignmentLearningContextsTableName}]
          WHERE [${AssignmentLearningContextFields.AssignmentID}] = @assignmentId
          AND [${AssignmentLearningContextFields.LearningContextID}] = @contextId
        )
        AND [${AssignmentLearningContextFields.AssignmentID}] = @assignmentId
      `)
  
      await request.query(`
        UPDATE [${AssignmentLearningObjectsTableName}]
        SET [${AssignmentLearningObjectFields.OrderID}] = [${AssignmentLearningObjectFields.OrderID}] - 1
        WHERE [${AssignmentLearningObjectFields.OrderID}] > (
          SELECT TOP(1) [${AssignmentLearningContextFields.OrderID}]
          FROM [${AssignmentLearningContextsTableName}]
          WHERE [${AssignmentLearningContextFields.AssignmentID}] = @assignmentId
          AND [${AssignmentLearningContextFields.LearningContextID}] = @contextId
        )
        AND [${AssignmentLearningObjectFields.AssignmentID}] = @assignmentId
      `)
    }

    // delete leaf nodes first
    log('debug', 'Deleting context(s)', { ids: learningContexts.map(context => context.fields.ID) })
    await deleteLearningContexts(transaction.request(), learningContexts)

    await transaction.commit()

    // now that the deletion is done lets notify the assignment creators of the change
    log('debug', 'Notifying assignment creators of the change', { ids: effectedAssignments.map(assignment => assignment.fields.ID) })
    await notifyOfAssignmentModification(effectedAssignments, learningContexts.find(context => context.fields.ID === contextID)!)
  } catch (err) {
    log('error', 'Failed to delete learning context', { id: contextID, errorMessage: getErrorMessage(err), success: false })
    if (!rolledBack) await transaction.rollback()
    throw err
  } finally {
    log('debug', 'Rolling back transaction')
    if (transaction) transaction.removeAllListeners('rollback')
  }
}

// Deletes multiple learning contexts, leaf nodes first
async function deleteLearningContexts (request: Request, learningContexts: LearningContextModel[]): Promise<void> {
  const conds = learningContexts.map((context, index) => {
    request.input('id_' + index, context.fields.ID)
    return '@id_' + index
  })

  const query = `DELETE FROM [${LearningContextTableName}] WHERE [${LearningContextFields.ID}] IN (${conds.join(', ')})`

  await request.query(query)
}

// Get a learning context and all it's nested contexts
async function getLearningContexts (transaction: Transaction, contextID: string): Promise<LearningContextModel[]> {
  const record = await getRows<LearningContext>(LearningContextTableName, transaction.request(), { ID: contextID })
  const nestedContexts = await getNestedContexts(transaction.request(), contextID)

  nestedContexts.push(new LearningContextModel(undefined, record[0]))
  return nestedContexts
}

// Recursive sql function to get all nested context
async function getNestedContexts (request: Request, contextID: string): Promise<LearningContextModel[]> {
  const query = `
    WITH nested as (
    SELECT *
        FROM [${LearningContextTableName}] WHERE [${LearningContextFields.ParentContextID}] = @id
        UNION ALL
            SELECT [${LearningContextTableName}].*
            FROM [${LearningContextTableName}]
            JOIN nested ON [${LearningContextTableName}].[${LearningContextFields.ParentContextID}] = nested.[${LearningContextFields.ID}]
    )
    SELECT * FROM nested
  `

  request.input('id', contextID)

  const res = await request.query<LearningContext>(query)

  return res.recordset.map(record => new LearningContextModel(undefined, record))
}

async function getEffectedAssignments (request: Request, contextID: string): Promise<AssignmentModel[]> {
  request.input('contextID', contextID)
  const response = await request.query<Assignment>(`
    SELECT *
    FROM [${AssignmentsTableName}]
    WHERE [${AssignmentFields.ID}] IN (
      SELECT [${AssignmentLearningContextFields.AssignmentID}]
      FROM [${AssignmentLearningContextsTableName}]
      WHERE [${AssignmentLearningContextFields.LearningContextID}] = @contextID
    )
    AND [${AssignmentFields.DeletedOn}] IS NULL
  `)
  return response.recordset.map(record => new AssignmentModel(undefined, record))
}

async function notifyOfAssignmentModification (assignments: AssignmentModel[], context: LearningContextModel): Promise<void> {
  for (const assignment of assignments) {
    if (assignment.fields.DeletedOn !== undefined && assignment.fields.DeletedOn !== null) {
      continue
    }
    // get the creators email
    const request = mssql.getPool().request()
    request.input('userID', assignment.fields.CreatedBy)
    const user = await request.query<User>(`
      SELECT TOP(1) [${UserFields.Email}]
      FROM [${UserTableName}]
      WHERE [${UserFields.ID}] = @userID
    `)

    if (user.recordset.length > 0 ) {
      const msg = `The ${context.fields.Label} ${context.fields.Title} has been deleted and removed from the assignment ${assignment.fields.Title}.`
      if (user.recordset[0].Email) {
        await sendGenericMessage(
          settings.amqp.service_queues.email, {
            to: [user.recordset[0].Email],
            message: msg,
            header: 'Assignment Updated',
            subject: 'Assignment Content Removed'
          },
          settings.amqp.command_timeout
        )
      }

      // notify the user of assignment deleted and removed
      await sendNotification({
        Title: 'Assignment Content Removed',
        Message: msg,
        PublishDate: new Date(),
        Everyone: false,
        SystemID: 'lms',
        Priority: 1,
        UserIDs: [user.recordset[0].ID!],
        GroupIDs: []
      })
    }
  }
}

/**
 * This function will reorder the learning objects and connected
 * learning contexts that this learning context is connected to
 */
async function updateLearningMatrix (request: Request, contextID: string): Promise<void> {
  // get the parent context ID's
  const parentContextQuery = `
    SELECT [${LearningContextConnectionFields.ParentContextID}], [${LearningContextConnectionFields.OrderID}] 
    FROM [${LearningContextConnectionsTableName}] 
    WHERE [${LearningContextConnectionFields.ConnectedContextID}] = @contextID`
  request.input('contextID', contextID)

  const parentContexts = (await request.query<LearningContextConnection>(parentContextQuery)).recordset.map(record => new LearningContextConnectionModel(record))

  // loop through the parent contexts and update the order ids
  // of the learning contexts and objects for the parent

  for (let i = 0; i < parentContexts.length; i++) {
    const updateContextConnections = `UPDATE [${LearningContextConnectionsTableName}] \
            SET [${LearningContextConnectionFields.OrderID}] = [${LearningContextConnectionFields.OrderID}] - 1 \
            WHERE [${LearningContextConnectionFields.OrderID}] > @orderID_${i} \
            AND [${LearningContextConnectionFields.ParentContextID}] = @parentID_${i}`

    const updateObjects = `UPDATE [${LearningObjectContextsTableName}] \
            SET [${LearningObjectContextFields.OrderID}] = [${LearningObjectContextFields.OrderID}] - 1 \
            WHERE [${LearningObjectContextFields.OrderID}] > @orderID_${i} \
            AND [${LearningObjectContextFields.LearningContextID}] = @parentID_${i}`

    const updateContext = `
      UPDATE [${LearningContextTableName}]
      SET [${LearningContextFields.OrderID}] = [${LearningContextFields.OrderID}] - 1
      WHERE [${LearningContextFields.OrderID}] > @orderID_${i}
      AND [${LearningContextFields.ParentContextID}] = @parentID_${i}
    `

    request.input(`orderID_${i}`, parentContexts[i].fields.OrderID)
    request.input(`parentID_${i}`, parentContexts[i].fields.ParentContextID)

    await request.query(updateContext)
    await request.query(updateObjects)
    await request.query(updateContextConnections)
  }

  // delete the context for the connected contexts

  const deleteQuery = `DELETE FROM [${LearningContextConnectionsTableName}] WHERE [${LearningContextConnectionFields.ConnectedContextID}] = @contextID`
  await request.query(deleteQuery)
}

// remove parent from context
async function removeParentFromContext (request: Request, contextId: string): Promise<void> {
  const query = `
    UPDATE [${LearningContextTableName}]
    SET [${LearningContextFields.ParentContextID}] = NULL
    WHERE [${LearningContextFields.ID}] = @contextID
  `
  request.input('contextID', contextId)
  await request.query(query)
}