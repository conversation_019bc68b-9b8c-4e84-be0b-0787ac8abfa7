import SkillLevel, { skillLevelSchema } from '../../../models/skill-level.model.js'
import updateService from '../../../services/mssql/skill-levels/update.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.update-skill-level', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-update-skill-level' label."

  try {
    const skillLevel = new SkillLevel(skillLevelSchema.partial().parse(req.body))
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    skillLevel.fields.ID = id

    const updated = (await updateService(skillLevel)).fields

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-update-skill-level' label and message indicating a successful update."
    log('info', 'Successfully updated skill level', { success: true, id: skillLevel.fields.ID, req })

    res.json(updated)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update skill level: input validation error', { success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to update skill level.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
