import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import Keyword, { createKeywordSchema } from '../../../models/keyword.model.js'

describe('HTTP Create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/keywords/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new Keyword({  })))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                keyword: {
                    Name: 'Test Keyword'
                }
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })


    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/keywords/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new Keyword({  })))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                keyword: {
                    Name: false
                }
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const message = mocks.res._getData()
        expect(message).eq('Invalid request data: Name: Expected string, received boolean')
    })

  
})