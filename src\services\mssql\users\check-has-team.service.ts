import mssql from '@lcs/mssql-utility'
import { UserTableName, UserFields } from '@tess-f/sql-tables/dist/id-mgmt/user.js'

export default async function checkUserHasClaim(userId: string): Promise<boolean> {
    const request = mssql.getPool().request()
    request.input('userID', userId)
    const results = await request.query<{ UserCount: number }>(`
        SELECT COUNT(*) AS UserCount
        FROM [${UserTableName}]
        WHERE [${UserFields.ManagerID}] = @userID
    `)

    return results.recordset[0].UserCount > 0
}
