import logger from '@lcs/logger'
import getContextCreators from '../../../services/mssql/learning-context/get-context-creators.service.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

const log = logger.create('Controller-HTTP.get-context-creators', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { type } = z.object({
      type: z.coerce.number().optional().superRefine((value, ctx) => {
        if (value && !Object.values(LearningContextTypes).includes(value)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Invalid learning context type',
            path: ['type']
          })
        }
      })
    }).parse(req.query)

    const creators = await getContextCreators(type)

    if (creators.length > 0) {
      log('info', 'Successfully retrieved learning context creators.', { count: creators.length, contextTypeId: type, success: true, req })
      res.json(creators)
    } else {
      log('info', 'No context creators found', { contextTypeId: type, success: true, req })
      res.json([])
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to retrieve context creators: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter, '))
    } else {
      log('error', 'Failed to retrieve context creators.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
