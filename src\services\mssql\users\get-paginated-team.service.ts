import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { User, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { getBasicUserFields } from './utils.js'

export default async function (managerID: string, offset: number = 0, limit: number = 10, search?: string): Promise<{ totalRecords: number, users: User[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('managerID', managerID)
  let query = `
    SELECT ${getBasicUserFields()},
      TotalRecords = COUNT(*) OVER()
    FROM [${UserTableName}]
    WHERE [${UserFields.ManagerID}] = @managerID
  `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [UserFields.FirstName, UserFields.LastName, UserFields.Username], 'any')})`
  }

  request.input('offset', offset)
  request.input('limit', limit)

  query += `
    ORDER BY [${UserFields.FirstName}], [${UserFields.LastName}]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<User & { TotalRecords: number }>(query)
  const totalRecords = results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0

  return {
    totalRecords,
    users: results.recordset
  }
}
