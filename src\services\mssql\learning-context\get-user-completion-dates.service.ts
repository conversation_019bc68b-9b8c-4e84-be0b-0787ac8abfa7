import mssql, { DB_Errors } from '@lcs/mssql-utility'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { FlatLearningContextTreeFields, FlatLearningContextTreeViewName } from '@tess-f/sql-tables/dist/lms/flat-learning-context-view.js'

export async function getUserInProgressDates (contextID: string, userID: string, to?: Date): Promise<{ startedOn: Date | undefined, LastAccessed: Date | undefined }> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('contextID', contextID)
  request.input('userID', userID)

  if (to) {
    request.input('to', to)
  }

  const baseQuery = `
    WITH Child_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}], [${LearningContextFields.VisibilityID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] = @contextID
      
      UNION ALL
      
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Child_Nodes] ON [Child_Nodes].[${LearningContextFields.ID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}] AND [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}] != ${Visibilities.Hidden}
    )
    SELECT TOP (1) [${LearnerProgressFields.CreatedOn}]
    FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @userID
    AND ([${LearnerProgressFields.LearningObjectID}] IN (
      SELECT [${LearningObjectContextFields.LearningObjectID}]
      FROM [${LearningObjectContextsTableName}]
      WHERE [${LearningObjectContextFields.LearningContextID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [Child_Nodes]
      )
    ) OR [${LearnerProgressFields.LearningContextSessionID}] IN (
      SELECT [${LearningContextSessionFields.ID}]
      FROM [${LearningContextSessionsTableName}]
      WHERE [${LearningContextSessionFields.LearningContextID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [Child_Nodes]
      )
    ))
    ${to ? `AND [${LearnerProgressFields.CreatedOn}] <= @to` : ''}
  `

  const started = await request.query<{CreatedOn: Date}>(baseQuery + `ORDER BY [${LearnerProgressFields.CreatedOn}] ASC`)
  const lastAccessed = await request.query<{CreatedOn: Date}>(baseQuery + `ORDER BY [${LearnerProgressFields.CreatedOn}] DESC`)

  return {
    LastAccessed: lastAccessed.recordset.length > 0 ? lastAccessed.recordset[0].CreatedOn : undefined,
    startedOn: started.recordset.length > 0 ? started.recordset[0].CreatedOn : undefined
  }
}

export async function getUserCompletedStartedDate (contextID: string, userID: string, lastCompleted: Date): Promise<Date> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('contextID', contextID)
  request.input('userID', userID)
  request.input('lastCompleted', lastCompleted)

  const previousCompletion = await request.query<{ CompletedOn: Date }>(`
    SELECT TOP (1) [${UserCompletedLearningContextFields.CompletedOn}]
    FROM [${UserCompletedLearningContextsTableName}]
    WHERE [${UserCompletedLearningContextFields.UserID}] = @userID
    AND [${UserCompletedLearningContextFields.LearningContextID}] = @contextID
    AND [${UserCompletedLearningContextFields.LessonStatusID}] >= ${LessonStatuses.fail}
    AND [${UserCompletedLearningContextFields.CompletedOn}] < @lastCompleted
    ORDER BY [${UserCompletedLearningContextFields.CompletedOn}] DESC
  `)

  const baseQuery = `
    WITH Child_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}], [${LearningContextFields.VisibilityID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] = @contextID
      
      UNION ALL
      
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Child_Nodes] ON [Child_Nodes].[${LearningContextFields.ID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}] AND [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}] != ${Visibilities.Hidden}
    )
    SELECT TOP (1) [${LearnerProgressFields.CreatedOn}]
    FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @userID
    AND ([${LearnerProgressFields.LearningObjectID}] IN (
      SELECT [${LearningObjectContextFields.LearningObjectID}]
      FROM [${LearningObjectContextsTableName}]
      WHERE [${LearningObjectContextFields.LearningContextID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [Child_Nodes]
      )
    ) OR [${LearnerProgressFields.LearningContextSessionID}] IN (
      SELECT [${LearningContextSessionFields.ID}]
      FROM [${LearningContextSessionsTableName}]
      WHERE [${LearningContextSessionFields.LearningContextID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [Child_Nodes]
      )
    ))
  `

  if (previousCompletion.recordset.length > 0) {
    request.input('previous', previousCompletion.recordset[0].CompletedOn)
    const started = await request.query<{ CreatedOn: Date }>(baseQuery + `
      AND [${LearnerProgressFields.CreatedOn}] BETWEEN @previous AND @lastCompleted
      ORDER BY [${LearnerProgressFields.CreatedOn}] ASC
    `)
    if (started.recordset.length > 0) {
      return started.recordset[0].CreatedOn
    } else {
      throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
    }
  }

  const started = await request.query<{ CreatedOn: Date }>(baseQuery + `
    AND [${LearnerProgressFields.CreatedOn}] <= @lastCompleted
    ORDER BY [${LearnerProgressFields.CreatedOn}] ASC
  `)
  if (started.recordset.length > 0) {
    return started.recordset[0].CreatedOn
  } else {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }
}
