import mssql, { deleteRow, getRows } from '@lcs/mssql-utility'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { Request } from 'mssql'
import logger from '@lcs/logger'
import AssignmentModel from '../../../models/assignment.model.js'
import { User, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { Assignment, AssignmentFields, AssignmentsTableName } from '@tess-f/sql-tables/dist/lms/assignment.js'
import { AssignmentLearningObjectFields, AssignmentLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-object.js'
import settings from '../../../config/settings.js'
import { sendGenericMessage } from '@tess-f/email/dist/amqp/send-generic-message.js'
import { AssignmentLearningContextFields, AssignmentLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-context.js'
import sendNotification from '../../../services/amqp/notification/send-notification.service.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import removeEvaluationConnection from '../../amqp/evaluation/remove-connection.service.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service-MSSQL.Delete-Learning-Object')

export default async function (id: string): Promise<number> {
  let rolledBack = false
  const pool = mssql.getPool()
  const transaction = pool.transaction()

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })
    log('verbose', 'begin delete learning object', { id })

    // get the object we are deleting
    const learningObject = await getLearningObject(transaction.request(), id)

    // get the assignments that this delete will effect
    const effectedAssignments = await getEffectedAssignments(transaction.request(), id)

    // update the effected assignments content order
    await Promise.all(effectedAssignments.map(async assignment => {
      const request = transaction.request()
      request.input('assignmentId', assignment.fields.ID)
      request.input('objectId', id)

      await request.query(`
        UPDATE [${AssignmentLearningContextsTableName}]
        SET [${AssignmentLearningContextFields.OrderID}] = [${AssignmentLearningContextFields.OrderID}] - 1
        WHERE [${AssignmentLearningContextFields.OrderID}] > (
          SELECT TOP(1) [${AssignmentLearningObjectFields.OrderID}]
          FROM [${AssignmentLearningObjectsTableName}]
          WHERE [${AssignmentLearningObjectFields.AssignmentID}] = @assignmentId
          AND [${AssignmentLearningObjectFields.LearningObjectID}] = @objectId
        )
        AND [${AssignmentLearningContextFields.AssignmentID}] = @assignmentId
      `)

      await request.query(`
        UPDATE [${AssignmentLearningObjectsTableName}]
        SET [${AssignmentLearningObjectFields.OrderID}] = [${AssignmentLearningObjectFields.OrderID}] - 1
        WHERE [${AssignmentLearningObjectFields.OrderID}] > (
          SELECT TOP(1) [${AssignmentLearningObjectFields.OrderID}]
          FROM [${AssignmentLearningObjectsTableName}]
          WHERE [${AssignmentLearningObjectFields.AssignmentID}] = @assignmentId
          AND [${AssignmentLearningObjectFields.LearningObjectID}] = @objectId
        )
        AND [${AssignmentLearningObjectFields.AssignmentID}] = @assignmentId
      `)
    }))

    const rowsDeleted = await deleteRow(transaction.request(), LearningObjectsTableName, { ID: id })
    log('verbose', 'deleted learning object', { id })

    await transaction.commit()

    if (
      learningObject.fields.LearningObjectTypeID === LearningObjectTypes.EVAL_ASSESSMENT ||
      learningObject.fields.LearningObjectTypeID === LearningObjectTypes.EVAL_CHECKLIST ||
      learningObject.fields.LearningObjectTypeID === LearningObjectTypes.EVAL_SURVEY
    ) {
      // remove the learning object connection from the evaluation
      try {
        await removeEvaluationConnection({
          EvaluationId: learningObject.fields.ContentID ?? '',
          ReferenceId: learningObject.fields.ID ?? '',
          SystemId: 'lms'
        })
      } catch (e) {
        log('error', 'Failed to remove learning object connection from evaluation.', { errorMessage: getErrorMessage(e), success: false })
      }
    }

    // now that we have successfully deleted the learning object let's notify
    // assignment creators that the object was removed from their assignments
    await notifyAssignmentCreators(effectedAssignments, learningObject)
    return rowsDeleted
  } catch (error) {
    if (!rolledBack && transaction) await transaction.rollback()
    throw error
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}

async function getLearningObject (request: Request, objectID: string): Promise<LearningObjectModel> {
  const result = await getRows<LearningObject>(LearningObjectsTableName, request, { ID: objectID })
  return new LearningObjectModel(result[0])
}

async function getEffectedAssignments (request: Request, objectID: string): Promise<AssignmentModel[]> {
  request.input('objectID', objectID)
  const result = await request.query<Assignment>(`
    SELECT *
    FROM [${AssignmentsTableName}]
    WHERE [${AssignmentFields.ID}] IN (
      SELECT [${AssignmentLearningObjectFields.AssignmentID}]
      FROM [${AssignmentLearningObjectsTableName}]
      WHERE [${AssignmentLearningObjectFields.LearningObjectID}] = @objectID
    )
  `)
  return result.recordset.map(record => new AssignmentModel(record))
}

async function notifyAssignmentCreators (assignments: AssignmentModel[], learningObject: LearningObjectModel) {
  for (const assignment of assignments) {
    const request = mssql.getPool().request()
    const user = await getRows<User>(UserTableName, request, { ID: assignment.fields.CreatedBy })
    if (user.length > 0) {
      const msg = `The file ${learningObject.fields.Title} has been deleted and removed from the assignment ${assignment.fields.Title}.`
      if (user[0].Email) {
        await sendGenericMessage(
          settings.amqp.service_queues.email, {
            to: [user[0].Email],
            message: msg,
            header: 'Assignment Updated',
            subject: 'Assignment Content Removed'
          },
          settings.amqp.command_timeout
        )
      }

      // notify the user of assignment deleted and removed
      await sendNotification({
        Title: 'Assignment Content Removed',
        Message: msg,
        PublishDate: new Date(),
        Everyone: false,
        SystemID: 'lms',
        Priority: 1,
        UserIDs: [user[0].ID!],
        GroupIDs: []
      })
    }
  }
}
