import { expect } from 'chai'
import mssql, { addRow } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import create from './create.service.js'
import getForReport from './get-for-report.service.js'
import getUserCompletionCountForContext from './get-user-completion-count-for-context.service.js'
import search from './search.service.js'
import UserCompletedLearningContextModel from '../../../models/user-completed-learning-context.model.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningContext } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

let userCompletedLearningContext: UserCompletedLearningContextModel
let learningContext: LearningContext

// FIXME: need to clean up test data
xdescribe('MSSQL User Completed Learning Contexts Service', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test user completed learning contexts',
      Description: `Running user-completed-learning-contexts on ${new Date()}`,
      ContextTypeID: LearningContextTypes.Course,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false
    }))
  })

  it('Unit test to create User Hidden Learning Contexts', async () => {
    userCompletedLearningContext = await create(new UserCompletedLearningContextModel({
      LearningContextID: learningContext.ID,
      UserID: AdminUserId,
      CompletedOn: new Date()
    }))
    expect(userCompletedLearningContext).to.be.not.eq(undefined)
  })

  it('Unit test to get for report', async () => {
    const results = await getForReport()
    expect(results).to.be.not.eq(undefined)
  })

  it('Unit test to get user completion count for context', async () => {
    const d = learningContext.CreatedOn!
    d.setDate(d.getDate() - 5)
    const results = await getUserCompletionCountForContext(AdminUserId, userCompletedLearningContext.fields.LearningContextID!, d, learningContext.CreatedOn)
    expect(results).to.be.not.eq(undefined)
  })

  it('Unit test to search', async () => {
    const results = await search(userCompletedLearningContext.fields.LearningContextID!, 0, 150)
    expect(results).to.be.not.eq(undefined)
  })
})
