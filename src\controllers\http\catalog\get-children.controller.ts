import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import getChildContextsService from '../../../services/mssql/catalog/get-child-contexts.service.js'
import getLearningObjectsService from '../../../services/mssql/learning-objects/get-multiple.service.js'
import learningContextExtrasMapper from '../../../mappers/learning-context-extras.mapper.js'
import { httpLogTransformer } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.Get-Child-Content-For-Context', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const contexts = await getChildContextsService(id)
    const objects = await getLearningObjectsService(id)
    log('info', 'Successfully retrieved content for learning context', { contextCount: contexts.length, objectCount: objects.length, success: true, req })

    // map the keywords on to the contexts (this will allow the UI to update the parent of context without breaking the keywords)
    await learningContextExtrasMapper(contexts, req.session.userId, { keywords: true })

    res.json(contexts.map(context => {
      const returnObj: any = context.fields
      returnObj.EntityType = 'Learning Context'
      return returnObj
    }).concat(objects.map(obj => {
      const returnObj: any = obj.fields
      returnObj.EntityType = 'Learning Object'
      return returnObj
    })))
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get child content of learning context: validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send('Invalid context id in request parameters')
    } else {
      log('error', 'Failed to get child content of learning context', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
