import mssql, { addRow } from '@lcs/mssql-utility'
import getLearningObject from '../learning-objects/get.service.js'
import createActivity from '../activity-stream/create.service.js'
import ActivityStreamModel from '../../../models/activity-stream.model.js'
import LearningObjectUserBookmarkModel from '../../../models/learning-object-user-bookmark.model.js'
import { LearningObjectUserBookmark } from '@tess-f/sql-tables/dist/lms/learning-object-user-bookmark.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'

export default async function (objectBookmark: LearningObjectUserBookmarkModel): Promise<LearningObjectUserBookmarkModel> {
  const pool = mssql.getPool()
  // create an activity stream record
  const lo = await getLearningObject(objectBookmark.fields.LearningObjectID!)
  const activity = new ActivityStreamModel({
    UserID: objectBookmark.fields.UserID,
    LinkText: lo.fields.Title,
    LinkID: objectBookmark.fields.LearningObjectID,
    ActivityID: Activities.BookmarkedLearningObject,
    CreatedOn: new Date()
  })
  await createActivity(activity)
  const record = await addRow<LearningObjectUserBookmark>(pool.request(), objectBookmark)
  return new LearningObjectUserBookmarkModel(record)
}
