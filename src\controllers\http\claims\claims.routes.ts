import { RequestHandler, Router } from 'express'
import getMultipleUserClaims from './user/get-multiple.controller.js'
import getMultipleGroupClaims from './group/get-multiple.controller.js'
import deleteGroupClaims from './group/delete.controller.js'
import createGroupClaims from './group/create.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

// User Claims
router.get('/user-claims/:id', getMultipleUserClaims as RequestHandler)
// Group Claims
router.get('/group-claims/:id',checkClaims([Claims.VIEW_SYSTEM_PERMISSIONS, Claims.MODIFY_SYSTEM_PERMISSIONS]), getMultipleGroupClaims as RequestHandler)
// route not used
router.delete('/group-claims/:id', checkClaims([Claims.MODIFY_SYSTEM_PERMISSIONS]), deleteGroupClaims as RequestHandler)
router.post('/group-claims/:id', checkClaims([Claims.MODIFY_SYSTEM_PERMISSIONS]), createGroupClaims as RequestHandler)

export default router
