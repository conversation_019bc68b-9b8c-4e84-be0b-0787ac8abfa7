import getAllForUsersILTCourseService from '../../../services/mssql/learner-progress/get-all-for-users-ILT-course.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'
const { INTERNAL_SERVER_ERROR, NOT_FOUND, BAD_REQUEST } = httpStatus

const log = logger.create('Controller-HTTP.get-user-learner-progress-for-ILT', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const progress = await getAllForUsersILTCourseService(id, req.session.userId)
    log('info', 'Successfully retrieved learning progress records for users ILT course', { count: progress.length, contextID: id, userID: req.session.userId, success: true, req })
    res.json(progress.map(prog => prog.fields))
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning progress for users ILT course: input validation error', { contextID: req.params.id, userID: req.session.userId, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learning progress for users ILT course because none were found in the database', { contextID: req.params.id, userID: req.session.userId, success: false, req })
      res.sendStatus(NOT_FOUND)
    } else {
      log('error', 'Failed to get learning progress for users ILT course', { contextID: req.params.id, userID: req.session.userId, success: false, errorMessage, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
