import get, { getAllContextRatingsForUser, getAllRatingsForContext, getUserRatingForContext } from '../../../services/mssql/learning-context-ratings/get.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NOT_FOUND } = httpStatus

const log = logger.create('Controller-HTTP.get-learning-context-ratings', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, click on "Home." Note the time. Check the LMS API log for the 'http-get-learning-context-ratings' label."

  try {
    const { id, context, user } = z.object({
      id: zodGUID.optional(),
      context: zodGUID.optional(),
      user: zodGUID.optional()
    }).superRefine(({ id, context, user }, ctx) => {
      if (!id && !context && !user) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Must provide either rating, context, or user id',
          path: ['query', 'id']
        })
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Must provide either rating, context, or user id',
          path: ['query', 'context']
        })
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Must provide either rating, context, or user id',
          path: ['query', 'user']
        })
      }
    }).parse(req.query)

    if (id) {
      const result = await get(id)
      log('info', 'Successfully retrieved learning context ratings.', { success: true, req })
      res.json(result.fields)
    } else if (context && user) {
      const result = await getUserRatingForContext(context, user)
      log('info', 'Successfully retrieved learning context ratings.', { success: true, req })
      res.json(result.fields)
    } else if (context) {
      const results = await getAllRatingsForContext(context)
      log('info', 'Successfully retrieved learning context ratings.', { success: true, req })
      res.json(results.map(rating => rating.fields))
    } else if (user) {
      const results = await getAllContextRatingsForUser(user)
      log('info', 'Successfully retrieved learning context ratings.', { success: true, req })
      res.json(results.map(rating => rating.fields))
    }
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning context ratings due to input validation error.', { errorMessage, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request query data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learning context ratings because they were not found in the database.', { success: false, req })
      res.sendStatus(NOT_FOUND)
    } else {
      log('error', 'Failed to get learning context ratings.', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
