const chai = require('chai');
const expect = chai.expect;
const uuidv4 = require('uuid/v4');
const tester = require('../api/utils/test-agent.utils');
const settings = require('../api/config/settings');

let keyword = { Name: uuidv4() };

let isolated = false;

describe('End-to-end: Keywords', () => {


    before( done => {
        if(tester.agent == null) {
            isolated = true;
            tester.create()
            .then( agent => {
                done();
            })
            .catch( err => {
                console.error(err);
                done();
            });
        } else {
            done();
        }
    });

    it('creates a keyword', done => {
        tester.agent.post(settings.server.root + 'keyword')
        .send({ keyword: keyword })
        .end((err, res) => {

            expect(res.statusCode).to.equal(200);
            expect(res.body).to.exist;
            expect(res.body.Name).to.exist;

            keyword = res.body;

            done();
        });
    });

    it('get multiple keywords', done => {
        tester.agent.get(settings.server.root + 'keywords')
        .end((err, res) => {

            expect(res.statusCode).to.equal(200);
            expect(res.body.length).to.be.gt(0);

            done();
        });
    });

    it('deletes a keyword', done => {
        tester.agent.delete(settings.server.root + 'keyword/' + keyword.Name)
        .end((err, res) => {

            expect(res.statusCode).to.equal(204);

            keyword = res.body;

            done();
        });
    });

    after((done)=>{
        if(isolated) {
            tester.close();
        } 
        done();
    });


});