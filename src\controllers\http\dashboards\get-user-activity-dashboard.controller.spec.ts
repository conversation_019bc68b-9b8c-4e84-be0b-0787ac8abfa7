import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: get user activity dashboard', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => sinon.restore())

  it('returns a bad request status code when id parameter is invalid', async () => {
    const controller = await esmock('./get-user-activity-dashboard.controller.js')
    const mocks = httpMocks.createMocks({ params: { id: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request parameter(s),')
    expect(data).to.include('id: Invalid')
  })

  it('returns the user activity metadata even when the date query params are invalid', async () => {
    const controller = await esmock('./get-user-activity-dashboard.controller.js', {
      '../../../services/mssql/my-learning/get-metadata.service.js': { default: sinon.stub().resolves({}) },
      '../../../services/mssql/learning-object-views/get-user-views-by-type.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/learning-context-views/get-user-views-by-type.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/keywords/get-view-count.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/learning-objects/get-view-history.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/activity-stream/get-by-user.service.js': { default: sinon.stub().resolves([]) }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() }, query: { from: 'test', to: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
  })

  it('returns the user activity metadata for the given date range', async () => {
    const controller = await esmock('./get-user-activity-dashboard.controller.js', {
      '../../../services/mssql/my-learning/get-metadata.service.js': { default: sinon.stub().resolves({}) },
      '../../../services/mssql/learning-object-views/get-user-views-by-type.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/learning-context-views/get-user-views-by-type.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/keywords/get-view-count.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/learning-objects/get-view-history.service.js': { default: sinon.stub().resolves([]) },
      '../../../services/mssql/activity-stream/get-by-user.service.js': { default: sinon.stub().resolves([]) }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() }, query: { from: new Date().toISOString(), to: new Date().toISOString() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
  })

  it('can gracefully handle an error', async () => {
    const controller = await esmock('./get-user-activity-dashboard.controller.js', {
      '../../../services/mssql/my-learning/get-metadata.service.js': { default: sinon.stub().rejects(new Error('Service Error')) }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() }, query: { from: new Date().toISOString(), to: new Date().toISOString() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })
})
