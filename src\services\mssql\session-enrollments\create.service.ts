import mssql, { addRow } from '@lcs/mssql-utility'
import SessionEnrollmentModel from '../../../models/session-enrollment.model.js'
import { SessionEnrollment } from '@tess-f/sql-tables/dist/lms/session-enrollment.js'

export default async function (sessionEnrollment: SessionEnrollmentModel): Promise<SessionEnrollmentModel> {
  const pool = mssql.getPool()
  const record = await addRow<SessionEnrollment>(pool.request(), sessionEnrollment)
  return new SessionEnrollmentModel(record)
}
