import mssql from '@lcs/mssql-utility'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import { v4 as uuidv4 } from 'uuid'
import { fillRequestHeaders } from '../../../utils/test-agent.utils.js'
import { startServices, shutdownServices } from '../../../utils/testing.utils.js'
import { LearningObjectJson } from '@tess-f/lms/dist/common/learning-object.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { ActivityStreamTableName } from '@tess-f/sql-tables/dist/lms/activity-stream.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import createContext from '../../../services/mssql/learning-context/create.service.js'
import deleteContext from '../../../services/mssql/learning-context/delete.service.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import createUserFavorite from '../../../services/mssql/learning-object-favorites/create.service.js'
import createUserBookmark from '../../../services/mssql/learning-object-bookmarks/create.service.js'
import createLearningObjectConnection from '../../../services/mssql/learning-object-contexts/create.service.js'
import LearningObjectContextModel from '../../../models/learning-object-context.model.js'
import LearningObjectUserBookmarkModel from '../../../models/learning-object-user-bookmark.model.js'
import LearningObjectUserFavoriteModel from '../../../models/learning-object-user-favorite.model.js'
import createKeyword from '../../../services/mssql/keywords/create.service.js'
import deleteKeyword from '../../../services/mssql/keywords/delete.service.js'
import KeywordModel from '../../../models/keyword.model.js'

import get from './get.controller.js'
import getMulti from './get-multiple.controller.js'
import create from './create.controller.js'
import update from './update.controller.js'
import del from './delete.controller.js'
import getPaginated from './get-paginated.controller.js'

const keyword1 = 'TEST-1'
const keyword2 = 'TEST-2'

// TODO: move these tests to their appropriate spec files and utilize mocks for the services.

describe('Controller - HTTP: Learning Object', () => {
  let created: LearningObjectJson
  let created2: LearningObjectJson
  let context: LearningContextModel

  before(async () => {
    await startServices()
    context = await createContext(new LearningContextModel({
      Title: 'Test Context',
      Description: 'Test Context for Learning Object Controller Tests',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      ContextTypeID: LearningContextTypes.ElectiveOptional
    }))
    await createKeyword(new KeywordModel({ Name: keyword1 }))
    await createKeyword(new KeywordModel({ Name: keyword2 }))
  })

  it('creates a learning object', async () => {
    const mocks = httpMocks.createMocks({
      body: {
        Title: uuidv4(),
        Description: 'Test Learning Object for Learning Object Controller Tests',
        Keywords: [keyword1],
        LearningObjectTypeID: 1,
        ContentID: uuidv4(),
        VisibilityID: Visibilities.Browsable
      },
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await create(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    created = JSON.parse(mocks.res._getData())

    expect(created.ID).to.exist
    expect(created.Keywords).to.exist
    expect(created.Keywords![0]).to.eql(keyword1)
  })

  it('creates a second learning object', async () => {
    const mocks = httpMocks.createMocks({
      body: {
        Title: uuidv4(),
        Description: 'Test Learning Object for Learning Object Controller Tests',
        Keywords: [keyword2],
        LearningObjectTypeID: 1,
        ContentID: uuidv4(),
        VisibilityID: Visibilities.Browsable
      },
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await create(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    created2 = JSON.parse(mocks.res._getData())

    expect(created2.ID).to.exist
    expect(created2.Keywords).to.exist
    expect(created2.Keywords![0]).to.eql(keyword2)
  })

  it('updates a learning object', async () => {
    const newTitle = uuidv4()
    const mocks = httpMocks.createMocks({
      body: {
        ID: created.ID,
        Title: newTitle,
        Keywords: [keyword2],
        LearningObjectTypeID: 1
      },
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await update(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const updated: LearningObjectJson = JSON.parse(mocks.res._getData())

    expect(updated.Title).to.eq(newTitle)
    expect(updated.Keywords).to.exist
    expect(updated.Keywords![0]).to.eql(keyword2)
  })

  it('get a learning object', async () => {
    const mocks = httpMocks.createMocks({
      params: { id: created.ID },
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await get(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const learningObject: LearningObjectJson = JSON.parse(mocks.res._getData())
    expect(learningObject.Views).to.equal(0)
    expect(learningObject.Keywords).exist
  })

  it('get a learning object and increments views', async () => {
    const mocks = httpMocks.createMocks({
      params: { id: created.ID },
      query: { incViews: 'true' },
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await get(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const learningObject: LearningObjectJson = JSON.parse(mocks.res._getData())
    expect(learningObject.Views).to.equal(1)
  })

  it('gets a learning object with rating', async () => {
    const mocks = httpMocks.createMocks({
      params: { id: created.ID },
      query: { rating: 'true' },
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await get(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const learningObject: LearningObjectJson = JSON.parse(mocks.res._getData())
    expect(learningObject.Rating).to.exist
    expect(learningObject.RatingCount).to.exist
  })

  it('gets a learning object with context count', async () => {
    const mocks = httpMocks.createMocks({
      params: { id: created.ID },
      query: { contextCount: 'true' },
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await get(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const learningObject = JSON.parse(mocks.res._getData())
    expect(learningObject.ID).to.equal(created.ID)
    expect(learningObject.ContextCount).exist
  })

  it('connects a learning object with a learning context', async () => {
    await createLearningObjectConnection(new LearningObjectContextModel({
      LearningContextID: context.fields.ID,
      LearningObjectID: created.ID,
      OrderID: 1,
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    }))
    expect(true).to.be.true
  })

  it('gets a learning object with orderID of a context', async () => {
    const mocks = httpMocks.createMocks({
      params: { id: created.ID },
      query: { forContextID: context.fields.ID },
      session: { userId: AdminUserId }
    })
    fillRequestHeaders(mocks.req)

    await get(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const learningObject: LearningObjectJson = JSON.parse(mocks.res._getData())
    expect(learningObject.ID).to.equal(created.ID)
    expect(learningObject.OrderID).exist
  })

  it('gets multiple learning objects', async () => {
    const mocks = httpMocks.createMocks({
      query: { contextCount: 'true' },
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await getMulti(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const learningObjects: LearningObjectJson[] = JSON.parse(mocks.res._getData())
    expect(learningObjects.length).to.be.gt(1)
    expect(learningObjects[0].Keywords).exist
    expect(learningObjects[0].Views).exist
  })

  it('gets a paginated list of learning objects', async () => {
    const mocks = httpMocks.createMocks({
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await getPaginated(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const results: { totalRecords: number, objects: LearningObjectJson[] } = JSON.parse(mocks.res._getData())
    expect(results.totalRecords).to.be.gte(0)
    expect(results.objects.length).to.be.gte(0)
    expect(results.objects.length).to.be.lte(10)
  })

  it('gets multiple learning objects linked to a context', async () => {
    const mocks = httpMocks.createMocks({
      query: { contextID: context.fields.ID },
      session: { userId: AdminUserId }
    })
    fillRequestHeaders(mocks.req)

    await getMulti(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const learningObjects: LearningObjectJson[] = JSON.parse(mocks.res._getData())
    expect(learningObjects.length).to.be.gt(0)
  })

  it('gets multiple learning objects with given visibility', async () => {
    const mocks = httpMocks.createMocks({
      query: { visibility: Visibilities.Browsable },
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await getMulti(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const learningObjects: LearningObjectJson[] = JSON.parse(mocks.res._getData())
    expect(learningObjects.length).to.be.gt(0)
  })

  it('gets multiple learning objects with contextCount', async () => {
    const mocks = httpMocks.createMocks({
      query: { contextCount: 'true' },
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await getMulti(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const learningObjects: LearningObjectJson[] = JSON.parse(mocks.res._getData())

    expect(learningObjects.length).to.be.gt(0)
    expect(learningObjects[0].ContextCount).to.exist
  })

  it('gets multiple learning objects with ratings', async () => {
    const mocks = httpMocks.createMocks({
      query: { rating: 'true' },
      session: { userId: AdminUserId }
    })
    fillRequestHeaders(mocks.req)

    await getMulti(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const learningObjects: LearningObjectJson[] = JSON.parse(mocks.res._getData())

    for (let i = 0; i < learningObjects.length; i++) {
      expect(learningObjects[i].RatingCount).to.exist
    }
  })

  it('favorites a learning object', async () => {
    await createUserFavorite(new LearningObjectUserFavoriteModel({
      UserID: AdminUserId,
      LearningObjectID: created.ID
    }))
    expect(true).to.be.true
  })

  it('bookmarks a learning object', async () => {
    await createUserBookmark(new LearningObjectUserBookmarkModel({
      UserID: AdminUserId,
      LearningObjectID: created.ID
    }))
  })

  it('deletes a learning object', async () => {
    const mocks = httpMocks.createMocks({
      params: { id: created.ID },
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await del(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(204)
  })

  it('deletes a second learning object', async () => {
    const mocks = httpMocks.createMocks({
      params: { id: created2.ID },
      session: { userId: AdminUserId, sessionId: uuidv4() }
    })
    fillRequestHeaders(mocks.req)

    await del(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(204)
  })

  after(async () => {
    await deleteContext(context.fields.ID!)
    await deleteKeyword(keyword1)
    await deleteKeyword(keyword2)
    await mssql.getPool().request().query(`
      DELETE FROM [${ActivityStreamTableName}]
    `)
    await shutdownServices()
  })
})
