import { Table } from '@lcs/mssql-utility'
import { zodDates, zodGUID } from '@tess-f/backend-utils/validators'
import { LearnerProgress, LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { z } from 'zod'

const requiredFields = z.object({
  [LearnerProgressFields.LessonStatusID]: z.nativeEnum(LessonStatuses).default(LessonStatuses.notAttempted),
  [LearnerProgressFields.UserID]: zodGUID
})

const optionalFields = z.object({
  [LearnerProgressFields.LessonLocation]: z.string().nullable().optional(),
  [LearnerProgressFields.RawScore]: z.number().nullable().optional(),
  [LearnerProgressFields.MinScore]: z.number().nullable().optional(),
  [LearnerProgressFields.MaxScore]: z.number().nullable().optional(),
  [LearnerProgressFields.TotalTime]: z.string().max(13).nullable().optional(),
  [LearnerProgressFields.Exit]: z.string().max(10).nullable().optional(),
  [LearnerProgressFields.SuspendData]: z.string().nullable().optional(),
  [LearnerProgressFields.LearningObjectID]: zodGUID.nullable().optional(),
  [LearnerProgressFields.StartedDate]: zodDates.nullable().optional(),
  [LearnerProgressFields.CompletedDate]: zodDates.nullable().optional(),
  [LearnerProgressFields.Attempts]: z.number().optional(),
  [LearnerProgressFields.Plays]: z.number().nullable().optional(),
  [LearnerProgressFields.Pauses]: z.number().nullable().optional(),
  [LearnerProgressFields.Grade]: z.string().max(1).nullable().optional(),
  [LearnerProgressFields.LearningContextSessionID]: zodGUID.nullable().optional()
})

export const createLearnerProgressSchema = requiredFields.merge(optionalFields).superRefine(({ LearningObjectID, LearningContextSessionID }, ctx) => {
  if (!!LearningObjectID && !!LearningContextSessionID) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Cannot set both LearningObjectID and LearningContextSessionID'
    })
  } else if (!LearningObjectID && !LearningContextSessionID) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Must set either LearningObjectID or LearningContextSessionID'
    })
  }
})

export const updateLearnerProgressSchema = optionalFields.merge(requiredFields.partial()).superRefine(({ LearningObjectID, LearningContextSessionID }, ctx) => {
  if (!!LearningObjectID && !!LearningContextSessionID) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Cannot set both LearningObjectID and LearningContextSessionID'
    })
  } else if (!LearningObjectID && !LearningContextSessionID) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Must set either LearningObjectID or LearningContextSessionID'
    })
  }
})

export default class LearnerProgressModel extends Table<LearnerProgress, LearnerProgress> {
  public fields: LearnerProgress

  constructor (fields?: LearnerProgress) {
    super(LearnerProgressTableName, [
      LearnerProgressFields.LessonStatusID,
      LearnerProgressFields.UserID,
      LearnerProgressFields.CreatedOn
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearnerProgress): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearnerProgress {
    return this.fields
  }
}
