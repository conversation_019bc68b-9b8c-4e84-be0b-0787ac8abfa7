import mssql from '@lcs/mssql-utility'
import { LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { LearningContextTableName, LearningContextFields } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { Request } from 'mssql'

export default async function (contextID: string, objectID: string): Promise<number> {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    await reorderObjects(transaction.request(), contextID, objectID)
    await reorderContextConnections(transaction.request(), contextID, objectID)
    await reorderCourseContexts(transaction.request(), contextID, objectID)
    const rowsDeleted = await deleteObjectContext(transaction.request(), contextID, objectID)

    await transaction.commit()

    return rowsDeleted
  } catch (error) {
    if (!rolledBack) await transaction.rollback()
    throw error
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}

async function deleteObjectContext (request: Request, contextID: string, objectID: string): Promise<number> {
  request.input('LearningObjectID', objectID)
  request.input('LearningContextID', contextID)

  const query = `
    DELETE FROM [${LearningObjectContextsTableName}]
    WHERE [${LearningObjectContextFields.LearningObjectID}] = @LearningObjectID
    AND [${LearningObjectContextFields.LearningContextID}] = @LearningContextID
  `

  const result = await request.query(query)
  return result.rowsAffected[0]
}

async function reorderObjects (request: Request, contextId: string, objectId: string): Promise<void> {
  request.input('contextId', contextId)
  request.input('objectId', objectId)

  await request.query(`
    UPDATE [${LearningObjectContextsTableName}]
    SET [${LearningObjectContextFields.OrderID}] = [${LearningObjectContextFields.OrderID}] - 1
    WHERE [${LearningObjectContextFields.OrderID}] > (
      SELECT TOP(1) [${LearningObjectContextFields.OrderID}]
      FROM [${LearningObjectContextsTableName}]
      WHERE [${LearningObjectContextFields.LearningObjectID}] = @objectId
      AND [${LearningObjectContextFields.LearningContextID}] = @contextId
    ) AND [${LearningObjectContextFields.LearningContextID}] = @contextId
  `)
}

async function reorderContextConnections (request: Request, parentContextId: string, objectId: string): Promise<void> {
  request.input('parentContextId', parentContextId)
  request.input('objectId', objectId)

  await request.query(`
    UPDATE [${LearningContextConnectionsTableName}]
    SET [${LearningContextConnectionFields.OrderID}] = [${LearningContextConnectionFields.OrderID}] - 1
    WHERE [${LearningContextConnectionFields.OrderID}] > (
      SELECT TOP(1) [${LearningObjectContextFields.OrderID}]
      FROM [${LearningObjectContextsTableName}]
      WHERE [${LearningObjectContextFields.LearningContextID}] = @parentContextId
      AND [${LearningObjectContextFields.LearningObjectID}] = @objectId
    ) AND [${LearningContextConnectionFields.ParentContextID}] = @parentContextId
  `)
}

async function reorderCourseContexts (request: Request, contextId: string, objectId: string): Promise<void> {
  request.input('contextId', contextId)
  request.input('objectId', objectId)

  await request.query(`
    UPDATE [${LearningContextTableName}]
    SET [${LearningContextFields.OrderID}] = [${LearningContextFields.OrderID}] - 1
    WHERE [${LearningContextFields.OrderID}] > (
      SELECT TOP(1) [${LearningObjectContextFields.OrderID}]
      FROM [${LearningObjectContextsTableName}]
      WHERE [${LearningObjectContextFields.LearningContextID}] = @contextId
      AND [${LearningObjectContextFields.LearningObjectID}] = @objectId
    ) AND [${LearningContextFields.ParentContextID}] = @contextId
  `)
}
