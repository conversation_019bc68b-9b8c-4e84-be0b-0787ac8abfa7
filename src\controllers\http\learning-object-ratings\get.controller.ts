import get, { getAllObjectRatings, getAllObjectRatingsForUser, getObjectRatingsForUser } from '../../../services/mssql/learning-object-ratings/get.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import { DB_Errors } from '@lcs/mssql-utility'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-learning-object-ratings', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id, obj, user } = z.object({
      id: zodGUID.optional(),
      obj: zodGUID.optional(),
      user: zodGUID.optional()
    }).superRefine(({ id, obj, user }, ctx) => {
      if (!id && !obj && !user) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'must provided either rating, object, or user id'
        })
      }
    }).parse(req.query)

    if (id) {
      const result = await get(id)
      log('info', 'Successfully retrieved learning object ratings.', { success: true, req })
      res.json(result.fields)
    } else if (obj && user) {
      const result = await getObjectRatingsForUser(obj, user)
      log('info', 'Successfully retrieved learning object ratings.', { success: true, req })
      res.json(result.fields)
    } else if (obj) {
      const result = await getAllObjectRatings(obj)
      log('info', 'Successfully retrieved learning object ratings.', { success: true, req })
      res.json(result.map(rating => rating.fields))
    } else if (user) {
      const result = await getAllObjectRatingsForUser(user)
      log('info', 'Successfully retrieved learning object ratings.', { success: true, req })
      res.json(result.map(rating => rating.fields))
    }
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning object ratings because of invalid input.', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request query data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learning object ratings because they were not found in the database.', { success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      log('error', 'Failed to get learning object ratings.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
