import mssql from '@lcs/mssql-utility'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextUserViewFields, LearningContextUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-view.js'
import { Request } from 'mssql'

export default async function (from?: Date, to?: Date): Promise<{ Views: number, Label: string }[]> {
  const pool = mssql.getPool()
  return await getUserViewsByType(pool.request(), from, to)
}

async function getUserViewsByType (request: Request, from?: Date, to?: Date): Promise<{ Views: number, Label: string }[]> {
  let query = `
    SELECT COUNT([${LearningContextUserViewsTableName}].[${LearningContextUserViewFields.LearningContextID}]) AS Views, [${LearningContextTableName}].[${LearningContextFields.Label}]
    FROM [${LearningContextUserViewsTableName}]
    INNER JOIN [${LearningContextTableName}] ON [${LearningContextUserViewsTableName}].[${LearningContextUserViewFields.LearningContextID}] = [${LearningContextTableName}].[${LearningContextFields.ID}]
  `

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
    query += `WHERE [${LearningContextUserViewsTableName}].[${LearningContextUserViewFields.CreatedOn}] BETWEEN @from AND @to `
  }

  query += `GROUP BY [${LearningContextTableName}].[${LearningContextFields.Label}]`
  const results = await request.query<{ Views: number, Label: string }>(query)
  return results.recordset
}
