import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningObjectContext from '../../../models/learning-object-context.model.js'

describe('HTTP update controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-object-contexts/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectContext()))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                objectID: uuid(),
                contextID: uuid()
            },
            body: {
                OrderID: 1,
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })


    it('returns an error if the request params are invalid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-object-contexts/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectContext()))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                objectID: false,
                contextID: false
            },
            body: {
                OrderID: 1,
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).to.include('Invalid request data')
        expect(data).to.include('objectID')
        expect(data).to.include('contextID')
        expect(data).to.include('Expected string, received boolean')
    })

    it('returns an error if the request body is invalid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-object-contexts/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectContext()))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                objectID: uuid(),
                contextID: uuid()
            },
            body: {
                OrderID: false,
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).to.include('Invalid request data')
        expect(data).to.include('OrderID')
        expect(data).to.include('Expected number, received boolean')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-object-contexts/update.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new LearningObjectContext()))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                objectID: uuid(),
                contextID: uuid()
            },
            body: {
                OrderID: 1,
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })



})