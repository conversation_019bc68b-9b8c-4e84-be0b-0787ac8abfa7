import mssql from '@lcs/mssql-utility'
import { AssignmentLearningContextFields, AssignmentLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-context.js'
import { AssignmentLearningObjectFields, AssignmentLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-object.js'
import { KeywordFields, KeywordsTableName } from '@tess-f/sql-tables/dist/lms/keyword.js'
import { LearningContextKeywordFields, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'
import { LearningObjectKeywordFields, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'

/**
 * @param {Date | null} from
 * @param {Date | null} to
 * @returns {Array<{
 *  Name: string,
 *  UseCount: number
 * }>}
 */
export default async function (from?: Date, to?: Date): Promise<{ Name: string, UseCount: number }[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  const query = `
    SELECT TOP 10 [${KeywordsTableName}].[${KeywordFields.Name}], COALESCE(SUM([ObjectUseCounts].[UseCount]), 0) + COALESCE(SUM([ContextUseCounts].[UseCount]), 0) AS UseCount
    FROM [${KeywordsTableName}]
    FULL JOIN (
      SELECT [${LearningObjectKeywordFields.Keyword}], COUNT([${LearningObjectKeywordFields.Keyword}]) AS UseCount
      FROM [${LearningObjectKeywordsTableName}]
      INNER JOIN [${AssignmentLearningObjectsTableName}] ON [${AssignmentLearningObjectsTableName}].[${AssignmentLearningObjectFields.LearningObjectID}] = [${LearningObjectKeywordsTableName}].[${LearningObjectKeywordFields.LearningObjectID}]
      ${from && to ? `WHERE [${AssignmentLearningObjectsTableName}].[${AssignmentLearningObjectFields.CreatedOn}] BETWEEN @from AND @to` : ''}
      GROUP BY [${LearningObjectKeywordFields.Keyword}]
    ) AS ObjectUseCounts ON [ObjectUseCounts].[Keyword] = [${KeywordsTableName}].[${KeywordFields.Name}]
    FULL JOIN (
      SELECT [${LearningContextKeywordFields.Keyword}], COUNT([${LearningContextKeywordFields.Keyword}]) AS UseCount
      FROM [${LearningContextKeywordsTableName}]
      INNER JOIN [${AssignmentLearningContextsTableName}] ON [${AssignmentLearningContextsTableName}].[${AssignmentLearningContextFields.LearningContextID}] = [${LearningContextKeywordsTableName}].[${LearningContextKeywordFields.LearningContextID}]
      ${from && to ? `WHERE [${AssignmentLearningContextsTableName}].[${AssignmentLearningContextFields.CreatedOn}] BETWEEN @from AND @to` : ''}
      GROUP BY [${LearningContextKeywordFields.Keyword}]
    ) AS ContextUseCounts ON [ContextUseCounts].[Keyword] = [${KeywordsTableName}].[${KeywordFields.Name}]
    GROUP BY [${KeywordsTableName}].[${KeywordFields.Name}]
    HAVING COALESCE(SUM([ObjectUseCounts].[UseCount]), 0) + COALESCE(SUM([ContextUseCounts].[UseCount]), 0) > 0
    ORDER BY [UseCount] DESC
  `

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const results = await request.query<{ Name: string, UseCount: number }>(query)
  return results.recordset
}
