import mssql from '@lcs/mssql-utility'
import { User, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'

export default async function (): Promise<User[]> {
  const results = await mssql.getPool().request().query<User>(`
    SELECT [${UserFields.ID}], [${UserFields.Avatar}], [${UserFields.FirstName}], [${UserFields.MiddleInitial}], [${UserFields.LastName}]
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] IN (
      SELECT [${LearningObjectFields.ModifiedBy}]
      FROM [${LearningObjectsTableName}]
      WHERE [${LearningObjectFields.ModifiedBy}] IS NOT NULL
    )
  `)

  return results.recordset
}
