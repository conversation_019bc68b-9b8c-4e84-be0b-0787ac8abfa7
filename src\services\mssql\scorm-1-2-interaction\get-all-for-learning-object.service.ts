import mssql, { streamQuery } from '@lcs/mssql-utility'
import { User, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearnerProgress, LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningObject, LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LessonStatusFields, LessonStatusTableName } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { SCORM_1_2_Interaction, ScormInteractionFields, ScormInteractionsTableName } from '@tess-f/sql-tables/dist/lms/scorm-1-2-interaction.js'

export type InteractionData =
  Required<SCORM_1_2_Interaction> &
  Required<Pick<LearnerProgress, 'CompletedDate' | 'MaxScore' | 'MinScore' | 'RawScore' | 'TotalTime'>> &
  Required<Pick<User, 'FirstName' | 'LastName'>> &
  Required<Pick<LearningObject, 'Title'>> &
  { LearningObjectId: string, LessonStatus: string }

export default async function getInteractionsForLearningObject (objectId: string): Promise<InteractionData[]> {
  const request = mssql.getPool().request()
  request.input('objectId', objectId)

  return await streamQuery<InteractionData>(request, `
    SELECT
      [${LearningObjectsTableName}].[${LearningObjectFields.Title}],
      [${LearningObjectsTableName}].[${LearningObjectFields.ID}] AS [LearningObjectId],
      COALESCE([${LearnerProgressTableName}].[${LearnerProgressFields.CompletedDate}], [${LearnerProgressTableName}].[${LearnerProgressFields.CreatedOn}]) AS [${LearnerProgressFields.CompletedDate}],
      [${LearnerProgressTableName}].[${LearnerProgressFields.MaxScore}],
      [${LearnerProgressTableName}].[${LearnerProgressFields.MinScore}],
      [${LearnerProgressTableName}].[${LearnerProgressFields.RawScore}],
      [${LearnerProgressTableName}].[${LearnerProgressFields.TotalTime}],
      [${UserTableName}].[${UserFields.FirstName}],
      [${UserTableName}].[${UserFields.LastName}],
      [${LessonStatusTableName}].[${LessonStatusFields.Name}] AS [LessonStatus],
      [${ScormInteractionsTableName}].*
    FROM [${ScormInteractionsTableName}]
      INNER JOIN [${LearnerProgressTableName}] ON [${LearnerProgressTableName}].[${LearnerProgressFields.ID}] = [${ScormInteractionsTableName}].[${ScormInteractionFields.LearnerProgressID}]
      INNER JOIN [${LessonStatusTableName}] ON [${LessonStatusTableName}].[${LessonStatusFields.ID}] = [${LearnerProgressTableName}].[${LearnerProgressFields.LessonStatusID}]
      INNER JOIN [${UserTableName}] ON [${UserTableName}].[${UserFields.ID}] = [${ScormInteractionsTableName}].[${ScormInteractionFields.UserID}]
      INNER JOIN [${LearningObjectsTableName}] ON [${LearningObjectsTableName}].[${LearningObjectFields.ID}] = [${LearnerProgressTableName}].[${LearnerProgressFields.LearningObjectID}]
        AND [${LearningObjectsTableName}].[${LearningObjectFields.ID}] = @objectId
  `)
}
