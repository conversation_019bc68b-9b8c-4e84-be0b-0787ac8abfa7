import mssql, { updateRow } from '@lcs/mssql-utility'
import getService from '../learning-objects/get.service.js'
import createService from '../activity-stream/create.service.js'
import ActivityStreamModel from '../../../models/activity-stream.model.js'
import LearningObjectRatingModel from '../../../models/learning-object-rating.model.js'
import { LearningObjectRating } from '@tess-f/sql-tables/dist/lms/learning-object-rating.js'

export default async function (updateModel: LearningObjectRatingModel): Promise<LearningObjectRatingModel> {
  const pool = mssql.getPool()
  // create an activity stream record
  const lo = await getService(updateModel.fields.LearningObjectID!)
  const activity = new ActivityStreamModel({
    UserID: updateModel.fields.UserID,
    LinkText: lo.fields.Title,
    LinkID: updateModel.fields.LearningObjectID,
    ActivityID: 1,
    Rating: updateModel.fields.Rating,
    CreatedOn: new Date()
  })
  await createService(activity)
  const result = await updateRow<LearningObjectRating>(pool.request(), updateModel, { ID: updateModel.fields.ID })

  return new LearningObjectRatingModel(result[0])
}
