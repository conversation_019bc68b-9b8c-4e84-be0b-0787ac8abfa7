import mssql from '@lcs/mssql-utility'
import { LearningObjectUserViewFields, LearningObjectUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-view.js'

export default async function getObjectViewCount (objectID: string, from?: Date, to?: Date): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('objectID', objectID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const results = await request.query<{Views: number}>(`
    SELECT COUNT(*) AS Views
    FROM [${LearningObjectUserViewsTableName}]
    WHERE [${LearningObjectUserViewFields.LearningObjectID}] = @objectID
    ${from && to ? `AND [${LearningObjectUserViewFields.CreatedOn}] BETWEEN @from AND @to` : ''}
  `)

  return results.recordset[0].Views
}
