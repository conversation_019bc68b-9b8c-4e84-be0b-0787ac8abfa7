import logger from '@lcs/logger'
import searchCatalog from '../../../services/mssql/catalog/search.service.js'
import { type Request, type Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodLimit, zodOffset } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.search-catalog', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { search, offset, limit } = z.object({
      search: z.string(),
      offset: zodOffset,
      limit: zodLimit
    }).parse(req.body)

    const results = await searchCatalog(search, offset, limit)

    log('info', `Successfully retrieved ${results.items.length} of ${results.totalRecords} catalog folders.`, { success: true, req })

    res.json({
      totalRecords: results.totalRecords,
      items: results.items.map(item => item.fields)
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to search catalog folders: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to search catalog folders', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
