import LearnerProgress, { updateLearnerProgressSchema } from '../../../models/learner-progress.model.js'
import update from '../../../services/mssql/learner-progress/update.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import gradeContexts from '../../../services/background/grade-contexts.service.js'
import issueLearningObjectCertificateController from '../../certificate/issue-learning-object-certificate.controller.js'
import getLearningObject from '../../../services/mssql/learning-objects/get.service.js'
import ActivityStream from '../../../models/activity-stream.model.js'
import createActivity from '../../../services/mssql/activity-stream/create.service.js'
import { gradeForLearningObject, gradeForMultiSessionCourse } from '../../../services/mssql/assignments/update-user-assignment-completion.service.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { LearningObjectSubTypes } from '@tess-f/sql-tables/dist/lms/learning-object-sub-type.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { setTimeout } from 'timers'
import httpStatus from 'http-status'
import { RedisClient } from '../../../services/redis/client.service.js'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.update-learner-progress', httpLogTransformer)
const { INTERNAL_SERVER_ERROR, BAD_REQUEST } = httpStatus
const backgroundTimers: {[key: string]: NodeJS.Timeout} = {}

/**
 * @param req.body {LearnerProgress} - Learner Progress model
 */
export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-update-learner-progress' label."

  try {
    const learnerProgress = new LearnerProgress(updateLearnerProgressSchema.parse(req.body))

    // Apply the param id as the category id to update
    learnerProgress.fields.ID = (z.object({ id: zodGUID }).parse(req.params)).id
    learnerProgress.fields.ModifiedOn = new Date()

    const updated = await update(learnerProgress)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-update-learner-progress' label and message indicating a successful update."
    log('info', `Successfully updated learner progress ID: ${updated.fields.ID}`, { success: true, req })

    res.json(updated.fields)

    if (updated.fields.LearningObjectID) {
      const learningObject = await getLearningObject(updated.fields.LearningObjectID)
      if (learningObject.fields.LearningObjectTypeID === LearningObjectTypes.CBT && learningObject.fields.LearningObjectSubTypeID === LearningObjectSubTypes.SCORM_1_2) {
        // this is a scorm object we need to wait to process the overall grading in case the object isn't done sending updates
        if (backgroundTimers[updated.fields.ID!]) {
          clearTimeout(backgroundTimers[updated.fields.ID!])
        }
        // set timer active in redis (this will help with multiple services running, if we don't pick up the reset)
        await RedisClient.setLearnerProgressTimerActive(learnerProgress.fields.ID)
        // wait 46 seconds to process this record (this will allow redis to expire our timer)
        backgroundTimers[updated.fields.ID!] = setTimeout((record: LearnerProgress) => (async (record: LearnerProgress) => {
          const timerStillActive = await RedisClient.checkLearnerProgressTimerActive(record.fields.ID!)
          if (!timerStillActive) {
            processRecord(record)
          } else {
            // another instance of the server picked up a more recent update kill this timer and let that instance handle the processing
            delete backgroundTimers[record.fields.ID!]
          }
        })(record), 46000, updated)
      } else {
        processRecord(updated)
      }
    } else {
      // this is an ILT record let's process it now
      processRecord(updated)
    }
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update learner progress: input validation error', { error, req, success: false })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    }
    else{
    log('error', 'Failed to update learner progress.', { error, req, success: false })
    res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}

/**
 * Process the updated record for context grading, certificate issuance, and activity tracking
 */
async function processRecord (record: LearnerProgress) {
  // clear the background timer if there is one
  if (backgroundTimers[record.fields.ID!]) {
    delete backgroundTimers[record.fields.ID!]
  }

  if (record.fields.LessonStatusID! > LessonStatuses.browsed) {
    // only grade things that have an attempt to them
    // now that the user has a response lets kick off our background grading service
    gradeContexts(record).catch(() => {
      // silent failure, the service should take care of logging any issues
    })
  }

  // update assignment completion status
  if (record.fields.LearningObjectID) {
    try {
      await gradeForLearningObject(record.fields.LearningObjectID, record.fields.UserID!)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      log('warn', 'Failed to updated users assignment completion', { errorMessage, success: false })
    }
  } else if (record.fields.LearningContextSessionID) {
    try {
      await gradeForMultiSessionCourse(record.fields.LearningContextSessionID, record.fields.UserID!)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      log('warn', 'Failed to update users assignment completion', { errorMessage, success: false })
    }
  }

  if (record.fields.LessonStatusID! > LessonStatuses.fail && record.fields.LearningObjectID && !record.fields.Certificate) {
    issueLearningObjectCertificateController(record).catch(() => {
      // silent failure
    })
  }

  // create an activity feed record
  let activityID = -1
  if (record.fields.LessonStatusID === LessonStatuses.browsed || record.fields.LessonStatusID === LessonStatuses.incomplete) {
    activityID = Activities.StartedLearningObject
  } else if (record.fields.LessonStatusID === LessonStatuses.fail) {
    activityID = Activities.FailedLearningObject
  } else if (record.fields.LessonStatusID === LessonStatuses.passed) {
    activityID = Activities.PassedLearningObject
  } else if (record.fields.LessonStatusID === LessonStatuses.completed) {
    activityID = Activities.CompletedLearningObject
  }

  if (activityID !== -1 && record.fields.LearningObjectID && record.fields.LearningObjectID !== null) {
    try {
      const learningObject = await getLearningObject(record.fields.LearningObjectID)
      const activity = new ActivityStream({
        UserID: record.fields.UserID,
        LinkID: record.fields.LearningObjectID,
        LinkText: learningObject.fields.Title,
        ActivityID: activityID,
        CreatedOn: new Date()
      })
      await createActivity(activity)
    } catch {
      // silent failure
    }
  }
}
