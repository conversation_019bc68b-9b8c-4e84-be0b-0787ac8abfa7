import { Table } from '@lcs/mssql-utility'
import { LearningElement, LearningElementsViewName } from '@tess-f/sql-tables/dist/lms/learning-elements-view.js'
import { LearningElementJson } from '@tess-f/lms/dist/common/learning-element.js'

export default class LearningElements_ViewModel extends Table<LearningElementJson, LearningElement> {
  public fields: LearningElementJson

  constructor (record?: LearningElement) {
    super(LearningElementsViewName)
    this.fields = {}

    if (record) this.importFromDatabase(record)
  }

  public importFromDatabase (record: LearningElement): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningElement {
    throw new Error('Cannot export database record for view')
  }
}
