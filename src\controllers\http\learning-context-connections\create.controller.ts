import create from '../../../services/mssql/learning-context-connections/create.service.js'
import LearningContextConnection, { createLearningContextConnectionSchema } from '../../../models/learning-context-connection.model.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { ZodError } from 'zod'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.create-learning-context-connection', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const learningContextConnection = new LearningContextConnection(createLearningContextConnectionSchema.parse(req.body))
    learningContextConnection.fields.CreatedBy = req.session.userId
    learningContextConnection.fields.CreatedOn = new Date()

    const created = (await create(learningContextConnection)).fields

    log('info', `Successfully created learning context connection for context ID ${created.ParentContextID} connected to ${created.ConnectedContextID}`, { success: true, req })

    res.json(created)
  } catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Failed to create learning context connection due to invalid data in the request.', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to create learning context connection.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
