/* eslint-disable camelcase */
import getCompletion from '../../../services/mssql/learning-context/get-completion.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'
const { INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller.HTTP.is-context-exam-locked', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { contextID, userID } = z.object({
      contextID: zodGUID,
      userID: zodGUID
    }).parse(req.params)

    const results = await getCompletion(contextID, userID, undefined, undefined, false)

    log('info', `Successfully determined that the context exam ${results.completion < 100 ? 'is' : 'is not'} locked`, { success: true, req })

    res.json(results.completion < 100)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to determine if learning context exam is locked: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else {
      log('error', 'Failed to determine if learning context exam is locked', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
