import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import getContextViewCount from './get-counts.service.js'
import getUserViewsByType from './get-user-views-by-type.service.js'
import getViewHistory from './get-view-history.service.js'
import getViewsByType from './get-views-by-type.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import LearningContextUserViewModel from '../../../models/learning-context-user-view.model.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'

let learningContextUserView: LearningContextUserViewModel
let learningContext: LearningContext

// FIXME: this needs to be updated to set up the test data
xdescribe('MSSQL Learning Contexts Views', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test context views',
      Description: `Running learning-context-views on ${new Date()}`,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      ContextTypeID: LearningContextTypes.Course
    }))
  })

  it('gets the context view count', async () => {
    const d = learningContextUserView.fields.CreatedOn!
    d.setDate(d.getDate() - 5)
    const result = await getContextViewCount(learningContextUserView.fields.ID!, d, new Date())
    expect(result).to.be.gte(0)
  })

  it('get user views by type', async () => {
    const d = learningContextUserView.fields.CreatedOn!
    d.setDate(d.getDate() - 5)
    const result = await getUserViewsByType(AdminUserId, d, new Date())
    expect(result.length).to.be.gte(0)
  })

  it('gets view history', async () => {
    const result = await getViewHistory(learningContextUserView.fields.ID!)
    expect(result.length).to.be.gte(0)
  })

  it('gets views by type', async () => {
    const d = learningContextUserView.fields.CreatedOn!
    d.setDate(d.getDate() - 5)
    const result = await getViewsByType(d, new Date())
    expect(result.length).to.be.gte(0)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { ID: learningContext.ID })
  })
})
