import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import LearningContextModel from '../../../models/learning-context.model.js'
import { isValidString, isSortDirectionValid } from '@tess-f/backend-utils/validators'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextKeywordFields, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'

/**
 * retrieves a paginated list of learning contexts
 * @param {number} offset - the number of rows to offset the query by
 * @param {number} limit - the number of records to limit the query to
 * @param {string} search - terms to search for ( title, description, course ID )
 * @param {{
 *   typeIds: number[],
 *   courseTypeIds: number[],
 *   keywords: string[],
 *   featured: boolean,
 *   modifiedByIds: string[],
 *   createdByIds: string[],
 *   labels: string[],
 *   visibilities: number[]
 * }} filters - query filters
 * @param {{
 *   sortColumn: string,
 *   sortDirection: string
 * }} sort - query sort options direction = (ASC || DESC)
 * @returns {{ totalRecords: number, contexts: LearningContextModel[] }}
 */
export default async function (
  offset: number = 0,
  limit: number = 10,
  search?: string,
  filters?: { typeIds?: number[], courseTypeIds?: number[], keywords?: string[], modifiedByIds?: string[], createdByIds?: string[], labels?: string[], visibilities?: number[]},
  sort?: { sortColumn: string, sortDirection: string }
): Promise<{ totalRecords: number, contexts: LearningContextModel[] }> {
  const pool = mssql.getPool()
  const request = pool.request()

  // build query
  let query = `SELECT *, TotalRecords = COUNT(*) OVER() FROM [${LearningContextTableName}] WHERE 1 = 1 `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [LearningContextFields.Title, LearningContextFields.Description, LearningContextFields.CourseID], 'any')}) `
  }

  if (filters) {
    if (filters.typeIds && filters.typeIds.length > 0) {
      query += 'AND ('
      filters.typeIds.forEach((id, index) => {
        request.input(`typeID_${index}`, id)
        query += `[${LearningContextFields.ContextTypeID}] = @typeID_${index} ${index + 1 < filters.typeIds!.length ? 'OR' : ''} `
      })
      query += ') '
    }

    if (filters.courseTypeIds && filters.courseTypeIds.length > 0) {
      query += 'AND ('
      filters.courseTypeIds.forEach((id, index) => {
        request.input(`courseID_${index}`, id)
        query += `[${LearningContextFields.CourseTypeID}] = @courseID_${index} ${index + 1 < filters.courseTypeIds!.length ? 'OR' : ''} `
      })
      query += ') '
    }

    if (filters.visibilities && filters.visibilities.length > 0) {
      query += 'AND ('
      filters.visibilities.forEach((id, index) => {
        request.input(`visID_${index}`, id)
        query += `[${LearningContextFields.VisibilityID}] = @visID_${index} ${index + 1 < filters.visibilities!.length ? 'OR' : ''} `
      })
      query += ') '
    }

    if (filters.keywords && filters.keywords.length > 0) {
      query += `AND ( [${LearningContextFields.ID}] IN ( SELECT [${LearningContextKeywordFields.LearningContextID}] FROM [${LearningContextKeywordsTableName}] WHERE `
      filters.keywords.forEach((key, index) => {
        request.input(`keyword_${index}`, key)
        query += `[${LearningContextKeywordFields.Keyword}] = @keyword_${index} ${index + 1 < filters.keywords!.length ? 'OR' : ''} `
      })
      query += ')) '
    }

    if (filters.createdByIds && filters.createdByIds.length > 0) {
      query += 'AND ('
      filters.createdByIds.forEach((id, index) => {
        request.input(`createdById_${index}`, id)
        query += `[${LearningContextFields.CreatedBy}] = @createdById_${index} ${index + 1 < filters.createdByIds!.length ? 'OR' : ''} `
      })
      query += ') '
    }

    if (filters.modifiedByIds && filters.modifiedByIds.length > 0) {
      query += 'AND ('
      filters.modifiedByIds.forEach((id, index) => {
        request.input(`modifiedById_${index}`, id)
        query += `[${LearningContextFields.ModifiedBy}] = @modifiedById_${index} ${index + 1 < filters.modifiedByIds!.length ? 'OR' : ''} `
      })
      query += ') '
    }

    if (filters.labels && filters.labels.length > 0) {
      query += 'AND ('
      const conds = filters.labels.map((label, index) => {
        request.input('label_' + index, label)
        return '@label_' + index
      })
      query += `[${LearningContextFields.Label}] IN (${conds.join(', ')}))`
    }
  }

  // add offset and limit params to the query
  request.input('Offset', offset)
  request.input('Limitby', limit)

  // order, limit and offset query
  if (sort && isSortDirectionValid(sort.sortDirection) &&
        isValidString(sort.sortColumn, [LearningContextFields.Title, LearningContextFields.CourseTypeID, LearningContextFields.ModifiedOn, LearningContextFields.CreatedOn])) {
    query += `ORDER BY [${sort.sortColumn}] ${sort.sortDirection} `
  } else {
    query += `ORDER BY [${LearningContextFields.ModifiedOn}] DESC, [${LearningContextFields.CreatedOn}] DESC, [${LearningContextFields.Title}] `
  }

  query += `
            OFFSET @Offset ROWS
            FETCH FIRST @Limitby ROWS ONLY
        `

  const result = await request.query<LearningContext & {TotalRecords: number}>(query)

  // return the results
  return {
    totalRecords: result.recordset.length > 0 ? result.recordset[0].TotalRecords : 0,
    contexts: result.recordset.map(record => new LearningContextModel(undefined, record))
  }
}
