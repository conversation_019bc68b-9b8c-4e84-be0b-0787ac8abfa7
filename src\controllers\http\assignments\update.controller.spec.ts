import logger from '@lcs/logger'
import esmock from 'esmock'
import httpStatus from 'http-status'
import httpMocks from 'node-mocks-http'
import Sinon from 'sinon'
import { expect } from 'chai'
import { v4 as uuid } from 'uuid'
import AssignmentsModel from '../../../models/assignment.model.js'

describe('HTTP Controller: update assignment', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns bad request when the assignment data is invalid', async () => {
    const controller = await esmock('./update.controller.js', {})
    const mocks = httpMocks.createMocks({ body: {
      Title: '12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890',
      EmailMessage: true,
      DueDate: 'next week',
      DirectReports: 'no',
      Users: [{ ID: 1 }],
      Groups: false
    } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('Title: String must contain at most 250 character(s)')
    expect(data).to.include('EmailMessage: Expected string, received boolean')
    expect(data).to.include('DueDate: Invalid date')
    expect(data).to.include('DirectReports: Expected boolean, received string')
    expect(data).to.include('Users.0.ID: Expected string, received number')
    expect(data).to.include('Groups: Expected array, received boolean')
  })

  it('should return bad request when the assignment id parameter is missing', async () => {
    const controller = await esmock('./update.controller.js', {})
    const mocks = httpMocks.createMocks({ body: { Title: 'Test Assignment' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Required')
  })

  it('should return bad request when the assignment id parameter is not a valid uuid', async () => {
    const controller = await esmock('./update.controller.js', {})
    const mocks = httpMocks.createMocks({ body: { Title: 'Test Assignment' }, params: { id: 'invalid' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Invalid')
  })

  it('should return bad request when the assignment id parameter is a number and not a uuid', async () => {
    const controller = await esmock('./update.controller.js', {})
    const mocks = httpMocks.createMocks({ body: { Title: 'Test Assignment' }, params: { id: 1 } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Expected string, received number')
  })

  it('should process the assignment data for update', async () => {
    const controller = await esmock('./update.controller.js', {
      '../../../services/mssql/users/get-all-user-ids.service.js': {
        default: Sinon.stub().returns(Promise.resolve([]))
      },
      '../../../services/mssql/assignments/update.service.js': {
        default: Sinon.stub().returns(Promise.resolve({ assignment: new AssignmentsModel({ ID: uuid() }), addedUserIds: [] }))
      }
    })
    const mocks = httpMocks.createMocks({ body: {
      Title: 'Test Assignment',
      EmailMessage: 'Take this content ASAP',
      DueDate: (new Date()).toISOString(),
      DirectReports: false,
      Everyone: true
    }, params: { id: uuid() }, session: { userId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
  })
})
