import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import { types } from 'util'

describe('HTTP get-paginated controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-paginated.controller', {
            '../../../services/mssql/learning-objects/get-paginated.service.js': {
                default: Sinon.stub().returns(Promise.resolve({totalRecords: 1, objects: []}))
            },
            '../../../services/mssql/learning-object-keywords/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            },
            '../../../services/mssql/learning-object-contexts/get-object-use-count.service.js': {
                default: Sinon.stub().returns(Promise.resolve(1))
            },
            '../../../services/mssql/learning-object-ratings/get-average-for-object.service.js': {
                default: Sinon.stub().returns(Promise.resolve({average: 1, count: 1}))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
              types: [1, 2],
              rating: true,
              offset: 0,
              limit: 10
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })


    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-paginated.controller', {
            '../../../services/mssql/learning-objects/get-paginated.service.js': {
                default: Sinon.stub().returns(Promise.resolve({totalRecords: 1, objects: []}))
            },
            '../../../services/mssql/learning-object-keywords/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            },
            '../../../services/mssql/learning-object-contexts/get-object-use-count.service.js': {
                default: Sinon.stub().returns(Promise.resolve(1))
            },
            '../../../services/mssql/learning-object-ratings/get-average-for-object.service.js': {
                default: Sinon.stub().returns(Promise.resolve({average: 1, count: 1}))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
              types: false,
              rating: 0,
              offset: false,
              limit: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected boolean, received number')
        expect(mocks.res._getData()).include('types')
    })
  

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-paginated.controller', {
            '../../../services/mssql/learning-objects/get-paginated.service.js': {
                default: Sinon.stub().rejects(Promise.resolve({totalRecords: 1, objects: []}))
            },
            '../../../services/mssql/learning-object-keywords/get.service.js': {
                default: Sinon.stub().rejects(Promise.resolve([]))
            },
            '../../../services/mssql/learning-object-contexts/get-object-use-count.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(1))
            },
            '../../../services/mssql/learning-object-ratings/get-average-for-object.service.js': {
                default: Sinon.stub().rejects(Promise.resolve({average: 1, count: 1}))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
              types: [1, 2],
              rating: true,
              offset: 0,
              limit: 10
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })
 

})