import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'

describe('HTTP Controller: get content overview', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => sinon.restore())

  it('returns bad request when the search/filter data is invalid', async () => {
    const controller = await esmock('./get-content-overview.controller.js')
    const mocks = httpMocks.createMocks({ body: {
      search: 1,
      filters: {
        labels: ['test', 1, true],
        objectTypes: [30, 'test'],
        keywords: ['test', 1, true],
        sortColumn: 1,
        sortDirection: 'up'
      }
    }})
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('search: Expected string, received number')
    expect(data).to.include('filters.labels.1: Expected string, received number')
    expect(data).to.include('filters.labels.2: Expected string, received boolean')
    expect(data).to.include('filters.objectTypes.0: Invalid enum value. Expected 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16, received \'30\'')
    expect(data).to.include('filters.objectTypes.1: Invalid enum value. Expected 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16, received \'test\'')
    expect(data).to.include('filters.keywords.1: Expected string, received number')
    expect(data).to.include('filters.keywords.2: Expected string, received boolean')
    expect(data).to.include('filters.sortColumn: Expected string, received number')
    expect(data).to.include('filters.sortDirection: Invalid enum value. Expected \'asc\' | \'desc\' | \'ASC\' | \'DESC\', received \'up\'')
  })

  it('should return the dashboard content overview', async () => {
    const controller = await esmock('./get-content-overview.controller.js', {
      '../../../services/mssql/content/search.service.js': {
        default: sinon.stub().resolves({ elements: [], totalRecords: 0 })
      }
    })
    const mocks = httpMocks.createMocks()
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
    expect(data.elements).to.exist
    expect(data.totalRecords).to.exist
    expect(data.elements).to.be.an('array')
    expect(data.totalRecords).to.be.a('number')
    expect(data.totalRecords).to.equal(0)
    expect(data.elements.length).to.equal(0)
  })
})
