import Statement from '../../../models/amqp/lrs/statement.model.js'
import { getSystemConfig } from '../../../services/amqp/system/get-system-config.service.js'
import processCmi5Controller from './process-cmi5.controller.js'
import processEvaluationStatementController from './process-evaluation-statement.controller.js'
import processStatementController from './process-statement.controller.js'

export default async function (statement: Statement) {
  const systemConfig = await getSystemConfig()

  if (statement.context?.extensions?.['https://w3id.org/xapi/cmi5/context/extensions/sessionid'] !== undefined && statement.context?.registration !== undefined) {
    return await processCmi5Controller(statement)
  } else if (
    statement.object.objectType === 'Activity' &&
    statement.object.id.includes(`${systemConfig.Domain}${systemConfig.Domain.endsWith('/') ? '' : '/'}evaluation/`) &&
    statement.actor.account !== undefined &&
    statement.actor.account.homePage === systemConfig.Domain
  ) {
    // we are working with statement from the evaluations module
    return await processEvaluationStatementController(statement)
  } else {
    return await processStatementController(statement)
  }
}
