import getLearningObjectByCmi5ContextId from '../../../services/mssql/learning-objects/get-id-by-cmi5-context-id.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.get-learning-object-by-cmi-context-id', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const learningObject = await getLearningObjectByCmi5ContextId(id)
    log('info', 'Successfully retrieved learning object for cmi5 course', { id: learningObject.fields.ID, success: true, req })
    res.json(learningObject.fields)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning object by cmi5 context id: validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learning object by cmi5 context id because it was not found in the database', { contextId: req.params.id, success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      log('error', 'Failed to get learning object by cmi5 context id', { contextId: req.params.id, error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
