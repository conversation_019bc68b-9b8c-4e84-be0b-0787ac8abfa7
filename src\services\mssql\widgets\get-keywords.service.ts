import mssql, { getRows } from '@lcs/mssql-utility'
import { WidgetKeyword, WidgetKeywordsTableName } from '@tess-f/sql-tables/dist/lms/widget-keyword.js'
export default async function getWidgetKeywordsService (widgetID: string): Promise<string[]> {
  const pool = mssql.getPool()
  const records = await getRows<WidgetKeyword>(WidgetKeywordsTableName, pool.request(), { WidgetID: widgetID })
  return records.map(record => record.Keyword!)
}
