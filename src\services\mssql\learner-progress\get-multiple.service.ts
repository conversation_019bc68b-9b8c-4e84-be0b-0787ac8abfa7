import mssql, { DB_Errors } from '@lcs/mssql-utility'
import { LearnerProgress, LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { Request } from 'mssql'
import LearnerProgressModel from '../../../models/learner-progress.model.js'

export default async function (userID: string, learningObjectId: string): Promise<LearnerProgressModel[]> {
  const pool = mssql.getPool()
  return await getLearningProgress(pool.request(), userID, learningObjectId)
}

async function getLearningProgress (request: Request, userID: string, identifier: string) {
  request.input('userID', userID)
  request.input('identifier', identifier)
  const query = `
    SELECT * FROM [${LearnerProgressTableName}] 
    WHERE [${LearnerProgressFields.UserID}] = @userID 
    AND (
      [${LearnerProgressFields.LearningObjectID}] = @identifier 
      OR [${LearnerProgressFields.LearningContextSessionID}] = @identifier
    )
  `

  const results = await request.query<LearnerProgress>(query)
  if (results.recordset.length === 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }
  return results.recordset.map(record => new LearnerProgressModel(record))
}
