import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import { LearningObjectUserBookmarkFields } from '@tess-f/sql-tables/dist/lms/learning-object-user-bookmark'
import LearningObjectBookmark, { LearningObjectBookmarkSchema } from '../../../models/learning-object-user-bookmark.model.js'

describe('HTTP create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-object-bookmarks/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectBookmark({})))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                UserID: uuid(),
                LearningObjectID: uuid()
            }
            
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })


    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-object-bookmarks/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectBookmark({})))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                UserID: false,
                LearningObjectID: false
            }
            
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('UserID')
        expect(mocks.res._getData()).include('LearningObjectID')

    })


    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-object-bookmarks/create.service.js': {
                default: Sinon.stub().returns(Promise.reject(new LearningObjectBookmark({})))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                UserID: uuid(),
                LearningObjectID: uuid()
            }
            
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })
})