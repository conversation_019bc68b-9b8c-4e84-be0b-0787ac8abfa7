const chai = require('chai');
const expect = chai.expect;
const tester = require('../api/utils/test-agent.utils');
const uuidv4 = require('uuid/v4');
const settings = require('../api/config/settings');

const userID = uuidv4();

let isolated = false;


describe("Claims", function() {

    this.timeout(30000);
    
    before( done => {
        if(tester.agent == null) {
            isolated = true;
            tester.create()
            .then( agent => {
                done();
            })
            .catch( err => {
                console.error(err);
                done();
            });
        } else {
            done();
        }
    });

    it('should get all claims types', done => {
        tester.agent
        .get(settings.server.root + 'claims')
        .end((err, res) => {
            expect(res.body.length).to.be.greaterThan(1);
            done();
        });
    });

    it('should create claims for a user', done => {
        tester.agent
        .post(settings.server.root + 'user-claims/' + userID)
        .send([ 'IsExecutive', 'isInstructor' ])
        .end((err, res) => {
            expect( res.statusCode ).to.equal(201);
            done();
        });
    });

    it('should get the users claims', done => {
        tester.agent
        .get(settings.server.root + 'user-claims/' + userID)
        .end((err, res) => {
            expect( res.statusCode ).to.equal(200);

            const claims = res.body;

            let claim = claims.find( claim => claim.Claim === 'IsExecutive');
            expect(claim).to.exist;
            claim = claims.find( claim => claim.Claim === 'isInstructor');
            expect(claim).to.exist;
            
            done();

        });
    });

    it('should update the new claim', done => {
        tester.agent
        .put(settings.server.root + 'user-claims/' + userID)
        .send([ 'IsExecutive', 'isInstructor', 'isAdmin' ])
        .end((err, res) => {
            expect( res.statusCode ).to.equal(201);            
            done();

        });

    });

    it('should delete the new claim', done => {
        tester.agent
        .delete(settings.server.root + 'user-claims/' + userID)
        .end((err, res) => {
            expect( res.statusCode ).to.equal(204);
            done();
        });
    });
    
    after((done)=>{
        if(isolated) {
            tester.close();
        } 
        done();
    });

})