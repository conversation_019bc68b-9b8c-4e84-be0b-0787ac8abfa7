import type { Request, Response } from 'express'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
import getLearningContext from '../../../services/mssql/learning-context/get.service.js'
import getContentType from '../../../services/mssql/content/get-content-type.service.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import getCmi5CourseContextForAu from '../../../services/mssql/learning-context/get-id-of-cmi5-course-by-au-id.service.js'
import isAuLockedService from '../../../services/mssql/cmi5-au/is-au-locked.service.js'
import { getLearningContextContentMatrix } from '../../../services/mssql/learning-context/utils.service.js'
import getUserCompletionCountForContextService from '../../../services/mssql/user-completed-learning-contexts/get-user-completion-count-for-context.service.js'
import getUserCompletionCountForObjectService from '../../../services/mssql/learner-progress/get-user-completion-count-for-object.service.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.is-content-locked', httpLogTransformer)

export default async function (req: Request, res: Response): Promise<void> {
  try {
    const { contextId, contentId } = z.object({
      contextId: zodGUID,
      contentId: zodGUID
    }).parse(req.params)

    // 1. get the context
    const context = await getLearningContext(contextId, undefined)
    // 2. get the content item type (learning object or learning context)
    const contentType = await getContentType(contentId)

    if (context.fields.ContextTypeID === LearningContextTypes.CMI5Course || context.fields.ContextTypeID === LearningContextTypes.CMI5Block) {
      if (contentType === 'Learning Context') {
        // this is a cmi5 block within a cmi5 course or another cmi5 block
        // this content is not locked
        log('info', 'Content is a cmi5 block and not locked', { id: contentId, success: true, req })
        res.send(false)
        return
      }

      const auCourse = context.fields.ContextTypeID === LearningContextTypes.CMI5Course ? context : await getCmi5CourseContextForAu(contentId)
      const result = await isAuLockedService(auCourse.fields.ID!, contentId, req.session.userId)
      log('info', 'Successfully determined if content is locked', { locked: result, success: true, req })
      res.send(result)
      return
    }

    // if we have gotten this far the context is not cmi5
    // check to see if the context is enforcing sequencing
    if (context.fields.EnforceContentSequencing !== true) {
      log('info', 'Content is not locked because sequencing is not enforced', { success: true, req })
      res.send(false)
      return
    }

    const contextStatus = await checkContextContentStatus(context.fields.ID!, contentId, req.session.userId)

    log('info', 'Successfully determined if content is locked', { locked: contextStatus ?? false, success: true, req })
    res.send(contextStatus ?? false)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to determine if content is locked: validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter(s), '))
    } else {
      log('error', 'Unexpected error', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}


async function checkContextContentStatus (contextId: string, contentId: string, userId: string): Promise<boolean | undefined> {
  const matrix = await getLearningContextContentMatrix(contextId)
  // loop through each item in the matrix (do not check within sub contexts)
  for (const item of matrix) {
    if (item.Id === contentId) {
      return false
    }

    if (item.type === 'context' && (await getUserCompletionCountForContextService(userId, item.Id)) < 1) {
      // we can stop looping this item is not complete and the item we are looking for is sequenced lower
      // this content is locked
      log('debug', 'Context is not complete', { locked: true, contextId: item.Id, success: true })
      return true
    }

    if (item.type === 'object' && (await getUserCompletionCountForObjectService(userId, item.Id)) < 1) {
      // we can stop looping this item is not complete and the item we are looking for is sequenced lower
      // this content is locked
      log('debug', 'Object is not complete', { locked: true, objectId: item.Id, success: true })
      return true
    }
  }
}