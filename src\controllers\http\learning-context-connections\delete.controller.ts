import del from '../../../services/mssql/learning-context-connections/delete.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'
const { INTERNAL_SERVER_ERROR, NOT_FOUND, NO_CONTENT, BAD_REQUEST } = httpStatus

const log = logger.create('Controller-HTTP.delete-learning-context-connection', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { parentContextID, connectedContextID } = z.object({
      parentContextID: zodGUID,
      connectedContextID: zodGUID
    }).parse(req.params)

    const numRowsAffected = await del(parentContextID, connectedContextID)

    if (numRowsAffected > 0) {
      log('info', 'Successfully deleted learning context connection', { parentContextID, connectedContextID, success: true, req })
      res.sendStatus(NO_CONTENT)
    } else {
      log('warn', 'Failed to delete learning context connection, because it was not found in the database.', { parentContextID, req, connectedContextID, success: false })
      res.sendStatus(NOT_FOUND)
    }
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete learning context connection: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to delete learning context connection, because it was not found in the database.', { parentContextId: req.params.parentContextID, req, connectedContextId: req.params.connectedContextID, success: false })
      res.sendStatus(NOT_FOUND)
    } else {
      log('error', 'Failed to delete learning object context.', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
