import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'

describe('HTTP Delete controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./delete.controller', {
            '../../../services/mssql/learning-context-connections/delete.service.js': {
                default: Sinon.stub().returns(1)
            }
        })

        const mocks = httpMocks.createMocks({
            params: {
                parentContextID: uuid(),
                connectedContextID: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.NO_CONTENT)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./delete.controller', {
            '../../../services/mssql/learning-context-connections/delete.service.js': {
                default: Sinon.stub().returns(1)
            }
        })

        const mocks = httpMocks.createMocks({
            params: {
                parentContextID: false,
                connectedContextID: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).to.include('Invalid request parameter data')
        expect(data).to.include('Expected string, received boolean')
        expect(data).to.include('parentContextID')
        expect(data).to.include('connectedContextID')
    })

    it('returns an internal server error if the request data is rejected', async () => {
        const controller = await esmock('./delete.controller', {
            '../../../services/mssql/learning-context-connections/delete.service.js': {
                default: Sinon.stub().rejects(1)
            }
        })

        const mocks = httpMocks.createMocks({
            params: {
                parentContextID: uuid(),
                connectedContextID: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

})