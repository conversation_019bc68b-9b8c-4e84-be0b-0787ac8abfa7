import mssql from '@lcs/mssql-utility'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'

/**
 *
 * @param {string} objectID
 * @returns {number} the number of learning contexts that use this learning object
 */
export default async function (objectID: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()

  const query = `
      SELECT COUNT(*) AS ContextCount
      FROM [${LearningObjectContextsTableName}]
      WHERE [${LearningObjectContextFields.LearningObjectID}] = @id
  `

  request.input('id', objectID)

  const results = await request.query<{ ContextCount: number }>(query)

  return results.recordset[0].ContextCount
}
