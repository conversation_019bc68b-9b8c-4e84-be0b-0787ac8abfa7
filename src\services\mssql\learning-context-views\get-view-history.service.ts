import mssql from '@lcs/mssql-utility'
import { LearningContextUserViewFields, LearningContextUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-view.js'

export default async function (learningContextUserViewId: string, from?: Date, to?: Date): Promise<{ Views: number, CreatedOn: Date }[]> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('contextID', learningContextUserViewId)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const results = await request.query<{ Views: number, CreatedOn: Date }>(`
    SELECT COUNT([${LearningContextUserViewFields.ID}]) AS Views, CONVERT(date, [${LearningContextUserViewFields.CreatedOn}]) as CreatedOn
    FROM [${LearningContextUserViewsTableName}]
    WHERE [${LearningContextUserViewFields.LearningContextID}] = @contextID
    ${from && to ? `AND [${LearningContextUserViewFields.CreatedOn}] BETWEEN @from AND @to` : ''}
    GROUP BY CONVERT(date, [${LearningContextUserViewFields.CreatedOn}])
  `)

  return results.recordset
}
