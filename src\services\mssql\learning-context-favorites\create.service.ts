import mssql, { addRow } from '@lcs/mssql-utility'
import createActivity from '../activity-stream/create.service.js'
import ActivityStream from '../../../models/activity-stream.model.js'
import getContext from '../learning-context/get.service.js'
import LearningContextUserFavoritesModel from '../../../models/learning-context-user-favorite.model.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { LearningContextUserFavorite } from '@tess-f/sql-tables/dist/lms/learning-context-user-favorite.js'

export default async function (contextFavorite: LearningContextUserFavoritesModel): Promise<LearningContextUserFavoritesModel> {
  const pool = mssql.getPool()

  // create activity stream record
  const context = await getContext(contextFavorite.fields.LearningContextID!, undefined)
  const activity = new ActivityStream({
    UserID: contextFavorite.fields.UserID,
    LinkText: context.fields.Label + ': ' + context.fields.Title,
    LinkID: contextFavorite.fields.LearningContextID,
    ActivityID: Activities.FavoritedContext,
    CreatedOn: new Date()
  })
  await createActivity(activity)
  const record = await addRow<LearningContextUserFavorite>(pool.request(), contextFavorite)
  return new LearningContextUserFavoritesModel(record)
}
