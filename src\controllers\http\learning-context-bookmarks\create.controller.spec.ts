import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextBookmark from '../../../models/learning-context-user-bookmark.model.js'

describe('HTTP Create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context-bookmarks/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextBookmark({LearningContextID: uuid(), UserID: uuid()})))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                UserID: uuid(),
                LearningContextID: uuid(),
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
        
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context-bookmarks/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextBookmark({LearningContextID: uuid(), UserID: uuid()})))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                UserID: false,
                LearningContextID: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('UserID')
        expect(mocks.res._getData()).include('LearningContextID')

        
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-context-bookmarks/create.service.js': {
                default: Sinon.stub().returns(Promise.reject(new LearningContextBookmark({LearningContextID: uuid(), UserID: uuid()})))
            }
        })

        const mocks = httpMocks.createMocks({
            body: {
                UserID: uuid(),
                LearningContextID: uuid(),
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
        
    })

})