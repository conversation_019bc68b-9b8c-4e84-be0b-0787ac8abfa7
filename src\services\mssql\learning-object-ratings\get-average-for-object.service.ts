import mssql, { DB_Errors } from '@lcs/mssql-utility'
import { LearningObjectRatingFields, LearningObjectRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-rating.js'

/**
 *
 * @param {string} objectID learning object ID
 * @returns {{ average: number, count: number }}
 */
export default async function (objectID: string): Promise<{ average: number, count: number}> {
  const pool = mssql.getPool()
  const request = pool.request()

  const query = `
    SELECT AVG([${LearningObjectRatingFields.Rating}]) AS average, COUNT([${LearningObjectRatingFields.ID}]) AS count
    FROM [${LearningObjectRatingsTableName}]
    WHERE [${LearningObjectRatingFields.LearningObjectID}] = @id
  `

  request.input('id', objectID)

  const results = await request.query<{ average: number, count: number}>(query)

  if (results.recordset.length <= 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }

  return results.recordset[0]
}
