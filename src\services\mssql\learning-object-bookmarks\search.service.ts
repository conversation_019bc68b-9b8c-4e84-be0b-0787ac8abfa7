import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { UserGroupFields, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'
import { LearningObjectUserBookmarkFields, LearningObjectUserBookmarksTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-bookmark.js'

export default async function (objectID: string, offset = 0, limit = 10, search?: string, groupIDs?: string[]): Promise<{ totalRecords: number, ids: string[] }> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('objectID', objectID)
  request.input('limit', limit)
  request.input('offset', offset)

  let query = `
    SELECT [${UserFields.ID}], TotalRecords = COUNT(*) OVER()
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] IN (
      SELECT [${LearningObjectUserBookmarkFields.UserID}]
      FROM [${LearningObjectUserBookmarksTableName}]
      WHERE [${LearningObjectUserBookmarkFields.LearningObjectID}] = @objectID
    )
  `

  if (search) {
    query += ` AND ${parseSearchTerms(request, search, [UserFields.FirstName, UserFields.LastName], 'any')} `
  }

  if (groupIDs && groupIDs.length > 0) {
    const conds = groupIDs.map((id, index) => {
      request.input(`group_${index}`, id)
      return `@group_${index}`
    })
    query += `
      AND [${UserFields.ID}] IN (
        SELECT [${UserGroupFields.UserID}]
        FROM [${UserGroupTableName}]
        WHERE [${UserGroupFields.GroupID}] IN (
          ${conds.join(', ')}
        )
      )
    `
  }

  query += `
    ORDER BY [${UserFields.FirstName}], [${UserFields.LastName}]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<{ TotalRecords: number, ID: string }>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    ids: results.recordset.map(record => record.ID)
  }
}
