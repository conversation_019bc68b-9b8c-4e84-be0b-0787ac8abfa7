import mssql, { streamQuery } from '@lcs/mssql-utility'
import { UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LessonStatusFields, LessonStatusTableName } from '@tess-f/sql-tables/dist/lms/lesson-status.js'

export interface LearnerProgressWithUserData {
  [LearnerProgressFields.CompletedDate]: Date
  [LearnerProgressFields.ID]: string
  [LearnerProgressFields.MaxScore]: number | null
  [LearnerProgressFields.MinScore]: number | null
  [LearnerProgressFields.RawScore]: number | null
  [LearnerProgressFields.TotalTime]: string | null
  [UserFields.FirstName]: string
  [UserFields.LastName]: string
  LessonStatus: string,
  [LearnerProgressFields.UserID]: string
}

export default async function getAllLearnerProgressForInteractionReport (learningObjectID:string): Promise<LearnerProgressWithUserData[]> {
  const request = mssql.getPool().request()
  request.input('learningObjectID', learningObjectID)

  return await streamQuery<LearnerProgressWithUserData>(request, `
    SELECT
      COALESCE([${LearnerProgressTableName}].[${LearnerProgressFields.CompletedDate}], [${LearnerProgressTableName}].[${LearnerProgressFields.CreatedOn}]) AS [${LearnerProgressFields.CompletedDate}],
      [${LearnerProgressTableName}].[${LearnerProgressFields.ID}],
      [${LearnerProgressTableName}].[${LearnerProgressFields.MaxScore}],
      [${LearnerProgressTableName}].[${LearnerProgressFields.MinScore}],
      [${LearnerProgressTableName}].[${LearnerProgressFields.RawScore}],
      [${LearnerProgressTableName}].[${LearnerProgressFields.TotalTime}],
      [${UserTableName}].[${UserFields.FirstName}],
      [${UserTableName}].[${UserFields.LastName}],
      [${LessonStatusTableName}].[${LessonStatusFields.Name}] AS [LessonStatus],
      [${LearnerProgressTableName}].[${LearnerProgressFields.UserID}]
    FROM [${LearnerProgressTableName}]
      JOIN [${UserTableName}] ON [${UserTableName}].[${UserFields.ID}] = [${LearnerProgressTableName}].[${LearnerProgressFields.UserID}]
      JOIN [${LessonStatusTableName}] ON [${LessonStatusTableName}].[${LessonStatusFields.ID}] = [${LearnerProgressTableName}].[${LearnerProgressFields.LessonStatusID}]
    WHERE [${LearnerProgressTableName}].[${LearnerProgressFields.LearningObjectID}] = @learningObjectID
  `)
}
