import mssql, { getRows } from '@lcs/mssql-utility'
import { AssignmentLearningObject, AssignmentLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/assignment-learning-object.js'
import AssignmentLearningObjectModel from '../../../models/assignment-learning-object.model.js'
import logger from '@lcs/logger'
import { getErrorMessage } from '@tess-f/backend-utils'
const log = logger.create('Service.Get-Multiple-Assignment-Learning-Contexts')

export default async function (assignmentID: string): Promise<AssignmentLearningObjectModel[]> {
  try {
    const pool = mssql.getPool()
    const records = await getRows<AssignmentLearningObject>(AssignmentLearningObjectsTableName, pool.request(), { AssignmentID: assignmentID })
    return records.map(record => new AssignmentLearningObjectModel(record))
  } catch (error) {
    // Unexpected error
    log('error', 'Unexpected error', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}
