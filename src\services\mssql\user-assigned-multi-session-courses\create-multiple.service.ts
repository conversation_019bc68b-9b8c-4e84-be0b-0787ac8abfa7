import { addRow } from '@lcs/mssql-utility'
import { ConnectionPool } from 'mssql'
import getContextService from '../learning-context/get.service.js'
import UserAssignedMultiSessionCourseModel from '../../../models/user-assigned-multi-session-course.model.js'
import { getMultiSessionAssignmentContentForLearningContext } from '../learning-context/utils.service.js'
import logger from '@lcs/logger'
import AssignmentUserModel from '../../../models/assignment-users.model.js'
import AssignmentLearningContextModel from '../../../models/assignment-learning-context.model.js'
import createStream from '../activity-stream/create.service.js'
import ActivityStreamModel from '../../../models/activity-stream.model.js'
import { UserAssignedMultiSessionCourses } from '@tess-f/sql-tables/dist/lms/user-assigned-multi-session-course.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service.Create-Multiple-User-Assigned-Multi-Session-Courses')

export default async function (pool: ConnectionPool, assignmentGUID: string, users: AssignmentUserModel[], contexts: AssignmentLearningContextModel[], groupUserIDs: string[], assignedByGUID: string, dueDate?: Date): Promise<void> {
  let userIDs: string[] = []
  users.forEach(user => {
    if (user.fields.UserID && !userIDs.includes(user.fields.UserID)) {
      userIDs.push(user.fields.UserID)
    }
  })

  userIDs = userIDs.concat(groupUserIDs)

  const userAssignedMultiSessionCourses: UserAssignedMultiSessionCourseModel[] = []

  // create an assigned multi session course record for each user and learning context
  for (const assignmentContext of contexts) {
    // get the context
    const context = await getContextService(assignmentContext.fields.LearningContextID!, undefined)
    let linkText

    if (context.fields.ContextTypeID === LearningContextTypes.Course && context.fields.CourseTypeID === CourseTypes.InstructorLed) {
      // this is an ILT course
      for (const id of userIDs) {
        userAssignedMultiSessionCourses.push(new UserAssignedMultiSessionCourseModel({
          AssignmentID: assignmentGUID,
          UserID: id,
          LearningContextID: context.fields.ID,
          DueDate: dueDate,
          CreatedOn: new Date()
        }))
      }
      linkText = `${context.fields.Label} Course: ${context.fields.Title}`
    } else {
      // this is another context
      // we need to get all the contexts for this context that are ILT courses
      const iltAssignments = await getMultiSessionAssignmentContentForLearningContext(pool.request(), context.fields.ID!)
      for (const iltAssignment of iltAssignments) {
        for (const id of userIDs) {
          const userAssignment = new UserAssignedMultiSessionCourseModel({ ...iltAssignment.exportJsonToDatabase() })
          userAssignment.fields.AssignmentID = assignmentGUID
          userAssignment.fields.UserID = id
          userAssignment.fields.CreatedOn = new Date()
          userAssignment.fields.DueDate = dueDate
          userAssignedMultiSessionCourses.push(userAssignment)
        }
      }
      linkText = `${context.fields.Label}: ${context.fields.Title}`
    }

    for (const id of userIDs) {
      try {
        await createStream(new ActivityStreamModel({
          UserID: assignedByGUID,
          LinkText: linkText,
          LinkID: context.fields.ID,
          ActivityID: Activities.AssignedContext,
          CreatedOn: new Date(),
          ToUserID: id
        }))
      } catch {}
    }
  }

  // commit the records to the database
  for (const assignment of userAssignedMultiSessionCourses) {
    try {
      await addRow<UserAssignedMultiSessionCourses>(pool.request(), assignment)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      // if the insert fails because the record exists just ignore the error
      if (errorMessage.toLowerCase().includes('violation of unique key constraint')) {
        // user already has the assignment
        log('warn', 'Failed to create user assigned multi session course record: user has assignment', {
          userId: assignment.fields.UserID,
          contextId: assignment.fields.LearningContextID,
          assignmentId: assignment.fields.AssignmentID,
          forContextId: assignment.fields.ForContextID,
          dueDate: assignment.fields.DueDate?.toISOString(),
          subContextId: assignment.fields.SubContextID,
          success: false
        })
      } else {
        log('error', 'Failed to create user assigned multi session course record', { errorMessage, userId: assignment.fields.UserID, assignmentId: assignment.fields.AssignmentID, success: false })
        throw error
      }
    }
  }

  log('info', 'Successfully created user assigned multi session course records', { userCount: userIDs.length, courseCount: contexts.length, success: true })
}
