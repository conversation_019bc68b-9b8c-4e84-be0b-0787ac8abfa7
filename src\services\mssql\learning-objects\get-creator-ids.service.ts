import mssql from '@lcs/mssql-utility'
import { User, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearningObjectsTableName, LearningObjectFields } from '@tess-f/sql-tables/dist/lms/learning-object.js'

export default async function (): Promise<User[]> {
  const records = await mssql.getPool().request().query<User>(`
    SELECT [${UserFields.ID}], [${UserFields.FirstName}], [${UserFields.LastName}], [${UserFields.MiddleInitial}], [${UserFields.Avatar}]
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] IN (
      SELECT [${LearningObjectFields.CreatedBy}]
      FROM [${LearningObjectsTableName}]
      WHERE [${LearningObjectFields.CreatedBy}] IS NOT NULL
    )
  `)

  return records.recordset
}
