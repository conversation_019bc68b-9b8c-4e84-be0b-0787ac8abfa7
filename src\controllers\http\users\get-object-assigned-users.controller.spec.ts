import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'

describe('HTTP get-object-assigned-users controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-object-assigned-users.controller', {
            '../../../services/mssql/assignments/search-object-user-assignments.service.js': {
                default: Sinon.stub().returns(Promise.resolve({totalRecords: 1, users: [{AssignmentID: uuid(), UserID: uuid(), Title: 'test', CreatedBy: 'test', CreatedOn: new Date(), DueDate: new Date()}]}))
            },
            '../../../services/mssql/assignments/get-user-object-assignment-status.service.js': {
                default: Sinon.stub().returns(Promise.resolve(0))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                limit: 10,
                offset: 1
            },
            params: {
                id: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-object-assigned-users.controller', {
            '../../../services/mssql/assignments/search-object-user-assignments.service.js': {
                default: Sinon.stub().returns(Promise.resolve({totalRecords: 1, users: [{AssignmentID: uuid(), UserID: uuid(), Title: 'test', CreatedBy: 'test', CreatedOn: new Date(), DueDate: new Date()}]}))
            },
            '../../../services/mssql/assignments/get-user-object-assignment-status.service.js': {
                default: Sinon.stub().returns(Promise.resolve(0))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                limit: 10,
                offset: 1
            },
            params: {
                id: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).to.include('Invalid request data')
        expect(data).to.include('id')
        expect(data).to.include('Expected string, received boolean')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-object-assigned-users.controller', {
            '../../../services/mssql/assignments/search-object-user-assignments.service.js': {
                default: Sinon.stub().rejects(Promise.resolve({totalRecords: 1, users: [{AssignmentID: uuid(), UserID: uuid(), Title: 'test', CreatedBy: 'test', CreatedOn: new Date(), DueDate: new Date()}]}))
            },
            '../../../services/mssql/assignments/get-user-object-assignment-status.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(0))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                limit: 10,
                offset: 1
            },
            params: {
                id: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })


})