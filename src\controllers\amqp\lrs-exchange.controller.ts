import rabbitMQ from '@lcs/rabbitmq'
import logger from '@lcs/logger'
import handleCreate from './lrs-exchange/handle-create.controller.js'
import { getErrorMessage } from '@tess-f/backend-utils'

// import controllers

const log = logger.create('Controller-AMQP.lrs-exchange')

const recognizedRoutes = new Map<string, Function>([
  ['created', handleCreate]
])

export default class LRSExchange {
  private consumer!: Function

  constructor (exchangeName: string, subscriptionQueue: string, routes: string[] | '' | undefined) {
    this.connect(exchangeName, subscriptionQueue, routes)
  }

  private async connect (exchangeName: string, subscriptionQueue: string, routes: string[] | '' | undefined) {
    await rabbitMQ.assertExchange(exchangeName, 'direct')
    // setup lrs exchange consumer on the subscription queue
    this.consumer = await rabbitMQ.createExchangeConsumer(
      exchangeName,
      subscriptionQueue,
      routes,
      (message, route, resolve, reject) => { this.onMessage(message, route, resolve, reject) },
      (error) => { this.onReceivedError(error) }
    )

    log('info', 'Consumer is active', { success: true })
  }

  private async onMessage (message: any, route: string, resolve: Function, reject: Function) {
    // Handle the message and resolve if successful
    try {
      await this.routeMessage(message, route)
      resolve()
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      // send back the message
      reject()
      // log the error
      log('error', 'Unable to handle event', { errorMessage, success: false })
    }
  }

  private async routeMessage (message: any, route: string) {
    // route to the controllers
    if (recognizedRoutes.has(route)) {
      await recognizedRoutes.get(route)!(message)
    } else {
      log('warn', 'Received an unknown routing key', { route, success: false })
    }
  }

  async close () {
    if (!this.consumer) { throw new Error('Cannot close consumer because it does not exist') }
    await this.consumer()
  }

  private async onReceivedError (error: unknown) {
    const errorMessage = getErrorMessage(error)
    log('error', 'Incoming message error', { errorMessage, success: false })
  }
}
