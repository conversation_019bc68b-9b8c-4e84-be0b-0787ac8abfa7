import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import { startServices, shutdownServices } from '../../../utils/testing.utils.js'

import getCourseReportController from './get-course-report.controller.js'

describe('Controller - HTTP: Analytics', () => {
  before(async () => await startServices())

  it('gets a report of completed courses', async () => {
    const mocks = httpMocks.createMocks()

    await getCourseReportController(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(200)
    const headers = mocks.res._getHeaders()
    expect(headers['content-type']).to.not.be.undefined
    expect(headers['content-type']).to.equal('text/csv')
  })

  after(async () => await shutdownServices())
})
