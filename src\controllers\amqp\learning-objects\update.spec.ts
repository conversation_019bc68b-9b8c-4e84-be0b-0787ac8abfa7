import logger from '@lcs/logger'
import LearningObjectModel from '../../../models/learning-object.model.js'
import Sinon from 'sinon'
import { expect } from 'chai'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('Controller.AMQP: update learning object', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns unsuccessful when the message data is missing', async () => {
    const controller = await esmock('./update.js')
    const message: any = { command: 'test', data: undefined }
    const response = await controller(message)
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('Required')
  })

  it('returns unsuccessful when the message data is invalid', async () => {
    const controller = await esmock('./update.js')
    const message: any = { command: 'test', data: {
      Title: ['Test'],
      Description: true,
      LearningObjectTypeID: 156,
      MinutesToComplete: 'test',
      VisibilityID: '1',
      EnableCertificates: 'true',
      URL: 123,
      LearningObjectSubTypeID: '1',
      SystemId: '123456789012345678901234567890123456789012345678901234567890',
      Keywords: 'test',
      ObjectiveIds: ['test']
    } }
    const response = await controller(message)
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('Title: Expected string, received array')
    expect(response.message).to.include('Description: Expected string, received boolean')
    expect(response.message).to.include('LearningObjectTypeID: Invalid enum value. Expected 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16, received \'156\'')
    expect(response.message).to.include('MinutesToComplete: Expected number, received string')
    expect(response.message).to.include('VisibilityID: Invalid enum value. Expected 1 | 2 | 3, received \'1\'')
    expect(response.message).to.include('EnableCertificates: Expected boolean, received string')
    expect(response.message).to.include('URL: Expected string, received number')
    expect(response.message).to.include('LearningObjectSubTypeID: Invalid enum value. Expected 1 | 2 | 3, received \'1\'')
    expect(response.message).to.include('SystemId: String must contain at most 50 character(s)')
    expect(response.message).to.include('Keywords: Expected array, received string')
    expect(response.message).to.include('ObjectiveIds.0: Invalid')
  })

  it('returns unsuccessful when the message data is missing the ModifiedBy and ID fields', async () => {
    const controller = await esmock('./update.js')
    const message: any = { command: 'test', data: {
      Title: 'Test'
    } }
    const response = await controller(message)
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('ModifiedBy: Required')
    expect(response.message).to.include('ID: Required')
  })

  it('returns unsuccessful when the message data ModifiedBy and ID fields are invalid', async () => {
    const controller = await esmock('./update.js')
    const message: any = { command: 'test', data: {
      Title: 'Test',
      ModifiedBy: 123,
      ID: '123'
    } }
    const response = await controller(message)
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('ModifiedBy: Expected string, received number')
    expect(response.message).to.include('ID: Invalid')
  })

  it('returns the object object', async () => {
    const controller = await esmock('./update.js', {
      '../../../services/mssql/learning-objects/update.service.js': {
        default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({
          ID: 'test',
          Title: 'Test'
        })))
      }
    })
    const response = await controller({
      command: 'test',
      data: {
        Title: 'Test',
        ID: uuid(),
        ModifiedBy: uuid()
      }
    })
    expect(response.success).to.be.true
    expect(response.data).to.exist
    expect(response.data?.ID).to.equal('test')
  })

  it('returns unsuccessful when the object to be updated could not be found', async () => {
    const controller = await esmock('./update.js', {
      '../../../services/mssql/learning-objects/update.service.js': {
        default: Sinon.stub().returns(Promise.reject(new Error(dbErrors.default.NOT_FOUND_IN_DB)))
      }
    })
    const response = await controller({
      command: 'test',
      data: {
        ID: uuid(),
        Title: 'Test',
        ModifiedBy: uuid()
      }
    })
    expect(response.success).to.be.false
    expect(response.message).to.equal(dbErrors.default.NOT_FOUND_IN_DB)
  })

  it('returns unsuccessful when the given keywords are invalid', async () => {
    const controller = await esmock('./update.js', {
      '../../../services/mssql/learning-objects/update.service.js': {
        default: Sinon.stub().returns(Promise.reject(new Error('Bad object keyword')))
      }
    })
    const response = await controller({
      command: 'test',
      data: {
        ID: uuid(),
        Keywords: ['Bad'],
        ModifiedBy: uuid()
      }
    })
    expect(response.success).to.be.false
    expect(response.message).to.equal('Bad object keyword')
  })

  it('returns unsuccessful when the service fails for an unknown reason', async () => {
    const controller = await esmock('./update.js', {
      '../../../services/mssql/learning-objects/update.service.js': {
        default: Sinon.stub().returns(Promise.reject(new Error('Service Error')))
      }
    })
    const response = await controller({
      command: 'test',
      data: {
        ID: uuid(),
        Title: 'Test',
        ModifiedBy: uuid()
      }
    })
    expect(response.success).to.be.false
    expect(response.message).to.equal('Service Error')
  })
})
