import getRelated from '../../../services/mssql/related-learning/get.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import learningObjectExtrasMapper from '../../../mappers/learning-object-extras.mapper.js'
import learningContextExtrasMapper from '../../../mappers/learning-context-extras.mapper.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.get-related-learning', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, click on a learning object within the course outline when viewing a single course. Note the time. Check the LMS API log for the 'http-get-related-learning' label."
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const result = await getRelated(id)

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, click on a learning object within the course outline when viewing a single course. Note the time. Check the LMS API log for the 'http-get-related-learning' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved related learning.', { req })

    await learningObjectExtrasMapper(result.learningObjects, req.session.userId, {
      rating: true,
      bookmark: true,
      favorite: true,
      completion: true
    })

    await learningContextExtrasMapper(result.learningContexts, req.session.userId, {
      rating: true,
      bookmark: true,
      favorite: true,
      completion: true,
      duration: true
    })

    res.json({
      learningObjects: result.learningObjects.map(learningObject => learningObject.fields),
      learningContexts: result.learningContexts.map(context => context.fields)
    })
  } catch (error) {
    // STIG V-69425 data access (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get related learning: input validation error', { error, req, success: false })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter, '))
    } else {
      log('error', 'Failed to get related learning.', { error, req, success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
