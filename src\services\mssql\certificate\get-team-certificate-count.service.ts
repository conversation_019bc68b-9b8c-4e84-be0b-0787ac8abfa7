import mssql from '@lcs/mssql-utility'
import { UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'

export default async function getTeamCertificateCount (managerID: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('managerID', managerID)

  const certificateCount = await request.query<{ TotalRecords: number }>(`
    SELECT COUNT(*) AS [TotalRecords]
    FROM (
      SELECT [${LearnerProgressFields.Certificate}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.UserID}] IN (
        SELECT [${UserFields.ID}]
        FROM [${UserTableName}]
        WHERE [${UserFields.ManagerID}] = @managerID
      )
      AND [${LearnerProgressFields.Certificate}] IS NOT NULL

      UNION ALL

      SELECT [${UserCompletedLearningContextFields.Certificate}]
      FROM [${UserCompletedLearningContextsTableName}]
      WHERE [${UserCompletedLearningContextFields.Certificate}] IS NOT NULL
      AND [${UserCompletedLearningContextFields.UserID}] IN (
        SELECT [${UserFields.ID}]
        FROM [${UserTableName}]
        WHERE [${UserFields.ManagerID}] = @managerID
      )
      AND [${UserCompletedLearningContextFields.Certificate}] NOT IN (
        SELECT [${LearnerProgressFields.Certificate}]
        FROM [${LearnerProgressTableName}]
        WHERE [${LearnerProgressFields.UserID}] IN (
          SELECT [${UserFields.ID}]
          FROM [${UserTableName}]
          WHERE [${UserFields.ManagerID}] = @managerID
        )
        AND [${LearnerProgressFields.Certificate}] IS NOT NULL
      )
    ) AS [Certificates]
  `)

  return certificateCount.recordset[0].TotalRecords
}
