import { getSessionsForContext as getSessions } from '../../../services/mssql/learning-context-sessions/get.service.js'
import getInstructors from '../../../services/mssql/learning-context-session-instructors/get.service.js'
import getEnrollments from '../../../services/mssql/session-enrollments/get.service.js'
import logger from '@lcs/logger'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import LearningContextSessionInstructor from '../../../models/learning-context-session-instructor.model.js'
import SessionEnrollment from '../../../models/session-enrollment.model.js'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus
import getMultipleUsersByIds from '../../../services/mssql/users/get-multiple-by-ids.service.js'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-learning-context-sessions', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const _sessions = await getSessions(id)

    const sessions = await Promise.all(_sessions.map(async session => {
      let sessionInstructors: LearningContextSessionInstructor[], sessionEnrollments: SessionEnrollment[]

      // get the related records
      try {
        sessionInstructors = await getInstructors(session.fields.ID)
      } catch (error) {
        if (getErrorMessage(error) !== dbErrors.default.NOT_FOUND_IN_DB) {
          throw error
        }
        sessionInstructors = []
      }
      try {
        sessionEnrollments = await getEnrollments(session.fields.ID, undefined)
      } catch (error) {
        if (getErrorMessage(error) !== dbErrors.default.NOT_FOUND_IN_DB) {
          throw error
        }
        sessionEnrollments = []
      }

      const instructorUserIDs = sessionInstructors.map(record => record.fields.UserID!)

      session.fields.Instructors = await getMultipleUsersByIds(instructorUserIDs)
      session.fields.Enrollments = sessionEnrollments.map(sessionEnrollment => sessionEnrollment.fields)
      return session.fields
    }))
    // STIG V-69425 data access (success)
    log('info', 'Successfully retrieved learning context session(s).', { count: _sessions.length, success: true, req })

    res.json(sessions)
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning context sessions: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get learning context sessions because they were not found in the database.', { success: false, req })
      res.json([])
    } else {
      log('error', 'Failed to retrieve learning context sessions.', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
