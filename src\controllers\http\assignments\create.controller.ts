// Assignments
import logger from '@lcs/logger'
import Assignment, { createAssignmentSchema } from '../../../models/assignment.model.js'
import AssignmentUser from '../../../models/assignment-users.model.js'
import AssignmentLearnContext from '../../../models/assignment-learning-context.model.js'
import AssignmentLearnObj from '../../../models/assignment-learning-object.model.js'
import create from '../../../services/mssql/assignments/create.service.js'
import { Request, Response } from 'express'
import getIDsOfMyDirectReports from '../../../services/mssql/users/get-ids-of-my-direct-reports.service.js'
import getAllUserIDs from '../../../services/mssql/users/get-all-user-ids.service.js'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import sendAssignmentNotification from '../../../services/email/send-assignment-notification.service.js'
import { mapUsersFromAssignment } from '../../../utils/assignment-utils.js'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.create-assignment', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const assignment = new Assignment(createAssignmentSchema.parse(req.body))
    const users: AssignmentUser[] = []
    const groupUserIDs: string[] = []

    assignment.fields.CreatedBy = req.session.userId
    assignment.fields.CreatedOn = new Date()
    
    const { contexts, learningObjects } = getLearningContentFromRequest(req.body, req.session.userId)

    if (!assignment.fields.Everyone && !assignment.fields.DirectReports) {
      await mapUsersFromAssignment(assignment, users, groupUserIDs)
    } else if (assignment.fields.Everyone) {
      // we need to get everyone together so that their tracking can be set up by the service
      const userIDs = await getAllUserIDs()
      userIDs.forEach(id => {
        users.push(new AssignmentUser({
          UserID: id
        }))
      })
    } else if (assignment.fields.DirectReports) {
      // we need to get the user ids of the direct reports for the user creating the assignment
      const userIDs = await getIDsOfMyDirectReports(assignment.fields.CreatedBy)
      userIDs.forEach(id => {
        users.push(new AssignmentUser({
          UserID: id
        }))
      })
    }

    // double check that we have users to assign this content to
    if (users.length <= 0) {
      log('warn', 'Failed to create assignment: no users to assign content to', { success: false, req })
      res.status(BAD_REQUEST).send('Cannot create assignment, no users selected to assign content to')
      return
    }

    const result = await create(assignment, users, contexts, learningObjects, groupUserIDs)
    const created = result.assignment.fields

    log('info', 'Successfully created assignment', { id: assignment.fields.ID, userCount: users.length, success: true, req })

    res.json(created)

    // send assignment notification
    try {
      const userIds = users.filter(assignmentUser => assignmentUser.fields.UserID !== undefined).map(assignmentUser => assignmentUser.fields.UserID!)
      groupUserIDs.forEach(id => {
        if (!userIds.includes(id)) {
          userIds.push(id)
        }
      })
      await sendAssignmentNotification(result.assignment, userIds)
    } catch {
      // just log that we didn't send the emails the user already got a response from us
      log('warn', 'Failed to send assignment notification email', { success: false, req })
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to create assignment: validation error', { success: false, req, error })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data:'))
    } else {
      log('error', 'Failed to create assignment.', { success: false, req, error })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}

function getLearningContentFromRequest (body: any, userId: string): { contexts: AssignmentLearnContext[], learningObjects: AssignmentLearnObj[] } {
  const { LearningContexts, LearningObjects } = z.object({
    LearningContexts: z.array(z.object({ ID: zodGUID, OrderID: z.number() })).optional().default([]),
    LearningObjects: z.array(z.object({ ID: zodGUID, OrderID: z.number() })).optional().default([])
  }).superRefine(({ LearningContexts, LearningObjects }, ctx) => {
    if (LearningContexts.length <= 0 && LearningObjects.length <= 0) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'No assignment content defined'
      })
    }
  }).parse(body)

  return {
    contexts: LearningContexts.map(context => {
      return new AssignmentLearnContext({
        LearningContextID: context.ID,
        OrderID: context.OrderID,
        CreatedBy: userId,
        CreatedOn: new Date()
      })
    }),
    learningObjects: LearningObjects.map(object => {
      return new AssignmentLearnObj({
        LearningObjectID: object.ID,
        OrderID: object.OrderID,
        CreatedBy: userId,
        CreatedOn: new Date()
      })
    })
  }
}
