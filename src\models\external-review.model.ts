import { Table } from '@lcs/mssql-utility'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { ExternalReviewCommunication, ExternalReviewCommunicationFields, ExternalReviewCommunicationTableName } from '@tess-f/sql-tables/dist/lms/external-review-communication.js'
import { ExternalReviewReviewStatuses } from '@tess-f/sql-tables/dist/lms/external-review-status.js'

export const createMessageSchema = z.object({
  Message: z.string().max(500)
})

export const additionalFields = z.object({
  ReviewId: z.string(),
  StatusId: z.nativeEnum(ExternalReviewReviewStatuses),//zodGUID, //zodGUID,
  CreatedBy: z.string(),
  CreatedOn: z.date()
})

export const createMessageReviewSchema = createMessageSchema.merge(additionalFields)
export default class ExternalReviewCommunicationModel extends Table<ExternalReviewCommunication, ExternalReviewCommunication> {
  public fields: ExternalReviewCommunication

  constructor (fields?: ExternalReviewCommunication) {
    super(ExternalReviewCommunicationTableName, [
      ExternalReviewCommunicationFields.Message,
      ExternalReviewCommunicationFields.CreatedBy,
      ExternalReviewCommunicationFields.CreatedOn,
      ExternalReviewCommunicationFields.ReviewId,
      ExternalReviewCommunicationFields.StatusId
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: ExternalReviewCommunication): void {
    this.fields = record
  }

  public exportJsonToDatabase (): ExternalReviewCommunication {
    return this.fields
  }
}
