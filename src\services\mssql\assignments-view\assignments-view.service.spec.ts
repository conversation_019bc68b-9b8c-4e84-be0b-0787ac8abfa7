import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import getMulti from './get-multiple.service.js'

describe('MSSQL Assignment View', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
  })

  it('gets multiple assignment learning context records by assignment ID', async () => {
    const contexts = await getMulti(1, 150)
    expect(contexts.totalRecords).to.be.gte(0)
  })
})
