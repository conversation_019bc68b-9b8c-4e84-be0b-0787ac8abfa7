import logger from '@lcs/logger'
import getCurrentAssignmentCounts from '../../../services/mssql/assignments/get-current-counts.service.js'
import getTopTenKeywords from '../../../services/mssql/keywords/get-top-ten-assignment-keyword-use-count.service.js'
import getContentTypeCounts from '../../../services/mssql/assignments/get-content-type-counts.service.js'
import getAssignmentCountsHistory from '../../../services/mssql/assignments/get-count-history.service.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer } from '@tess-f/backend-utils'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.get-assignments-dashboard', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    let from: Date | undefined
    let to: Date | undefined
    const filterDates = z.object({
      from: z.coerce.date().optional(),
      to: z.coerce.date().optional()
    }).safeParse(req.query)

    if (filterDates.success) {
      from = filterDates.data.from
      to = filterDates.data.to
    }

    // get the counts overview
    const counts = await getCurrentAssignmentCounts()
    log('info', 'Successfully retrieved current assignment counts', { success: true, req })
    const topTenKeywords = await getTopTenKeywords(from, to)
    topTenKeywords.sort((a, b) => a.Name.localeCompare(b.Name))
    log('info', 'Successfully retrieved top ten assignment content keywords', { success: true, req })
    const ContentCountsByType = await getContentTypeCounts(from, to)
    log('info', 'Successfully retrieved assignment content type counts', { success: true, req })
    const AssignmentActivity = await getAssignmentCountsHistory(from, to)
    log('info', 'Successfully retrieved assignment counts history', { success: true, req })

    res.json({
      Overview: {
        Active: counts.Active,
        Overdue: counts.Overdue,
        Completed: counts.Completed,
        InProgress: counts.InProgress
      },
      ContentCountsByType,
      ContentByKeywords: topTenKeywords.map(record => { return { Keyword: record.Name, UseCount: record.UseCount } }),
      AssignmentActivity: AssignmentActivity.map(record => record.fields)
    })
  } catch (error) {
    log('error', 'Failed to generate the assignment dashboard data.', { error, req, success: false })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
