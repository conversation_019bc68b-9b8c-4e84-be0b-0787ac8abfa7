import { Request, Response } from 'express'
import SCORM1_2InteractionModel, { scorm12InteractionSchema } from '../../../models/scorm-1-2-interaction.model.js'
import createScorm12Interaction from '../../../services/mssql/scorm-1-2-interaction/create.service.js'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
import deleteInteractionsForUserSession from '../../../services/mssql/scorm-1-2-interaction/delete-for-user-session.service.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'

const log = logger.create('Controller-http.create-multiple-scorm-1.2-interactions', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    // check that we have a body
    const data = z.array(scorm12InteractionSchema).min(1).parse(req.body)
  
    // ensure all interactions are for one user
    const userId = data[0].UserID
  
    if (data.filter(interaction => interaction.UserID !== userId).length > 0) {
      log('warn', 'Unable to process request due to multiple users in body', { success: false, req })
      res.status(httpStatus.BAD_REQUEST).send('Only one users interactions can be processed at a time')
      return
    }
  
    // ensure all interactions are for one session (i.e. only one learner progress id)
    const learnerProgressID = data[0].LearnerProgressID
  
    if (data.filter(interaction => interaction.LearnerProgressID !== learnerProgressID).length > 0) {
      log('warn', 'Unable to process request due to multiple sessions data in the request body', { success: false, req })
      res.status(httpStatus.BAD_REQUEST).send('Only one session can be processed at a time')
      return
    }

    // remove previous interactions, if we have any
    await deleteInteractionsForUserSession(userId, learnerProgressID)

    const interactions = (await Promise.all(data.map(async body => {
      const interaction = new SCORM1_2InteractionModel(body)
      return await createScorm12Interaction(interaction)
    }))).filter(interaction => interaction)

    log('info', 'Successfully created SCORM 1.2 Interactions', { count: interactions.length, success: true, req })

    res.json(interactions.map(interaction => interaction.fields))
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to create SCORM 1.2 interactions: input validation error', { success: false, req, error })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to create SCORM 1.2 interactions', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
