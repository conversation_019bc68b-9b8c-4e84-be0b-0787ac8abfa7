import rabbitmq from '@lcs/rabbitmq'
import logger from '@lcs/logger'
import Sinon from 'sinon'
import { expect } from 'chai'
import service from './create-statement.service.js'
import { v4 as uuid, validate } from 'uuid'

describe('Service-AMQP: create LRS statement', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('should return the id of the created statement', async () => {
    Sinon.stub(rabbitmq, 'executeRPC').returns(Promise.resolve({
      success: true,
      id: uuid()
    }))
    const id = await service({
      verb: { id: 'test' },
      actor: { objectType: 'Agent', name: 'test' },
      timestamp: (new Date()).toISOString(),
      object: { id: 'test', objectType: 'Activity' },
      authority: {}
    })
    expect(id).to.exist
    expect(validate(id)).to.be.true
  })

  it('should return message from service when present and call was unsuccessful', done => {
    Sinon.stub(rabbitmq, 'executeRPC').returns(Promise.resolve({
      success: false,
      message: 'Test Service Failure'
    }))
    service({
      verb: { id: 'test' },
      actor: { objectType: 'Agent', name: 'test' },
      timestamp: (new Date()).toISOString(),
      object: { id: 'test', objectType: 'Activity' },
      authority: {}
    }).then(() => {
      done(new Error('Should not return success response'))
    }, (err: any) => {
      expect(err).to.exist
      expect(err instanceof Error).to.be.true
      expect(err.message).to.equal('Test Service Failure')
      done()
    })
  })

  it('should return unknown error when the RPC call encounters an error', done => {
    Sinon.stub(rabbitmq, 'executeRPC').returns(Promise.reject(new Error('RPC Timeout')))
    service({
      verb: { id: 'test' },
      actor: { objectType: 'Agent', name: 'test' },
      timestamp: (new Date()).toISOString(),
      object: { id: 'test', objectType: 'Activity' },
      authority: {}
    }).then(() => {
      done(new Error('Should not return success response'))
    }, (err: any) => {
      expect(err).to.exist
      expect(err instanceof Error).to.be.true
      expect(err.message).to.equal('Unknown Error')
      done()
    })
  })
})
