import { Request<PERSON><PERSON><PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getController from './get.controller.js'
import updateController from './update.controller.js'

const router = Router()

router.get('/object-rating', getController as RequestHandler)
router.post('/object-rating', createController as RequestHandler)
router.put('/object-rating/:id', updateController as RequestHandler)
router.delete('/object-rating/:id', deleteController as RequestHandler)

export default router
