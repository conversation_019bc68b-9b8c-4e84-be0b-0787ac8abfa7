import { Request<PERSON>and<PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getMultipleController from './get-multiple.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.delete('/keyword/:name', checkClaims([Claims.CREATE_SYSTEM_LABELS]), deleteController as RequestHandler)
router.post('/keyword', checkClaims([Claims.CREATE_SYSTEM_LABELS]), createController as RequestHandler)
router.get('/keywords', getMultipleController as RequestHandler)

export default router
