import { RequestHand<PERSON>, Router } from 'express'
import getContentForWidgetController from './get-for-widget.controller.js'
import searchController from './search.controller.js'
import isContentLockedController from './is-locked.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.post('/search-content', checkClaims([Claims.BROWSE_CONTENT, Claims.VIEW_MY_LEARNING]), searchController as RequestHandler)
router.get('/widget-content/:id', getContentForWidgetController as RequestHandler)
router.get('/content/is-locked/:contextId/:contentId', isContentLockedController as RequestHandler)

export default router
