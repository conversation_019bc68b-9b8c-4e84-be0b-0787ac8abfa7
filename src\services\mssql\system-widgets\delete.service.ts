import mssql, { deleteRow } from '@lcs/mssql-utility'
import { SystemWidgetsTableName } from '@tess-f/sql-tables/dist/lms/system-widget.js'

export default async function deleteSystemWidget (id: string) {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    // delete the system widget
    await deleteRow(transaction.request(), SystemWidgetsTableName, { ID: id })

    await transaction.commit()
  } catch (error) {
    if (!rolledBack) transaction.rollback()
    throw error
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}
