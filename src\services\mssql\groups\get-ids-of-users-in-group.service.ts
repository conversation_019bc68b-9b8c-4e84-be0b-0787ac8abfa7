import mssql, { DB_Errors as dbErrors, getRows } from '@lcs/mssql-utility'
import { UserGroup, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'
import { getErrorMessage } from '@tess-f/backend-utils'

export default async function getIdsOfUsersInGroup (groupId: string): Promise<string[]> {
  try {
    const userGroups = await getRows<UserGroup>(UserGroupTableName, mssql.getPool().request(), { GroupID: groupId })
    return userGroups.map(userGroup => userGroup.UserID!)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
      return []
    }
    throw error
  }
}
