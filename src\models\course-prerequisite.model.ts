import { Table } from '@lcs/mssql-utility'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { CoursePrerequisiteJson } from '@tess-f/lms/dist/common/course-prerequisite.js'
import { CoursePrerequisite, CoursePrerequisiteFields, CoursePrerequisitesTableName } from '@tess-f/sql-tables/dist/lms/course-prerequisite.js'
import z from 'zod'
import { createPrerequisiteAlternativeSchema } from './course-prerequisite-alternative.model.js'

export const createPrerequisiteSchema = z.object({
  PrereqCourseID: zodGUID.nullish(),
  PrereqObjectId: zodGUID.nullish(),
  Enforce: z.boolean().default(false),
  Alternatives: z.array(createPrerequisiteAlternativeSchema).default([])
}).superRefine(({ PrereqCourseID, PrereqObjectId }, ctx) => {
  if (!!PrereqCourseID && !!PrereqObjectId) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Cannot set both PrereqCourseID and PrereqObjectId',
      path: ['PrereqCourseID']
    })
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Cannot set both PrereqCourseID and PrereqObjectId',
      path: ['PrereqObjectId']
    })
  } else if (!PrereqCourseID && !PrereqObjectId) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Must set either PrereqCourseID or PrereqObjectId'
    })
  }
})

export default class CoursePrerequisiteModel extends Table<CoursePrerequisiteJson, CoursePrerequisite> {
  public fields: CoursePrerequisiteJson

  constructor (fields?: CoursePrerequisiteJson, record?: CoursePrerequisite) {
    super(CoursePrerequisitesTableName, [
      CoursePrerequisiteFields.CourseID,
      CoursePrerequisiteFields.CreatedBy,
      CoursePrerequisiteFields.CreatedOn,
      CoursePrerequisiteFields.Enforce
    ])
    if (fields) this.fields = fields
    else this.fields = { Alternatives: [] }

    if (record) this.importFromDatabase(record)
  }

  public importFromDatabase (record: CoursePrerequisite): void {
    this.fields = {
      ID: record.ID,
      CourseID: record.CourseID,
      CreatedBy: record.CreatedBy,
      CreatedOn: record.CreatedOn,
      Enforce: record.Enforce,
      Alternatives: [],
      ModifiedBy: record.ModifiedBy,
      ModifiedOn: record.ModifiedOn,
      PrereqCourseID: record.PrereqCourseID,
      PrereqObjectId: record.PrereqObjectId
    }
  }

  public exportJsonToDatabase (): CoursePrerequisite {
    return {
      ID: this.fields.ID,
      CourseID: this.fields.CourseID,
      CreatedBy: this.fields.CreatedBy,
      CreatedOn: this.fields.CreatedOn,
      Enforce: this.fields.Enforce,
      ModifiedBy: this.fields.ModifiedBy,
      ModifiedOn: this.fields.ModifiedOn,
      PrereqCourseID: this.fields.PrereqCourseID,
      PrereqObjectId: this.fields.PrereqObjectId
    }
  }
}
