import { expect } from 'chai'
import mssql from '@lcs/mssql-utility'
import settings from '../../../../config/settings.js'
import logger from '@lcs/logger'
import create from './create.service.js'
import remove from './delete.service.js'
import getMultiple from './get-multiple.service.js'
import update from './update.service.js'
import GroupClaim from '../../../../models/group-claim.model.js'
import { AdminGroupId } from '@tess-f/sql-tables/dist/id-mgmt/group.js'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

describe('MSSQL Group Claims Service', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
  })

  it('should delete a group claim', async () => {
    const removed = await remove(AdminGroupId, Claims.BROWSE_CONTENT)
    expect(removed).to.equal(1)
  })

  it('should create group claims', async () => {
    const groupClaims = [
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.BROWSE_CONTENT })
    ]
    await create(groupClaims)
  })

  it('should get all group claims', async () => {
    const claims = await getMultiple(AdminGroupId)

    // Verify general things about return value.
    expect(claims).to.exist
    expect(claims).to.be.an('array')
    expect(claims.length).to.gte(2)

    // Verify each claim is present.
    let claim = claims.find(claim => claim.fields.Claim === 'Can_View_Assignments')
    expect(claim).to.exist
    claim = claims.find(claim => claim.fields.Claim === 'Can_View_My_Learning')
    expect(claim).to.exist
  })

  it('should delete single group claim', async () => {
    await remove(AdminGroupId, 'Can_View_Assignments')
    const rowsRemoved = await remove(AdminGroupId, 'Can_View_My_Learning')
    expect(rowsRemoved).to.equal(1)
  })

  it('should delete group claims', async () => {
    const rowsRemoved = await remove(AdminGroupId)
    expect(rowsRemoved).to.gte(1)
  })

  it('should update group claims', async () => {
    const groupClaims = [
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.BROWSE_CONTENT }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.CREATE_ASSIGNMENTS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.CREATE_CATALOG_ITEMS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.CREATE_COURSE }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.CREATE_FILE }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.CREATE_NOTIFICATIONS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.CREATE_SYSTEM_LABELS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.CREATE_SYSTEM_WIDGETS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.DELETE_ASSIGNMENTS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.DELETE_CATALOG_ITEMS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.DELETE_COURSE }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.DELETE_FILE }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.DELETE_NOTIFICATION }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.DELETE_SYSTEM_LABELS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.DELETE_SYSTEM_WIDGETS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.MODIFY_ASSIGNMENTS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.MODIFY_CATALOG_ITEMS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.MODIFY_COURSE }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.MODIFY_FILE }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.MODIFY_NOTIFICATIONS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.MODIFY_SYSTEM_LABELS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.MODIFY_SYSTEM_PERMISSIONS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.MODIFY_SYSTEM_WIDGETS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.PUBLISH_CATALOG_ITEMS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.PUBLISH_COURSE }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.PUBLISH_SYSTEM_WIDGETS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.VIEW_ADMIN_OVERVIEW }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.VIEW_ASSIGNMENTS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.VIEW_ASSIGNMENT_DASHBOARD }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.VIEW_CATALOG }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.VIEW_CONTENT_OVERVIEW }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.VIEW_COURSES }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.VIEW_FILES }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.VIEW_MY_LEARNING }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.VIEW_MY_ACTIVITY }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.VIEW_NOTIFICATIONS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.VIEW_SYSTEM_LABELS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.VIEW_SYSTEM_PERMISSIONS }),
      new GroupClaim({ GroupID: AdminGroupId, Claim: Claims.VIEW_SYSTEM_WIDGETS })
    ]
    await update(AdminGroupId, groupClaims)
  })

  it('should get all updated group claims', async () => {
    const claims = await getMultiple(AdminGroupId)

    // Verify general things about return value.
    expect(claims).to.exist
    expect(claims).to.be.an('array')
    expect(claims.length).to.gte(1)

    // Verify each claim is present.
    let claim = claims.find(claim => claim.fields.Claim === 'Can_View_Admin_Overview')
    expect(claim).to.exist
    claim = claims.find(claim => claim.fields.Claim === 'Can_View_Files')
    expect(claim).to.exist
    claim = claims.find(claim => claim.fields.Claim === 'Can_View_My_Learning')
    expect(claim).to.exist
  })
})
