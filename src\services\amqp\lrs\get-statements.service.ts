import rabbitmq from '@lcs/rabbitmq'
import logger from '@lcs/logger'
import settings from '../../../config/settings.js'
import Statement, { AgentJson } from '../../../models/amqp/lrs/statement.model.js'
import { RpcResponse } from '@tess-f/shared-config/dist/types/rpc-response.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service.AMQP.Get-LRS-Statements')

export default async function getStatementsFromLRS (message: {
  format?: string
  statementId?: string
  voidedStatementId?: string
  agent?: AgentJson
  relatedAgents?: boolean
  verb?: string
  activity?: string
  relatedActivities?: boolean
  registration?: string
  since?: Date
  until?: Date
  limit?: number
  ascending?: boolean
  offset?: number
}): Promise<{
  statements: Statement[],
  totalRecords: number
}> {
  try {
    const response: RpcResponse<{ statements: Statement[], totalRecords: number }> = await rabbitmq.executeRPC(
      settings.amqp.service_queues.lrs, {
        command: 'get',
        data: message
      },
      settings.amqp.rpc_timeout
    )

    if (response.success) {
      log('info', 'Successfully fetched statements from LRS', { count: response.data?.totalRecords, success: true })
      return response.data!
    } else {
      throw new Error(response.message ?? 'RPC error')
    }
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to get statements', { errorMessage, success: false })
    throw error
  }
}
