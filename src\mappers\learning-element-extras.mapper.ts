import logger from '@lcs/logger'
import { DB_Errors } from '@lcs/mssql-utility'
import LearningElement from '../models/learning-elements-view.model.js'
import getContextUserFavorite from '../services/mssql/learning-context-favorites/get.service.js'
import getContextUserBookmark from '../services/mssql/learning-context-bookmarks/get.service.js'
import { getUserRatingForContext } from '../services/mssql/learning-context-ratings/get.service.js'
import getObjectUserFavorite from '../services/mssql/learning-object-favorites/get.service.js'
import getObjectUserBookmark from '../services/mssql/learning-object-bookmarks/get.service.js'
import { getObjectRatingsForUser } from '../services/mssql/learning-object-ratings/get.service.js'
import getContextPrereqs from '../services/mssql/course-prerequisite/get-for-context.service.js'
import getUpcomingForContextService from '../services/mssql/learning-context-sessions/get-upcoming-for-context.service.js'
import { getKeywordsForContext, getKeywordsForObject } from '../services/mssql/keywords/get-multiple.service.js'
import getContextDuration from '../services/mssql/learning-context/get-duration.service.js'
import getContextCompletion from '../services/mssql/learning-context/get-completion.service.js'
import getObjectCompletion from '../services/mssql/learning-objects/get-completion.service.js'
import getLearningContextDueDate from '../services/mssql/assignments/get-context-due-date.service.js'
import getLearningObjectDueDate from '../services/mssql/assignments/get-object-due-date.service.js'
import getEnrolledSessionsForUser from '../services/mssql/learning-context-sessions/get-enrolled-sessions-for-user.service.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Mapper.Learning-element-extras')

export default async function (data: LearningElement[], userID: string, opts: {
  rating?: boolean
  bookmark?: boolean
  favorite?: boolean
  keywords?: boolean
  prerequisites?: boolean
  sessions?: boolean
  duration?: boolean
  completion?: boolean
  dueDate?: boolean
  enrolledSessions?: boolean
}): Promise<void[]> {
  return await Promise.all(data.map(async element => {
    if (element.fields.EntityType === 'Learning Context') {
      if (opts.bookmark) {
        try {
          element.fields.UserBookmark = (await getContextUserBookmark(element.fields.ID!, userID)).fields
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get user bookmark for learning context', { id: element.fields.ID, errorMessage, success: false })
            throw error
          }
        }
      }

      if (opts.favorite) {
        try {
          element.fields.UserFavorite = (await getContextUserFavorite(element.fields.ID!, userID)).fields
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get user Favorite for learning context', { id: element.fields.ID, errorMessage, success: false })
            throw error
          }
        }
      }

      if (opts.rating) {
        try {
          element.fields.UserRating = (await getUserRatingForContext(element.fields.ID!, userID)).fields
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get users rating for learning context', { id: element.fields.ID!, errorMessage, success: false })
            throw error
          }
        }
      }

      if (opts.keywords) {
        try {
          element.fields.Keywords = await getKeywordsForContext(element.fields.ID!)
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get keywords for learning context', { id: element.fields.ID, errorMessage, success: false })
            throw error
          }
        }
      }

      if (opts.prerequisites) {
        try {
          element.fields.Prerequisites = (await getContextPrereqs(element.fields.ID!)).map(record => record.fields)
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get course prerequisites for context', { id: element.fields.ID, errorMessage, success: false })
            throw error
          }
        }
      }

      if (opts.sessions) {
        try {
          element.fields.Sessions = (await getUpcomingForContextService(element.fields.ID!)).map(record => record.fields)
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get upcoming sessions for learning context', { id: element.fields.ID, errorMessage, success: false })
            throw error
          }
        }
      } else if (opts.enrolledSessions) {
        try {
          element.fields.Sessions = (await getEnrolledSessionsForUser(element.fields.ID!, userID)).map(record => record.fields)
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          log('error', 'Failed to get enrolled sessions', { id: element.fields.ID, errorMessage, success: false })
          throw error
        }
      }

      if (opts.duration) {
        try {
          element.fields.MinutesToComplete = await getContextDuration(element.fields.ID!)
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          log('error', 'Failed to get context duration', { id: element.fields.ID, errorMessage, success: false })
          throw error
        }
      }

      if (opts.completion) {
        try {
          element.fields.PercentComplete = (await getContextCompletion(element.fields.ID!, userID)).completion
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          log('error', 'Failed to get completion amount for learning context', { id: element.fields.ID, errorMessage, success: false })
          throw error
        }
      }

      if (opts.dueDate) {
        try {
          element.fields.DueDate = await getLearningContextDueDate(element.fields.ID!, userID)
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get due date for users assigned content', { id: element.fields.ID, errorMessage, success: false })
            throw error
          }
        }
      }
    } else if (element.fields.EntityType === 'Learning Object') {
      if (opts.rating) {
        try {
          element.fields.UserRating = (await getObjectRatingsForUser(element.fields.ID!, userID)).fields
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get user rating for learning object', { id: element.fields.ID, errorMessage, success: false })
            throw error
          }
        }
      }

      if (opts.favorite) {
        try {
          element.fields.UserFavorite = (await getObjectUserFavorite(element.fields.ID!, userID)).fields
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get user favorite for learning object', { id: element.fields.ID, errorMessage, success: false })
            throw error
          }
        }
      }

      if (opts.bookmark) {
        try {
          element.fields.UserBookmark = (await getObjectUserBookmark(element.fields.ID!, userID)).fields
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get user bookmark for learning object', { id: element.fields.ID, errorMessage, success: false })
            throw error
          }
        }
      }

      if (opts.keywords) {
        try {
          element.fields.Keywords = await getKeywordsForObject(element.fields.ID!)
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get keywords for learning object', { id: element.fields.ID, errorMessage, success: false })
          }
        }
      }

      if (opts.completion) {
        try {
          element.fields.PercentComplete = await getObjectCompletion(element.fields.ID!, userID)
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          log('error', 'Failed to get learning object completion', { id: element.fields.ID, errorMessage, success: false })
        }
      }

      if (opts.dueDate) {
        try {
          element.fields.DueDate = await getLearningObjectDueDate(element.fields.ID!, userID)
        } catch (error) {
          const errorMessage = getErrorMessage(error)
          if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get due date for users assigned content', { id: element.fields.ID, errorMessage, success: false })
            throw error
          }
        }
      }
    }
  }))
}
