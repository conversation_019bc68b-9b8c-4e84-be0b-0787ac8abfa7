import settings from '../../config/settings.js'
import PDFDocument from 'pdfkit'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename)

export default async function (userName: string, completionName: string, completedOn: Date): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      const doc = new PDFDocument({ size: 'LETTER', layout: 'landscape', margin: 0 })

      const chunks: any[] = []
      doc.on('data', chunk => chunks.push(chunk))
      doc.on('end', () => {
        const buffer = Buffer.concat(chunks)
        resolve(buffer.toString('base64'))
      })

      doc.image(settings.certificate_background, 0, 0).save()

      // learner name
      const usernameFontSize = fittedSize(userName, 48, 30, 0.5, { width: 510, heigth: 48 })
      doc.font(path.join(__dirname, '../../fonts/Roboto-Bold.ttf'), usernameFontSize)
        .text(userName, 95, 280 + (48 - usernameFontSize), {
          align: 'center',
          width: 510
        })
        .save()

      // content title
      const contentFontSize = fittedSize(completionName, 25, 18, 0.5, { width: 510, heigth: 25 })
      doc.font(path.join(__dirname, '../../fonts/Roboto-Italic.ttf'), contentFontSize)
        .text(completionName, 95, 365, {
          align: 'center',
          width: 510
        })
        .save()

      // completion date
      doc.font(path.join(__dirname, '../../fonts/Roboto-Bold.ttf'), 14)
        .fill('#FFF')
        .text(`Completed On:\n${completedOn.toLocaleDateString()}`, 620, 565, {
          align: 'right',
          width: 150
        })
        .save()

      doc.end()
    } catch (error) {
      reject(error)
    }
  })
}

function measureHeight (text: string, fontSize: number, width: number): number {
  const temp = new PDFDocument()
  temp.fontSize(fontSize)
  temp.x = 0
  temp.y = 0
  temp.text(text, { width })
  return temp.y
}

function fittedSize (text: string, fontSize: number, min: number, step: number, bounds: { width: number, heigth: number }): number {
  if (fontSize <= min) return min
  const height = measureHeight(text, fontSize, bounds.width)
  if (height <= bounds.heigth) return fontSize
  return fittedSize(text, fontSize - step, min, step, { width: bounds.width, heigth: bounds.heigth - step })
}
