import getTopRated from '../../../services/mssql/learning-objects/get-top-rated.service.js'
import getTopViewed from '../../../services/mssql/learning-objects/get-top-viewed.service.js'
import getContentUploads from '../../../services/mssql/learning-objects/get-uploads-by-type.service.js'
import getAssignments from '../../../services/mssql/assignments/get-count-by-date-range.service.js'
import getObjectViewsByType from '../../../services/mssql/learning-object-views/get-views-by-type.service.js'
import getContextViewsByType from '../../../services/mssql/learning-context-views/get-views-by-type.service.js'
import getKeywordViewCounts from '../../../services/mssql/keywords/get-view-count.service.js'
import getLearningObjects from '../../../services/mssql/learning-objects/get-multiple.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getCountOfAllUsers from '../../../services/mssql/users/get-count-of-all-users.service.js'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer } from '@tess-f/backend-utils'
import { z } from 'zod'
const { INTERNAL_SERVER_ERROR } = httpStatus

// Logging tools
const log = logger.create('Controller-HTTP.get-admin-overview-dashboard', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, as an administrator go to the "Admin Overview" dashboard. Note the time. Check the LMS API log for the 'http-get-admin-overview-dashboard' label."

  // All other data retrieval is logged by the called functions.

  try {
    const filterDates = z.object({
      from: z.coerce.date().optional(),
      to: z.coerce.date().optional()
    }).safeParse(req.query)

    let from: Date | undefined
    let to: Date | undefined
    
    if (filterDates.success) {
      from = filterDates.data.from
      to = filterDates.data.to
    }

    // get the users
    const TotalUsers = await getCountOfAllUsers()

    // top rated
    const topRated = await getTopRated(5, from, to)
    topRated.sort((a, b) => {
      if (a.fields.Rating === b.fields.Rating) {
        return 0
      } else if ((a.fields.Rating ?? 0) > (b.fields.Rating ?? 0)) {
        return -1
      } else {
        return 1
      }
    })

    // top viewed
    const topViewed = await getTopViewed(5, from, to)
    topViewed.sort((a, b) => {
      if (a.fields.Views === b.fields.Views) {
        return 0
      } else if ((a.fields.Views ?? 0) > (b.fields.Views ?? 0)) {
        return -1
      } else {
        return 1
      }
    })

    let uploads, viewsByType: Array<{Views: number, LearningObjectTypeID: number} | {Views: number, Label: string}>, viewsByKeyword, allLearningObjects

    try {
      uploads = await getContentUploads(from, to)
      // STIG V-69425 data access (success)
      log('info', 'Successfully retrieved content uploads.', { success: true, req })
    } catch (error) {
      // STIG V-69425 data access (failure)
      const errorMessage = getErrorMessage(error)
      log('error', 'Failed to retrieve content uploads.', { errorMessage, success: false, req })
      throw error
    }

    let assignmentsCount = 0
    try {
      assignmentsCount = await getAssignments(from, to)
      // STIG V-69425 data access (success)
      log('info', 'Successfully retrieved assignment count.', { success: true, req })
    } catch (error) {
      // STIG V-69425 data access (failure)
      const errorMessage = getErrorMessage(error)
      log('error', 'Failed to retrieve assignment count.', { errorMessage, success: false, req })
      throw error
    }

    try {
      viewsByType = await getObjectViewsByType(from, to)
      const contextViewsByType = await getContextViewsByType(from, to)
      viewsByType = viewsByType.concat(contextViewsByType)
      // STIG V-69425 data access (success)
      log('info', 'Successfully retrieved object views by type.', { success: true, req })
    } catch (error) {
      // STIG V-69425 data access (failure)
      const errorMessage = getErrorMessage(error)
      log('error', 'Failed to retrieve object views by type.', { errorMessage, success: false, req })
      throw error
    }

    try {
      viewsByKeyword = await getKeywordViewCounts(from, to)
      // sort the results
      viewsByKeyword.sort((a, b) => a.Name.localeCompare(b.Name))
      // STIG V-69425 data access (success)
      log('info', 'Successfully retrieved object views by keyword.', { success: true, req })
    } catch (error) {
      // STIG V-69425 data access (failure)
      const errorMessage = getErrorMessage(error)
      log('error', 'Failed to retrieve object view by keyword.', { errorMessage, success: false, req })
      throw error
    }

    try {
      allLearningObjects = await getLearningObjects()
      if (to && from) {
        allLearningObjects = allLearningObjects.filter(obj => {
          if (obj.fields.CreatedOn! >= from && obj.fields.CreatedOn! <= to) {
            return true
          }
          return false
        })
      }
      // STIG V-69425 data access (success)
      log('info', 'Successfully retrieved learning objects.', { success: true, req })
    } catch (error) {
      // STIG V-69425 data access (failure)
      const errorMessage = getErrorMessage(error)
      log('error', 'Failed to retrieve learning objects.', { errorMessage, success: false, req })
      throw error
    }

    // STIG V-69425 data access (success)
    log('info', 'Successfully retrieved admin overview dashboard data.', { success: true, req })

    res.json({
      SystemOverview: {
        TotalUsers,
        UploadedContent: allLearningObjects.length,
        Assignments: assignmentsCount,
        Views: 0
      },
      TopRated: topRated.map(record => record.fields),
      TopViewed: topViewed.map(record => record.fields),
      UploadsByView: uploads,
      LearningObjectViewsByType: viewsByType,
      LearningObjectViewsByKeyword: viewsByKeyword
    })
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    log('error', 'Failed to retrieve admin overview dashboard data.', { errorMessage, success: false, req })
    res.sendStatus(INTERNAL_SERVER_ERROR)
  }
}
