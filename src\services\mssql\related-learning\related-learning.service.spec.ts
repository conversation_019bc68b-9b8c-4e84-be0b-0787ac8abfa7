import { expect } from 'chai'
import mssqlConnection from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import get from './get.service.js'

const objectID = 'EAF1DA0B-9B3D-4709-8830-D7219F925AD1'

describe('MSSQL Related Learning', () => {
  before('Connect to SQL', async () => {
    await mssqlConnection.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
  })

  it('gets related learning', async () => {
    const result = await get(objectID)
    expect(result.learningContexts.length).to.gte(0)
    expect(result.learningObjects.length).to.gte(0)
  })
})
