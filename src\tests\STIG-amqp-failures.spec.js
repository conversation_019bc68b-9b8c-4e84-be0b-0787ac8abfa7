const { expect } = require('chai')
const logger = require('../api/../shared_libraries/libs/logger')
const settings = require('../api/config/settings')

const claimsCreate = require('../api/controllers/amqp/claims/create')
const claimsDelete = require('../api/controllers/amqp/claims/delete')
const claimsGetMulti = require('../api/controllers/amqp/claims/get-multi')

const coursePrereqCreate = require('../api/controllers/amqp/course-prerequisite/create')
const coursePrereqDelete = require('../api/controllers/amqp/course-prerequisite/delete')
const coursePrereqGetIndices = require('../api/controllers/amqp/course-prerequisite/get-indices')
const coursePrereqGet = require('../api/controllers/amqp/course-prerequisite/get')

const eventCaptureUpdateLearnerProgress = require('../api/controllers/amqp/event-capture/update-learner-progress')

const keywordsCreate = require('../api/controllers/amqp/keywords/create')
const keywordsDelete = require('../api/controllers/amqp/keywords/delete')
const keywordsGetMulti = require('../api/controllers/amqp/keywords/get-multi')

const learnerProgCreate = require('../api/controllers/amqp/learner-progress/create')
const learnerProgDelete = require('../api/controllers/amqp/learner-progress/delete')
const learnerProgGetIndices = require('../api/controllers/amqp/learner-progress/get-indices')
const learnerProgGet = require('../api/controllers/amqp/learner-progress/get')

const learningContextRatingsCreate = require('../api/controllers/amqp/learning-context-ratings/create')
const learningContextRatingsDelete = require('../api/controllers/amqp/learning-context-ratings/delete')
const learningContextRatingsGetIndices = require('../api/controllers/amqp/learning-context-ratings/get-indices')
const learningContextRatingsGet = require('../api/controllers/amqp/learning-context-ratings/get')

const learningContextsCreate = require('../api/controllers/amqp/learning-contexts/create')
const learningContextsDelete = require('../api/controllers/amqp/learning-contexts/delete')
const learningContextsGetIndices = require('../api/controllers/amqp/learning-contexts/get-indices')
const learningContextsGet = require('../api/controllers/amqp/learning-contexts/get')

const learningObjectsCreate = require('../api/controllers/amqp/learning-objects/create')
const learningObjectsDelete = require('../api/controllers/amqp/learning-objects/delete')
const learningObjectsGetIndices = require('../api/controllers/amqp/learning-objects/get-indices')
const learningObjectsGet = require('../api/controllers/amqp/learning-objects/get')

const userClaimsCreate = require('../api/controllers/amqp/user-claims/create')
const userClaimsDelete = require('../api/controllers/amqp/user-claims/delete')
const userClaimsGetMulti = require('../api/controllers/amqp/user-claims/get-multi')

const nonExistingID = '00000000-0000-0000-0000-000000000000'

describe('STIG logging for amqp failure conditions', function () {
  before(async () => {
    await logger.init({
      name: 'test',
      level: 'silly',
      use_console: true,
      use_elasticsearch: false
      // use_elasticsearch: true,
      // elasticsearch_host: 'http://************:3389'
    })
  })

  // ---------- claims ----------

  it('should log a failed amqp-create-claims', async () => {
    const res = await claimsCreate({ claim: { UserID: nonExistingID } })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-delete-claims', async () => {
    const res = await claimsDelete({ claim: undefined })
    expect(res.success).to.equal(false)
  })

  // ---------- course-prerequisite ----------

  it('should log a failed amqp-create-course-prereq', async () => {
    const res = await coursePrereqCreate({ coursePrerequisite: undefined })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-get-course-prereq', async () => {
    const res = await coursePrereqGet({ ID: nonExistingID })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-delete-course-prereq', async () => {
    const res = await coursePrereqDelete({ id: undefined })
    expect(res.success).to.equal(false)
  })

  // ---------- keywords ----------

  it('should log a failed amqp-create-keyword', async () => {
    const res = await keywordsCreate({ keyword: undefined })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-delete-keyword', async () => {
    const res = await keywordsDelete({ keywordName: nonExistingID })
    expect(res.success).to.equal(false)
  })

  // ---------- learner-progress ----------

  it('should log a failed amqp-create-learner-progress', async () => {
    const res = await learnerProgCreate({ learnerProgress: undefined })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-delete-learner-progress', async () => {
    const res = await learnerProgDelete({ id: undefined })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-get-learner-progress', async () => {
    const res = await learnerProgGet({ userID: undefined, objectID: undefined })
    expect(res.success).to.equal(false)
  })

  // ---------- learning-context-ratings ----------

  it('should log a failed amqp-create-learning-context-rating', async () => {
    const res = await learningContextRatingsCreate({ learningContextRating: undefined })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-delete-learning-context-rating', async () => {
    const res = await learningContextRatingsDelete({ id: undefined })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-get-learning-context-rating', async () => {
    const res = await learningContextRatingsGet({ ID: undefined })
    expect(res.success).to.equal(false)
  })

  // ---------- learning-contexts ----------

  it('should log a failed amqp-create-learning-contexts', async () => {
    const res = await learningContextsCreate({ learningContext: undefined })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-delete-learning-contexts', async () => {
    const res = await learningContextsDelete({ id: undefined })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-get-learning-context', async () => {
    const res = await learningContextsGet({ ID: nonExistingID })
    expect(res.success).to.equal(false)
  })

  // ---------- learning-objects ----------

  it('should log a failed amqp-create-learning-object', async () => {
    const res = await learningObjectsCreate({ learningObject: undefined })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-delete-learning-object', async () => {
    const res = await learningObjectsDelete({ id: undefined })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-get-learning-object', async () => {
    const res = await learningObjectsGet({ ID: nonExistingID })
    expect(res.success).to.equal(false)
  })

  // ---------- user-claims ----------

  it('should log a failed amqp-create-user-claims', async () => {
    const res = await userClaimsCreate({ claim: undefined })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-delete-user-claim', async () => {
    const res = await userClaimsDelete({ id: undefined })
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-get-multi-user-claims', async () => {
    const res = await userClaimsGetMulti()
    expect(res.success).to.equal(false)
  })

  // //////////////////////////////////////////////////
  // // Forced catastrophic failures below:

  it('should log a failed amqp-get-multi-claims', async () => {
    // Force a catastrophic failure
    settings.db.password = 'notThePassword'

    const res = await claimsGetMulti()
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-get-course-prereq-indices', async () => {
    const res = await coursePrereqGetIndices()
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-get-multi-keyword', async () => {
    const res = await keywordsGetMulti()
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-get-learner-progress-indices', async () => {
    const res = await learnerProgGetIndices()
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-get-learning-context-ratings-indices', async () => {
    const res = await learningContextRatingsGetIndices()
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-get-learning-context-indices', async () => {
    const res = await learningContextsGetIndices()
    expect(res.success).to.equal(false)
  })

  it('should log a failed amqp-get-learning-objects-indices', async () => {
    const res = await learningObjectsGetIndices()
    expect(res.success).to.equal(false)
  })

  after(done => {
    logger.close()
    done()
  })
})
