import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningObject from '../../../models/learning-object.model.js'

describe('HTTP create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-objects/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObject({ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                Title: 'test',
                Description: 'test',
                LearningObjectTypeID: 1,
                MinutesToComplete: 0,
                VisibilityID: 1,
                EnableCertificates: false,
                URL: 'test',
                LearningObjectSubTypeID: 1,
                SystemId: 'test',
                Keywords: ['test'],
                ObjectiveIds: [uuid()]
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })


    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-objects/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObject({ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                Title: false,
                Description: false,
                LearningObjectTypeID: false,
                MinutesToComplete: false,
                VisibilityID: false,
                EnableCertificates: false,
                URL: false,
                LearningObjectSubTypeID: false,
                SystemId: false,
                Keywords: false,
                ObjectiveIds: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('Expected number, received boolean')
        expect(mocks.res._getData()).include('Title')
        expect(mocks.res._getData()).include('Description')
        expect(mocks.res._getData()).include('LearningObjectTypeID')
        expect(mocks.res._getData()).include('MinutesToComplete')
        expect(mocks.res._getData()).include('VisibilityID')
        expect(mocks.res._getData()).include('URL')
        expect(mocks.res._getData()).include('LearningObjectSubTypeID')
        expect(mocks.res._getData()).include('SystemId')
        expect(mocks.res._getData()).include('Keywords')
        expect(mocks.res._getData()).include('ObjectiveIds')

    })
   

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/learning-objects/create.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new LearningObject({ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                Title: 'test',
                Description: 'test',
                LearningObjectTypeID: 1,
                MinutesToComplete: 0,
                VisibilityID: 1,
                EnableCertificates: false,
                URL: 'test',
                LearningObjectSubTypeID: 1,
                SystemId: 'test',
                Keywords: ['test'],
                ObjectiveIds: [uuid()]
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })

})