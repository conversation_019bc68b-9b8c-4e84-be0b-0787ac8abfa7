import mssql from '@lcs/mssql-utility'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import { LearningContextSession, LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { SessionEnrollmentFields, SessionEnrollmentsTableName } from '@tess-f/sql-tables/dist/lms/session-enrollment.js'

/**
 * Returns all sessions for of a learning context that a user is enrolled
 * where the given exam is used to mark the session complete
 * Does not return sessions that have already been completed
 * @param userID the id of the user to get sessions for
 * @param examID the id of the exam that is used to mark the session as completed
 */
export default async function getLearningContextSessionsForUserAndExam (userID: string, examID: string): Promise<LearningContextSessionModel[]> {
  const request = mssql.getPool().request()
  request.input('userID', userID)
  request.input('examID', examID)

  const results = await request.query<LearningContextSession>(`
    SELECT [${LearningContextSessionsTableName}].*
    FROM [${LearningContextSessionsTableName}]
      JOIN [${LearningContextTableName}] ON [${LearningContextSessionsTableName}].[${LearningContextSessionFields.LearningContextID}] = [${LearningContextTableName}].[${LearningContextFields.ID}]
        AND [${LearningContextFields.ExamID}] = @examID
      JOIN [${SessionEnrollmentsTableName}] ON [${SessionEnrollmentsTableName}].[${SessionEnrollmentFields.SessionID}] = [${LearningContextSessionsTableName}].[${LearningContextSessionFields.ID}]
        AND [${SessionEnrollmentFields.UserID}] = @userID AND [${SessionEnrollmentFields.Waitlisted}] = 0
    WHERE [${LearningContextSessionsTableName}].[${LearningContextSessionFields.ID}] NOT IN (
      SELECT [${LearnerProgressFields.LearningContextSessionID}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.UserID}] = @userID
      AND [${LearnerProgressFields.LearningContextSessionID}] IS NOT NULL
    )
  `)

  return results.recordset.map(record => new LearningContextSessionModel(undefined, record))
}
