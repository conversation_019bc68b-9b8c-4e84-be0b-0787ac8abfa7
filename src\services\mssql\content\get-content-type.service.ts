import mssql, { DB_Errors } from '@lcs/mssql-utility'
import { LearningElementsViewFields, LearningElementsViewName } from '@tess-f/sql-tables/dist/lms/learning-elements-view.js'

export default async function getContentType (contentId: string): Promise<'Learning Context' | 'Learning Object'> {
  const request = mssql.getPool().request()
  request.input('contentID', contentId)

  const result = await request.query<{ Type: 'Learning Context' | 'Learning Object' }>(`
    SELECT TOP 1 [${LearningElementsViewFields.EntityType}] AS [Type]
    FROM [${LearningElementsViewName}]
    WHERE [${LearningElementsViewFields.ID}] = @contentID
  `)

  if (result.recordset.length !== 1) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }

  return result.recordset[0].Type
}
