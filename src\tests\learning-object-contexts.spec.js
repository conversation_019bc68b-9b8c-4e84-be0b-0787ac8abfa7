const chai = require('chai');
const expect = chai.expect;
const uuidv4 = require('uuid/v4');
const tester = require('../api/utils/test-agent.utils');
const settings = require('../api/config/settings');

const userID = uuidv4();
const contextID = "BF003B1F-CC06-46D5-A948-D3D5C44A03D4"; // Course 4
const objectID =  "FA9C921C-67AC-4700-A6C2-452D4F962483"; // Learning Object 4

let isolated = false;

describe("E2E: Learning Object Contexts", function() {

    this.timeout(30000);
    
    before( done => {
        if(tester.agent == null) {
            isolated = true;
            tester.create()
            .then( agent => {
                done();
            })
            .catch( err => {
                console.error(err);
                done();
            });
        } else {
            done();
        }
    });

    it("creates a learning object context", done => {
        tester.agent
        .post(settings.server.root + 'object-context')
        .send({
            LearningObjectID: objectID,
            LearningContextID: contextID
        })
        .end((err, res) => {
            expect( res.statusCode ).to.equal( 200 );
            done();
        });

    });

    it("updates a learning object context", done => {
        tester.agent
        .put(settings.server.root + 'object-context/' + contextID + '/' + objectID )
        .send({ OrderID: 2 })
        .end((err, res) => {
            expect( res.statusCode ).to.equal( 200 );
            expect( res.body.OrderID ).to.equal( 2 );
            done();
        });
    })

    it("deletes a learning object context", done => {
        tester.agent
        .delete(settings.server.root + 'object-context/' + contextID + '/' + objectID )
        .end((err, res) => {
            expect( res.statusCode ).to.equal( 204 );
            done();
        });

    });

    after((done)=>{
        if(isolated) {
            tester.close();
        } 
        done();
    });

})