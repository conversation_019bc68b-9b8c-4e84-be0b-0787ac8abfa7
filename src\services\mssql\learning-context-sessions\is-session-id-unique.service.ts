import mssql, { DB_Errors, getRows } from '@lcs/mssql-utility'
import { LearningContextSession, LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'

export default async function (sessionID: string, id?: string): Promise<boolean> {
  try {
    const pool = mssql.getPool()
    const request = pool.request()
    let recordCount = 0

    if (id) {
      const query = `
        SELECT * 
        FROM [${LearningContextSessionsTableName}] 
        WHERE [${LearningContextSessionFields.ID}] != @GUID 
        AND [${LearningContextSessionFields.SessionID}] = @sessionID
      `
      request.input('GUID', id)
      request.input('sessionID', sessionID)
      const sessions = await request.query(query)
      recordCount = sessions.recordset.length
    } else {
      const sessions = await getRows<LearningContextSession>(LearningContextSessionsTableName, request, { SessionID: sessionID })
      recordCount = sessions.length
    }

    return recordCount === 0
  } catch (error) {
    if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
      return true
    } else {
      throw error
    }
  }
}
