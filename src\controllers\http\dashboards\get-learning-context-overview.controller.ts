import logger from '@lcs/logger'
import { Request, Response } from 'express'
import getService from '../../../services/mssql/learning-context/get.service.js'
import getContextViewCount from '../../../services/mssql/learning-context-views/get-counts.service.js'
import { getContextCompletionCounts, getContextInProgressCount } from '../../../services/mssql/learning-context/get-completion-counts.service.js'
import getViewHistoryService from '../../../services/mssql/learning-context-views/get-view-history.service.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-learning-context-overview', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)

    const context = await getService(id, undefined, { rating: true })

    let from: Date | undefined, to: Date | undefined
    const filterDates = z.object({
      from: z.coerce.date().optional(),
      to: z.coerce.date().optional()
    }).safeParse(req.query)

    if (filterDates.success) {
      from = filterDates.data.from
      to = filterDates.data.to
    }

    // we need to get specific view counts with filtered dates
    context.fields.Views = await getContextViewCount(context.fields.ID!, from, to)
    context.fields.Completions = await getContextCompletionCounts(context.fields.ID!, from, to)
    context.fields.InProgress = await getContextInProgressCount(context.fields.ID!, from, to)
    const ViewHistory = await getViewHistoryService(context.fields.ID!, from, to)

    log('info', 'Successfully retrieved learning context overview data.', { req, rating: context.fields.Rating, views: context.fields.Views, completions: context.fields.Completions, inProgress: context.fields.InProgress, viewHistoryCount: ViewHistory.length, success: true })
    res.json({
      element: context.fields,
      ViewHistory
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get learning context overview: validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to get learning context overview', { id: req.params.id, success: false, error, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
