import logger from '@lcs/logger'
import Sinon from 'sinon'
import LearningContextModel from '../../../models/learning-context.model.js'
import { expect } from 'chai'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('Controller.AMQP: get learning context', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns the requested learning context', async () => {
    const controller = await esmock('./get.js', {
      '../../../services/mssql/learning-context/get.service.js': {
        default: Sinon.stub().returns(Promise.resolve(new LearningContextModel({
          ID: 'Test',
          Title: 'Test',
          Label: 'Test'
        })))
      }
    })
    
    const response = await controller({ command: 'get', data: { ID: uuid() } })
    expect(response.success).to.be.true
    expect(response.data?.ID).to.equal('Test')
  })

  it('returns unsuccessful when the service encounters an error', async () => {
    const controller = await esmock('./get.js', {
      '../../../services/mssql/learning-context/get.service.js': {
        default: Sinon.stub().returns(Promise.reject(new Error('Service Failure')))
      }
    })
    const response = await controller({ command: 'get', data: { ID: uuid() } })
    expect(response.success).to.be.false
    expect(response.message).to.equal('Service Failure')
  })

  it('returns an error when the ID param is missing from the request data', async () => {
    const controller = await esmock('./get.js')
    const response = await controller({ command: 'get', data: { } })
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('ID: Required')
  })

  it('returns an error when the ID param is not a uuid', async () => {
    const controller = await esmock('./get.js')
    const response = await controller({ command: 'get', data: { ID: 'test' } })
    expect(response.success).to.be.false
    expect(response.message).to.include('Invalid request data:')
    expect(response.message).to.include('ID: Invalid')
  })
})
