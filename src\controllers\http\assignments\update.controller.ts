import logger from '@lcs/logger'
import Assignment, { updateAssignmentSchema } from '../../../models/assignment.model.js'
import AssignmentUser from '../../../models/assignment-users.model.js'
import update from '../../../services/mssql/assignments/update.service.js'
import { Request, Response } from 'express'
import getAllUserIDs from '../../../services/mssql/users/get-all-user-ids.service.js'
import getIDsOfMyDirectReports from '../../../services/mssql/users/get-ids-of-my-direct-reports.service.js'
import httpStatus from 'http-status'
import sendAssignmentNotification from '../../../services/email/send-assignment-notification.service.js'
import { mapUsersFromAssignment } from '../../../utils/assignment-utils.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.update-assignment', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    let users: AssignmentUser[] = []
    const groupUserIDs: string[] = []

    const assignment = new Assignment(updateAssignmentSchema.parse(req.body))

    // Set the modified by and on fields
    assignment.fields.ID = (z.object({ id: zodGUID })).parse(req.params).id
    assignment.fields.ModifiedBy = req.session.userId
    assignment.fields.ModifiedOn = new Date()
    // set the deleted date to null since the user is updating this assignment (adding users, changing due date, etc)
    // if the user didn't change enough to keep it from deleting it will just get marked for deletion again on the next cron update
    assignment.fields.DeletedOn = null
    assignment.fields.StashNotificationSent = false

    if (assignment.fields.Everyone) {
      const userIDs = await getAllUserIDs()
      users = userIDs.map(id => new AssignmentUser({
        AssignmentID: assignment.fields.ID,
        UserID: id,
        GroupID: null
      }))
    } else if (assignment.fields.DirectReports) {
      const userIDs = await getIDsOfMyDirectReports(assignment.fields.CreatedBy!)
      users = userIDs.map(id => new AssignmentUser({
        AssignmentID: assignment.fields.ID,
        UserID: id,
        GroupID: null
      }))
    } else {
      await mapUsersFromAssignment(assignment, users, groupUserIDs)
      users.forEach(user => { user.fields.AssignmentID = assignment.fields.ID })
    }

    const result = await update(assignment, users, groupUserIDs)

    log('info', 'Successfully updated assignment', { assignmentId: result.assignment.fields.ID, success: true, req })

    // send the update to the caller
    res.json(result.assignment.fields)

    // don't attempt to send notification if there is no one to send it to
    if (result.addedUserIds.length <= 0) { return }
    // now we need to notify the added users of their new assignment
    try {
      await sendAssignmentNotification(result.assignment, result.addedUserIds)
    } catch {
      // just log the failure the UI already got a success response
      log('warn', 'Failed to send assignment notification email', { success: false, req })
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update assignment: validation error', { error, req, success: false })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to update assignment.', { error, req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
