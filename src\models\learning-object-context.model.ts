import { Table } from '@lcs/mssql-utility'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { LearningObjectContext, LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { z } from 'zod'

export const createLearningObjectContextSchema = z.object({
  [LearningObjectContextFields.LearningObjectID]: zodGUID,
  [LearningObjectContextFields.LearningContextID]: zodGUID,
  [LearningObjectContextFields.OrderID]: z.number().int()
})

export default class LearningObjectContextModel extends Table<LearningObjectContext, LearningObjectContext> {
  public fields: LearningObjectContext

  constructor (fields?: LearningObjectContext) {
    super(LearningObjectContextsTableName, [
      LearningObjectContextFields.LearningObjectID,
      LearningObjectContextFields.LearningContextID,
      LearningObjectContextFields.CreatedBy,
      LearningObjectContextFields.CreatedOn,
      LearningObjectContextFields.OrderID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearningObjectContext): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningObjectContext {
    return this.fields
  }
}
