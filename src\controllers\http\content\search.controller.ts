import logger from '@lcs/logger'
import searchContent from '../../../services/mssql/content/search.service.js'
import { Request, Response } from 'express'
import learningElementExtrasMapper from '../../../mappers/learning-element-extras.mapper.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'
const { INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.search-content', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { offset, limit, search, filters, sortColumn, sortDirection } = z.object({
      offset: zodOffset,
      limit: zodLimit,
      search: z.string().optional(),
      filters: z.object({
        labels: z.array(z.string()).optional(),
        objectTypeIds: z.array(z.nativeEnum(LearningObjectTypes)).optional(),
        keywords: z.array(z.string()).optional(),
        myLearning: z.enum(['enrolled', 'favorites', 'my plan', 'in progress', 'assigned', 'completed']).optional(),
        userId: z.string().optional(),
      }).optional().default({}),
      sortColumn: z.string().optional(),
      sortDirection: z.enum(['ASC', 'DESC']).optional(),
    }).parse(req.body)

    const forUser = zodGUID.optional().safeParse(req.body)
    if (forUser.success) {
      filters.userId = forUser.data
    } else {
      filters.userId = req.session.userId
    }

    const opts = z.object({
      rating: z.boolean().optional().default(false),
      bookmark: z.boolean().optional().default(false),
      favorite: z.boolean().optional().default(false),
      duration: z.boolean().optional().default(false),
      completion: z.boolean().optional().default(false),
      dueDate: z.boolean().optional().default(false),
      enrolledSessions: z.boolean().optional().default(false)
    }).parse(req.body.mappers ?? {})
    const content = await searchContent(offset, limit, search, { ...filters, userID: filters.userId }, true, sortColumn, sortDirection)

    if (filters?.myLearning === 'assigned') {
      opts.dueDate = true
    } else if (filters?.myLearning === 'enrolled') {
      opts.enrolledSessions = true
    }

    await learningElementExtrasMapper(content.elements, filters.userId!, opts)

    log('info', 'Successfully retrieved learning elements', { count: content.elements.length, totalRecords: content.totalRecords, success: true, req })
    res.json({
      totalRecords: content.totalRecords,
      elements: content.elements.map(element => element.fields)
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to search content: input validation error', { error, req, success: false })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data:'))
    } else {
      log('error', 'Failed to search content', { error, req, success: false })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
