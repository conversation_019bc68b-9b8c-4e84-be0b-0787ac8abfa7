import del from '../../../services/mssql/keywords/delete.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'
const { INTERNAL_SERVER_ERROR, NOT_FOUND, NO_CONTENT, BAD_REQUEST } = httpStatus

const log = logger.create('Controller-HTTP.delete-keyword', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-delete-keyword' label."

  try {
    const { name } = z.object({ name: z.string().max(50) }).parse(req.params)
    const numRowsAffected = await del(name)

    if (numRowsAffected > 0) {
      // STIG V-69427 changing data (success)
      // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-delete-keyword' label and message indicating successful deletion."
      log('info', 'Successfully deleted keyword', { success: true, req })

      res.sendStatus(NO_CONTENT)
    } else {
      // STIG V-69427 changing data (failure)
      log('warn', 'Failed to delete keyword because it was not found in the database.', { success: false, req })

      res.sendStatus(NOT_FOUND)
    }
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete keyword: input validation error', { success: false, req, error })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid keyword parameter: '))
    }
    else{
    log('error', 'Failed to delete keyword.', { error, req, success: false })
    res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
