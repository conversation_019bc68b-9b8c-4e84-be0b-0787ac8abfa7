import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import Sinon from 'sinon'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
import { AssignmentTypes } from '@tess-f/sql-tables/dist/lms/assignment-type'
import { v4 as uuid } from 'uuid'
import AssignmentsModel from '../../../models/assignment.model'

describe('HTTP-Controller: create assignment', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns a bad request status code when body is missing', async () => {
    const mocks = httpMocks.createMocks()
    const create = await esmock('./create.controller.js')
    await create(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('Required')
  })

  it('returns a bad request status code when body is missing required fields', async () => {
    const mocks = httpMocks.createMocks({ body: { EmailMessage: 'Test' }})
    const create = await esmock('./create.controller.js')
    await create(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('Title: Required')
    expect(data).to.include('TypeID: Required')
  })

  it('returns a bad request status code when no assignees are defined', async () => {
    const mocks = httpMocks.createMocks({ body: {
      TypeID: AssignmentTypes.Manual,
      Everyone: false,
      Title: 'Bad Test Assignment: no assignees'
    }})
    const create = await esmock('./create.controller.js')
    await create(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('No assignees defined')
  })

  it('returns a bad request status code when no assignment content is defined', async () => {
    const mocks = httpMocks.createMocks({
      body: {
        TypeID: AssignmentTypes.Manual,
        Everyone: true,
        Title: 'Bad Test Assignment: no content'
      },
      session: { userId: uuid() }
    })
    const create = await esmock('./create.controller.js')
    await create(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('No assignment content defined')
  })

  it('can create an assignment when data is valid', async () => {
    const mocks = httpMocks.createMocks({
      body: {
        TypeID: AssignmentTypes.Manual,
        Everyone: true,
        Title: 'Test Assignment',
        LearningContexts: [{ ID: uuid(), OrderID: 1 }]
      },
      session: { userId: uuid() }
    })
    const create = await esmock('./create.controller.js', {
      '../../../services/mssql/users/get-all-user-ids.service.js': { default: Sinon.stub().returns(Promise.resolve([uuid()])) },
      '../../../services/mssql/assignments/create.service.js': { default: Sinon.stub().returns(Promise.resolve({ assignment: new AssignmentsModel() })) },
      '../../../services/email/send-assignment-notification.service.js': { default: Sinon.stub().returns(Promise.resolve()) }
    })
    await create(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = JSON.parse(mocks.res._getData())
    expect(data).to.not.be.undefined
  })
})
