import mssql from '@lcs/mssql-utility'
import AssignmentCountsHistoryModel from '../../../models/assignment-counts-history.model.js'
import { AssignmentCountsHistory, AssignmentCountsHistoryFields, AssignmentCountsHistoryTableName } from '@tess-f/sql-tables/dist/lms/assignment-counts-history.js'

export default async function (from?: Date, to?: Date): Promise<AssignmentCountsHistoryModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  const query = `
    SELECT *
    FROM [${AssignmentCountsHistoryTableName}]
    WHERE [${AssignmentCountsHistoryFields.CreatedOn}] BETWEEN ${from && to ? '@from AND @to' : 'DATEADD(day, -30, GETDATE()) AND GETDATE()'}
    ORDER BY [${AssignmentCountsHistoryFields.CreatedOn}] ASC
  `

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const results = await request.query<AssignmentCountsHistory>(query)

  return results.recordset.map(record => new AssignmentCountsHistoryModel(record))
}
