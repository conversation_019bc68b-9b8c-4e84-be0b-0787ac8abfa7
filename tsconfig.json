{"compilerOptions": {"allowImportingTsExtensions": true, "experimentalDecorators": true, "target": "ESNext", "module": "NodeNext", "moduleResolution": "NodeNext", "outDir": "./build", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["types", "node_modules/@types"]}, "exclude": ["src/**/*.spec.ts"], "ts-node": {"files": true, "esm": true}}