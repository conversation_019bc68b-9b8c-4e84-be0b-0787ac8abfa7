import type { Request, Response, NextFunction } from 'express'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
import checkUserHasTeam from '../../../../services/mssql/users/check-has-team.service.js'
import { httpLogTransformer } from '@tess-f/backend-utils'

const log = logger.create('Middleware-HTTP.check-has-team', httpLogTransformer)

export default async function (req: Request, res: Response, next: NextFunction) {
    try {
        if (await checkUserHasTeam(req.session.userId)) {
            next()
        } else {
            log('warn', 'User does not have a team', { userID: req.session.userId })
            res.sendStatus(httpStatus.FORBIDDEN)
        }
    } catch (error) {
        log('error', 'Failed to check if user has a team', { userID: req.session.userId })
        res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
}
