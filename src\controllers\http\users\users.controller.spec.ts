import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import { startServices, shutdownServices } from '../../../utils/testing.utils.js'

import getContextCreators from './get-context-creators.controller.js'
import getContextModifiers from './get-context-modifiers.controller.js'
import getObjectCreators from './get-object-creators.controller.js'
import getObjectModifiers from './get-object-modifiers.controller.js'

//TODO: Consider moving these tests


describe('Controller - HTTP: Users', () => {
  before(async () => await startServices())

  it('gets a list of users who have created learning contexts', async () => {
    const mocks = httpMocks.createMocks()

    await getContextCreators(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const body = JSON.parse(mocks.res._getData())
    expect(body).to.be.an('array')
    expect(body.length).to.be.gte(0)
  })

  it('gets a list of users who have modified learning contexts', async () => {
    const mocks = httpMocks.createMocks()

    await getContextModifiers(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const body = JSON.parse(mocks.res._getData())
    expect(body).to.be.an('array')
    expect(body.length).to.be.gte(0)
  })

  it('gets a list of users who have created learning objects', async () => {
    const mocks = httpMocks.createMocks()

    await getObjectCreators(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const body = JSON.parse(mocks.res._getData())
    expect(body).to.be.an('array')
    expect(body.length).to.be.gte(0)
  })

  it('gets a list of users who have modified learning objects', async () => {
    const mocks = httpMocks.createMocks()

    await getObjectModifiers(mocks.req, mocks.res)

    expect(mocks.res._getStatusCode()).to.equal(200)
    const body = JSON.parse(mocks.res._getData())
    expect(body).to.be.an('array')
    expect(body.length).to.be.gte(0)
  })

  after(async () => await shutdownServices())
})
