import create from '../../../../services/mssql/claims/group/create.service.js'
import GroupClaim from '../../../../models/group-claim.model.js'
import errors from '../../../../config/errors.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.create-group-claims', httpLogTransformer)

export default async function (req: Request, res: Response) {  
  try {
    // Ensure body is an array of claims
    const { claims } = z.object({
      claims: z.array(z.string()).min(1)
    }).parse({ claims: req.body })
    const { id } = z.object({ id: zodGUID }).parse(req.params)

    const groupClaims = claims.map(claim => new GroupClaim({ GroupID: id, Claim: claim }))
    await create(groupClaims)

    log('info', 'Successfully granted permissions for group', { claims, groupId: id, success: true, req })

    res.json(groupClaims.map(claim => claim.fields))
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to create new group claim(s): input validation error', { success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else if (errorMessage === errors.INVALID_CLAIM) {
      log('warn', 'Failed to create new group claim(s) because invalid claim name(s) received: ', { claims: req.body, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send('Invalid claim name(s) received')
    } else {
      log('error', 'Failed to create group claim.', { errorMessage, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
