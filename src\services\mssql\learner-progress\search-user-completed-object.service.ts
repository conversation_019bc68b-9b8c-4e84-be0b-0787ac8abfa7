import mssql, { DB_Errors } from '@lcs/mssql-utility'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import { LearnerProgress, LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'

export default async function (userID: string, objectID: string, to?: Date): Promise<LearnerProgressModel> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('objectID', objectID)
  request.input('userID', userID)

  if (to) {
    request.input('to', to)
  }

  const results = await request.query<LearnerProgress>(`
    SELECT TOP (1) *
    FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.UserID}] = @userID
    AND [${LearnerProgressFields.LearningObjectID}] = @objectID
    AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.fail}
    ${to ? `AND [${LearnerProgressFields.CompletedDate}] <= @to` : ''}
    ORDER BY [${LearnerProgressFields.CompletedDate}] DESC
  `)

  if (results.recordset.length <= 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }

  return new LearnerProgressModel(results.recordset[0])
}
