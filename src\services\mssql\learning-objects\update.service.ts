import mssql, { updateRow, addRow } from '@lcs/mssql-utility'
import createActivity from '../activity-stream/create.service.js'
import ActivityStreamModel from '../../../models/activity-stream.model.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { ConnectionPool, Request } from 'mssql'
import LearningObjectKeywordModel from '../../../models/learning-object-keyword.model.js'
import { LearningObject } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningObjectKeyword, LearningObjectKeywordsTableName, LearningObjectKeywordFields } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import { Activities } from '@tess-f/sql-tables/dist/lms/activity.js'
import { LearningObjectJson } from '@tess-f/lms/dist/common/learning-object.js'
import getObjectiveIdsForLearningObject from '../learning-object-objectives/get-multi.service.js'
import createLearningObjectObjective from '../learning-object-objectives/create.service.js'
import LearningObjectObjectiveModel from '../../../models/learning-object-objective.model.js'
import removeLearningObjectObjective from '../learning-object-objectives/remove.service.js'
import updateLearningObjectObjective from '../learning-object-objectives/update.service.js'

export default async function (model: LearningObjectModel): Promise<LearningObjectModel> {
  const pool = mssql.getPool()

  // Delete and recreate the keywords
  await deleteObjectKeywords(pool.request(), model.fields.ID!)

  // update the row
  const record = await updateRow<LearningObject>(pool.request(), model, { ID: model.fields.ID })
  const updated = new LearningObjectModel(undefined, record[0])

  // Create new keywords
  if (model.fields.Keywords) {
    updated.fields.Keywords = await createKeywords(pool, model.fields.ID!, model.fields.Keywords)
  }

  // objectives
  updated.fields.ObjectiveIds = await updateObjectives(pool, model.fields)

  // create activity stream record
  const activity = new ActivityStreamModel({
    UserID: updated.fields.ModifiedBy ?? undefined,
    LinkText: updated.fields.Title,
    LinkID: updated.fields.ID,
    ActivityID: Activities.ModifiedLearningObject,
    CreatedOn: new Date()
  })
  await createActivity(activity)

  return updated
}

async function createKeywords (pool: ConnectionPool, objectID: string, keywords: string[]): Promise<string[]> {
  const output: string[] = []

  for (const word of keywords) {
    const keyword = new LearningObjectKeywordModel({
      Keyword: word,
      LearningObjectID: objectID
    })

    const record = await addRow<LearningObjectKeyword>(pool.request(), keyword)
    output.push(record.Keyword!)
  }

  return output
}

async function deleteObjectKeywords (request: Request, objectID: string): Promise<void> {
  request.input('LearningObjectID', objectID)

  const query = `
    DELETE FROM [${LearningObjectKeywordsTableName}]
    WHERE [${LearningObjectKeywordFields.LearningObjectID}] = @LearningObjectID
  `

  await request.query(query)
}

async function updateObjectives (pool: ConnectionPool, obj: LearningObjectJson): Promise<string[]> {
  const original = await getObjectiveIdsForLearningObject(obj.ID ?? '')

  if (obj.ObjectiveIds === undefined) {
    return original
  }

  const toAdd = obj.ObjectiveIds.filter(id => !original.includes(id))
  const toRemove = original.filter(id => !obj.ObjectiveIds!.includes(id))
  const toUpdate = obj.ObjectiveIds.filter((id, index) => original.includes(id) && original.indexOf(id) !== index)

  await Promise.all(toAdd.map(async id => {
    await createLearningObjectObjective(
      new LearningObjectObjectiveModel({ LearningObjectId: obj.ID, ObjectiveId: id, OrderId: obj.ObjectiveIds!.indexOf(id) + 1}),
      obj.Title ?? 'Learning Content',
      obj.LearningObjectTypeID ?? -1,
      pool.request()
    )
  }))

  await Promise.all(toRemove.map(async id => {
    await removeLearningObjectObjective(obj.ID ?? '', id, pool.request())
  }))

  await Promise.all(toUpdate.map(async id => {
    await updateLearningObjectObjective(new LearningObjectObjectiveModel({ LearningObjectId: obj.ID, ObjectiveId: id, OrderId: obj.ObjectiveIds!.indexOf(id) + 1 }), pool.request())
  }))

  return obj.ObjectiveIds
}
