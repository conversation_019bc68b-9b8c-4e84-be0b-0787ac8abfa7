// Gets the id of a learning object by the id of the context node that the object created
import mssql, { getRows } from '@lcs/mssql-utility'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'

export default async function getLearningObjectByCmi5ContextId (contextId: string): Promise<LearningObjectModel> {
  const record = await getRows<LearningObject>(LearningObjectsTableName, mssql.getPool().request(), { CMI5CourseNodeID: contextId })
  return new LearningObjectModel(undefined, record[0])
}
