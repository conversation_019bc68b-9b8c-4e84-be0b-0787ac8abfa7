import mssql, { getRows } from '@lcs/mssql-utility'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import LearningObjectModel from '../../../models/learning-object.model.js'

export default async function (contentID: string): Promise<LearningObjectModel[]> {
  const pool = mssql.getPool()
  const learningObjectRecords = await getRows<LearningObject>(LearningObjectsTableName, pool.request(), { ContentID: contentID })
  return learningObjectRecords.map(record => new LearningObjectModel(record))
}
