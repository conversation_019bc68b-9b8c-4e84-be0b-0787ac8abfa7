import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

describe('HTTP Controller: check if content is locked', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => sinon.restore())

  it('should return bad request when the context id param is missing', async () => {
    const controller = await esmock('./is-locked.controller.js')
    const mocks = httpMocks.createMocks({ params: { contentId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request parameter(s),')
    expect(data).to.include('contextId: Required')
  })

  it('should return bad request when the content id param is missing', async () => {
    const controller = await esmock('./is-locked.controller.js')
    const mocks = httpMocks.createMocks({ params: { contextId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request parameter(s),')
    expect(data).to.include('contentId: Required')
  })

  it('should return bad request when the content id param is not a valid uuid', async () => {
    const controller = await esmock('./is-locked.controller.js')
    const mocks = httpMocks.createMocks({ params: { contextId: uuid(), contentId: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request parameter(s),')
    expect(data).to.include('contentId: Invalid')
  })

  it('should return bad request when the context id param is not a valid uuid', async () => {
    const controller = await esmock('./is-locked.controller.js')
    const mocks = httpMocks.createMocks({ params: { contextId: 'test', contentId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request parameter(s),')
    expect(data).to.include('contextId: Invalid')
  })

  it('should return false if the content sequence is not enforced and the context is not cmi5', async () => {
    const controller = await esmock('./is-locked.controller.js', {
      '../../../services/mssql/learning-context/get.service.js': {
        default: sinon.stub().resolves(new LearningContextModel({ ID: uuid(), ContextTypeID: LearningContextTypes.Section, EnforceContentSequencing: false }))
      },
      '../../../services/mssql/content/get-content-type.service.js': {
        default: sinon.stub().resolves('Learning Object')
      }
    })
    const mocks = httpMocks.createMocks({
      params: { contextId: uuid(), contentId: uuid() }
    })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.be.false
  })

  it('can gracefully handle an error', async () => {
    const controller = await esmock('./is-locked.controller.js', {
      '../../../services/mssql/learning-context/get.service.js': {
        default: sinon.stub().rejects(new Error('unknown error'))
      }
    })
    const mocks = httpMocks.createMocks({
      params: { contextId: uuid(), contentId: uuid() }
    })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })
})
