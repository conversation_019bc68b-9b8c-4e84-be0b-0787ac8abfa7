import deleteService from '../../../services/mssql/skill-levels/delete.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.delete-skill-level', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-delete-skill-level' label."

  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const numRowsAffected = await deleteService(id)

    if (numRowsAffected > 0) {
      // STIG V-69427 changing data (success)
      // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-delete-skill-level' label and message indicating successful deletion."
      log('info', 'Successfully deleted skill level', { id, success: true, req })
      res.sendStatus(httpStatus.NO_CONTENT)
    } else {
      // STIG V-69427 changing data (failure)
      log('info', 'Failed to delete skill level because it was not found in the database.', { success: false, id, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    }
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete skill level: input validation error', { success: false, req, error })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter, '))
    } else {
      log('error', 'Failed to delete skill level.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
