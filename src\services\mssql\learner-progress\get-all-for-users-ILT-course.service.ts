import mssql, { DB_Errors } from '@lcs/mssql-utility'
import { LearnerProgress, LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import LearnerProgressModel from '../../../models/learner-progress.model.js'

/**
 *
 * @param {string} learningContextId
 * @returns {LearnerProgressModel[]}
 */
export default async function (learningContextId: string, userID: string) {
  const pool = mssql.getPool()
  const request = pool.request()

  const query = `
    SELECT *
    FROM [${LearnerProgressTableName}]
    WHERE [${LearnerProgressFields.LearningContextSessionID}] IN (
        SELECT [${LearningContextSessionFields.ID}]
        FROM [${LearningContextSessionsTableName}]
        WHERE [${LearningContextSessionFields.LearningContextID}] = @contextID
    ) AND [${LearnerProgressFields.UserID}] = @userID
    ORDER BY [${LearnerProgressFields.CompletedDate}] DESC, [${LearnerProgressFields.CreatedOn}] DESC, [${LearnerProgressFields.ModifiedOn}] DESC
  `
  request.input('contextID', learningContextId)
  request.input('userID', userID)
  const results = await request.query<LearnerProgress>(query)
  if (results.recordset.length <= 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }
  return results.recordset.map(record => new LearnerProgressModel(record))
}
