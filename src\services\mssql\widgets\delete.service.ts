import mssql, { deleteRow } from '@lcs/mssql-utility'
import { WidgetsTableName } from '@tess-f/sql-tables/dist/lms/widget.js'

export default async function deleteWidget (widgetID: string) {
  const pool = mssql.getPool()
  const transaction = pool.transaction()
  let rolledBack = false

  try {
    await transaction.begin()
    transaction.on('rollback', () => { rolledBack = true })

    // delete the widget
    await deleteRow(transaction.request(), WidgetsTableName, { ID: widgetID })

    await transaction.commit()
  } catch (error) {
    if (!rolledBack) transaction.rollback()
    throw error
  } finally {
    if (transaction) transaction.removeAllListeners('rollback')
  }
}
