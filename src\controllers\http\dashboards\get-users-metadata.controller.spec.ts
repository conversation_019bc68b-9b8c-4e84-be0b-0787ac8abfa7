import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'

describe('HTTP Controller: get users metadata', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => sinon.restore())

  it('returns a bad request status code when the query params are invalid', async () => {
    const controller = await esmock('./get-users-metadata.controller.js')
    const mocks = httpMocks.createMocks({ query: { from: 'tomorrow', to: 'yesterday', search: 1 } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request query data:')
    expect(data).to.include('search: Expected string, received number')
    expect(data).to.include('from: Invalid date')
    expect(data).to.include('to: Invalid date')
  })

  it('returns the requested data', async () => {
    const controller = await esmock('./get-users-metadata.controller.js', {
      '../../../services/mssql/users/get-paginated-user-ids.service.js': { default: sinon.stub().resolves({ totalRecords: 0, ids: [] }) }
    })
    const mocks = httpMocks.createMocks()
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
    expect(data.UserLearningMetadata).to.be.an('array')
    expect(data.UserLearningMetadata.length).to.equal(0)
    expect(data.TotalRecords).to.be.a('number')
    expect(data.TotalRecords).to.equal(0)
  })

  it('gracefully handles an error', async () => {
    const controller = await esmock('./get-users-metadata.controller.js', {
      '../../../services/mssql/users/get-paginated-user-ids.service.js': { default: sinon.stub().rejects(new Error('Service Error')) }
    })
    const mocks = httpMocks.createMocks()
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })
})
