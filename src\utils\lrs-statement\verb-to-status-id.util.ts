import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { xapiSatisfactionTypes } from '@tess-f/sql-tables/dist/lms/xapi-satisfaction-type.js'
import { CMI5Verbs } from '@tess-f/sql-tables/dist/lrs/verb.js'

export default function verbToLessonStatus (satisfactionTypeId: number): number {
  if (satisfactionTypeId === xapiSatisfactionTypes.Pass) {
    return LessonStatuses.passed
  } else if (satisfactionTypeId === xapiSatisfactionTypes.Fail) {
    return LessonStatuses.fail
  } else if (satisfactionTypeId === xapiSatisfactionTypes.Complete) {
    return LessonStatuses.completed
  }
  return LessonStatuses.browsed
}

export function cmi5VerbToLessonStatus (verb: string): number {
  if (verb === CMI5Verbs.Completed) {
    return LessonStatuses.completed
  } else if (verb === CMI5Verbs.Passed) {
    return LessonStatuses.passed
  }
  return LessonStatuses.fail
}
