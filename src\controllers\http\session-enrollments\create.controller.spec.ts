import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import SessionEnrollment from '../../../models/session-enrollment.model.js'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import LocationModel from '../../../models/location.model.js'
import LearningContextModel from '../../../models/learning-context.model.js'

describe('HTTP create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())

    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/session-enrollments/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SessionEnrollment()))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().resolves({})
            },
            '../../../services/mssql/learning-context-sessions/get.service.js': {
                default: Sinon.stub().resolves(new LearningContextSessionModel({StartDate: new Date(), EndDate: new Date()}))
            },
            '../../../services/mssql/locations/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LocationModel()))
            },
            '../../../services/mssql/learning-context/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextModel({})))
            },
            '../../../services/mssql/learning-context-session-instructors/get.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../../../services/mssql/learning-context-sessions/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionModel({ ID: uuid() })))
            },
            '../../../services/mssql/users/get-multiple-by-ids.service.js': {
                 default: Sinon.stub().returns(Promise.resolve([{ User: uuid() }]))
                 },
                 '../../../services/mssql/users/get-by-id.service.js': {
                     default: Sinon.stub().returns(Promise.resolve({ ID: uuid() }))
                 },
                 '@tess-f/email/dist/amqp/send-instructor-enrollment.js': {
                     sendInstructorEnrollmentMessage: Sinon.stub().returns(Promise.resolve({success: true}))
                 },
                 '../../../services/amqp/notification/send-notification.service.js': {
                     default: Sinon.stub().returns(Promise.resolve(true))
                 },
                 '../../../services/amqp/email/send-admin-session-enrollments.service.js': {
                     default: Sinon.stub().returns(Promise.resolve(true))
                 },
                 '@tess-f/email/dist/amqp/send-learner-enrollment.js': {
                    sendLearnerEnrollmentMessage: Sinon.stub().returns(Promise.resolve({success: true}))
                 }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: 
                {
                    SessionID: uuid(),
                    UserID: uuid()
                }
            

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/session-enrollments/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new SessionEnrollment()))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().resolves({})
            },
            '../../../services/mssql/learning-context-sessions/get.service.js': {
                default: Sinon.stub().resolves(new LearningContextSessionModel({StartDate: new Date(), EndDate: new Date()}))
            },
            '../../../services/mssql/locations/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LocationModel()))
            },
            '../../../services/mssql/learning-context/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextModel({})))
            },
            '../../../services/mssql/learning-context-session-instructors/get.service.js': {
                default: Sinon.stub().resolves([])
            },
            '../../../services/mssql/learning-context-sessions/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionModel({ ID: uuid() })))
            },
            '../../../services/mssql/users/get-multiple-by-ids.service.js': {
                 default: Sinon.stub().returns(Promise.resolve([{ User: uuid() }]))
                 },
                 '../../../services/mssql/users/get-by-id.service.js': {
                     default: Sinon.stub().returns(Promise.resolve({ ID: uuid() }))
                 },
                 '@tess-f/email/dist/amqp/send-instructor-enrollment.js': {
                     sendInstructorEnrollmentMessage: Sinon.stub().returns(Promise.resolve({success: true}))
                 },
                 '../../../services/amqp/notification/send-notification.service.js': {
                     default: Sinon.stub().returns(Promise.resolve(true))
                 },
                 '../../../services/amqp/email/send-admin-session-enrollments.service.js': {
                     default: Sinon.stub().returns(Promise.resolve(true))
                 },
                 '@tess-f/email/dist/amqp/send-learner-enrollment.js': {
                    sendLearnerEnrollmentMessage: Sinon.stub().returns(Promise.resolve({success: true}))
                 }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: 
                {
                    SessionID: false,
                    UserID: false
                }
            

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('SessionID')
        expect(mocks.res._getData()).include('UserID')

    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/session-enrollments/create.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new SessionEnrollment()))
            },
            '../../../services/mssql/session-enrollments/get.service.js': {
                default: Sinon.stub().rejects({})
            },
            '../../../services/mssql/learning-context-sessions/get.service.js': {
                default: Sinon.stub().rejects(new LearningContextSessionModel({StartDate: new Date(), EndDate: new Date()}))
            },
            '../../../services/mssql/locations/get.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new LocationModel()))
            },
            '../../../services/mssql/learning-context/get.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new LearningContextModel({})))
            },
            '../../../services/mssql/learning-context-session-instructors/get.service.js': {
                default: Sinon.stub().rejects([])
            },
            '../../../services/mssql/learning-context-sessions/update.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new LearningContextSessionModel({ ID: uuid() })))
            },
            '../../../services/mssql/users/get-multiple-by-ids.service.js': {
                 default: Sinon.stub().rejects(Promise.resolve([{ User: uuid() }]))
                 },
                 '../../../services/mssql/users/get-by-id.service.js': {
                     default: Sinon.stub().rejects(Promise.resolve({ ID: uuid() }))
                 },
                 '@tess-f/email/dist/amqp/send-instructor-enrollment.js': {
                     sendInstructorEnrollmentMessage: Sinon.stub().returns(Promise.resolve({success: true}))
                 },
                 '../../../services/amqp/notification/send-notification.service.js': {
                     default: Sinon.stub().rejects(Promise.resolve(true))
                 },
                 '../../../services/amqp/email/send-admin-session-enrollments.service.js': {
                     default: Sinon.stub().rejects(Promise.resolve(true))
                 },
                 '@tess-f/email/dist/amqp/send-learner-enrollment.js': {
                    sendLearnerEnrollmentMessage: Sinon.stub().rejects(Promise.resolve({success: true}))
                 }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: 
                {
                    SessionID: uuid(),
                    UserID: uuid()
                }
            

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })

})