import { Table } from '@lcs/mssql-utility'
import { LearningContextSession, LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { LearningContextSessionJson } from '@tess-f/lms/dist/common/learning-context-session.js'
import { z } from 'zod'
import { SessionStatuses } from '@tess-f/sql-tables/dist/lms/session-status.js'
import { zodDates, zodGUID } from '@tess-f/backend-utils/validators'

const requiredFields = z.object({
  [LearningContextSessionFields.SessionID]: z.string().max(50),
  [LearningContextSessionFields.SessionStatusID]: z.nativeEnum(SessionStatuses),
  [LearningContextSessionFields.StartDate]: zodDates,
  [LearningContextSessionFields.EndDate]: zodDates,
  [LearningContextSessionFields.LearningContextID]: zodGUID,
  [LearningContextSessionFields.Timezone]: z.string().max(100)
})

const optionalFields = z.object({
  [LearningContextSessionFields.MinEnrollments]: z.coerce.number().int().nullish(),
  [LearningContextSessionFields.MaxEnrollments]: z.coerce.number().int().nullish(),
  [LearningContextSessionFields.DisplayEnrollmentsOnHomePage]: z.boolean().optional(),
  [LearningContextSessionFields.EnableWaitlist]: z.boolean().optional(),
  [LearningContextSessionFields.LocationID]: zodGUID.optional().nullish(),
  [LearningContextSessionFields.WebinarURL]: z.string().url().max(1000).nullish(),
  [LearningContextSessionFields.WebinarDialInNumber]: z.string().max(150).nullish(),
  [LearningContextSessionFields.WebinarID]: z.string().max(50).nullish()
})

export const createLearningContextSessionSchema = requiredFields.merge(optionalFields).superRefine(({ MinEnrollments, MaxEnrollments }, ctx) => {
  if (MinEnrollments && MaxEnrollments && MinEnrollments > MaxEnrollments) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'Min Enrollments must be less than or equal to Max Enrollments', path: ['MinEnrollments'] })
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'Min Enrollments must be less than or equal to Max Enrollments', path: ['MaxEnrollments'] })
  }
}).superRefine(({ LocationID, WebinarURL, WebinarDialInNumber, WebinarID }, ctx) => {
  if (!LocationID && !WebinarURL && !WebinarDialInNumber && !WebinarID) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'Must provide a location for the session', path: ['LocationID'] })
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: 'Must provide a location for the session', path: ['WebinarURL'] })
  }
})

export const updateLearningContextSessionSchema = optionalFields.merge(requiredFields.partial())

export default class LearningContextSessionModel extends Table<LearningContextSessionJson, LearningContextSession> {
  public fields: LearningContextSessionJson

  constructor (fields?: LearningContextSessionJson, record?: LearningContextSession) {
    super(LearningContextSessionsTableName, [
      LearningContextSessionFields.SessionID,
      LearningContextSessionFields.DisplayEnrollmentsOnHomePage,
      LearningContextSessionFields.SessionStatusID,
      LearningContextSessionFields.CreatedOn,
      LearningContextSessionFields.CreatedBy,
      LearningContextSessionFields.LearningContextID,
      LearningContextSessionFields.Timezone,
      LearningContextSessionFields.StartDate,
      LearningContextSessionFields.EndDate
    ])
    if (fields) this.fields = fields
    else this.fields = {}

    if (record) this.importFromDatabase(record)
  }

  public importFromDatabase (record: LearningContextSession): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningContextSession {
    return {
      ID: this.fields.ID,
      SessionID: this.fields.SessionID,
      MinEnrollments: this.fields.MinEnrollments,
      MaxEnrollments: this.fields.MaxEnrollments,
      DisplayEnrollmentsOnHomePage: this.fields.DisplayEnrollmentsOnHomePage,
      EnableWaitlist: this.fields.EnableWaitlist,
      SessionStatusID: this.fields.SessionStatusID,
      StartDate: this.fields.StartDate,
      EndDate: this.fields.EndDate,
      LocationID: this.fields.LocationID,
      WebinarURL: this.fields.WebinarURL,
      WebinarID: this.fields.WebinarID,
      WebinarDialInNumber: this.fields.WebinarDialInNumber,
      CreatedOn: this.fields.CreatedOn,
      CreatedBy: this.fields.CreatedBy,
      ModifiedOn: this.fields.ModifiedOn,
      ModifiedBy: this.fields.ModifiedBy,
      LearningContextID: this.fields.LearningContextID,
      Timezone: this.fields.Timezone
    }
  }
}
