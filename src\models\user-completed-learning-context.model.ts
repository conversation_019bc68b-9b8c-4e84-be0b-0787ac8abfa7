import { Table } from '@lcs/mssql-utility'
import { UserCompletedLearningContext, UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'

export default class UserCompletedLearningContextModel extends Table<UserCompletedLearningContext, UserCompletedLearningContext> {
  public fields: UserCompletedLearningContext

  constructor (fields?: UserCompletedLearningContext) {
    super(UserCompletedLearningContextsTableName, [
      UserCompletedLearningContextFields.LearningContextID,
      UserCompletedLearningContextFields.UserID,
      UserCompletedLearningContextFields.CompletedOn
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: UserCompletedLearningContext): void {
    this.fields = record
  }

  public exportJsonToDatabase (): UserCompletedLearningContext {
    return this.fields
  }
}
