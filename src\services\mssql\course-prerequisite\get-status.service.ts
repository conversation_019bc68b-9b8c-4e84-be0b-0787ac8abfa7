import mssql, { DB_Errors } from '@lcs/mssql-utility'

export default async function getPrerequisiteStatus (prerequisiteId: string, userId: string): Promise<'Met' | 'Unmet' | 'In Progress'> {
  const request = mssql.getPool().request()

  request.input('PrerequisiteId', prerequisiteId)
  request.input('UserId', userId)

  const result = await request.query<{ Status: 'Met' | 'Unmet' | 'In Progress' }>(`
    exec LMS_GetPrerequisiteStatus @PrerequisiteId, @UserId  
  `)

  if (result.recordset.length <= 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }

  return result.recordset[0].Status
}
