import logger from '@lcs/logger'
import { Request, Response } from 'express'
import searchService from '../../../services/mssql/content/search.service.js'
import { getContextInProgressCount, getContextCompletionCounts } from '../../../services/mssql/learning-context/get-completion-counts.service.js'
import getContextAssignmentCountService from '../../../services/mssql/assignments/get-context-assignment-count.service.js'
import { getObjectCompletedCount, getObjectInProgressCount } from '../../../services/mssql/learning-objects/get-completion-counts.service.js'
import getObjectAssignmentCountService from '../../../services/mssql/assignments/get-object-assignment-count.service.js'
import { LearningElement } from '@tess-f/sql-tables/dist/lms/learning-elements-view.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodLimit, zodOffset } from '@tess-f/backend-utils/validators'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'

const log = logger.create('Controller-HTTP.get-content-overview', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { limit, offset, search, filters, sortColumn, sortDirection } = z.object({
      limit: zodLimit,
      offset: zodOffset,
      search: z.string().optional(),
      filters: z.object({
        labels: z.array(z.string()).optional(),
        objectTypeIds: z.array(z.nativeEnum(LearningObjectTypes)).optional(),
        keywords: z.array(z.string()).optional(),
      }).optional(),
      sortColumn: z.string().optional(),
      sortDirection: z.enum(['ASC', 'DESC']).optional()
    }).parse(req.body)

    const content = await searchService(offset, limit, search, filters, true, sortColumn, sortDirection)

    // Graph on our extra data (completions, in progress, assigned)
    const elements = await Promise.all(content.elements.map(async _element => {
      const element: LearningElement & { Completions: number, InProgress: number, Assigned: number } = { ..._element.fields, Completions: 0, InProgress: 0, Assigned: 0 } // map the fields to an any type so we can add extra properties
      if (_element.fields.EntityType === 'Learning Context') {
        element.Completions = await getContextCompletionCounts(_element.fields.ID!)
        element.InProgress = await getContextInProgressCount(_element.fields.ID!)
        element.Assigned = await getContextAssignmentCountService(_element.fields.ID!)
      } else if (_element.fields.EntityType === 'Learning Object') {
        element.Completions = await getObjectCompletedCount(_element.fields.ID!)
        element.InProgress = await getObjectInProgressCount(_element.fields.ID!)
        element.Assigned = await getObjectAssignmentCountService(_element.fields.ID!)
      }
      return element
    }))

    res.json({
      totalRecords: content.totalRecords,
      elements
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to search / get content overview: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data:'))
    } else {
      log('error', 'Failed to search / get content overview.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
