import logger from '@lcs/logger'
import { DB_Errors } from '@lcs/mssql-utility'
import LearningContext from '../models/learning-context.model.js'
import getContextUserFavorite from '../services/mssql/learning-context-favorites/get.service.js'
import getContextUserBookmark from '../services/mssql/learning-context-bookmarks/get.service.js'
import { getUserRatingForContext } from '../services/mssql/learning-context-ratings/get.service.js'
import getContextDuration from '../services/mssql/learning-context/get-duration.service.js'
import getContextCompletion from '../services/mssql/learning-context/get-completion.service.js'
import { getKeywordsForContext } from '../services/mssql/keywords/get-multiple.service.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import getObjectiveIdsForContext from '../services/mssql/learning-context-objectives/get-multi.service.js'

const log = logger.create('Mapper.Learning-context-extras')

export default async function (data: LearningContext[], userID: string, opts: {
  rating?: boolean
  bookmark?: boolean
  favorite?: boolean
  duration?: boolean
  completion?: boolean
  keywords?: boolean
}): Promise<void[]> {
  return await Promise.all(data.map(async element => {
    if (opts.bookmark) {
      try {
        element.fields.UserBookmark = (await getContextUserBookmark(element.fields.ID!, userID)).fields
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
          log('error', 'Failed to get user bookmark for learning context', { id: element.fields.ID, errorMessage, success: false })
          throw error
        }
      }
    }

    if (opts.favorite) {
      try {
        element.fields.UserFavorite = (await getContextUserFavorite(element.fields.ID!, userID)).fields
      } catch (error) {
        if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
          const errorMessage = getErrorMessage(error)
          log('error', 'Failed to get user Favorite for learning context', { id: element.fields.ID, errorMessage, success: false })
          throw error
        }
      }
    }

    if (opts.rating) {
      try {
        element.fields.UserRating = (await getUserRatingForContext(element.fields.ID!, userID)).fields
      } catch (error) {
        if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
          const errorMessage = getErrorMessage(error)
          log('error', 'Failed to get users rating for learning context', { id: element.fields.ID!, errorMessage, success: false })
          throw error
        }
      }
    }

    if (opts.duration) {
      try {
        element.fields.MinutesToComplete = await getContextDuration(element.fields.ID!)
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        log('error', 'Failed to get context duration', { id: element.fields.ID, errorMessage, success: false })
        throw error
      }
    }

    if (opts.completion) {
      try {
        element.fields.PercentComplete = (await getContextCompletion(element.fields.ID!, userID)).completion
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        log('error', 'Failed to get completion amount', { id: element.fields.ID, errorMessage, success: false })
        throw error
      }
    }

    if (opts.keywords) {
      try {
        element.fields.Keywords = await getKeywordsForContext(element.fields.ID!)
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        if (error instanceof Error && error.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
          log('error', 'Failed to get keywords', { id: element.fields.ID, errorMessage, success: false })
          throw error
        }
      }
    }

    try {
      element.fields.ObjectiveIds = await getObjectiveIdsForContext(element.fields.ID ?? '')
    } catch (error) {
      log('error', 'Failed to get learning objective ids for learning context', { id: element.fields.ID, errorMessage: getErrorMessage(error), success: false })
      element.fields.ObjectiveIds = []
    }
  }))
}
