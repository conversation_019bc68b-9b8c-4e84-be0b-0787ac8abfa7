import settings from '../config/settings.js'
import rabbitmq from '@lcs/rabbitmq'
import logger from '@lcs/logger'
import mssql from '@lcs/mssql-utility'
import sessionAuthority from '@lcs/session-authority'

export async function startServices (): Promise<void> {
  if (process.env.LOCK_TESTING_UTILS === 'true') { return }
  logger.init({ level: 'error' })
  await mssql.init(settings.mssql.connectionConfig, false, 50)
  await rabbitmq.connect(settings.amqp.config)
  await sessionAuthority.init(settings.sessionAuthority)
}

export async function shutdownServices (): Promise<void> {
  if (process.env.LOCK_TESTING_UTILS === 'true') { return }
  await sessionAuthority.disconnect()
  await rabbitmq.close()
  await mssql.getPool().close()
  logger.close()
}
