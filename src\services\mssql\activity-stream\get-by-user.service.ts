import mssql from '@lcs/mssql-utility'
import { ActivityStream, ActivityStreamFields, ActivityStreamTableName } from '@tess-f/sql-tables/dist/lms/activity-stream.js'
import { Request } from 'mssql'
import ActivityStreamModel from '../../../models/activity-stream.model.js'

export default async function (userID: string, from?: Date, to?: Date): Promise<ActivityStreamModel[]> {
  const pool = mssql.getPool()
  return await getStream(pool.request(), userID, from, to)
}

async function getStream (request: Request, userID: string, from?: Date, to?: Date): Promise<ActivityStreamModel[]> {
  request.input('userID', userID)
  let query = `
    SELECT *
    FROM [${ActivityStreamTableName}]
    WHERE (
      [${ActivityStreamFields.UserID}] = @userID
      OR [${ActivityStreamFields.ToUserID}] = @userID
    )
  `

  if (from && to) {
    query += `AND [${ActivityStreamFields.CreatedOn}] BETWEEN @from AND @to `
    request.input('from', from)
    request.input('to', to)
  }

  query += `ORDER BY [${ActivityStreamFields.CreatedOn}] DESC`

  const result = await request.query<ActivityStream>(query)
  return result.recordset.map(record => new ActivityStreamModel(record))
}
