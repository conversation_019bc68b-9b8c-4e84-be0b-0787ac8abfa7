import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import WidgetModel from '../../../models/widget.model.js'


describe('HTTP create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/widgets/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new WidgetModel({ ID: uuid() })))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid()
            },
            body: {
                Name: 'widget',
                UserID: uuid(),
                TypeID: 1,
                Title: 'test'
            }


        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/widgets/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new WidgetModel({ ID: uuid() })))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid()
            },
            body: {
                Name: false,
                UserID: false,
                TypeID: false,
                Title: false
            }


        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('TypeID')
        expect(mocks.res._getData()).include('Title')
        expect(mocks.res._getData()).include('Expected string, received boolean')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/widgets/create.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new WidgetModel({ ID: uuid() })))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid()
            },
            body: {
                Name: 'widget',
                UserID: uuid(),
                TypeID: 1,
                Title: 'test'
            }


        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

})