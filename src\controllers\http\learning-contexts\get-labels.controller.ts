import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import getLabels from '../../../services/mssql/learning-context/get-distinct-labels.service.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodStringToBoolean } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.Get-Distinct-Context-Labels', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { allLabels, contextType } = z.object({
      allLabels: zodStringToBoolean.optional().default('false'),
      contextType: z.coerce.number().int().optional()
    }).parse(req.query)

    const labels = await getLabels(allLabels, contextType)
    log('info', 'Successfully retrieved distinct browseable learning context labels', { count: labels.length, success: true, req })
    res.json(labels)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('error', 'Failed to get distinct browseable learning context labels', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else {
      log('error', 'Failed to get distinct browseable learning context labels', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
