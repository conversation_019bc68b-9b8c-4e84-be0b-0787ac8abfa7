import logger from '@lcs/logger'
import { User } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { Moment } from 'moment-timezone'
import LocationModel from '../../../models/location.model.js'
import { getSystemConfig } from '../system/get-system-config.service.js'
import { sendFormattedMessage } from '@tess-f/email/dist/amqp/send-admin-formatted-message.js'
import settings from '../../../config/settings.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import sendNotification from '../../../services/amqp/notification/send-notification.service.js'
import { AdminGroupId } from '@tess-f/sql-tables/dist/id-mgmt/group.js'

const log = logger.create('Service-AMQP.send-admin-enrollment-confirmation')

export default async function sendAdminSessionEnrollments (sessionContext: LearningContextModel, session: LearningContextSessionModel, learners: User[], start: Moment, end: Moment, location?: LocationModel) {
  let systemName = 'TESS'
  try {
    const config = await getSystemConfig()
    systemName = config.DisplayName
  } catch {
    // silent failure
  }

  const learnerBody = learners.map(learner => {
    if (learner.Email) {
      return `<li><a href="mailto:${learner.Email}">${learner.FirstName} ${learner.LastName} (${learner.Username})</a></li>`
    }
    return `<li>${learner.FirstName} ${learner.LastName} (${learner.Username})</li>`
  })

  const learnerText = learners.map(learner => {
    if (learner.Email) {
      return `${learner.FirstName} ${learner.LastName} (${learner.Username}): ${learner.Email}`
    }
    return `${learner.FirstName} ${learner.LastName} (${learner.Username})`
  })

  let body = `
    <p>The following learners have registered for ${sessionContext.fields.Title} - ${session.fields.SessionID}.</p>
    <h3>Learner(s) Registered</h3>
    <ul>${learnerBody.join('')}</ul>
    <h3>Date/Time Information</h3>
    <p>
      <strong>${start.format('dddd, MMMM Do')}</strong><br>
      ${start.format('h:mm')} - ${end.format('h:mm')} (${end.format('z')})
    </p>
  `

  let text = `
    Enrollment Confirmation\n\nThe following learners have registered for ${sessionContext.fields.Title} - ${session.fields.SessionID}.\n Learner(s) Registered:
    ${learnerText.join('\n')}
    Date/Time Information:
    ${start.format('dddd, MMMM Do')}\n${start.format('h:mm')} - ${end.format('h:mm')} (${end.format('z')})
  `

  if (location) {
    body += `
      <h3>Location Information</h3>
      <p>
        <strong>${location.fields.Title}</strong><br>
        ${location.fields.AddressLine1}
        ${location.fields.AddressLine2 ? location.fields.AddressLine2 + '<br>' : ''}
        ${location.fields.City}, ${location.fields.State} ${location.fields.Zip}<br>
        ${location.fields.Country}
      <p>
    `

    text += `
      Location Information:\n
      ${location.fields.Title}
      ${location.fields.AddressLine1}
      ${location.fields.AddressLine2}
      ${location.fields.City}, ${location.fields.State} ${location.fields.Zip}
      ${location.fields.Country}
    `
  }

  if (session.fields.WebinarDialInNumber && session.fields.WebinarID && session.fields.WebinarURL) {
    body += `
      <h3>Webinar Information</h3>
      ${session.fields.WebinarURL ? '<p>Webinar URL: ' + session.fields.WebinarURL + '<br>' : '</p>'}
      ${session.fields.WebinarID ? 'Webinar/Conference ID: ' + session.fields.WebinarID + '<br>' : '</p>'}
      ${session.fields.WebinarDialInNumber ? 'Dial-In Number: ' + session.fields.WebinarDialInNumber + '</p>' : '</p>'}
    `

    text += `
      \nWebinar Information:\n
      ${session.fields.WebinarURL ? 'Webinar URL: ' + session.fields.WebinarURL + '' : ''}
      ${session.fields.WebinarID ? 'Webinar/Conference ID: ' + session.fields.WebinarID + '' : ''}
      ${session.fields.WebinarDialInNumber ? 'Dial-In Number: ' + session.fields.WebinarDialInNumber + '\n' : '\n'}
    `
  }

  body += `<h3>Additional Information</h3><p>Login to ${systemName} to modify this session or enrollment information.</p>`
  text += `\nAdditional Information\n\nLogin to ${systemName} to modify this session or enrollment information.\n\n`

  if (settings.mail.send_enrollments_to_admin) {
    try {
      const response = await sendFormattedMessage(
        settings.amqp.service_queues.email, {
          body,
          text,
          header: 'Enrollment Confirmation',
          subject: 'Enrollment Confirmation'
        },
        settings.amqp.command_timeout
      )
      if (response.success) {
        log('info', 'Successfully sent admins enrollment confirmation', { success: true })
      } else {
        log('warn', 'Failed to send admin enrollment confirmation', { errorMessage: response.message, success: false })
      }
    } catch (error) {
      log('error', 'Failed to send admin enrollment confirmation', { errorMessage: getErrorMessage(error), success: false })
    }

    // send admin notification
    await sendNotification({
      Title: 'Enrollment Confirmation',
      Message: body,
      PublishDate: new Date(),
      Everyone: false,
      SystemID: 'lms',
      Priority: 1,
      GroupIDs: [AdminGroupId]
    })
  }
}
