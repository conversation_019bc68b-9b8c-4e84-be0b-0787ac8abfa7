import mssql, { addRow } from '@lcs/mssql-utility'
import { UserCompletedLearningContext } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import UserCompletedLearningContextModel from '../../../models/user-completed-learning-context.model.js'

export default async function (model: UserCompletedLearningContextModel): Promise<UserCompletedLearningContextModel> {
  const pool = mssql.getPool()
  const record = await addRow<UserCompletedLearningContext>(pool.request(), model)
  return new UserCompletedLearningContextModel(record)
}
