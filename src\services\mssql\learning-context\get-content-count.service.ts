import mssql from '@lcs/mssql-utility'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'

export default async function (contextID: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('contextID', contextID)
  return (await request.query<{ Count: number }>(`
    SELECT
    (SELECT COUNT(*) FROM [${LearningObjectContextsTableName}] WHERE [${LearningObjectContextFields.LearningContextID}] = @contextID)
    +
    (SELECT COUNT(*) FROM [${LearningContextConnectionsTableName}] WHERE [${LearningContextConnectionFields.ParentContextID}] = @contextID)
    +
    (SELECT COUNT(*) FROM [${LearningContextTableName}] WHERE [${LearningContextFields.ParentContextID}] = @contextID)
    AS Count
  `)).recordset[0].Count
}
