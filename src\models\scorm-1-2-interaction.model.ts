import { Table } from '@lcs/mssql-utility'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { SCORM_1_2_Interaction, ScormInteractionFields, ScormInteractionsTableName } from '@tess-f/sql-tables/dist/lms/scorm-1-2-interaction.js'
import { z } from 'zod'

export const scorm12InteractionSchema = z.object({
  [ScormInteractionFields.ID]: z.string().max(255),
  [ScormInteractionFields.Time]: z.string().max(15).optional(),
  [ScormInteractionFields.Type]: z.string().max(20).optional(),
  [ScormInteractionFields.Weighting]: z.string().max(20).optional(),
  [ScormInteractionFields.StudentResponse]: z.string().max(255).optional(),
  [ScormInteractionFields.Result]: z.string().max(25).optional(),
  [ScormInteractionFields.Latency]: z.string().max(15).optional(),
  [ScormInteractionFields.LearnerProgressID]: zodGUID,
  [ScormInteractionFields.CorrectResponses]: z.string().optional(),
  [ScormInteractionFields.UserID]: zodGUID
})

export default class SCORM1_2InteractionModel extends Table<SCORM_1_2_Interaction, SCORM_1_2_Interaction> {
  public fields: SCORM_1_2_Interaction

  constructor (fields?: SCORM_1_2_Interaction) {
    super(ScormInteractionsTableName, [
      ScormInteractionFields.ID,
      ScormInteractionFields.LearnerProgressID,
      ScormInteractionFields.UserID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: SCORM_1_2_Interaction): void {
    this.fields = record
  }

  public exportJsonToDatabase (): SCORM_1_2_Interaction {
    return this.fields
  }
}
