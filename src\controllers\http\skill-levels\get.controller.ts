import getService from '../../../services/mssql/skill-levels/get.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.get-skill-level', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, click on a single course. Note the time. Check the LMS API log for the 'http-get-skill-level' label."

  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const skillLevel = (await getService(id)).fields

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, click on a single course. Note the time. Check the LMS API log for the 'http-get-skill-level' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved skill level', { id: skillLevel.ID, success: true, req })

    res.json(skillLevel)
  } catch (error) {
    // STIG V-69425 data access (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get skill level: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to get skill level, not found in database', { error, success: false, id: req.params.id, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      log('error', 'Failed to get skill level.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
