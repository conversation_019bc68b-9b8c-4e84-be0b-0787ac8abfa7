import mssql from '@lcs/mssql-utility'
import { UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'

export default async function (objectID: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('objectID', objectID)

  const assigned = await request.query<{AssignmentCount: number}>(`
    SELECT COUNT(*) AS AssignmentCount FROM (
      SELECT DISTINCT [${UserAssignedLearningObjectFields.AssignmentID}], [${UserAssignedLearningObjectFields.UserID}]
      FROM [${UserAssignedLearningObjectsTableName}]
      WHERE [${UserAssignedLearningObjectFields.LearningObjectID}] = @objectID
    ) AS DistinctAssignments
  `)

  return assigned.recordset[0].AssignmentCount
}
