import Keyword, { createKeywordSchema } from '../../../models/keyword.model.js'
import create from '../../../services/mssql/keywords/create.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { ZodError } from 'zod'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.create-keyword', httpLogTransformer)

/**
 * @param req.body.keyword {KeywordModel} - Keyword model with required creation fields
 */
export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-create-keyword' label."

  try {
    const keyword = new Keyword(createKeywordSchema.parse(req.body.keyword))

    const created = (await create(keyword)).fields

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-create-keyword' label and message indicating successful creation."
    log('info', 'Successfully created new keyword', { success: true, req })

    res.json(created)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create new keyword due to invalid data in the request.', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    }
    else {
      log('error', 'Failed to create new keyword.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
