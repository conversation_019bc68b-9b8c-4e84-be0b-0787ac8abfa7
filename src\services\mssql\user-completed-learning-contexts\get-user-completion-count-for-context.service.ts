import mssql from '@lcs/mssql-utility'
import { UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'

export default async function (userID: string, contextID: string, from?: Date, to?: Date): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('userID', userID)
  request.input('contextID', contextID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const results = await request.query<{ Completions: number }>(`
    SELECT COUNT(*) AS Completions
    FROM [${UserCompletedLearningContextsTableName}]
    WHERE [${UserCompletedLearningContextFields.UserID}] = @userID
    AND [${UserCompletedLearningContextFields.LearningContextID}] = @contextID
    AND [${UserCompletedLearningContextFields.LessonStatusID}] >= ${LessonStatuses.fail}
    ${from && to ? `AND [${UserCompletedLearningContextFields.CompletedOn}] BETWEEN @from AND @to` : ''}
  `)

  return results.recordset[0].Completions
}
