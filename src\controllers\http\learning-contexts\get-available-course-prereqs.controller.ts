import getAvailable from '../../../services/mssql/learning-context/get-available-course-prereqs.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { z } from 'zod'

const { INTERNAL_SERVER_ERROR, BAD_REQUEST } = httpStatus
const log = logger.create('Controller-HTTP.get-available-course-prerequisites', httpLogTransformer)

/*
    Query options
        contextID: string - used to filter out courses that have this context as a prereq and the prereqs this context already has - optional (if ommited will return all courses)
        search: string - value to search title and description for
        offset: number - number of records to offset the query by
        limit: number - number of records to limit the query to (must be between 10 and 100)
*/
export default async function (req: Request, res: Response) {
  try {
    const { limit, offset, search, contextID, labels } = z.object({
      limit: zodLimit,
      offset: zodOffset,
      search: z.string().optional(),
      contextID: zodGUID.optional(),
      labels: z.string().optional().transform(value => (value ?? '').split(','))
    }).parse(req.query)

    const results = await getAvailable(contextID, offset, limit, search, labels)

    log('info', 'Successfully retrieved available course prerequisites', { count: results.contexts.length, totalRecords: results.totalRecords, success: true, req })

    res.json({
      totalRecords: results.totalRecords,
      contexts: results.contexts.map(context => context.fields)
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('error', 'Failed to get available courses to use as prereq for another course: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request query parameter, '))
    } else {
      log('error', 'Failed to get available courses to use as prereq for another course', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
