import mssql, { DB_Errors, getRows } from '@lcs/mssql-utility'
import { LearningContextTableName, LearningContextFields } from '@tess-f/sql-tables/dist/lms/learning-context.js'

export default async function (courseID: string, id?: string): Promise<boolean> {
  try {
    const pool = mssql.getPool()
    const request = pool.request()

    let contexts
    let recordCount = 0

    if (id) {
      const query = `SELECT * FROM [${LearningContextTableName}] WHERE [${LearningContextFields.ID}] != @GUID AND [${LearningContextFields.CourseID}] = @courseID`
      request.input('GUID', id)
      request.input('courseID', courseID)
      contexts = await request.query(query)
      recordCount = contexts.recordset.length
    } else {
      contexts = await getRows(LearningContextTableName, request, { CourseID: courseID })
      recordCount = contexts.length
    }

    return recordCount === 0
  } catch (error) {
    if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
      return true
    } else {
      throw error
    }
  }
}
