import rabbitmq from '@lcs/rabbitmq'
import type { RpcMessage } from '@tess-f/shared-config/dist/types/rpc-message.js'
import type { RpcResponse } from '@tess-f/shared-config/dist/types/rpc-response.js'
import settings from '../../../config/settings.js'
import type { EvaluationConnection } from '@tess-f/sql-tables/dist/evaluations/evaluation-connection.js'

export default async function removeEvaluationConnection (request: Pick<EvaluationConnection, 'EvaluationId' | 'ReferenceId' | 'SystemId'>): Promise<RpcResponse<unknown>> {
  const message: RpcMessage<Pick<EvaluationConnection, 'EvaluationId' | 'ReferenceId' | 'SystemId'>> = {
    command: settings.amqp.service_commands.evaluations.removeEvaluationConnection,
    data: request
  }

  return await rabbitmq.executeRPC(
    settings.amqp.service_queues.evaluations,
    message,
    settings.amqp.command_timeout
  )
}
