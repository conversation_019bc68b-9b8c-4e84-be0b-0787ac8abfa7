import create from '../../../services/mssql/learning-objects/create.service.js'
import logger from '@lcs/logger'
import LearningObject, { createLearningObjectSchema } from '../../../models/learning-object.model.js'
import { RpcMessage } from '@tess-f/shared-config/dist/types/rpc-message.js'
import { LearningObjectJson } from '@tess-f/lms/dist/common/learning-object.js'
import { RpcResponse } from '@tess-f/shared-config/dist/types/rpc-response.js'
import { getErrorMessage, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-AMQP.create-learning-object')

export default async function (req: RpcMessage<LearningObjectJson>): Promise<RpcResponse<LearningObjectJson>> {
  try {
    const learningObject = new LearningObject(createLearningObjectSchema.parse(req.data))
    const { CreatedBy } = z.object({ CreatedBy: zodGUID }).parse(req.data)
    learningObject.fields.CreatedOn = new Date()
    learningObject.fields.CreatedBy = CreatedBy

    const created = await create(learningObject)

    log('info', 'Successfully created learning object', { id: created.fields.ID, success: true })

    return {
      success: true,
      data: created.fields
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to created learning object: input validation error', { errorMessage: error.message, success: false })
      return {
        success: false,
        message: zodErrorToMessage(error, 'Invalid request data: ')
      }
    }
    else
    {
      const errorMessage = getErrorMessage(error)
    log('error', 'Failed to create learning object.', { errorMessage, success: false })

    return {
      success: false,
      message: errorMessage
           }
    }
  }
}
