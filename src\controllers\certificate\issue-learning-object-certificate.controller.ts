import logger from '@lcs/logger'
import LearnerProgress from '../../models/learner-progress.model.js'
import getLearningObject from '../../services/mssql/learning-objects/get.service.js'
import createCertificateService from '../../services/file/create-certificate.service.js'
import saveCertificateService from '../../services/amqp/file/save-certificate.service.js'
import updateRecord from '../../services/mssql/learner-progress/update-record-only.service.js'
import getUserById from '../../services/mssql/users/get-by-id.service.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Controller-Certificate.issue-learning-object-certificate')

export default async function (learnerProgress: LearnerProgress) {
  // first we need to get the learning object for this learning progress and check if it issues certs
  const learningObject = await getLearningObject(learnerProgress.fields.LearningObjectID!)
  if (learningObject.fields.EnableCertificates) {
    // this object does issue certificates let's make one for the uer
    // get the user
    const user = await getUserById(learnerProgress.fields.UserID!)

    const pdf = await createCertificateService(`${user.FirstName} ${user.LastName}`, learningObject.fields.Title!, learnerProgress.fields.CompletedDate ?? learnerProgress.fields.CreatedOn!)

    if (pdf) {
      try {
        const contentID = await saveCertificateService(pdf, `${user.FirstName}_${user.LastName}_Completion_Certificate_${learningObject.fields.Title?.split(' ').join('_')}`)
        learnerProgress.fields.Certificate = contentID
      } catch (error) {
        log('error', 'Failed to save certificate for learning object completion', { errorMessage: getErrorMessage(error), userId: user.ID, learningObjectId: learningObject.fields.ID, progressId: learnerProgress.fields.ID, success: false })
        return
      }
    }

    await updateRecord(learnerProgress)
  }
}
