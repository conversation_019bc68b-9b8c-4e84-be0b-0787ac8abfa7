const { expect } = require("chai");
const validator = require("./validator.utils");

describe("Validator Utils ", () => {
  it("should expect object to exist", done => {
    const obj = {};
    expect(validator.exists(obj)).to.be.true;
    done();
  });
  it("should expect object not to exist", done => {
    const obj = null;
    expect(validator.exists(obj)).to.be.false;
    done();
  });
  it("should expect array to exist", done => {
    const obj = ['item'];
    expect(validator.isArray(obj)).to.be.true;
    done();
  });
  it("should expect array not to exist", done => {
    const obj = {};
    expect(validator.isArray(obj)).to.be.false;
    done();
  });

  it("should expect function to exist", done => {
    const obj = { func: ()=>{} };
    expect(validator.isFunctionDefined(obj, "func")).to.be.true;
    done();
  });
  it("should expect guid to be valid", done => {
    const obj = "93662BC9-5C8B-42E4-8314-FD3BC66052DD";
    expect(validator.isGUID(obj)).to.be.true;
    done();
  });
  it("should expect object property to be defined", done => {
    const obj = {
        Title: ''
    };
    expect(validator.isPropDefined(obj, "Title")).to.be.true;
    done();
  });
  it("should expect object property not to be defined", done => {
    const obj = {};
    expect(validator.isPropDefined(obj, "Title")).to.be.false;
    done();
  });
  it("should expect object property to be an string", done => {
    const obj = {
        Title: "BA71751C-F1BB-4B0B-9D69-B2057A7A6ACA"
    };
    expect(validator.isPropString(obj, "Title")).to.be.true;
    done();
  });
  it("should expect object property not to be a string", done => {
    const obj = {
        Title: 0
    };
    expect(validator.isPropString(obj, "Title")).to.be.false;
    done();
  });
  it("should expect object property to be a number", done => {
    const obj = {
        Views: 1.2
    };
    expect(validator.isPropNumber(obj, "Views")).to.be.true;
    done();
  });
  it("should expect object property not to be a number", done => {
    const obj = {};
    expect(validator.isPropNumber(obj, "Views")).to.be.false;
    done();
  });
  
  it("should expect object property to be an int", done => {
    const obj = {
        Views: 1
    };
    expect(validator.isPropInt(obj, "Views")).to.be.true;
    done();
  });
  it("should expect object property not to be an int", done => {
    const obj = {
        Views: 1.2
    };
    expect(validator.isPropInt(obj, "Views")).to.be.false;
    done();
  });
  it("should expect object property to be a date", done => {
    const obj = {
        Created: new Date()
    };
    expect(validator.isPropDate(obj, "Created")).to.be.true;
    done();
  });
  it("should expect object property not to be a date", done => {
    const obj = {
        Created: {}
    };
    expect(validator.isPropDate(obj, "Created")).to.be.false;
    done();
  });
  it("should expect object property to be a boolean", done => {
    const obj = {
        Enabled: true
    };
    expect(validator.isPropBoolean(obj, "Enabled")).to.be.true;
    done();
  });
  it("should expect object property not to be a boolean", done => {
    const obj = {};
    expect(validator.isPropBoolean(obj, "Enabled")).to.be.false;
    done();
  });
  it("should expect object property to be an object", done => {
    const obj = {
        Node: {}
    };
    expect(validator.isPropObject(obj, "Node")).to.be.true;
    done();
  });
  it("should expect object property to be an object", done => {
    const obj = {
        Node: ''
    };
    expect(validator.isPropObject(obj, "Node")).to.be.false;
    done();
  });

});
