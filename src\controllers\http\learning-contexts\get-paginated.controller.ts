import getPaginated from '../../../services/mssql/learning-context/get-paginated.service.js'
import logger from '@lcs/logger'
import getUserFavorite from '../../../services/mssql/learning-context-favorites/get.service.js'
import getUserBookmark from '../../../services/mssql/learning-context-bookmarks/get.service.js'
import { getUserRatingForContext as getUserRating } from '../../../services/mssql/learning-context-ratings/get.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import getPrereqsForContext from '../../../services/mssql/course-prerequisite/get-for-context.service.js'
import getNestedContexts from '../../../services/mssql/learning-context/get-nested-contexts.service.js'
import getRatingForContext from '../../../services/mssql/learning-context-ratings/get-average-for-context.service.js'
import getContentCount from '../../../services/mssql/content/get-content-count-for-context.service.js'
import getUpcomingSessions from '../../../services/mssql/learning-context-sessions/get-upcoming-for-context.service.js'
import { Request, Response } from 'express'
import getParentContextIDs from '../../../services/mssql/learning-context/get-parent-context-ids.service.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'

const log = logger.create('Controller-HTTP.get-paginated-learning-contexts', httpLogTransformer)

/*
    Query options
        types: number[]
        rating: boolean
        offset: number
        limit: number
        search: string
        featured: boolean
        keywords: string[]
        courseTypes: number[]
        prereqs: boolean
        nested: boolean
        sessions: boolean
        labels: string[]
*/
export default async function (req: Request, res: Response) {
  try {
    // #region query params

    const { rating, offset, limit, search, prereqs, nested, sessions, typeIds, labels, courseTypes: courseTypes, keywords, modifiedByIds, createdByIds, visibilities, sortColumn, sortDirection, connectionCount } = z.object({
      rating: z.boolean().optional(),
      offset: zodOffset,
      limit: zodLimit,
      search: z.string().optional(),
      prereqs: z.boolean().optional(),
      nested: z.boolean().optional(),
      sessions: z.boolean().optional(),
      typeIds: z.array(z.nativeEnum(LearningContextTypes)).optional(),
      labels: z.array(z.string()).optional(),
      courseTypes: z.array(z.nativeEnum(CourseTypes)).optional(),
      keywords: z.array(z.string()).optional(),
      modifiedByIds: z.array(zodGUID).optional(),
      createdByIds: z.array(zodGUID).optional(),
      visibilities: z.array(z.nativeEnum(Visibilities)).optional(),
      sortColumn: z.string().optional(),
      sortDirection: z.string().toUpperCase().or(z.literal('ASC')).or(z.literal('DESC')).optional(),
      connectionCount: z.boolean().optional().default(false)
    }).parse(req.body)

    let sortOptions: { sortColumn: string, sortDirection: string } | undefined
    if (sortColumn && sortDirection) {
      sortOptions = { sortColumn, sortDirection }
    }

    // #endregion query params

    // #region get contexts

    const results = await getPaginated(offset, limit, search, { typeIds, labels, courseTypeIds: courseTypes, keywords, modifiedByIds, createdByIds, visibilities }, sortOptions)

    // #endregion get contexts

    // #region map contexts, and add extra data

    const contexts = await Promise.all(results.contexts.map(async _context => {
      const context = _context.fields

      // #region prereqs

      if (prereqs) {
        try {
          const contextPrereqs = await getPrereqsForContext(context.ID!)
          context.Prerequisites = contextPrereqs.map(prereq => prereq.fields)
        } catch (e) {
          const errorMessage = getErrorMessage(e)
          if (e instanceof Error && e.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get prerequisites for context ID: ', { contextId: context.ID, errorMessage, success: false, req })
            throw e
          }
        }
      }

      // #endregion prereqs

      // #region nested contexts

      if (nested) {
        try {
          const nestedContexts = await getNestedContexts(context.ID!)
          context.Contexts = nestedContexts
        } catch (e) {
          const errorMessage = getErrorMessage(e)
          log('error', 'Failed to get nested contexts for context ID: ', { contextId: context.ID, errorMessage, success: false, req })
          throw e
        }
      }

      // #endregion nested contexts

      // #region get rating

      if (rating) {
        try {
          const contextRating = await getRatingForContext(context.ID!)
          context.Rating = contextRating.average
          context.RatingCount = contextRating.count
        } catch (e) {
          const errorMessage = getErrorMessage(e)
          if (e instanceof Error && e.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
            log('error', 'Failed to get rating for context ID: ', { contextId: context.ID, errorMessage, success: false, req })
            throw e
          }
        }
      }

      // #endregion get rating

      // #region content count

      if (context.ContextTypeID === LearningContextTypes.Path || context.ContextTypeID === LearningContextTypes.Collection) {
        try {
          context.ContentCount = await getContentCount(context.ID!)
        } catch (e) {
          const errorMessage = getErrorMessage(e)
          log('error', 'Failed to get content count for context ID: ', { contextId: context.ID, errorMessage, success: false, req })
          throw e
        }
      }

      // #endregion content count

      // #region sessions

      if (context.ContextTypeID === LearningContextTypes.Course &&
                context.CourseTypeID === CourseTypes.InstructorLed &&
                sessions) {
        try {
          const upcomingSessions = await getUpcomingSessions(context.ID!)
          context.Sessions = upcomingSessions.map(session => session.fields)
        } catch (err) {
          const errorMessage = getErrorMessage(err)
          if (err instanceof Error && err.message === DB_Errors.default.NOT_FOUND_IN_DB) {
            context.Sessions = []
          } else {
            log('error', 'Failed to get upcoming sessions for context ID: ', { contextId: context.ID, errorMessage, success: false, req })
            throw err
          }
        }
      }

      // #endregion sessions

      // #region user favorite

      try {
        const userFavorite = await getUserFavorite(context.ID!, req.session.userId)
        context.UserFavorite = userFavorite.fields
      } catch (e) {
        const errorMessage = getErrorMessage(e)
        if (e instanceof Error && e.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
          log('error', 'Failed to get learning context favorite for context ID and UserID', { contextId: context.ID, userId: req.session.userId, errorMessage, success: false, req })
          throw e
        }
      }

      // #endregion user favorite

      // #region user bookmark

      try {
        const userBookmark = await getUserBookmark(context.ID!, req.session.userId)
        context.UserBookmark = userBookmark.fields
      } catch (e) {
        const errorMessage = getErrorMessage(e)
        if (e instanceof Error && e.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
          log('error', 'Failed to get learning context favorite for context ID and UserID', { contextId: context.ID, userId: req.session.userId, errorMessage, success: false, req })
          throw e
        }
      }

      // #endregion user bookmark

      // #region users rating

      try {
        const userRating = await getUserRating(context.ID!, req.session.userId)
        context.UserRating = userRating.fields
      } catch (e) {
        const errorMessage = getErrorMessage(e)
        if (e instanceof Error && e.message !== DB_Errors.default.NOT_FOUND_IN_DB) {
          log('error', 'Failed to get learning context favorite for context ID and UserID', { contextId: context.ID, userId: req.session.userId, errorMessage, success: false, req })
          throw e
        }
      }

      // #endregion users rating

      if (connectionCount) {
        context.UseCount = (await getParentContextIDs(context.ID!)).length
      }

      return context
    }))

    // #endregion map contexts

    log('info', 'Successfully retrieved learning contexts', { count: contexts.length, totalRecords: results.totalRecords, success: true, req })

    res.json({
      totalRecords: results.totalRecords,
      contexts
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('error', 'Failed to get paginated learning contexts: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to get paginated learning contexts', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
