import Location, { createLocationSchema } from '../../../models/location.model.js'
import createLocation from '../../../services/mssql/locations/create.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { ZodError } from 'zod'

const log = logger.create('Controller-HTTP.create-locations', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  try {
    const location = new Location(createLocationSchema.parse(req.body))
    location.fields.CreatedBy = req.session.userId
    location.fields.CreatedOn = new Date()

    const createdLocation = await createLocation(location)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, bookmark a course. Note the time. Check the LMS API log for the 'http-create-learning-context-bookmark' label and message indicating successful creation."
    log('info', 'Successfully created location', { id: createdLocation.fields.ID, success: true, req })

    res.json(createdLocation.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create location due to invalid data in the request.', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else if (errorMessage.startsWith('Violation of UNIQUE KEY constraint')) {
      log('warn', 'Failed to create location because a location with the same name already exists in the database.', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send('Location with the given title already exists')
    } else {
      log('error', 'Failed to create location.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
