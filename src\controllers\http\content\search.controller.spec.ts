import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: search content', () => {
  before(() => logger.init({ level: 'warn' }))
  afterEach(() => sinon.restore())

  it('returns bad request when the filter data is invalid', async () => {
    const controller = await esmock('./search.controller.js')
    const mocks = httpMocks.createMocks({ body: {
      labels: ['test', 1, true],
      objectTypeIds: [30, 'test'],
      keywords: ['test', 1, true],
      sortColumn: 1,
      sortDirection: 'up',
      myLearning: 'test',
      userId: 1
    }})
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('labels.1: Expected string, received number')
    expect(data).to.include('labels.2: Expected string, received boolean')
    expect(data).to.include('objectTypeIds.0: Invalid enum value. Expected 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16, received \'30\'')
    expect(data).to.include('objectTypeIds.1: Invalid enum value. Expected 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16, received \'test\'')
    expect(data).to.include('keywords.1: Expected string, received number')
    expect(data).to.include('keywords.2: Expected string, received boolean')
    expect(data).to.include('sortColumn: Expected string, received number')
    expect(data).to.include('sortDirection: Invalid enum value. Expected \'asc\' | \'desc\' | \'ASC\' | \'DESC\', received \'up\'')
    expect(data).to.include('myLearning: Invalid enum value. Expected \'enrolled\' | \'favorites\' | \'my plan\' | \'in progress\' | \'assigned\' | \'completed\', received \'test\'')
    expect(data).to.include('userId: Expected string, received number')
  })

  it('returns bad request when the search params in the request body are invalid', async () => {
    const controller = await esmock('./search.controller.js')
    const mocks = httpMocks.createMocks({ body: {
      search: 1
    }, session: { userId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('search: Expected string, received number')
  })

  it('returns bad request when the mapper params in the request body are invalid', async () => {
    const controller = await esmock('./search.controller.js')
    const mocks = httpMocks.createMocks({ body: {
      mappers: {
        rating: 'yes',
        bookmark: 1,
        favorite: 'test',
        duration: 3,
        completion: 'no',
        dueDate: 'yes',
        enrolledSessions: 'yes'
      }
    }, session: { userId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('rating: Expected boolean, received string')
    expect(data).to.include('bookmark: Expected boolean, received number')
    expect(data).to.include('favorite: Expected boolean, received string')
    expect(data).to.include('duration: Expected boolean, received number')
    expect(data).to.include('completion: Expected boolean, received string')
    expect(data).to.include('dueDate: Expected boolean, received string')
    expect(data).to.include('enrolledSessions: Expected boolean, received string')
  })

  it('should search the content', async () => {
    const controller = await esmock('./search.controller.js', {
      '../../../services/mssql/content/search.service.js': {
        default: sinon.stub().returns(Promise.resolve({
          totalRecords: 1,
          elements: []
        }))
      },
      '../../../mappers/learning-element-extras.mapper.js': {
        default: sinon.stub().resolves()
      }
    })
    const mocks = httpMocks.createMocks({ body: {
      search: 'test'
    }, session: { userId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.not.be.undefined
    expect(data.totalRecords).to.equal(1)
    expect(data.elements).to.not.be.undefined
    expect(data.elements).to.be.an('array')
    expect(data.elements.length).to.equal(0)
  })

  it('can gracefully handle an error', async () => {
    const controller = await esmock('./search.controller.js', {
      '../../../services/mssql/content/search.service.js': {
        default: sinon.stub().rejects(new Error('unknown error'))
      }
    })
    const mocks = httpMocks.createMocks({ body: {
      search: 'test'
    }, session: { userId: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })
})
