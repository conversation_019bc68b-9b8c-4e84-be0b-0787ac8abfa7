import mssql from '@lcs/mssql-utility'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContextTableName, LearningContextFields, LearningContext } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

export default async function (): Promise<LearningContextModel[]> {
  // This service will fetch the top level learning contexts (folders) of the catalog
  // Top level contexts (folders) have no parent and are of context type section or cmi5 course
  const query = `
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE (
      [${LearningContextFields.ContextTypeID}] = ${LearningContextTypes.Section}
      OR [${LearningContextFields.ContextTypeID}] = ${LearningContextTypes.CMI5Course}
    )
    AND [${LearningContextFields.ParentContextID}] IS NULL
    ORDER BY [${LearningContextFields.Label}] ASC, [${LearningContextFields.Title}] ASC
  `
  const records = await mssql.getPool().request().query<LearningContext>(query)
  return records.recordset.map(record => new LearningContextModel(undefined, record))
}
