import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextModel from '../../../models/learning-context.model'


describe('HTTP get-ilt-context-without-session-for-assignment controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-ilt-context-without-session-for-assignment.controller', {
            '../../../services/mssql/learning-context/get-ILT-contexts-without-sessions-for-assignment.service.js': {
                default: Sinon.stub().returns(Promise.resolve([new LearningContextModel()]))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
        expect(mocks.res._getJSONData()).to.be.an('array')

    })


    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-ilt-context-without-session-for-assignment.controller', {
            '../../../services/mssql/learning-context/get-ILT-contexts-without-sessions-for-assignment.service.js': {
                default: Sinon.stub().returns(Promise.resolve([new LearningContextModel()]))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).to.include('Invalid request parameter data')
        expect(mocks.res._getData()).to.include('Expected string, received boolean')
        expect(mocks.res._getData()).to.include('id')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-ilt-context-without-session-for-assignment.controller', {
            '../../../services/mssql/learning-context/get-ILT-contexts-without-sessions-for-assignment.service.js': {
                default: Sinon.stub().rejects(Promise.resolve([new LearningContextModel()]))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })
  
    

})