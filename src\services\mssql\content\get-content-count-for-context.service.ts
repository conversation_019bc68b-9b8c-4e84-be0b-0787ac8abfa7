import mssql from '@lcs/mssql-utility'
import { LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'

export default async function (contextID: string): Promise<number> {
  let count = 0

  const pool = mssql.getPool()

  // #region count the number contexts that belong to this context

  const request = pool.request()

  request.input('parentID', contextID)

  const connectedQuery = `
            SELECT COUNT(${LearningContextConnectionFields.ConnectedContextID}) AS ContextCount
            FROM [${LearningContextConnectionsTableName}]
            WHERE [${LearningContextConnectionFields.ParentContextID}] = @parentID
        `

  const connectedResults = await request.query<{ ContextCount: number }>(connectedQuery)

  count += connectedResults.recordset[0].ContextCount

  // #endregion

  // #region count the number of learning objects that are a part of this context

  const objectQuery = `
            SELECT COUNT (${LearningObjectContextFields.LearningContextID}) as ObjectCount
            FROM [${LearningObjectContextsTableName}]
            WHERE [${LearningObjectContextFields.LearningContextID}] = @parentID
        `

  const objectResults = await request.query<{ ObjectCount: number }>(objectQuery)

  count += objectResults.recordset[0].ObjectCount

  // #endregion

  return count
}
