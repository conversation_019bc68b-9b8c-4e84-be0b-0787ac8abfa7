import { Table } from '@lcs/mssql-utility'
import { GroupClaim, GroupClaimFields, GroupClaimsTableName } from '@tess-f/sql-tables/dist/lms/group-claim.js'

export default class GroupClaimModel extends Table<GroupClaim, GroupClaim> {
  public fields: GroupClaim

  constructor (fields?: GroupClaim) {
    super(GroupClaimsTableName, [
      GroupClaimFields.Claim,
      GroupClaimFields.GroupID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: GroupClaim): void {
    this.fields = record
  }

  public exportJsonToDatabase (): GroupClaim {
    return this.fields
  }
}
