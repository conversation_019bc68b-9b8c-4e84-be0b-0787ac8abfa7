import mssql, { deleteRow } from '@lcs/mssql-utility'
import { LearningContextUserBookmarksTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-bookmark.js'

export default async function (contextID: string, userID: string): Promise<number> {
  const pool = mssql.getPool()
  return await deleteRow(pool.request(), LearningContextUserBookmarksTableName, { LearningContextID: contextID, UserID: userID })
}
