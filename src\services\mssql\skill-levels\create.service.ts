import mssql, { addRow } from '@lcs/mssql-utility'
import SkillLevelModel from '../../../models/skill-level.model.js'
import { SkillLevel } from '@tess-f/sql-tables/dist/lms/skill-level.js'

export default async function (model: SkillLevelModel): Promise<SkillLevelModel> {
  const pool = mssql.getPool()
  const record = await addRow<SkillLevel>(pool.request(), model)
  return new SkillLevelModel(record)
}
