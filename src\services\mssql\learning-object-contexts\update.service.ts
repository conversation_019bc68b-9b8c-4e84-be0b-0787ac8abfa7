import mssql, { updateRow } from '@lcs/mssql-utility'
import LearningObjectContextModel from '../../../models/learning-object-context.model.js'
import { LearningObjectContext } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'

export default async function (objectContext: LearningObjectContextModel): Promise<LearningObjectContextModel> {
  const pool = mssql.getPool()
  const records = await updateRow<LearningObjectContext>(pool.request(), objectContext, { LearningObjectID: objectContext.fields.LearningObjectID, LearningContextID: objectContext.fields.LearningContextID })
  return new LearningObjectContextModel(records[0])
}
