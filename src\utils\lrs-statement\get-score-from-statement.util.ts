import Statement from '../../models/amqp/lrs/statement.model.js'

export default function getScore (statement: Statement): number | undefined {
  if (statement.result?.score) {
    if (statement.result.score.raw !== null && statement.result.score.raw !== undefined) {
      return statement.result.score.raw
    } else if (statement.result.score.scaled !== null && statement.result.score.scaled !== undefined) {
      return statement.result.score.scaled * 100
    }
  }
  return undefined
}
