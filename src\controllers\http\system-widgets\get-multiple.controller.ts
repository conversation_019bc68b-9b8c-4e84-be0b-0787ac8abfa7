import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import getMultipleGroupsByIds from '../../../services/mssql/groups/get-multiple-by-ids.service.js'
import getGroupsForSystemWidgetsService from '../../../services/mssql/system-widget-groups/get-multiple.service.js'
import getMultipleSystemWidgets from '../../../services/mssql/system-widgets/get-multiple.service.js'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-multiple-system-widgets', httpLogTransformer)

export default async function getMultipleSystemWidgetsController (req: Request, res: Response) {
  try {
    const { limit, offset, search, groupIDs, everyone, published } = z.object({
      limit: zodLimit,
      offset: zodOffset,
      search: z.string().optional(),
      groupIDs: z.array(zodGUID).optional(),
      everyone: z.boolean().optional(),
      published: z.boolean().optional()
    }).parse(req.body)

    const systemWidgets = await getMultipleSystemWidgets(offset, limit, search, groupIDs, everyone, published)

    log('info', 'Successfully fetched system widgets', { total: systemWidgets.totalRecords, count: systemWidgets.systemWidgets.length, success: true, req })

    systemWidgets.systemWidgets = await Promise.all(systemWidgets.systemWidgets.map(async systemWidget => {
      if (!systemWidget.fields.Everyone) {
        try {
          const systemGroups = await getGroupsForSystemWidgetsService(systemWidget.fields.ID!)
          systemWidget.fields.Groups = await getMultipleGroupsByIds(systemGroups.map(group => group.fields.GroupID!))
        } catch (error) {
          const message = getErrorMessage(error)
          if (message === dbErrors.default.NOT_FOUND_IN_DB) {
            systemWidget.fields.Groups = []
          } else {
            throw error
          }
        }
      }
      return systemWidget
    }))

    res.json({
      totalRecords: systemWidgets.totalRecords,
      systemWidgets: systemWidgets.systemWidgets.map(widget => widget.fields)
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to fetch system widgets: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to fetch system widgets', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
