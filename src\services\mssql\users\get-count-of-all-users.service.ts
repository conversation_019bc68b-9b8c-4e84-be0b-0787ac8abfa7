import mssql from '@lcs/mssql-utility'
import { GhostUserId, UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'

export default async function getCountOfAllUsers (): Promise<number> {
  const results = await mssql.getPool().request().query<{UserCount: number}>(`
    SELECT COUNT(*) AS UserCount
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] != '${GhostUserId}'
  `)
  return results.recordset[0].UserCount
}
