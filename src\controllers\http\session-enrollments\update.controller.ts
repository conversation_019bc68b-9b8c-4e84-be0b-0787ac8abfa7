import { Request, Response } from 'express'
import SessionEnrollment, { sessionEnrollmentSchema } from '../../../models/session-enrollment.model.js'
import updateSessionEnrollment from '../../../services/mssql/session-enrollments/update.service.js'
import logger from '@lcs/logger'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { ZodError } from 'zod'

const log = logger.create('Controller-HTTP.create-learning-context-session-enrollments', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  let sessionId

  try {
    const sessionEnrollment = new SessionEnrollment(sessionEnrollmentSchema.parse(req.body))
    sessionEnrollment.fields.ModifiedBy = req.session.userId
    sessionEnrollment.fields.ModifiedOn = new Date()

    const updatedSessionEnrollment = await updateSessionEnrollment(sessionEnrollment)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, bookmark a course. Note the time. Check the LMS API log for the 'http-create-learning-context-bookmark' label and message indicating successful creation."
    log('info', 'Successfully updated learning context session enrollment for session and user', { success: true, sessionId, userId: sessionEnrollment.fields.UserID, req })

    res.json(updatedSessionEnrollment.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof ZodError) {
      log('warn', 'Failed to update learning context session enrollment due to invalid data in the request.', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      const errorMessage = getErrorMessage(error)
      if (errorMessage.startsWith('Violation of UNIQUE KEY constraint')) {
        log('warn', 'Failed to update learning context session enrollments for session and user, session enrollment already exists.', { sessionId, error, success: false, req })
      } else {
        log('error', 'Failed to update learning context session enrollments.', { error, success: false, req })
      }
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
