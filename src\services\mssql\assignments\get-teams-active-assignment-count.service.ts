import mssql from '@lcs/mssql-utility'
import { UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { UserInProgressAssignmentFields, UserInProgressAssignmentsViewName } from '@tess-f/sql-tables/dist/lms/user-in-progress-assignmnets-view.js'
import { UserNotStartedAssignmentFields, UserNotStartedAssignmentsViewName } from '@tess-f/sql-tables/dist/lms/user-not-started-assignments-view.js'
import { UserOverdueAssignmentFields, UserOverdueAssignmentsViewName } from '@tess-f/sql-tables/dist/lms/user-overdue-assignments-view.js'

export default async function getTeamsActiveAssignmentCount (managerId: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('managerId', managerId)

  const result = await request.query<{ ActiveCount: number }>(`
    SELECT COUNT(*) AS [ActiveCount]
    FROM (
      SELECT *
      FROM [${UserInProgressAssignmentsViewName}]
      WHERE [${UserInProgressAssignmentFields.UserID}] IN (
        SELECT [${UserFields.ID}]
        FROM [${UserTableName}]
        WHERE [${UserFields.ManagerID}] = @managerId
      )

      UNION ALL

      SELECT *
      FROM [${UserNotStartedAssignmentsViewName}]
      WHERE [${UserNotStartedAssignmentFields.UserID}] IN (
        SELECT [${UserFields.ID}]
        FROM [${UserTableName}]
        WHERE [${UserFields.ManagerID}] = @managerId
      )

      UNION ALL

      SELECT *
      FROM [${UserOverdueAssignmentsViewName}]
      WHERE [${UserOverdueAssignmentFields.UserID}] IN (
        SELECT [${UserFields.ID}]
        FROM [${UserTableName}]
        WHERE [${UserFields.ManagerID}] = @managerId
      )
    ) AS activeAssignments
  `)

  return result.recordset[0].ActiveCount
}
