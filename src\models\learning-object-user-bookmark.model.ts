import { Table } from '@lcs/mssql-utility'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { LearningObjectUserBookmark, LearningObjectUserBookmarkFields, LearningObjectUserBookmarksTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-bookmark.js'
import { z } from 'zod'

export const LearningObjectBookmarkSchema = z.object({
  [LearningObjectUserBookmarkFields.UserID]: zodGUID,
  [LearningObjectUserBookmarkFields.LearningObjectID]: zodGUID
})

export default class LearningObjectUserBookmarkModel extends Table<LearningObjectUserBookmark, LearningObjectUserBookmark> {
  public fields: LearningObjectUserBookmark

  constructor (fields?: LearningObjectUserBookmark) {
    super(LearningObjectUserBookmarksTableName, [
      LearningObjectUserBookmarkFields.UserID,
      LearningObjectUserBookmarkFields.LearningObjectID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearningObjectUserBookmark): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningObjectUserBookmark {
    return this.fields
  }
}
