import mssql from '@lcs/mssql-utility'
import { LearningContextConnection, LearningContextConnectionFields } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'
import { FlatLearningContextTreeFields, FlatLearningContextTreeViewName } from '@tess-f/sql-tables/dist/lms/flat-learning-context-view.js'

export type ContextConnection = Required<Pick<LearningContextConnection, 'ParentContextID' | 'ConnectedContextID' | 'OrderID'>>

export async function byConnectedContextID (connectedContextID: string): Promise<ContextConnection[]> {
  const request = mssql.getPool().request()
  request.input('connectedContextID', connectedContextID)

  const result = await request.query<ContextConnection>(`
    SELECT [${FlatLearningContextTreeFields.ID}] AS [${LearningContextConnectionFields.ConnectedContextID}],
      [${FlatLearningContextTreeFields.ParentContextID}],
      [${FlatLearningContextTreeFields.OrderID}]
    FROM [${FlatLearningContextTreeViewName}]
    WHERE [${FlatLearningContextTreeFields.ID}] = @connectedContextID
  `)

  return result.recordset
}
