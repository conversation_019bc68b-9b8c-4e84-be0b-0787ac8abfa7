import { Table } from '@lcs/mssql-utility'
import { LearningContextObjective, LearningContextObjectiveFields, LearningContextObjectiveTableName } from '@tess-f/sql-tables/dist/lms/learning-context-objective.js'

export default class LearningContextObjectiveModel extends Table<LearningContextObjective, LearningContextObjective> {
  fields: LearningContextObjective

  constructor (fields: LearningContextObjective) {
    super(
      LearningContextObjectiveTableName, [
        LearningContextObjectiveFields.LearningContextId,
        LearningContextObjectiveFields.ObjectiveId
      ]
    )

    this.fields = fields
  }

  importFromDatabase (record: LearningContextObjective): void {
    this.fields = {
      LearningContextId: record.LearningContextId,
      ObjectiveId: record.ObjectiveId,
      OrderId: record.OrderId
    }
  }

  exportJsonToDatabase (): LearningContextObjective {
    return {
      LearningContextId: this.fields.LearningContextId,
      ObjectiveId: this.fields.ObjectiveId,
      OrderId: this.fields.OrderId
    }
  }
}
