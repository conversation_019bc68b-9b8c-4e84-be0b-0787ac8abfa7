import update from '../../../services/mssql/learning-objects/update.service.js'
import LearningObject, { updateLearningObjectSchema } from '../../../models/learning-object.model.js'
import logger from '@lcs/logger'
import { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { RpcMessage } from '@tess-f/shared-config/dist/types/rpc-message.js'
import { LearningObjectJson } from '@tess-f/lms/dist/common/learning-object.js'
import { RpcResponse } from '@tess-f/shared-config/dist/types/rpc-response.js'
import { getErrorMessage, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-AMQP.update-learning-object')

export default async function (req: RpcMessage<LearningObjectJson>): Promise<RpcResponse<LearningObjectJson>> {
  try {
    const learningObject = new LearningObject(updateLearningObjectSchema.parse(req.data))
    learningObject.fields.ModifiedOn = new Date()
    const { ModifiedBy, ID } = z.object({ ModifiedBy: zodGUID, ID: zodGUID }).parse(req.data)
    learningObject.fields.ID = ID
    learningObject.fields.ModifiedBy = ModifiedBy

    const result = await update(learningObject)

    const updated = result.fields

    log('info', 'Successfully updated learning object', { success: true })

    return {
      success: true,
      data: updated
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to update learning object: input validation error', { errorMessage: error.message, success: false })
      return {
        success: false,
        message: zodErrorToMessage(error, 'Invalid request data: ')
      }
    }

    const errorMessage = getErrorMessage(error)
    if (errorMessage === dbErrors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to update learning object because it was not found in the database.', { id: req.data.ID, success: false })
    } else if (errorMessage === 'Bad object keyword') {
      log('warn', 'Failed to update learning object due to a bad keyword.', { keywords: req.data.Keywords, success: false })
    } else {
      log('error', 'Failed to update learning object', { id: req.data.ID, errorMessage, success: false })
    }
    return {
      success: false,
      message: errorMessage
    }
  }
}
