import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'

import update from './update-user-learning-object-status.service.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { Assignment } from '@tess-f/sql-tables/dist/lms/assignment.js'
import AssignmentsModel from '../../../models/assignment.model.js'
import { AssignmentTypes } from '@tess-f/sql-tables/dist/lms/assignment-type.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObject, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import UserAssignedLearningObjectModel from '../../../models/user-assigned-learning-object.model.js'
import { LearnerProgress, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningContextSession, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import { SessionStatuses } from '@tess-f/sql-tables/dist/lms/session-status.js'
import { LearningContext, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { v4 as uuidv4 } from 'uuid'

let assignment: Assignment
let learningObject: LearningObject
let learningContext: LearningContext
let learnerProgress: LearnerProgress
let learningContextSession: LearningContextSession

let assignedObject: UserAssignedLearningObjectModel

// FIXME: need to update the set up for these tests
xdescribe('MSSQL User Assigned Learning Objects', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    assignment = await addRow<Assignment>(pool.request(), new AssignmentsModel({
      Title: 'Running user assigned learning object MSSQL unit test',
      TypeID: AssignmentTypes.Manual,
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    }))
    learningObject = await addRow<LearningObject>(pool.request(), new LearningObjectModel({
      Title: 'Test user assigned learning objects',
      Description: 'Running user-assigned-learning-objects',
      ContentID: uuidv4(),
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      LearningObjectTypeID: LearningObjectTypes.PDF
    }))
    assignedObject = new UserAssignedLearningObjectModel({
      AssignmentID: assignment.ID,
      UserID: AdminUserId,
      LearningObjectID: learningObject.ID,
      CreatedOn: new Date()
    })
    // create course and session.
    learningContext = await addRow<LearningContext>(pool.request(), new LearningContextModel({
      Title: 'Test user assigned learning objects',
      Description: `Running user-assigned-learning-objects on ${new Date()}`,
      ContextTypeID: LearningContextTypes.Course,
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false
    }))
    learningContextSession = await addRow<LearningContextSession>(pool.request(), new LearningContextSessionModel({
      SessionID: 'user-assigned-learning-object session',
      DisplayEnrollmentsOnHomePage: false,
      EnableWaitlist: false,
      SessionStatusID: SessionStatuses.Hidden,
      StartDate: new Date(),
      EndDate: new Date(),
      CreatedOn: new Date(),
      CreatedBy: AdminUserId,
      LearningContextID: learningContext.ID,
      Timezone: 'UTC'
    }))
    learnerProgress = await addRow<LearnerProgress>(pool.request(), new LearnerProgressModel({
      LessonStatusID: 1,
      UserID: AdminUserId,
      CreatedOn: new Date(),
      LearningContextSessionID: learningContextSession.ID
    }))
  })

  it('can update an assigned learning objects status', async () => {
    const result = await update(AdminUserId, learningObject.ID!, 5, learnerProgress.ID!)
    expect(result).to.gte(1)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<LearningObject>(pool.request(), LearningObjectsTableName, { ID: learningObject.ID })
    await deleteRow<LearningContext>(pool.request(), LearningContextTableName, { ID: learningContext.ID })
    await deleteRow<LearningContextSession>(pool.request(), LearningContextSessionsTableName, { ID: learningContextSession.ID })
    await deleteRow<LearnerProgress>(pool.request(), LearnerProgressTableName, { ID: learnerProgress.ID })
  })
})
