import { Request, Response } from 'express'
import deleteService from '../../../services/mssql/session-enrollments/delete.service.js'
import logger from '@lcs/logger'
import getSessionEnrollments from '../../../services/mssql/session-enrollments/get.service.js'
import getSession from '../../../services/mssql/learning-context-sessions/get.service.js'
import updateSession from '../../../services/mssql/learning-context-sessions/update.service.js'
import { DB_Errors } from '@lcs/mssql-utility'
import moment from 'moment-timezone'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.delete-session-enrollments', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-delete-skill-level' label."
  try {
    const { userID, sessionID } = z.object({
      userID: zodGUID,
      sessionID: zodGUID,
    }).parse(req.params)

    const deleteResult = await deleteService(sessionID, userID)
    
    let allEnrollmentsForSession = []
    try {
      allEnrollmentsForSession = await getSessionEnrollments(sessionID, undefined)
    } catch (error) {
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        allEnrollmentsForSession = []
      } else {
        throw error
      }
    }

    const session = await getSession(sessionID)

    try {
      // if the session hasn't started yet, the course was full and there is now room in the course,
      // set the session statusID to open (2)
      const now = moment.tz(Date.now(), session.fields.Timezone!)
      const start = moment.tz(session.fields.StartDate!, session.fields.Timezone!)
      if (session.fields.SessionStatusID === 3 &&
                start.isAfter(now) &&
                (session.fields.MaxEnrollments && session.fields.MaxEnrollments > allEnrollmentsForSession.length)) {
        session.fields.SessionStatusID = 2
        await updateSession(session)
      }
    } catch (error) {
      log('error', 'Failed to update session on enrollment deletion for user', { sessionID, userID, success: false, req })
      throw error
    }

    if (deleteResult > 0) {
      // STIG V-69427 changing data (success)
      // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-delete-skill-level' label and message indicating successful deletion."
      log('info', 'Successfully deleted session enrollment with ID for user ', { sessionID, userID, req })
      res.sendStatus(httpStatus.NO_CONTENT)
    } else {
      // STIG V-69427 changing data (failure)
      log('warn', 'Failed to delete session enrollment with ID for user because it was not found in the database.', { sessionID, userID, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    }
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete session enrollment due to invalid data in the request.', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameters, '))
    } else {
      log('error', 'Failed to delete session enrollment.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
