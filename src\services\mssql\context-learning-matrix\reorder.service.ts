import mssql from '@lcs/mssql-utility'
import { LearningContextConnectionFields, LearningContextConnectionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-connection.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { Request } from 'mssql'

export default async function (parentID: string, itemID: string, oldOrderID: number, newOrderID: number) {
  const pool = mssql.getPool()
  const request = pool.request()
  // set the request inputs
  request.input('parentID', parentID)
  request.input('itemID', itemID)
  request.input('oldOrderID', oldOrderID)
  request.input('newOrderID', newOrderID)
  // old order id less than new order id move item down the list
  if (oldOrderID < newOrderID) {
    await moveItemDown(request)
  }
  // old order id more than new order id move item up the list
  if (oldOrderID > newOrderID) {
    await moveItemUp(request)
  }
  // set the new order id
  await setNewOrderID(request)
}

async function moveItemDown (request: Request) {
  // move the items in the list up since the item is moving down
  await request.query(`
    UPDATE [${LearningContextTableName}]
    SET [${LearningContextFields.OrderID}] = [${LearningContextFields.OrderID}] - 1
    WHERE [${LearningContextFields.OrderID}] <= @newOrderID
    AND [${LearningContextFields.OrderID}] > @oldOrderID
    AND [${LearningContextFields.ParentContextID}] = @parentID
  `)
  await request.query(`
    UPDATE [${LearningObjectContextsTableName}]
    SET [${LearningObjectContextFields.OrderID}] = [${LearningObjectContextFields.OrderID}] - 1
    WHERE [${LearningObjectContextFields.OrderID}] <= @newOrderID
    AND [${LearningObjectContextFields.OrderID}] > @oldOrderID
    AND [${LearningObjectContextFields.LearningContextID}] = @parentID
  `)
  await request.query(`
    UPDATE [${LearningContextConnectionsTableName}]
    SET [${LearningContextConnectionFields.OrderID}] = [${LearningContextConnectionFields.OrderID}] - 1
    WHERE [${LearningContextConnectionFields.OrderID}] <= @newOrderID
    AND [${LearningContextConnectionFields.OrderID}] > @oldOrderID
    AND [${LearningContextConnectionFields.ParentContextID}] = @parentID
  `)
}

async function moveItemUp (request: Request) {
  // move the items in the list down since the item is moving up
  await request.query(`
    UPDATE [${LearningContextTableName}]
    SET [${LearningContextFields.OrderID}] = [${LearningContextFields.OrderID}] + 1
    WHERE [${LearningContextFields.OrderID}] >= @newOrderID
    AND [${LearningContextFields.OrderID}] < @oldOrderID
    AND [${LearningContextFields.ParentContextID}] = @parentID
  `)
  await request.query(`
    UPDATE [${LearningObjectContextsTableName}]
    SET [${LearningObjectContextFields.OrderID}] = [${LearningObjectContextFields.OrderID}] + 1
    WHERE [${LearningObjectContextFields.OrderID}] >= @newOrderID
    AND [${LearningObjectContextFields.OrderID}] < @oldOrderID
    AND [${LearningObjectContextFields.LearningContextID}] = @parentID
  `)
  await request.query(`
    UPDATE [${LearningContextConnectionsTableName}]
    SET [${LearningContextConnectionFields.OrderID}] = [${LearningContextConnectionFields.OrderID}] + 1
    WHERE [${LearningContextConnectionFields.OrderID}] >= @newOrderID
    AND [${LearningContextConnectionFields.OrderID}] < @oldOrderID
    AND [${LearningContextConnectionFields.ParentContextID}] = @parentID
  `)
}

async function setNewOrderID (request: Request) {
  // set the order ID of the item we are moving
  await request.query(`
    UPDATE [${LearningContextTableName}]
    SET [${LearningContextFields.OrderID}] = @newOrderID
    WHERE [${LearningContextFields.ID}] = @itemID
    AND [${LearningContextFields.ParentContextID}] = @parentID
  `)
  await request.query(`
    UPDATE [${LearningObjectContextsTableName}]
    SET [${LearningObjectContextFields.OrderID}] = @newOrderID
    WHERE [${LearningObjectContextFields.LearningContextID}] = @parentID
    AND [${LearningObjectContextFields.LearningObjectID}] = @itemID
  `)
  await request.query(`
    UPDATE [${LearningContextConnectionsTableName}]
    SET [${LearningContextConnectionFields.OrderID}] = @newOrderID
    WHERE [${LearningContextConnectionFields.ParentContextID}] = @parentID
    AND [${LearningContextConnectionFields.ConnectedContextID}] = @itemID
  `)
}
