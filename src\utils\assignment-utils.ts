import getIdsOfUsersInGroup from '../services/mssql/groups/get-ids-of-users-in-group.service.js'
import Assignment from '../models/assignment.model.js'
import AssignmentUser from '../models/assignment-users.model.js'

export async function mapUsersFromAssignment (assignment: Assignment, users: AssignmentUser[], groupUserIDs: string[]) {
  if (assignment.fields.Users) {
    assignment.fields.Users.forEach(user => {
      users.push(new AssignmentUser({ UserID: user.ID }))
    })
  }

  if (assignment.fields.Groups) {
    for (const assignmentGroup of assignment.fields.Groups) {
      users.push(new AssignmentUser({ GroupID: assignmentGroup.ID }))
      // get the users for the group
      const groupUsers = await getIdsOfUsersInGroup(assignmentGroup.ID!)
      groupUsers.forEach(userId => {
        if (!groupUserIDs.includes(userId) && !users.some(user => user.fields.UserID === userId)) {
          groupUserIDs.push(userId)
        }
      })
    }
  }
}
