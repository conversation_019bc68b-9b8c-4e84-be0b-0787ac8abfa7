import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { LocationFields, LocationsTableName } from '@tess-f/sql-tables/dist/lms/location.js'
import LocationModel from '../../../models/location.model.js'

export default async function (offset = 0, limit = 10, search?: string): Promise<{ totalRecords: number, locations: LocationModel[]}> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('offset', offset)
  request.input('limit', limit)

  let query = `
    SELECT [${LocationsTableName}].*, TotalRecords = COUNT(*) OVER()
    FROM [${LocationsTableName}]
    WHERE 1 = 1
  `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [LocationFields.Title], 'any')}) `
  }

  query += `
    ORDER BY [${LocationFields.Title}]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `
  const records = await request.query(query)
  return {
    totalRecords: records.recordset.length > 0 ? records.recordset[0].TotalRecords : 0,
    locations: records.recordset.map(record => new LocationModel(record))
  }
}
