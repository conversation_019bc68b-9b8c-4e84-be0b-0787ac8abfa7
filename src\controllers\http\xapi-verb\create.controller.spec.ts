import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import XAPIVerbModel from '../../../models/xapi-verb.model.js'



describe('HTTP create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/xapi-verb/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new XAPIVerbModel({Id: uuid()})))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid()
            },
            body: {
                Verb: 'verb',
                SatisfactionTypeId: 1
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })


    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/xapi-verb/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new XAPIVerbModel({Id: uuid()})))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid()
            },
            body: {
                Verb: false,
                SatisfactionTypeId: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('Verb')
        expect(mocks.res._getData()).include('SatisfactionTypeId')
    })


    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/xapi-verb/create.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new XAPIVerbModel({Id: uuid()})))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid()
            },
            body: {
                Verb: 'verb',
                SatisfactionTypeId: 1
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })


})