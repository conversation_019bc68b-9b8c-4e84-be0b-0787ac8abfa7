import mssql, { addRow } from '@lcs/mssql-utility'
import UserPreference from '../../../models/user-preference.model.js'
import { UserPreferences } from '@tess-f/sql-tables/dist/lms/user-preference.js'

export default async function (model: UserPreference): Promise<UserPreference> {
  const pool = mssql.getPool()
  const created = await addRow<UserPreferences>(pool.request(), model)
  return new UserPreference(created)
}
