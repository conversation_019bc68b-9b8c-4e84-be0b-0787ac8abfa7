import mssql, { getRows } from '@lcs/mssql-utility'
import { LearningContextUserFavorite, LearningContextUserFavoritesTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-favorite.js'
import LearningContextFavoritesModel from '../../../models/learning-context-user-favorite.model.js'

export default async function getUserFavoriteForContext (contextID: string, userID: string): Promise<LearningContextFavoritesModel> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextUserFavorite>(LearningContextUserFavoritesTableName, pool.request(), { LearningContextID: contextID, UserID: userID })
  return new LearningContextFavoritesModel(records[0])
}

export async function getContextFavoritesForUser (userID: string): Promise<LearningContextFavoritesModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextUserFavorite>(LearningContextUserFavoritesTableName, pool.request(), { UserID: userID })
  return records.map(record => new LearningContextFavoritesModel(record))
}

export async function getFortiesForContext (contextID: string): Promise<LearningContextFavoritesModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<LearningContextUserFavorite>(LearningContextUserFavoritesTableName, pool.request(), { LearningContextID: contextID })
  return records.map(record => new LearningContextFavoritesModel(record))
}
