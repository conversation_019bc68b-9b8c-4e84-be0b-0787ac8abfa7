const { expect } = require('chai');
const tester = require('../api/utils/test-agent.utils');
const uuidv4 = require('uuid/v4');
const settings = require('../api/config/settings');

const tmpUserID = uuidv4();
const contextID = 'AD5C499B-6AD5-4C5E-BC48-938527CEA049';

let isolated = false;
let createdRating;

xdescribe('Ratings', () => {


    before( done => {
        if(tester.agent == null) {
            isolated = true;
            tester.create()
            .then( agent => {
                done();
            })
            .catch( err => {
                console.error(err);
                done();
            });
        } else {
            done();
        }
    });

    it('creates a rating', done => {

        tester.agent.post(settings.server.root + 'rating')
            .send({
                UserID: tmpUserID,
                Rating: 3,
                LearningContextID: contextID
            })
            .end((err, res) => {

                expect(res.status).to.equal(200);
                expect(res.body.ID).to.exist;

                createdRating = res.body;

                done();

            });
    });

    it('updates a rating', done => {

        tester.agent.put( settings.server.root + 'rating/' + createdRating.ID )
            .send({
                Rating: 1
            })
            .end((err, res) => {

                expect(res.status).to.equal(200);
                expect(res.body.Rating).to.equal(1);

                done();

            });
    });

    it('gets a rating', done => {

        tester.agent.get(settings.server.root + 'rating?id=' + createdRating.ID )
            .end((err, res) => {

                expect(res.status).to.equal(200);
                expect(res.body.ID).to.equal(createdRating.ID);

                done();

            });
    })

    it('deletes a rating', done => {

        tester.agent.delete(settings.server.root + 'rating/' + createdRating.ID ) 
            .end((err, res) => {

                expect(res.status).to.equal(204);
                done();

            });
    })


    after((done)=>{
        if(isolated) {
            tester.close();
        } 
        done();
    });
})