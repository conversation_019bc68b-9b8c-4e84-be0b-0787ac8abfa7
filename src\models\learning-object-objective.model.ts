import { Table } from '@lcs/mssql-utility'
import { LearningObjectObjective, LearningObjectObjectiveFields, LearningObjectObjectiveTableName } from '@tess-f/sql-tables/dist/lms/learning-object-objective.js'

export default class LearningObjectObjectiveModel extends Table<LearningObjectObjective, LearningObjectObjective> {
  fields: LearningObjectObjective

  constructor (fields: LearningObjectObjective) {
    super(LearningObjectObjectiveTableName, [
      LearningObjectObjectiveFields.LearningObjectId,
      LearningObjectObjectiveFields.ObjectiveId
    ])

    this.fields = fields
  }

  importFromDatabase(record: LearningObjectObjective): void {
    this.fields = {
      LearningObjectId: record.LearningObjectId,
      ObjectiveId: record.ObjectiveId,
      OrderId: record.OrderId
    }
  }

  exportJsonToDatabase(): LearningObjectObjective {
    return {
      LearningObjectId: this.fields.LearningObjectId,
      ObjectiveId: this.fields.ObjectiveId,
      OrderId: this.fields.OrderId
    }
  }
}
