import mssql, { getRows } from '@lcs/mssql-utility'
import { SystemWidgetGroup, SystemWidgetGroupsTableName } from '@tess-f/sql-tables/dist/lms/system-widget-group.js'
import SystemWidgetGroupModel from '../../../models/system-widget-group.model.js'

export default async function getGroupsForSystemWidgetsService (systemID: string): Promise<SystemWidgetGroupModel[]> {
  const pool = mssql.getPool()
  const records = await getRows<SystemWidgetGroup>(SystemWidgetGroupsTableName, pool.request(), { SystemWidgetID: systemID })
  return records.map(record => new SystemWidgetGroupModel(record))
}
