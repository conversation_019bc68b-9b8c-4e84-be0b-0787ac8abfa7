import { Request<PERSON><PERSON><PERSON>, Router } from 'express'
import createWidgetController from './create.controller.js'
import deleteWidgetController from './delete.controller.js'
import getWidgetsController from './get-multiple.controller.js'
import resetController from './reset.controller.js'
import updateWidgetController from './update.controller.js'

const router = Router()

router.post('/widget', createWidgetController as RequestHandler)
router.delete('/widget/:id', deleteWidgetController as RequestHandler)
router.post('/get-widgets', getWidgetsController as RequestHandler)
router.put('/widget', updateWidgetController as RequestHandler)
router.delete('/reset-widgets', resetController as RequestHandler)

export default router
