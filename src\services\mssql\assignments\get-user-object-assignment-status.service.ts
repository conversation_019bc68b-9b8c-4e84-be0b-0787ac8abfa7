import mssql, { DB_Errors } from '@lcs/mssql-utility'
import { AssignmentFields, AssignmentsTableName } from '@tess-f/sql-tables/dist/lms/assignment.js'
import { AssignmentsWithNoContentViewFields, AssignmentsWithNoContentViewName } from '@tess-f/sql-tables/dist/lms/assignments-with-no-content-view.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'
import { AssignmentStatus } from '../../../utils/assignment-status.js'

// Assignment Status
// 1 = Not Started
// 2 = In Progress
// 3 = Completed
// 4 = Overdue

export default async function (objectID: string, assignmentID: string, userID: string, from?: Date, to?: Date): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('objectID', objectID)
  request.input('userID', userID)
  request.input('assignmentID', assignmentID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  const query = `
    WITH Assignment_Status AS (
      SELECT [${UserAssignedLearningObjectFields.AssignmentID}], [${UserAssignedLearningObjectFields.DueDate}], [${UserAssignedLearningObjectFields.LessonStatusID}], [${UserAssignedLearningObjectFields.Deleted}], [${UserAssignedLearningObjectFields.UserID}], [${UserAssignedLearningObjectFields.LearnerProgressID}]
      FROM [${UserAssignedLearningObjectsTableName}]
      WHERE [${UserAssignedLearningObjectFields.UserID}] = @userID
      AND [${UserAssignedLearningObjectFields.LearningObjectID}] = @objectID
      ${from && to ? `AND [${UserAssignedLearningObjectFields.CreatedOn}] BETWEEN @from AND @to` : ''}
    ), Overdue_Status AS (
      SELECT [${AssignmentFields.ID}]
      FROM [${AssignmentsTableName}]
      WHERE [${AssignmentFields.ID}] IN (
        SELECT [${UserAssignedLearningObjectFields.AssignmentID}]
        FROM [Assignment_Status]
        WHERE [${UserAssignedLearningObjectFields.DueDate}] IS NOT NULL
        AND [${UserAssignedLearningObjectFields.DueDate}] < ${to ? '@to' : 'GETDATE()'}
        AND [${UserAssignedLearningObjectFields.LessonStatusID}] < ${LessonStatuses.fail}
        AND [${UserAssignedLearningObjectFields.Deleted}] = 0
      )
    ), Completed_Status AS (
      SELECT [${AssignmentFields.ID}]
      FROM [${AssignmentsTableName}]
      WHERE [${AssignmentFields.ID}] IN (
        SELECT [${UserAssignedLearningObjectFields.AssignmentID}]
        FROM [Assignment_Status]
        WHERE [${UserAssignedLearningObjectFields.LessonStatusID}] >= ${LessonStatuses.fail}
        AND [${UserAssignedLearningObjectFields.Deleted}] = 0
      ) AND [${AssignmentFields.ID}] NOT IN (
        SELECT [${UserAssignedLearningObjectFields.AssignmentID}]
        FROM [Assignment_Status]
        WHERE [${UserAssignedLearningObjectFields.LessonStatusID}] < ${LessonStatuses.fail}
        AND [${UserAssignedLearningObjectFields.Deleted}] = 0
      )
    ), In_Progress_Status AS (
      SELECT [${AssignmentFields.ID}]
      FROM [${AssignmentsTableName}]
      WHERE [${AssignmentFields.ID}] NOT IN (
        SELECT [${AssignmentFields.ID}]
        FROM [Overdue_Status]
      ) AND [${AssignmentFields.ID}] NOT IN (
        SELECT [${AssignmentFields.ID}]
        FROM [Completed_Status]
      ) AND [${AssignmentFields.ID}] IN (
        SELECT [${UserAssignedLearningObjectFields.AssignmentID}]
        FROM [Assignment_Status]
        WHERE [${UserAssignedLearningObjectFields.LessonStatusID}] > ${LessonStatuses.notAttempted}
        AND [${UserAssignedLearningObjectFields.Deleted}] = 0
      )
    ), Not_Started_Status AS (
      SELECT [${AssignmentFields.ID}]
      FROM [${AssignmentsTableName}]
      WHERE [${AssignmentFields.ID}] NOT IN (
        SELECT [${AssignmentFields.ID}]
        FROM [Overdue_Status]
      ) AND [${AssignmentFields.ID}] NOT IN (
        SELECT [${AssignmentFields.ID}]
        FROM [Completed_Status]
      ) AND [${AssignmentFields.ID}] NOT IN (
        SELECT [${AssignmentFields.ID}]
        FROM [In_Progress_Status]
      ) AND [${AssignmentFields.ID}] IN (
        SELECT [${UserAssignedLearningObjectFields.AssignmentID}]
        FROM [Assignment_Status]
      )
    )
    SELECT [${AssignmentFields.ID}],
      CASE
        WHEN [${AssignmentFields.ID}] IN (SELECT [${AssignmentFields.ID}] FROM [Not_Started_Status]) THEN ${AssignmentStatus.Assigned}
        WHEN [${AssignmentFields.ID}] IN (SELECT [${AssignmentFields.ID}] FROM [In_Progress_Status]) THEN ${AssignmentStatus.InProgress}
        WHEN [${AssignmentFields.ID}] IN (SELECT [${AssignmentFields.ID}] FROM [Completed_Status]) THEN ${AssignmentStatus.Completed}
        WHEN [${AssignmentFields.ID}] IN (SELECT [${AssignmentFields.ID}] FROM [Overdue_Status]) THEN ${AssignmentStatus.Overdue}
        WHEN [${AssignmentsWithNoContentViewFields.ID}] IN (SELECT [${AssignmentsWithNoContentViewFields.ID}] FROM [${AssignmentsWithNoContentViewName}]) THEN ${AssignmentStatus.NoContent}
      END AS [Status]
    FROM [${AssignmentsTableName}]
    WHERE [${AssignmentFields.ID}] = @assignmentID
  `

  const results = await request.query<{ ID: string, Status: number }>(query)

  if (results.recordset.length <= 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }
  return results.recordset[0].Status
}
