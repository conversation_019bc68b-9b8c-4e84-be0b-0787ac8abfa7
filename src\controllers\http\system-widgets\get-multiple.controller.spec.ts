import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import SystemWidgetGroupModel from '../../../models/system-widget-group.model'

describe('HTTP get-multiple controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-multiple.controller', {
            '../../../services/mssql/system-widgets/get-multiple.service.js': {
                default: Sinon.stub().returns(Promise.resolve({totalRecords: 1,  systemWidgets: []}))
            },
            '../../../services/mssql/system-widget-groups/get-multiple.service.js': {
                default: Sinon.stub().returns(Promise.resolve([new SystemWidgetGroupModel]))
            },
            '../../../services/mssql/groups/get-multiple-by-ids.service.js': {
                default: Sinon.stub().returns(Promise.resolve([{ Group: uuid() }]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            },
            body: {
                offset: 0
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-multiple.controller', {
            '../../../services/mssql/system-widgets/get-multiple.service.js': {
                default: Sinon.stub().rejects(Promise.resolve({totalRecords: 1,  systemWidgets: []}))
            },
            '../../../services/mssql/system-widget-groups/get-multiple.service.js': {
                default: Sinon.stub().rejects(Promise.resolve([new SystemWidgetGroupModel]))
            },
            '../../../services/mssql/groups/get-multiple-by-ids.service.js': {
                default: Sinon.stub().rejects(Promise.resolve([{ Group: uuid() }]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
               id: uuid()
            },
            body: {
                offset: 0
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })


})