# Wolf Microk8s - TESS DEV
elasticsearch:
  host: 'http://internal-a69a0f871e9424cd9b6986baa5ecb3df-1982458030.us-gov-west-1.elb.amazonaws:443'
  elasticPassword: asdfqwerasdfqwer

microsoftSql:
  host: 'internal-a03e3d31e3ad64a7a9870ee3b5600907-1457924983.us-gov-west-1.elb.amazonaws.com'
  port: 443
  password: Letmein!
  database: Tess_Test

rabbitmq:
  host: 'internal-a649f2c054680479b98a2be985cbcdbc-522434951.us-gov-west-1.elb.amazonaws.com'
  port: 443
  ## Override these queue names for local testing with FDS:

  #remoteProcedureCalls:
  #  fileDelivery:
  #    queue: 'file-delivery-hwd'
  #  dam:
  #    queue: 'dam-service-hwd'

redis:
  host: 'redis://internal-a88ffab87f48e45049fb8d22478718be-845739681.us-gov-west-1.elb.amazonaws.com:443'
  password: G9xRyqjwhkdeKAP5

security:
  jwtSecret: '02E89FBC3D0842BAEA94C53F5402BF12FEA9640F1558D2D67DB03A115E476518'
  cookieSecret: '02E89FBC3D0842BAEA94C53F5402BF12FEA9640F1558D2D67DB03A115E476518'
  singleUseTokenSecret: '02E89FBC3D0842BAEA94C53F5402BF12FEA9640F1558D2D67DB03A115E476518'
  singleUseQrKey: '02E89FBC3D0842BAEA94C53F5402BF12FEA9640F1558D2D67DB03A115E476518'
  singleUseQrSalt: '02E89FBC3D0842BAEA94C53F5402BF12FEA9640F1558D2D67DB03A115E476518'