import mssql from '@lcs/mssql-utility'
import { Request } from 'mssql'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LearningObject, LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningObjectRatingFields, LearningObjectRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-rating.js'
import { LearningObjectUserViewFields, LearningObjectUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-view.js'

// Gets top rated learning objects
export default async function (limit: number = 5, from?: Date, to?: Date): Promise<LearningObjectModel[]> {
  const pool = mssql.getPool()
  const topRated = await getRatingsMap(pool.request(), limit, from, to)
  const ids = topRated.map(rated => rated.LearningObjectID)
  const learningObjects = await getLearningObjects(pool.request(), ids)

  learningObjects.forEach(obj => {
    const rating = topRated.find(rate => rate.LearningObjectID === obj.fields.ID)
    if (rating) {
      obj.fields.Rating = rating.average
      obj.fields.RatingCount = rating.count
    }
  })

  return learningObjects
}

async function getRatingsMap (request: Request, limit: number, from?: Date, to?: Date): Promise<{ average: number, count: number, LearningObjectID: string }[]> {
  let query = 'SELECT TOP(' + limit + ') '
  query += `AVG(${LearningObjectRatingFields.Rating}) AS average, COUNT(${LearningObjectRatingFields.ID}) AS count, [${LearningObjectRatingsTableName}].[${LearningObjectRatingFields.LearningObjectID}] `
  query += `FROM [${LearningObjectRatingsTableName}] `
  if (from && to) {
    query += `JOIN (SELECT DISTINCT(${LearningObjectUserViewFields.LearningObjectID}) AS LOID FROM [${LearningObjectUserViewsTableName}] `
    query += `WHERE [${LearningObjectUserViewFields.CreatedOn}] BETWEEN @from AND @to) AS userViews ON userViews.LOID = [${LearningObjectRatingsTableName}].[${LearningObjectRatingFields.LearningObjectID}] `
    request.input('from', from)
    request.input('to', to)
  }
  query += `GROUP BY [${LearningObjectRatingsTableName}].[${LearningObjectRatingFields.LearningObjectID}] `
  query += 'ORDER BY average DESC'

  const res = await request.query<{ average: number, count: number, LearningObjectID: string }>(query)
  return res.recordset
}

async function getLearningObjects (request: Request, ids: string[]): Promise<LearningObjectModel[]> {
  if (ids.length === 0) return []

  const conds = ids.map((id, index) => {
    request.input('id_' + index, id)
    return '@id_' + index
  })

  let query = `SELECT * FROM [${LearningObjectsTableName}] `
  query += `WHERE [${LearningObjectFields.ID}] IN (${conds.join(', ')})`

  const res = await request.query<LearningObject>(query)
  return res.recordset.map(record => new LearningObjectModel(undefined, record))
}
