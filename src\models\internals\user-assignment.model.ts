
export interface UserAssignmentDatabaseRecord {
  DueDate?: Date
  LearningObjectID?: string
  LearningContextID?: string
  LessonStatusID: number
  UserID: string
  ForContextID?: string
  ForContextTitle?: string
  ForContextLabel?: string
  ForContextGradeTypeID?: number
  ForContextTypeID?: number
  ForContextRequiredContentCount?: number
  ForContextExamID?: string
  SubContextID?: string
  SubContextTitle?: string
  SubContextLabel?: string
  SubContextTypeID?: number
  SubContextGradeTypeID?: number
  SubContextRequiredContentCount?: number
  SubContextParentID?: string
  SubContextOrderID?: number
  Completed: boolean
  LearningObjectTypeID?: number
  StartedDate?: Date
  LastAccessed?: Date
  ItemProgress: number
  ItemScore: number
  EntityType: 'LearningObject' | 'LearningContext',
  Title: string
  SubContextExamID?: string
  ForContextMaxScore?: number
  ForContextMinScore?: number
  SubContextMaxScore?: number
  SubContextMinScore?: number
}
