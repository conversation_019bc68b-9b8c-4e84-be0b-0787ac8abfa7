import logger from '@lcs/logger'
import { Request, Response } from 'express'
import searchService from '../../../services/mssql/session-enrollments/search-context-session-enrollments.service.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodDates, zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.search-context-session-enrollment-users', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { offset, limit, search, groupIDs } = z.object({
      offset: zodOffset,
      limit: zodLimit,
      search: z.string().optional(),
      groupIDs: z.array(zodGUID).optional()
    }).parse(req.body)

    const { from, to } = z.object({
      from: zodDates.optional(),
      to: zodDates.optional()
    }).parse(req.query)

    const { id } = z.object({ id: zodGUID }).parse(req.params)

    const userEnrollments = await searchService(id, offset, limit, search, groupIDs, from, to)
    log('info', 'Successfully fetched session enrollments for learning context', {
      search,
      groupIDs,
      from,
      to,
      contextID: id,
      count: userEnrollments.enrollments.length,
      totalRecords: userEnrollments.totalRecords,
      success: true,
      req
    })

    res.json({
      totalRecords: userEnrollments.totalRecords,
      users: userEnrollments.enrollments.map(enrollment => {
        return {
          UserID: enrollment.UserID,
          SessionTitle: enrollment.SessionTitle,
          SessionID: enrollment.SessionID,
          EnrolledOn: enrollment.CreatedOn
        }
      })
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get context session enrollments due to invalid data in the request.', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to get context session enrollments', { contextID: req.params.id, success: false, error, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
