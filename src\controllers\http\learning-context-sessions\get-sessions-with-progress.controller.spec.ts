import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'


describe('HTTP Get-sessions-with-progress controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-sessions-with-progress.controller', {
            '../../../services/mssql/learning-context-sessions/get-sessions-with-progress.service.js': {
                default: Sinon.stub().returns(Promise.resolve([new LearningContextSessionModel]))
            }
        })

        const mocks = httpMocks.createMocks({
            params: {
                id: uuid()
            },
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-sessions-with-progress.controller', {
            '../../../services/mssql/learning-context-sessions/get-sessions-with-progress.service.js': {
                default: Sinon.stub().returns(Promise.resolve([new LearningContextSessionModel]))
            }
        })

        const mocks = httpMocks.createMocks({
            params: {
                id: false
            },
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('id')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-sessions-with-progress.controller', {
            '../../../services/mssql/learning-context-sessions/get-sessions-with-progress.service.js': {
                default: Sinon.stub().returns(Promise.reject([new LearningContextSessionModel]))
            }
        })

        const mocks = httpMocks.createMocks({
            params: {
                id: uuid()
            },
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })

})