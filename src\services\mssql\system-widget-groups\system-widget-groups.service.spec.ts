import { expect } from 'chai'
import mssql, { addRow, deleteRow, getRows } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import create from './create.service.js'
import getMultiple from './get-multiple.service.js'
import remove from './delete.service.js'
import SystemWidgetGroupModel from '../../../models/system-widget-group.model.js'
import SystemWidgetModel from '../../../models/system-widget.model.js'
import { SystemWidget, SystemWidgetsTableName } from '@tess-f/sql-tables/dist/lms/system-widget.js'
import { UserGroup, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'

let systemWidgetGroup: SystemWidgetGroupModel
let systemWidget: SystemWidget
let adminGroupId: string

describe('MSSQL System Widgets Objects', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    adminGroupId = (await getRows<UserGroup>(UserGroupTableName, pool.request(), { UserID: AdminUserId }))[0].GroupID!
    systemWidget = await addRow<SystemWidget>(pool.request(), new SystemWidgetModel({
      Title: 'Testing System Widgets Groups',
      Everyone: false,
      Published: false
    }))
  })

  it('create system widget', async () => {
    systemWidgetGroup = await create(new SystemWidgetGroupModel({
      SystemWidgetID: systemWidget.ID,
      GroupID: adminGroupId
    }))
    expect(systemWidgetGroup.fields.SystemWidgetID).to.eq(systemWidget.ID)
  })

  it('can get all records', async () => {
    const result = await getMultiple(systemWidgetGroup.fields.SystemWidgetID!)
    expect(result.length).to.gte(1)
  })

  it('can delete system widget', async () => {
    await remove(systemWidgetGroup.fields.SystemWidgetID!, systemWidgetGroup.fields.GroupID!)
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<SystemWidget>(pool.request(), SystemWidgetsTableName, { ID: systemWidget.ID })
  })
})
