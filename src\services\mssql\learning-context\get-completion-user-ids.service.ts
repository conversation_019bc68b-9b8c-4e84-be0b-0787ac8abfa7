import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { UserFields, UserTableName } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { UserGroupFields, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { FlatLearningContextTreeFields, FlatLearningContextTreeViewName } from '@tess-f/sql-tables/dist/lms/flat-learning-context-view.js'

export async function getInProgressUserIDs (offset = 0, limit = 10, contextID: string, from?: Date, to?: Date, search?: string, groupIDs?: string[]): Promise<{totalRecords: number, ids: string[]}> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('contextID', contextID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  let groupConditions: string[] = []
  if (groupIDs && groupIDs.length > 0) {
    groupConditions = groupIDs.map((id, index) => {
      request.input(`group_${index}`, id)
      return `@group_${index}`
    })
  }

  let query = `
    WITH Child_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}], [${LearningContextFields.VisibilityID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] = @contextID
      UNION ALL
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}],[${FlatLearningContextTreeViewName}]. [${FlatLearningContextTreeFields.VisibilityID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Child_Nodes] ON [Child_Nodes].[${FlatLearningContextTreeFields.ID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}] AND [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}] != ${Visibilities.Hidden}
    )
    SELECT [${UserFields.ID}], TotalRecords = COUNT(*) OVER()
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] IN (
      SELECT DISTINCT [${LearnerProgressFields.UserID}]
      FROM [${LearnerProgressTableName}]
      WHERE ([${LearnerProgressFields.LearningObjectID}] IN (
        SELECT [${LearningObjectContextFields.LearningObjectID}]
        FROM [${LearningObjectContextsTableName}]
        WHERE [${LearningObjectContextFields.LearningContextID}] IN (
          SELECT [${LearningContextFields.ID}]
          FROM [Child_Nodes]
        )
      ) OR [${LearnerProgressFields.LearningContextSessionID}] IN (
        SELECT [${LearningContextSessionFields.ID}]
        FROM [${LearningContextSessionsTableName}]
        WHERE [${LearningContextSessionFields.LearningContextID}] IN (
          SELECT [${LearningContextFields.ID}]
          FROM [Child_Nodes]
        )
      ))
      ${from && to ? `AND [${LearningContextSessionFields.CreatedOn}] BETWEEN @from AND @to` : ''}
    ) AND [${UserFields.ID}] NOT IN (
      SELECT [${UserCompletedLearningContextFields.UserID}]
      FROM [${UserCompletedLearningContextsTableName}]
      WHERE [${UserCompletedLearningContextFields.LearningContextID}] = @contextID
      AND [${UserCompletedLearningContextFields.LessonStatusID}] >= ${LessonStatuses.fail}
      ${from && to ? `AND [${UserCompletedLearningContextFields.CompletedOn}] BETWEEN @from AND @to` : ''}
    )
  `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [UserFields.FirstName, UserFields.LastName], 'any')}) `
  }

  if (groupConditions.length > 0) {
    query += `AND ([${UserFields.ID}] IN (SELECT [${UserGroupFields.UserID}] FROM [${UserGroupTableName}] WHERE [${UserGroupFields.GroupID}] IN (${groupConditions.join(', ')})))`
  }

  request.input('offset', offset)
  request.input('limit', limit)

  query += `
    ORDER BY [${UserFields.FirstName}], [${UserFields.LastName}]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<{ID: string, TotalRecords: number}>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    ids: results.recordset.map(record => record.ID)
  }
}

export async function getCompletedUserIDs (offset = 0, limit = 10, contextID: string, from?: Date, to?: Date, search?: string, groupIDs?: string[]): Promise<{ totalRecords: number, ids: string[] }> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('offset', offset)
  request.input('limit', limit)
  request.input('contextID', contextID)

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  let query = `
    SELECT [${UserFields.ID}], [${UserFields.FirstName}], [${UserFields.LastName}], TotalRecords = COUNT(*) OVER()
    FROM [${UserTableName}]
    WHERE [${UserFields.ID}] IN (
      SELECT [${UserCompletedLearningContextFields.UserID}]
      FROM [${UserCompletedLearningContextsTableName}]
      WHERE [${UserCompletedLearningContextFields.LearningContextID}] = @contextID
      ${from && to ? `AND [${UserCompletedLearningContextFields.CompletedOn}] BETWEEN @from AND @to` : ''}
    )
  `

  if (search) {
    query += ` AND (${parseSearchTerms(request, search, [UserFields.FirstName, UserFields.LastName], 'any')}) `
  }

  if (groupIDs && groupIDs.length > 0) {
    const conds = groupIDs.map((id, index) => {
      request.input(`group_${index}`, id)
      return `@group_${index}`
    })
    query += `
      AND [${UserFields.ID}] IN (
        SELECT [${UserGroupFields.UserID}]
        FROM [${UserGroupTableName}]
        WHERE [${UserGroupFields.GroupID}] IN (
          ${conds.join(', ')}
        )
      )
    `
  }

  query += `
    ORDER BY [${UserFields.FirstName}], [${UserFields.LastName}]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<{ TotalRecords: number, ID: string }>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    ids: results.recordset.map(record => record.ID)
  }
}
