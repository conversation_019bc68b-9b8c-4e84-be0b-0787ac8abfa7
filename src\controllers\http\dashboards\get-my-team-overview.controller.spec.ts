import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: get my team overview', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => sinon.restore())

  it('returns info on the given users team', async () => {
    const controller = await esmock('./get-my-team-overview.controller.js', {
      '../../../services/mssql/certificate/get-team-certificate-count.service.js': { default: sinon.stub().resolves(0) },
      '../../../services/mssql/learner-progress/get-teams-average-score.service.js': { default: sinon.stub().resolves(80) },
      '../../../services/mssql/assignments/get-teams-overdue-assignment-count.service.js': { default: sinon.stub().resolves(0) },
      '../../../services/mssql/assignments/get-teams-active-assignment-count.service.js': { default: sinon.stub().resolves(0) }
    })
    const mocks = httpMocks.createMocks({ session: { userId: uuid() }})
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
    expect(data.Certifications).to.be.a('number')
    expect(data.Certifications).to.equal(0)
    expect(data.AverageScore).to.be.a('number')
    expect(data.AverageScore).to.equal(80)
    expect(data.OverdueAssignments).to.be.a('number')
    expect(data.OverdueAssignments).to.equal(0)
    expect(data.ActiveAssignments).to.be.a('number')
    expect(data.ActiveAssignments).to.equal(0)
  })

  it('gracefully handles an error', async () => {
    const controller = await esmock('./get-my-team-overview.controller.js', {
      '../../../services/mssql/certificate/get-team-certificate-count.service.js': { default: sinon.stub().rejects(new Error('Service Error')) },
      '../../../services/mssql/learner-progress/get-teams-average-score.service.js': { default: sinon.stub().resolves(80) },
      '../../../services/mssql/assignments/get-teams-overdue-assignment-count.service.js': { default: sinon.stub().resolves(0) },
      '../../../services/mssql/assignments/get-teams-active-assignment-count.service.js': { default: sinon.stub().resolves(0) }
    })
    const mocks = httpMocks.createMocks({ session: { userId: uuid() }})
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })
})
