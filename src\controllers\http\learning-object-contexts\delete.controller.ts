import del from '../../../services/mssql/learning-object-contexts/delete.service.js'
import logger from '@lcs/logger'
import { Request, Response } from 'express'
import { DB_Errors } from '@lcs/mssql-utility'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.delete-learning-object-contexts', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, as an admin remove a learning object from a course. Note the time. Check the LMS API log for the 'http-delete-learning-object-contexts' label."

  try {
    const { contextID, learningObjectID } = z.object({
      contextID: zodGUID,
      learningObjectID: zodGUID
    }).parse(req.params)

    const numRowsAffected = await del(contextID, learningObjectID)

    if (numRowsAffected > 0) {
      // STIG V-69427 changing data (success)
      // STIGTEST "In the LMS application, as an admin remove a learning object from a course. Note the time. Check the LMS API log for the 'http-delete-learning-object-contexts' label and message indicating successful deletion."
      log('info', 'Successfully deleted learning object context', { contextID, learningObjectID, success: true, req })

      res.sendStatus(httpStatus.NO_CONTENT)
    } else {
      // STIG V-69427 changing data (failure)
      log('info', 'Failed to delete learning context because it was not found in the database.', { contextID, learningObjectID, success: false, req })

      res.sendStatus(httpStatus.NOT_FOUND)
    }
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete learning object context: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      // STIG V-69427 changing data (failure)
      log('warn', 'Failed to delete learning object context because it was not found in the database.', { contextID: req.params.contextID, learningObjectID: req.params.learningObjectID, success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      // STIG V-69427 changing data (failure)
      log('error', 'Failed to delete learning object contexts.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
