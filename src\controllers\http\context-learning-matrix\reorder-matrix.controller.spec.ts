import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: reorder learning matrix', () => {
  before(() => logger.init({ level: 'warn' }))
  afterEach(() => sinon.restore())

  it('should return bad request when the request params are invalid', async () => {
    const controller = await esmock('./reorder-matrix.controller.js')
    const mocks = httpMocks.createMocks({ params: { oldOrderID: 'test', newOrderID: 'test', parentID: 'test', itemID: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request parameter(s),')
    expect(data).to.include('oldOrderID: Expected number, received nan')
    expect(data).to.include('newOrderID: Expected number, received nan')
    expect(data).to.include('parentID: Invalid')
    expect(data).to.include('itemID: Invalid')
  })

  it('should return bad request when the parentID param is missing', async () => {
    const controller = await esmock('./reorder-matrix.controller.js')
    const mocks = httpMocks.createMocks({ params: { oldOrderID: '12', newOrderID: '12', itemID: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request parameter(s),')
    expect(data).to.include('parentID: Required')
  })

  it('should return bad request when the itemID param is missing', async () => {
    const controller = await esmock('./reorder-matrix.controller.js')
    const mocks = httpMocks.createMocks({ params: { oldOrderID: '12', newOrderID: '12', parentID: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request parameter(s),')
    expect(data).to.include('itemID: Required')
  })

  it('should return no content when the reordering is successful', async () => {
    const controller = await esmock('./reorder-matrix.controller.js', {
      '../../../services/mssql/context-learning-matrix/reorder.service.js': {
        default: sinon.stub().resolves()
      }
    })
    const mocks = httpMocks.createMocks({ params: { oldOrderID: '12', newOrderID: '12', parentID: uuid(), itemID: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.NO_CONTENT)
  })

  it('should return internal server error when an error occurs', async () => {
    const controller = await esmock('./reorder-matrix.controller.js', {
      '../../../services/mssql/context-learning-matrix/reorder.service.js': {
        default: sinon.stub().rejects(new Error('test error'))
      }
    })
    const mocks = httpMocks.createMocks({ params: { oldOrderID: '12', newOrderID: '12', parentID: uuid(), itemID: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })
})
