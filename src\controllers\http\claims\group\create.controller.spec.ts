import logger from '@lcs/logger'
import httpStatus from 'http-status'
import Sinon from 'sinon'
import { expect } from 'chai'
import httpMock from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: create group claims', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns bad request when the claims are not valid strings', async () => {
    const controller = await esmock('./create.controller.js')
    const mocks = httpMock.createMocks({ body: ['claim', 2, true] })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('claims.1: Expected string, received number')
    expect(data).to.include('claims.2: Expected string, received boolean')
  })

  it('returns bad request when the claims array is empty', async () => {
    const controller = await esmock('./create.controller.js')
    const mocks = httpMock.createMocks({ body: [] })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('claims: Array must contain at least 1 element(s)')
  })

  it('returns bad request when the id parameter is missing', async () => {
    const controller = await esmock('./create.controller.js')
    const mocks = httpMock.createMocks({ body: ['test'] })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Required')
  })

  it('returns bad request when the id parameter is not a valid uuid', async () => {
    const controller = await esmock('./create.controller.js')
    const mocks = httpMock.createMocks({ body: ['test'], params: { id: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Invalid')
  })

  it('returns bad request when the id parameter is a number and not a valid uuid', async () => {
    const controller = await esmock('./create.controller.js')
    const mocks = httpMock.createMocks({ body: ['test'], params: { id: 123 } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Expected string, received number')
  })

  it('returns ok when the request data is valid', async () => {
    const controller = await esmock('./create.controller.js', {
      '../../../../services/mssql/claims/group/create.service.js': {
        default: Sinon.stub().returns(Promise.resolve())
      }
    })
    const groupId = uuid()
    const mocks = httpMock.createMocks({ body: ['test'], params: { id: groupId } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.be.a('array')
    expect(data.length).to.be.gte(1)
    expect(data.every((groupClaim: any) => groupClaim.GroupID === groupId)).to.be.true
  })
})
