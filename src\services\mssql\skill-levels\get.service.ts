import mssql, { DB_Errors as dbErrors, getRows } from '@lcs/mssql-utility'
import SkillLevelModel from '../../../models/skill-level.model.js'
import logger from '@lcs/logger'
import { SkillLevel, SkillLevelsTableName } from '@tess-f/sql-tables/dist/lms/skill-level.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const log = logger.create('Service.Get-Skill-Level')

export default async function (skillLevelID: string): Promise<SkillLevelModel> {
  try {
    const pool = mssql.getPool()
    const records = await getRows<SkillLevel>(SkillLevelsTableName, pool.request(), { ID: skillLevelID })
    return new SkillLevelModel(records[0])
  } catch (error) {
    const errorMsg = getErrorMessage(error)
    if (errorMsg !== dbErrors.default.NOT_FOUND_IN_DB) {
      log('error', 'Unexpected error', { errorMessage: errorMsg, success: false })
    }
    throw error
  }
}
