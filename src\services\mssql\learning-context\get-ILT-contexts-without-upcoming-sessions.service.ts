import mssql, { DB_Errors } from '@lcs/mssql-utility'
import LearningContextModel from '../../../models/learning-context.model.js'
import { LearningContext, LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import { Visibilities } from '@tess-f/sql-tables/dist/lms/visibilities.js'
import { SessionStatuses } from '@tess-f/sql-tables/dist/lms/session-status.js'
import { FlatLearningContextTreeFields, FlatLearningContextTreeViewName } from '@tess-f/sql-tables/dist/lms/flat-learning-context-view.js'

/**
 * retrieves a list of of learning contexts that are ILT courses that have
 * no upcoming sessions. If the ID is for a course will return the learning context itself
 * if it has no session. If the ID is for a learning path or collection, it will return a list
 * of all ILT courses that do not have upcoming sessions.
 * @param {string} contextID - the ID of the context to check for upcoming sessions on
 * @returns {LearningContextModel[]}
 */
export default async function (contextID: string): Promise<LearningContextModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  const query = `
    WITH Child_Nodes AS (
      SELECT [${LearningContextFields.ID}], [${LearningContextFields.ParentContextID}], [${LearningContextFields.VisibilityID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] = @contextID
      UNION ALL
      SELECT [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}], [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}]
      FROM [${FlatLearningContextTreeViewName}]
        INNER JOIN [Child_Nodes] ON [Child_Nodes].[${FlatLearningContextTreeFields.ID}] = [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.ParentContextID}] AND [${FlatLearningContextTreeViewName}].[${FlatLearningContextTreeFields.VisibilityID}] != ${Visibilities.Hidden}
    ), ILT_Nodes AS (
      SELECT [${LearningContextFields.ID}]
      FROM [${LearningContextTableName}]
      WHERE [${LearningContextFields.ID}] IN (
        SELECT [${LearningContextFields.ID}]
        FROM [Child_Nodes]
      )
      AND [${LearningContextFields.ContextTypeID}] = ${LearningContextTypes.Course}
      AND [${LearningContextFields.CourseTypeID}] = ${CourseTypes.InstructorLed}
    )
    SELECT *
    FROM [${LearningContextTableName}]
    WHERE (
        [${LearningContextFields.ID}] NOT IN (
            SELECT [${LearningContextSessionFields.LearningContextID}]
            FROM [${LearningContextSessionsTableName}]
            WHERE [${LearningContextSessionFields.StartDate}] >= GETDATE()
            AND [${LearningContextSessionFields.SessionStatusID}] = ${SessionStatuses.Open}
        ) AND [${LearningContextFields.ID}] = @contextID
        AND [${LearningContextFields.ContextTypeID}] = ${LearningContextTypes.Course}
        AND [${LearningContextFields.CourseTypeID}] = ${CourseTypes.InstructorLed}
    ) OR (
        [${LearningContextFields.ID}] NOT IN (
            SELECT [${LearningContextSessionFields.LearningContextID}]
            FROM [${LearningContextSessionsTableName}]
            WHERE [${LearningContextSessionFields.StartDate}] >= GETDATE()
            AND [${LearningContextSessionFields.SessionStatusID}] = ${SessionStatuses.Open}
            AND [${LearningContextSessionFields.LearningContextID}] IN (
                SELECT [${LearningContextFields.ID}]
                FROM [ILT_Nodes]
            )
        ) AND [${LearningContextFields.ID}] IN (
            SELECT [${LearningContextFields.ID}]
            FROM [ILT_Nodes]
        )
    )
`

  request.input('contextID', contextID)
  const results = await request.query<LearningContext>(query)

  if (results.recordset.length <= 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  } else {
    return results.recordset.map(record => new LearningContextModel(undefined, record))
  }
}
