import { Table } from '@lcs/mssql-utility'
import { LearningO<PERSON>Keyword, LearningObjectKeywordFields, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'

export default class LearningObjectKeywordModel extends Table<LearningObjectKeyword, LearningObjectKeyword> {
  public fields: LearningObjectKeyword

  constructor (fields?: LearningObjectKeyword) {
    super(LearningObjectKeywordsTableName, [
      LearningObjectKeywordFields.LearningObjectID,
      LearningObjectKeywordFields.Keyword
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: LearningObjectKeyword): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningObjectKeyword {
    return this.fields
  }
}
