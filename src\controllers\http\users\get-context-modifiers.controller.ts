import logger from '@lcs/logger'
import getContextModifiers from '../../../services/mssql/learning-context/get-context-modifiers.service.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'

const log = logger.create('Controller-HTTP.get-context-modifiers', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { type } = z.object({
      type: z.coerce.number().optional().superRefine((value, ctx) => {
        if (value && !Object.values(LearningContextTypes).includes(value)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Invalid learning context type',
            path: ['type']
          })
        }
      })
    }).parse(req.query)

    const modifiers = await getContextModifiers(type)

    if (modifiers.length > 0) {
      log('info', 'Successfully retrieved learning context modifiers', { count: modifiers.length, contextTypeId: type, success: true, req })
      res.json(modifiers)
    } else {
      log('info', 'No learning context modifiers found', { contextTypeId: type, success: true, req })
      res.json([])
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to retrieve context modifiers: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter, '))
    } else {
      log('error', 'Failed to retrieve context modifiers.', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
