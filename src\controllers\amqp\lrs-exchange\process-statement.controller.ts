import logger from '@lcs/logger'
import Statement from '../../../models/amqp/lrs/statement.model.js'
import getUserByLrsAgent from '../../../services/mssql/users/get-lrs-agent.service.js'
import { getErrorMessage } from '@tess-f/backend-utils'
import getXAPIVerbs from '../../../services/mssql/xapi-verb/get-all.service.js'
import getLearningObjectByIRI from '../../../services/mssql/learning-objects/get-by-iri.service.js'
import createLearnerProgress from '../../../services/mssql/learner-progress/create.service.js'
import LearnerProgressModel from '../../../models/learner-progress.model.js'
import gradeContextsService from '../../../services/background/grade-contexts.service.js'
import verbToLessonStatus from '../../../utils/lrs-statement/verb-to-status-id.util.js'
import getScore from '../../../utils/lrs-statement/get-score-from-statement.util.js'

const log = logger.create('Controller-AMQP.process-lrs-statement')

export default async function (statement: Statement) {
  log('verbose', 'Processing LRS statement')
  if (statement.object.objectType !== 'Activity') {
    log('verbose', 'Failed to process LRS statement object is not an activity', { success: false })
    return
  }

  if (statement.actor.objectType !== 'Agent') {
    log('verbose', 'Failed to process LRS statement actor is not an agent', { success: false })
    return
  }

  const knownVerbs = await getXAPIVerbs()
  const statementVerb = knownVerbs.find(verb => verb.fields.Verb === statement.verb.id)

  if (statementVerb === undefined) {
    log('verbose', 'Failed to process LRS statement unknown verb', { success: false })
    return
  }

  try {
    // get the user that this agent is associated with
    // TODO: use the LRS to get the person object and search for the account for the LMS
    const user = await getUserByLrsAgent(statement.actor)

    // get the learning object that this activity is associated with
    const learningObject = await getLearningObjectByIRI(statement.object.id)

    // if we have gotten this far we have a valid user, valid learning object, and valid verb
    // let's make a new learning progress record
    const learnerProgress = await createLearnerProgress(new LearnerProgressModel({
      UserID: user.ID,
      LearningObjectID: learningObject.fields.ID,
      CreatedOn: new Date(statement.timestamp),
      LessonStatusID: verbToLessonStatus(statementVerb.fields.SatisfactionTypeId!),
      RawScore: getScore(statement)
    }))

    // grade any learning contexts that use this learning object
    await gradeContextsService(learnerProgress, true)
  } catch (error) {
    log('error', 'Failed to process LRS statement', { errorMessage: getErrorMessage(error), success: false })
  }
}
