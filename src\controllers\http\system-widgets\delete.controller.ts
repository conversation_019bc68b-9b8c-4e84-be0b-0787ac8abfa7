import logger from '@lcs/logger'
import { DB_Errors } from '@lcs/mssql-utility'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import deleteSystemWidget from '../../../services/mssql/system-widgets/delete.service.js'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.delete-system-widget', httpLogTransformer)

export default async function deleteSystemWidgetController (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    await deleteSystemWidget(id)
    log('info', 'Successfully deleted system widget', { id, success: true, req })
    res.sendStatus(httpStatus.NO_CONTENT)
  } catch (error) {
    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to delete system widget: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to delete system widget because it was not found in the database', { id: req.params.id, success: false, req })
      res.sendStatus(httpStatus.NOT_FOUND)
    } else {
      log('error', 'Failed to delete system widget', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
