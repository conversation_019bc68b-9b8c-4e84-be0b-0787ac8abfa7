import mssql, { DB_Errors, getRows } from '@lcs/mssql-utility'
import UserPreference from '../../../models/user-preference.model.js'
import create from './create.service.js'
import { UserPreferences, UserPreferencesTableName } from '@tess-f/sql-tables/dist/lms/user-preference.js'

export default async function (UserID: string): Promise<UserPreference> {
  try {
    const pool = mssql.getPool()
    const records = await getRows<UserPreferences>(UserPreferencesTableName, pool.request(), { UserID })
    return new UserPreference(records[0])
  } catch (error) {
    if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
      return await create(new UserPreference({
        UserID,
        GettingStarted: true
      }))
    } else {
      throw error
    }
  }
}
