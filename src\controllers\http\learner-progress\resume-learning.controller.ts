import { DB_Errors } from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import resumeLearning from '../../../services/mssql/learner-progress/resume-learning.service.js'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const { INTERNAL_SERVER_ERROR, NOT_FOUND, BAD_REQUEST } = httpStatus
const log = logger.create('Controller-HTTP.resume-learning', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, click on "Home." Note the time. Check the LMS API log for the 'http-resume-learning' label."

  try {
    const { userID } = z.object({ userID: zodGUID }).parse(req.params)

    const result = await resumeLearning(userID)

    // STIG V-69425 data access (success)
    // STIGTEST "In the LMS application, click on "Home." Note the time. Check the LMS API log for the 'http-resume-learning' label and message indicating successful retrieval."
    log('info', 'Successfully retrieved resume learning for user', { userId: userID, success: true, req })

    res.json({
      LearningObject: result.LearningObject.fields,
      Progress: result.Progress.fields
    })
  } catch (error) {
    // STIG V-69425 data access (failure)

    const errorMessage = getErrorMessage(error)
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to resume learning: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameter data: '))
    } else if (errorMessage === DB_Errors.default.NOT_FOUND_IN_DB) {
      log('warn', 'Failed to resume learning. Resume learning data for user not found!', { userId: req.params.userID, success: false, req })
      res.sendStatus(NOT_FOUND)
    } else {
      log('error', 'Failed to resume learning.', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
