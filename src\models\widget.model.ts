import { Table } from '@lcs/mssql-utility'
import { Widget, WidgetFields, WidgetsTableName } from '@tess-f/sql-tables/dist/lms/widget.js'
import { WidgetJson } from '@tess-f/lms/dist/common/widget.js'
import { z } from 'zod'
import { WidgetTypes } from '@tess-f/sql-tables/dist/lms/widget-type.js'
import { zodGUID } from '@tess-f/backend-utils/validators'

const widgetSchema = z.object({
  [WidgetFields.Title]: z.string().max(500),
  [WidgetFields.TypeID]: z.nativeEnum(WidgetTypes),
  [WidgetFields.MaxItemCount]: z.number().int().positive().optional().nullable(),
  [WidgetFields.Carousel]: z.boolean().optional().default(false),
  [WidgetFields.Columns]: z.array(z.string()).optional().nullable(),
  [WidgetFields.Filter]: z.boolean().optional().default(true),
  [WidgetFields.Search]: z.string().optional().nullable(),
  [WidgetFields.ContextLabels]: z.array(z.string()).optional().nullable(),
  [WidgetFields.ObjectTypes]: z.array(z.number()).optional().nullable(),
  [WidgetFields.SortBy]: z.string().optional().nullable(),
  [WidgetFields.SortDirection]: z.string().optional().nullable(),
  [WidgetFields.MyLearning]: z.string().optional().nullable(),
  [WidgetFields.IsAdmin]: z.boolean().optional().default(false),
  [WidgetFields.OrderID]: z.number().int().nonnegative().optional().default(1),
  [WidgetFields.ColumnID]: z.number().int().optional().default(-1),
  Keywords: z.array(z.string()).optional(),
  Priority: z.number().int().positive().optional(),
  LearningContexts: z.array(z.object({ ID: zodGUID, OrderID: z.number().int().nonnegative() })).optional(),
  LearningObjects: z.array(z.object({ ID: zodGUID, OrderID: z.number().int().nonnegative() })).optional()
})

export const createWidgetSchema = widgetSchema.superRefine(({ Filter, LearningContexts, LearningObjects }, ctx) => {
  if ((!Filter && !LearningContexts && !LearningObjects) ||
    (!Filter && ((LearningObjects?.length ?? 0) + (LearningContexts?.length ?? 0) <= 0))) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Missing Widget content',
      path: ['Filter']
    })
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Missing Widget content',
      path: ['LearningContexts']
    })
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Missing Widget content',
      path: ['LearningObjects']
    })
  }
}).superRefine(({ Columns, TypeID }, ctx) => {
  if (TypeID === WidgetTypes.Table && (!Columns || Columns.length <= 0)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: 'Missing Widget table columns',
      path: ['Columns']
    })
  }
})

export const updateWidgetSchema = widgetSchema.partial()

export default class WidgetModel extends Table<WidgetJson, Widget> {
  public fields: WidgetJson

  constructor (fields?: WidgetJson, record?: Widget) {
    super(WidgetsTableName, [
      WidgetFields.Title,
      WidgetFields.Carousel,
      WidgetFields.Filter,
      WidgetFields.IsAdmin,
      WidgetFields.CreatedBy,
      WidgetFields.OrderID,
      WidgetFields.TypeID
    ])
    if (fields) this.fields = fields
    else this.fields = {}

    if (record) this.importFromDatabase(record)
  }

  public importFromDatabase (record: Widget): void {
    this.fields.ID = record.ID
    this.fields.Title = record.Title
    this.fields.TypeID = record.TypeID
    this.fields.MaxItemCount = record.MaxItemCount
    this.fields.Carousel = record.Carousel
    this.fields.Columns = record.Columns ? record.Columns.split(',') : null
    this.fields.Filter = record.Filter
    this.fields.Search = record.Search
    this.fields.ContextLabels = record.ContextLabels ? record.ContextLabels.split(',') : null
    this.fields.ObjectTypes = record.ObjectTypes ? record.ObjectTypes.split(',').map(type => Number(type)) : null
    this.fields.SortBy = record.SortBy
    this.fields.SortDirection = record.SortDirection
    this.fields.MyLearning = record.MyLearning
    this.fields.IsAdmin = record.IsAdmin
    this.fields.CreatedBy = record.CreatedBy
    this.fields.OrderID = record.OrderID
    this.fields.ColumnID = record.ColumnID
  }

  public exportJsonToDatabase (): Widget {
    return {
      ID: this.fields.ID,
      Carousel: this.fields.Carousel,
      Columns: this.fields.Columns ? this.fields.Columns.join(',') : undefined,
      ContextLabels: this.fields.ContextLabels ? this.fields.ContextLabels.join(',') : undefined,
      CreatedBy: this.fields.CreatedBy,
      Filter: this.fields.Filter,
      IsAdmin: this.fields.IsAdmin,
      MaxItemCount: this.fields.MaxItemCount,
      MyLearning: this.fields.MyLearning,
      ObjectTypes: this.fields.ObjectTypes ? this.fields.ObjectTypes.join(',') : undefined,
      OrderID: this.fields.OrderID,
      Search: this.fields.Search,
      SortBy: this.fields.SortBy,
      SortDirection: this.fields.SortDirection,
      Title: this.fields.Title,
      TypeID: this.fields.TypeID,
      ColumnID: this.fields.ColumnID
    }
  }
}
