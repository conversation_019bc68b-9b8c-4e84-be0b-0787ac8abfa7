import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'

describe('HTTP Controller: get assignments dashboard', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => sinon.restore())

  it('can get the assignments dashboard even when the query dates are invalid', async () => {
    const controller = await esmock('./get-assignments-dashboard.controller.js', {
      '../../../services/mssql/assignments/get-current-counts.service.js': {
        default: sinon.stub().resolves({ Active: 5, Completed: 5, Overdue: 0, InProgress: 15 })
      },
      '../../../services/mssql/assignments/get-count-history.service.js': {
        default: sinon.stub().resolves([])
      },
      '../../../services/mssql/keywords/get-top-ten-assignment-keyword-use-count.service.js': {
        default: sinon.stub().resolves([])
      },
      '../../../services/mssql/assignments/get-content-type-counts.service.js': {
        default: sinon.stub().resolves([])
      }
    })
    const mocks = httpMocks.createMocks({ query: { from: 'yesterday', to: 'tomorrow' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
  })

  it('can get the assignments dashboard within the given date range', async () => {
    const controller = await esmock('./get-assignments-dashboard.controller.js', {
      '../../../services/mssql/assignments/get-current-counts.service.js': {
        default: sinon.stub().resolves({ Active: 5, Completed: 5, Overdue: 0, InProgress: 15 })
      },
      '../../../services/mssql/assignments/get-count-history.service.js': {
        default: sinon.stub().resolves([])
      },
      '../../../services/mssql/keywords/get-top-ten-assignment-keyword-use-count.service.js': {
        default: sinon.stub().resolves([])
      },
      '../../../services/mssql/assignments/get-content-type-counts.service.js': {
        default: sinon.stub().resolves([])
      }
    })
    const mocks = httpMocks.createMocks({ query: { from: new Date().toISOString(), to: new Date().toISOString() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
  })
})
