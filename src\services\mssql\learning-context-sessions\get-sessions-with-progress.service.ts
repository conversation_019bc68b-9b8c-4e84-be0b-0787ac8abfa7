import mssql, { DB_Errors } from '@lcs/mssql-utility'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import { LearningContextSession, LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'

/**
 *
 * @returns {LearningContextSessionModel[]}
 */
export default async function (contextID: string, userID: string): Promise<LearningContextSessionModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  const query = `
    SELECT *
    FROM [${LearningContextSessionsTableName}]
    WHERE [${LearningContextSessionFields.ID}] IN (
      SELECT [${LearnerProgressFields.LearningContextSessionID}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.LearningContextSessionID}] IN (
          SELECT [${LearningContextSessionFields.ID}]
          FROM [${LearningContextSessionsTableName}]
          WHERE [${LearningContextSessionFields.LearningContextID}] = @contextID
      ) AND [${LearnerProgressFields.UserID}] = @userID
    )
  `

  request.input('contextID', contextID)
  request.input('userID', userID)

  const results = await request.query<LearningContextSession>(query)

  if (results.recordset.length <= 0) {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }

  return results.recordset.map(record => new LearningContextSessionModel(undefined, record))
}
