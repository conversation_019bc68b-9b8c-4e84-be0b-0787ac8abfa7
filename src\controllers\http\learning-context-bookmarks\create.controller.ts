import LearningContextBookmark, { createLearningContextBookmarkSchema } from '../../../models/learning-context-user-bookmark.model.js'
import create from '../../../services/mssql/learning-context-bookmarks/create.service.js'
import logger from '@lcs/logger'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { ZodError } from 'zod'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR } = httpStatus

const log = logger.create('Controller-HTTP.create-learning-context-bookmark', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, bookmark a course. Note the time. Check the LMS API log for the 'http-create-learning-context-bookmark' label."

  try {
    const bookmark = new LearningContextBookmark(createLearningContextBookmarkSchema.parse(req.body))
    const result = await create(bookmark)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, bookmark a course. Note the time. Check the LMS API log for the 'http-create-learning-context-bookmark' label and message indicating successful creation."
    log('info', 'Successfully created learning context bookmark.', { success: true, req, contextId: result.fields.LearningContextID, userId: result.fields.UserID })

    res.json(result.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create bookmark for learning context: input validation error', { errorMessage, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else if (errorMessage.startsWith('Violation of UNIQUE KEY constraint')) {
      log('warn', 'Failed to create bookmark for learning context because that bookmark already exists in the database.', { contextId: req.body.LearningContextID, errorMessage, success: false, req })
      res.status(BAD_REQUEST).send('User Bookmark already exists')
    } else {
      log('error', 'Failed to create learning context bookmark.', { errorMessage, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
