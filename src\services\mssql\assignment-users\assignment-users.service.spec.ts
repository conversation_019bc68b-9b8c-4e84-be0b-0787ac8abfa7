import { expect } from 'chai'
import mssql, { addRow, deleteRow } from '@lcs/mssql-utility'
import settings from '../../../config/settings.js'
import logger from '@lcs/logger'
import { v4 as uuidv4 } from 'uuid'
import getMultiple from './get-multiple.service.js'
import search from './search-active-assignment-users.service.js'
import deleteLearningObject from '../learning-objects/delete.service.js'
import deleteAssignment from '../assignments/delete.service.js'
import { Assignment } from '@tess-f/sql-tables/dist/lms/assignment.js'
import AssignmentModel from '../../../models/assignment.model.js'
import { AdminUserId } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import UserAssignedLearningObjectModel from '../../../models/user-assigned-learning-object.model.js'
import { UserAssignedLearningObject, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'
import { LearningObject } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import LearningObjectModel from '../../../models/learning-object.model.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { AssignmentUser, AssignmentUsersTableName } from '@tess-f/sql-tables/dist/lms/assignment-user.js'
import AssignmentUserModel from '../../../models/assignment-users.model.js'

let assignment: Assignment
let assignmentUser: AssignmentUser
let learningObject: LearningObject
let userAssignedLearningObject: UserAssignedLearningObject

describe('MSSQL Assignment User', () => {
  before('Connect to SQL', async () => {
    await mssql.init(settings.mssql.connectionConfig, false, 50)
    await logger.init({ level: 'silly' })
    const pool = mssql.getPool()
    assignment = await addRow<Assignment>(pool.request(), new AssignmentModel({
      Title: 'Testing assignment users',
      TypeID: 1,
      Everyone: true,
      CreatedBy: AdminUserId,
      CreatedOn: new Date()
    }))
    assignmentUser = await addRow<AssignmentUser>(pool.request(), new AssignmentUserModel({
      AssignmentID: assignment.ID,
      UserID: AdminUserId
    }))
    learningObject = await addRow<LearningObject>(pool.request(), new LearningObjectModel({
      Title: 'Test assignment users',
      Description: 'Running assignment users',
      CreatedBy: AdminUserId,
      CreatedOn: new Date(),
      EnableCertificates: false,
      LearningObjectTypeID: LearningObjectTypes.Audio,
      ContentID: uuidv4()
    }))
    userAssignedLearningObject = await addRow<UserAssignedLearningObject>(pool.request(), new UserAssignedLearningObjectModel({
      AssignmentID: assignment.ID,
      UserID: AdminUserId,
      LearningObjectID: learningObject.ID,
      LessonStatusID: LessonStatuses.browsed,
      CreatedOn: new Date(),
      Deleted: false
    }))
  })

  // FIXME: Potentially is not working as expected
  it('searches for assignment user records by assignment ID', async () => {
    const assignmentUsers = await search(assignment.ID!, 1, 150)
    // expect(assignmentUsers.TotalRecords).to.be.gte(1)
    expect(assignmentUsers.TotalRecords).to.be.gte(0)
  })

  it('should fail to search for assignment user records by fake ID', async () => {
    try {
      const assignmentUsers = await search(uuidv4(), 1, 150)
      expect(assignmentUsers.TotalRecords).to.be.eq(0)
    } catch (error) {
      expect(error).to.exist
    }
  })

  it('gets multiple assignment user records by assignment ID', async () => {
    const assignmentUsers = await getMultiple(assignment.ID!)
    expect(assignmentUsers.length).to.be.gte(1)
  })

  it('should fail to get multiple assignment user records by fake ID', async () => {
    try {
      const assignmentUsers = await getMultiple(uuidv4())
      expect(assignmentUsers.length).to.be.eq(0)
    } catch (error) {
      expect(error).to.exist
    }
  })

  after('Delete created objects in SQL', async () => {
    const pool = mssql.getPool()
    await deleteRow<UserAssignedLearningObject>(
      pool.request(),
      UserAssignedLearningObjectsTableName,
      {
        ID: userAssignedLearningObject.ID,
        AssignmentID: userAssignedLearningObject.AssignmentID,
        LearningObjectID: userAssignedLearningObject.LearningObjectID
      }
    )
    await deleteLearningObject(learningObject.ID!)
    await deleteRow<AssignmentUser>(pool.request(), AssignmentUsersTableName, { AssignmentID: assignmentUser.AssignmentID, UserID: AdminUserId })
    await deleteAssignment(assignment.ID!)
  })
})
