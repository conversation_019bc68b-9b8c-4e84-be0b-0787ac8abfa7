import { Table } from '@lcs/mssql-utility'
import { Learning<PERSON>ontextKeyword, LearningContextKeywordFields, LearningContextKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-keyword.js'

export default class LearningContextKeywordModel extends Table<LearningContextKeyword, LearningContextKeyword> {
  public fields: LearningContextKeyword

  constructor (fields?: LearningContextKeyword) {
    super(LearningContextKeywordsTableName, [
      LearningContextKeywordFields.LearningContextID,
      LearningContextKeywordFields.Keyword
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: <PERSON>ContextKeyword): void {
    this.fields = record
  }

  public exportJsonToDatabase (): LearningContextKeyword {
    return this.fields
  }
}
