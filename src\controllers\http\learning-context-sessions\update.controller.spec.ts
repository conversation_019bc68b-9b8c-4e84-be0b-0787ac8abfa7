import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningContextSessionModel from '../../../models/learning-context-session.model.js'
import { SystemConfig } from '@tess-f/system-config/dist/http/system-config.js'
import exp from 'constants'
import LearningContextSessionInstructor from '../../../models/learning-context-session-instructor.model.js'


describe('HTTP update controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context-session-instructors/delete.service.js': {
                default: Sinon.stub().returns(Promise.resolve(1))
            },
            '../../../services/mssql/learning-context-session-instructors/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionInstructor({ SessionID: uuid(), UserID: uuid()})))
            },
            '../../../services/mssql/learning-context-sessions/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            body: {
                ID: uuid()
            },
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context-session-instructors/delete.service.js': {
                default: Sinon.stub().returns(Promise.resolve(1))
            },
            '../../../services/mssql/learning-context-session-instructors/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionInstructor({ SessionID: uuid(), UserID: uuid()})))
            },
            '../../../services/mssql/learning-context-sessions/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningContextSessionModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            body: {
                ID: false,
            },
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('ID')

    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-context-session-instructors/delete.service.js': {
                default: Sinon.stub().returns(Promise.reject(1))
            },
            '../../../services/mssql/learning-context-session-instructors/create.service.js': {
                default: Sinon.stub().returns(Promise.reject(new LearningContextSessionInstructor({ SessionID: uuid(), UserID: uuid()})))
            },
            '../../../services/mssql/learning-context-sessions/update.service.js': {
                default: Sinon.stub().returns(Promise.reject(new LearningContextSessionModel({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            body: {
                ID: uuid(),
                contexts: [uuid()]
            },
            params: {
                contexts: {}
            },
            session: {
                userId: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })



})