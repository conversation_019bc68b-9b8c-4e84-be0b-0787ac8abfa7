import rabbitmq from '@lcs/rabbitmq'
import logger from '@lcs/logger'
import Sinon from 'sinon'
import { expect } from 'chai'
import service from './get-statements.service.js'
import { v4 as uuid } from 'uuid'

describe('Service.AMQP: get statements from LRS', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns the requested statements', async () => {
    Sinon.stub(rabbitmq, 'executeRPC').returns(Promise.resolve({
      success: true,
      data: {
        totalRecords: 1,
        statements: [{
          id: uuid()
        }]
      }
    }))
    const result = await service({ format: 'ids' })
    expect(result).to.exist
    expect(result.totalRecords).to.equal(1)
    expect(result.statements).to.exist
    expect(result.statements.length).to.equal(1)
  })

  it('throws an error with message from rpc consumer when call is unsuccessful', done => {
    Sinon.stub(rabbitmq, 'executeRPC').returns(Promise.resolve({
      success: false,
      message: 'Test Service Failure'
    }))
    service({ format: 'ids' }).then(() => {
      done(new Error('Test should not pass'))
    }, (error: any) => {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.equal('Test Service Failure')
      done()
    })
  })

  it('throws an error with generic message when rpc consumer call is unsuccessful and no message is provided', done => {
    Sinon.stub(rabbitmq, 'executeRPC').returns(Promise.resolve({
      success: false
    }))
    service({ format: 'ids' }).then(() => {
      done(new Error('Test should not pass'))
    }, (error: any) => {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.equal('RPC error')
      done()
    })
  })

  it('throws an error when rpc call fails with an error', done => {
    Sinon.stub(rabbitmq, 'executeRPC').returns(Promise.reject(new Error('RPC timeout')))
    service({ format: 'ids' }).then(() => {
      done(new Error('Test should not pass'))
    }, (error: any) => {
      expect(error).to.exist
      expect(error instanceof Error).to.be.true
      expect(error.message).to.equal('RPC timeout')
      done()
    })
  })
})
