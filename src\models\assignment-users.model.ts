import { Table } from '@lcs/mssql-utility'
import { AssignmentUser, AssignmentUserFields, AssignmentUsersTableName } from '@tess-f/sql-tables/dist/lms/assignment-user.js'

export default class AssignmentUserModel extends Table<AssignmentUser, AssignmentUser> {
  public fields: AssignmentUser

  constructor (fields?: AssignmentUser) {
    super(AssignmentUsersTableName, [
      AssignmentUserFields.AssignmentID
    ])
    if (fields) this.fields = fields
    else this.fields = {}
  }

  public importFromDatabase (record: AssignmentUser): void {
    this.fields = record
  }

  public exportJsonToDatabase (): AssignmentUser {
    return this.fields
  }
}
