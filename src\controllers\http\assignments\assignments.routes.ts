import { RequestHand<PERSON>, Router } from 'express'
import createController from './create.controller.js'
import deleteController from './delete.controller.js'
import getAssignmentDetailsController from './get-assignment-details.controller.js'
import getMultipleController from './get-multiple.controller.js'
import getController from './get.controller.js'
import updateController from './update.controller.js'
import { checkClaims } from '@tess-f/backend-utils/middlewares'
import { Claims } from '@tess-f/sql-tables/dist/lms/claim.js'

const router = Router()

router.post('/assignments', checkClaims([Claims.CREATE_ASSIGNMENTS, Claims.MODIFY_ASSIGNMENTS, Claims.VIEW_ASSIGNMENTS]), getMultipleController as RequestHandler)
router.post('/assignment', checkClaims([Claims.CREATE_ASSIGNMENTS]), createController as RequestHandler)
router.put('/assignment/:id', checkClaims([Claims.MODIFY_ASSIGNMENTS, Claims.CREATE_ASSIGNMENTS]), updateController as RequestHandler)
router.delete('/assignment/:id', checkClaims([Claims.DELETE_ASSIGNMENTS]), deleteController as RequestHandler)
router.get('/assignment/:id', checkClaims([Claims.VIEW_ASSIGNMENTS, Claims.CREATE_ASSIGNMENTS, Claims.MODIFY_ASSIGNMENTS]), getController as RequestHandler)
router.get('/assignment-details/:id', getAssignmentDetailsController as RequestHandler)

export default router
