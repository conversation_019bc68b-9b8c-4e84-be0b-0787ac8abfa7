import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningObjectModel from '../../../models/learning-object.model'

describe('HTTP get-by-cmi5-context-id controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-by-cmi5-context-id.controller', {
            '../../../services/mssql/learning-objects/get-id-by-cmi5-context-id.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-by-cmi5-context-id.controller', {
            '../../../services/mssql/learning-objects/get-id-by-cmi5-context-id.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request parameter data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('id')

    })

  
    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get-by-cmi5-context-id.controller', {
            '../../../services/mssql/learning-objects/get-id-by-cmi5-context-id.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new LearningObjectModel({ ID: uuid(), IRI: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                id: uuid()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })

 

})