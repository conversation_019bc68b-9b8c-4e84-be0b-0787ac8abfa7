import LearningObjectBookmark, { LearningObjectBookmarkSchema } from '../../../models/learning-object-user-bookmark.model.js'
import create from '../../../services/mssql/learning-object-bookmarks/create.service.js'
import logger from '@lcs/logger'
import { getErrorMessage, httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { ZodError } from 'zod'

const log = logger.create('Controller-HTTP.create-learning-object-bookmark', httpLogTransformer)

export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, bookmark a learning object within a course. Note the time. Check the LMS API log for the 'http-create-learning-object-bookmark' label."

  
  try {
    const bookmark = new LearningObjectBookmark(LearningObjectBookmarkSchema.parse(req.body))
    const result = await create(bookmark)

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, bookmark a learning object within a course. Note the time. Check the LMS API log for the 'http-create-learning-object-bookmark' label and message indicating successful creation."
    log('info', `Successfully bookmarked learning object: ${result.fields.LearningObjectID}`, { success: true, req })

    res.json(result.fields)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    const errorMessage = getErrorMessage(error)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create learning object bookmark: input validation error', { errorMessage, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else if (errorMessage.startsWith('Violation of UNIQUE KEY constraint')) {
      log('warn', 'Failed to create learning object bookmark because it already exists in the database.', { errorMessage, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send('Bookmark already exists')
    } else {
      log('error', 'Failed to create learning object bookmark.', { errorMessage, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
