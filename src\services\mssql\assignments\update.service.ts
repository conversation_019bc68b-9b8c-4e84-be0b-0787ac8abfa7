import mssql, { addRow, DB_Errors, getRows, updateRow } from '@lcs/mssql-utility'
import createUserAssignedObjects from '../user-assigned-learning-objects/create-multiple.service.js'
import createUserAssignedMultiSessionCourse from '../user-assigned-multi-session-courses/create-multiple.service.js'
import getAssignmentObjects from '../assignment-learning-objects/get-multiple.service.js'
import getAssignmentContexts from '../assignment-learning-contexts/get-multiple.service.js'
import { ConnectionPool, Request } from 'mssql'
import AssignmentModel from '../../../models/assignment.model.js'
import AssignmentUserModel from '../../../models/assignment-users.model.js'
import AssignmentLearningContextModel from '../../../models/assignment-learning-context.model.js'
import AssignmentLearningObjectModel from '../../../models/assignment-learning-object.model.js'
import { Assignment, AssignmentsTableName } from '@tess-f/sql-tables/dist/lms/assignment.js'
import { AssignmentUser, AssignmentUsersTableName, AssignmentUserFields } from '@tess-f/sql-tables/dist/lms/assignment-user.js'
import { UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'
import { UserAssignedMultiSessionCoursesFields, UserAssignedMultiSessionCoursesTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-multi-session-course.js'
import getUserIdsOfUsersInGroup from '../users/get-ids-of-users-in-group.service.js'

export default async function (assignment: AssignmentModel, users: AssignmentUserModel[], groupUserIDs: string[]): Promise<{ assignment: AssignmentModel, currentUsers: AssignmentUserModel[], addedUserIds: string[], unchangedUserIds: string[] }> {
  const pool = mssql.getPool()
  let wasEveryone = false
  try {
    const currentAssignment = await getRows<Assignment>(AssignmentsTableName, pool.request(), { ID: assignment.fields.ID })
    wasEveryone = currentAssignment[0].Everyone ?? false
  } catch {}

  const updatedRecords = await updateRow<Assignment>(pool.request(), assignment, { ID: assignment.fields.ID })
  const updated = new AssignmentModel(updatedRecords[0])

  // update the due date of current assignments that the user has not completed yet
  const updateDueDateRequest = pool.request()
  updateDueDateRequest.input('assignmentId', updated.fields.ID)
  updateDueDateRequest.input('dueDate', updated.fields.DueDate ?? null)
  await updateDueDateRequest.query(`
    UPDATE [${UserAssignedLearningObjectsTableName}]
    SET [${UserAssignedLearningObjectFields.DueDate}] = @dueDate
    WHERE [${UserAssignedLearningObjectFields.AssignmentID}] = @assignmentId
    AND [${UserAssignedLearningObjectFields.Completed}] = 0
  `)
  await updateDueDateRequest.query(`
    UPDATE [${UserAssignedMultiSessionCoursesTableName}]
    SET [${UserAssignedMultiSessionCoursesFields.DueDate}] = @dueDate
    WHERE [${UserAssignedMultiSessionCoursesFields.AssignmentID}] = @assignmentId
    AND [${UserAssignedMultiSessionCoursesFields.Completed}] = 0
  `)

  const updatedUsersResults = users ? await updateUsers(pool, users, assignment.fields.ID!, groupUserIDs, assignment.fields.CreatedBy!, assignment.fields.Everyone!, wasEveryone, assignment.fields.DueDate) : { currentUsers: [], addedUserIds: [], unchangedUserIds: [] }

  return {
    assignment: updated,
    currentUsers: updatedUsersResults.currentUsers,
    addedUserIds: updatedUsersResults.addedUserIds,
    unchangedUserIds: updatedUsersResults.unchangedUserIds
  }
}

async function updateUsers (pool: ConnectionPool, users: AssignmentUserModel[], assignmentID: string, groupUserIDs: string[], assignedByID: string, everyone: boolean, wasEveryone: boolean, dueDate?: Date): Promise<{ currentUsers: AssignmentUserModel[], addedUserIds: string[], unchangedUserIds: string[] }> {
  // first lets get the users from the database
  let currentUsers: AssignmentUserModel[]
  try {
    const userRecords = await getRows<AssignmentUser>(AssignmentUsersTableName, pool.request(), { AssignmentID: assignmentID })
    currentUsers = userRecords.map(record => new AssignmentUserModel(record))
  } catch (error) {
    if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
      currentUsers = []
    } else {
      throw error
    }
  }

  // get the user IDs of all the users assigned, including users assigned as a part of a group
  const userIdsBeforeUpdate = await getAssignedUserIds(pool.request(), assignmentID)

  // loop over the users and see who we are removing (users/groups)
  // if this was assigned to everyone and now it's not we need to remove users who are not in the current user assignment list
  let usersToRemove = currentUsers.filter(currentUser => !users.some(newUser => (newUser.fields.GroupID ?? undefined) === (currentUser.fields.GroupID ?? undefined) && (newUser.fields.UserID ?? undefined) === (currentUser.fields.UserID ?? undefined)))

  if (wasEveryone && !everyone) {
    // this was assigned to everyone but now we have list of users
    // we need to remove the users who are no longer a part of this assignment
    usersToRemove = userIdsBeforeUpdate.filter(currentUser => !users.some(newUser => newUser.fields.UserID === currentUser)).map(id => new AssignmentUserModel({ UserID: id, AssignmentID: assignmentID }))
  }

  // loop over the users and see who we are adding (users/groups)
  const usersToAdd = users.filter(newUser => !currentUsers.some(currentUser => (newUser.fields.UserID ?? undefined) === (currentUser.fields.UserID ?? undefined) && (newUser.fields.GroupID ?? undefined) === (currentUser.fields.GroupID ?? undefined)))

  if (usersToAdd.length > 0) {
    // lets add our new users if we are not adding the assignment to everyone
    if (!everyone) {
      for (const user of usersToAdd) {
        await addRow(pool.request(), user)
      }
    }
    // next we need to add in the assigned learning objects records for this user
    let contexts: AssignmentLearningContextModel[]
    try {
      contexts = await getAssignmentContexts(assignmentID)
    } catch (error) {
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        contexts = []
      } else {
        throw error
      }
    }
    let objects: AssignmentLearningObjectModel[]
    try {
      objects = await getAssignmentObjects(assignmentID)
    } catch (error) {
      if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
        objects = []
      } else {
        throw error
      }
    }
    await reverseDeletedAssignmentsForNewUsers(pool, users, assignmentID)
    await createUserAssignedObjects(pool, assignmentID, usersToAdd, contexts, objects, groupUserIDs, assignedByID, dueDate)
    await createUserAssignedMultiSessionCourse(pool, assignmentID, usersToAdd, contexts, groupUserIDs, assignedByID, dueDate)
  }

  // lets remove the users we no longer want
  for (const user of usersToRemove) {
    const request = pool.request()
    request.input('assignmentID', assignmentID)
    if (user.fields.UserID) {
      request.input('userID', user.fields.UserID)
      await request.query(`DELETE FROM [${AssignmentUsersTableName}] WHERE [${AssignmentUserFields.AssignmentID}] = @assignmentID AND [${AssignmentUserFields.UserID}] = @userId`)
    } else if (user.fields.GroupID) {
      request.input('groupID', user.fields.GroupID)
      await request.query(`DELETE FROM [${AssignmentUsersTableName}] WHERE [${AssignmentUserFields.AssignmentID}] = @assignmentID AND [${AssignmentUserFields.GroupID}] = @groupId`)
    }

    if (user.fields.UserID && !groupUserIDs.includes(user.fields.UserID)) {
      // only delete the users assigned content if they are also not a part of a group that is in this assignment
      await deleteUserAssignedObjects(pool.request(), user.fields.UserID, assignmentID)
      await deleteUserAssignedMultiSessionCourses(pool.request(), user.fields.UserID, assignmentID)
    } else if (user.fields.GroupID) {
      const idsOfUsersInGroup = await getUserIdsOfUsersInGroup(user.fields.GroupID)
      for (const id of idsOfUsersInGroup) {
        if (!usersToAdd.some(addUser => addUser.fields.UserID === id) && !groupUserIDs.includes(id)) {
          // only remove this user if they are not a part of the individual user list or they are not in another group that we are adding/keeping
          await deleteUserAssignedObjects(pool.request(), id, assignmentID)
          await deleteUserAssignedMultiSessionCourses(pool.request(), id, assignmentID)
        }
      }
    }
  }

  // a user was added to the assignment if they were not in the assignment list at the start of the update but they are in it now
  const userIdsAfterUpdate = await getAssignedUserIds(pool.request(), assignmentID)
  const addedUserIds = userIdsAfterUpdate.filter(id => !userIdsBeforeUpdate.includes(id))
  // gather a list of users that we didn't need to update (we may need to notify them of a due date change)
  const unchangedUserIds = userIdsBeforeUpdate.filter(id => userIdsAfterUpdate.includes(id))

  try {
    const records = await getRows<AssignmentUser>(AssignmentUsersTableName, pool.request(), { AssignmentID: assignmentID })
    return {
      currentUsers: records.map(record => new AssignmentUserModel(record)),
      addedUserIds,
      unchangedUserIds
    }
  } catch (error) {
    if (error instanceof Error && error.message === DB_Errors.default.NOT_FOUND_IN_DB) {
      return {
        currentUsers: [],
        addedUserIds,
        unchangedUserIds
      }
    } else {
      throw error
    }
  }
}

async function deleteUserAssignedObjects (request: Request, userID: string, assignmentID: string): Promise<boolean> {
  const query = `
    UPDATE [${UserAssignedLearningObjectsTableName}]
    SET [${UserAssignedLearningObjectFields.Deleted}] = 1
    WHERE [${UserAssignedLearningObjectFields.UserID}] = @userID
    AND [${UserAssignedLearningObjectFields.AssignmentID}] = @assignmentID
  `
  request.input('userID', userID)
  request.input('assignmentID', assignmentID)
  const res = await request.query(query)
  return res.rowsAffected[0] > 0
}

async function deleteUserAssignedMultiSessionCourses (request: Request, userID: string, assignmentID: string): Promise<boolean> {
  const query = `
    UPDATE [${UserAssignedMultiSessionCoursesTableName}]
    SET [${UserAssignedMultiSessionCoursesFields.Deleted}] = 1
    WHERE [${UserAssignedMultiSessionCoursesFields.UserID}] = @userID
    AND [${UserAssignedMultiSessionCoursesFields.AssignmentID}] = @assignmentID
  `
  request.input('userID', userID)
  request.input('assignmentID', assignmentID)
  const res = await request.query(query)
  return res.rowsAffected[0] > 0
}

// this will reverse the soft deletion the functions above does
// this will help keep the users progress if they were removed and then added back to an assignment
async function reverseDeletedAssignmentsForNewUsers (pool: ConnectionPool, users: AssignmentUserModel[], assignmentID: string) {
  const userIds: string[] = users.filter(user => user.fields.UserID).map(user => user.fields.UserID!)
  if (userIds.length <= 0) return

  // we need to batch the request in case we get thousands of users to reverse
  const batchedUserIds: {[key: number]: string[]} = {}
  const keys: number[] = []

  do {
    let i = 0
    batchedUserIds[keys.length] = []

    while (i < 1000 && userIds.length > 0) {
      batchedUserIds[keys.length].push(userIds.pop()!)
      i++
    }
    keys.push(keys.length)
  } while (userIds.length > 0)

  await Promise.all(keys.map(async key => {
    const request = pool.request()
    request.input('assignmentID', assignmentID)
    const conditions = batchedUserIds[key].map((id, index) => {
      request.input(`user_${index}`, id)
      return `@user_${index}`
    })
    await request.query(`
      UPDATE [${UserAssignedMultiSessionCoursesTableName}]
      SET [${UserAssignedMultiSessionCoursesFields.Deleted}] = 0
      WHERE [${UserAssignedMultiSessionCoursesFields.AssignmentID}] = @assignmentID
      AND [${UserAssignedMultiSessionCoursesFields.UserID}] IN (
        ${conditions.join(', ')}
      )
    `)

    await request.query(`
      UPDATE [${UserAssignedLearningObjectsTableName}]
      SET [${UserAssignedLearningObjectFields.Deleted}] = 0
      WHERE [${UserAssignedLearningObjectFields.AssignmentID}] = @assignmentID
      AND [${UserAssignedLearningObjectFields.UserID}] IN (
        ${conditions.join(', ')}
      )
    `)
  }))
}

async function getAssignedUserIds (request: Request, assignmentID: string): Promise<string[]> {
  // a user is assigned this if their ID is in the assigned learning objects or assigned multi session course table
  // and the record is not marked as deleted
  request.input('assignmentId', assignmentID)
  const results = await request.query<{ UserID: string }>(`
    SELECT DISTINCT [UserID]
    FROM (
      SELECT [${UserAssignedLearningObjectFields.UserID}]
      FROM [${UserAssignedLearningObjectsTableName}]
      WHERE [${UserAssignedLearningObjectFields.AssignmentID}] = @assignmentId
      AND [${UserAssignedLearningObjectFields.Deleted}] = 0

      UNION ALL

      SELECT [${UserAssignedMultiSessionCoursesFields.UserID}]
      FROM [${UserAssignedMultiSessionCoursesTableName}]
      WHERE [${UserAssignedMultiSessionCoursesFields.AssignmentID}] = @assignmentId
      AND [${UserAssignedMultiSessionCoursesFields.Deleted}] = 0
    ) AS [AssignedUsers]
  `)
  return results.recordset.map(record => record.UserID)
}
