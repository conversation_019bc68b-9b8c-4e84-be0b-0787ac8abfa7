import { Table } from '@lcs/mssql-utility'
import { xapiSatisfactionTypes } from '@tess-f/sql-tables/dist/lms/xapi-satisfaction-type.js'
import { xAPIVerb, xAPIVerbFields, xAPIVerbsTableName } from '@tess-f/sql-tables/dist/lms/xapi-verb.js'
import { z } from 'zod'

export const xapiVerbSchema = z.object({
  [xAPIVerbFields.Verb]: z.string(),
  [xAPIVerbFields.SatisfactionTypeId]: z.nativeEnum(xapiSatisfactionTypes)
})

export default class XAPIVerbModel extends Table<xAPIVerb, xAPIVerb> {
  fields: xAPIVerb

  constructor (fields?: xAPIVerb) {
    super(xAPIVerbsTableName, [
      xAPIVerbFields.Verb,
      xAPIVerbFields.SatisfactionTypeId
    ])

    this.fields = fields ?? {}
  }

  importFromDatabase (record: xAPIVerb): void {
    this.fields.Id = record.Id
    this.fields.Verb = record.Verb
    this.fields.SatisfactionTypeId = record.SatisfactionTypeId
  }

  exportJsonToDatabase (): xAPIVerb {
    return {
      Id: this.fields.Id,
      Verb: this.fields.Verb,
      SatisfactionTypeId: this.fields.SatisfactionTypeId
    }
  }
}
