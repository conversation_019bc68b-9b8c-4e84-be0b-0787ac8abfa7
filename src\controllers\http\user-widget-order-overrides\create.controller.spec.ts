import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import UserPreference, { userPreferenceSchema } from '../../../models/user-preference.model.js'
import UserPreferencesModel from '../../../models/user-preference.model.js'

describe('HTTP create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/widget-user-overrides/create-multiple.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid()
            },
            body: [{ ID: uuid(), OrderID: 1, ColumnID: 3 }]


        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/widget-user-overrides/create-multiple.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid()
            },
            body: [{ ID: false, OrderID: false, ColumnID: false }]


        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        const data = mocks.res._getData()
        expect(data).include('Invalid request data:')
        expect(data).include('ID')
        expect(data).include('OrderID')
        expect(data).include('ColumnID')
        expect(data).include('Expected string, received boolean')
        expect(data).include('Expected number, received boolean')
    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/widget-user-overrides/create-multiple.service.js': {
                default: Sinon.stub().rejects(Promise.resolve([]))
            }
            
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            params: {
                userID: uuid()
            },
            body: [{ ID: uuid(), OrderID: 1, ColumnID: 3 }]


        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)
    })



})