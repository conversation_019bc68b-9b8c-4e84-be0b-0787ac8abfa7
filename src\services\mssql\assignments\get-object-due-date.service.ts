import mssql, { DB_Errors as dbErrors } from '@lcs/mssql-utility'
import { UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'

/**
 * Returns the soonest due date for a given piece of content
 * @param objectID ID of the learning object to get a due date for
 * @param userID User who the content is assigned to
 */
export default async function getLearningObjectDueDate (objectID: string, userID: string): Promise<Date> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('userID', userID)
  request.input('objectID', objectID)

  const query = `
    SELECT TOP (1) [${UserAssignedLearningObjectFields.DueDate}]
    FROM [${UserAssignedLearningObjectsTableName}]
    WHERE [${UserAssignedLearningObjectFields.DueDate}] IS NOT NULL
    AND [${UserAssignedLearningObjectFields.UserID}] = @userID
    AND [${UserAssignedLearningObjectFields.LearningObjectID}] = @objectID
    ORDER BY [${UserAssignedLearningObjectFields.DueDate}] ASC
  `

  const results = await request.query<{ DueDate: Date }>(query)

  if (results.recordset.length <= 0) {
    throw new Error(dbErrors.default.NOT_FOUND_IN_DB)
  } else {
    return results.recordset[0].DueDate
  }
}
