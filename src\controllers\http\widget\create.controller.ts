import logger from '@lcs/logger'
import createWidgetService from '../../../services/mssql/widgets/create.service.js'
import { Request, Response } from 'express'
import Widget, { createWidgetSchema } from '../../../models/widget.model.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { ZodError } from 'zod'

const log = logger.create('Controller-HTTP.create-widget', httpLogTransformer)

export default async function createWidgetController (req: Request, res: Response) {
  try {
    const widget = new Widget(createWidgetSchema.parse(req.body))
    widget.fields.CreatedBy = req.session.userId

    const created = await createWidgetService(widget)
    log('info', 'Successfully created widget', { widgetID: created.fields.ID, success: true, req })
    res.json(created.fields)
  } catch (error) {
    if (error instanceof ZodError) {
      log('warn', 'Failed to create widget: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to create widget', { error, success: false, req })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)      
    }
  }
}
