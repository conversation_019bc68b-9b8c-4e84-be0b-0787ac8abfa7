import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
const { BAD_REQUEST, INTERNAL_SERVER_ERROR, NO_CONTENT } = httpStatus
import { sendILTSessionRequestMessage } from '@tess-f/email/dist/amqp/request-ilt-session.js'
import getUserById from '../../../services/mssql/users/get-by-id.service.js'
import settings from '../../../config/settings.js'
import sendNotification from '../../../services/amqp/notification/send-notification.service.js'
import getContextService from '../../../services/mssql/learning-context/get.service.js'
import { AdminGroupId } from '@tess-f/sql-tables/dist/id-mgmt/group.js'
import { getSystemConfig } from '../../../services/amqp/system/get-system-config.service.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.request-ilt-session', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const data = z.object({
      contexts: z.array(z.object({ ID: zodGUID })).min(1),
      message: z.string().optional()
    }).parse(req.body)

    const user = await getUserById(req.session.userId)
    const contexts = await Promise.all(data.contexts.map(async context => await getContextService(context.ID, undefined)))

    const config = await getSystemConfig()
    const lmsConfig = config.Apps.find(app => app.Id === 'lms')
    const linkBase = `${config.Domain}${lmsConfig?.Address ?? '/lms'}/course-management/ilt/`

    // we have the info of the user sending the request
    const rpcResponse = await sendILTSessionRequestMessage(
      settings.amqp.service_queues.email, {
        from: user,
        courses: contexts.map(context => context.fields.Title!),
        message: data.message
      },
      settings.amqp.command_timeout
    )

    // send notification
    await Promise.all(contexts.map(async context => {
      const username = `${user.FirstName} ${user.LastName} (${user.Username})`
      await sendNotification({
        Title: `${context.fields.Title} session request`,
        Message: `
        <p>${user.Email ? '<a href="mailto:' + user.Email + '">' + username + '</a>' : username} has requested additional sessions for <a href="${linkBase}${context.fields.ID}">${context.fields.Title}</a></p>
          ${data.message ? '<p>' + data.message + '</p>' : ''}
        `,
        PublishDate: new Date(),
        Everyone: false,
        SystemID: 'lms',
        Priority: 1,
        UserIDs: [context.fields.CreatedBy!],
        GroupIDs: [AdminGroupId],
        CreatedBy: req.session.userId
      })
    }))

    if (rpcResponse.success) {
      log('info', 'Successfully sent ILT session request for course(s)', { count: data.contexts.length, success: true, req })
      res.sendStatus(NO_CONTENT)
    } else {
      log('warn', 'Failed to send ILT session request', { success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to send ILT session request: input validation error', { error, success: false, req })
      res.status(BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to send ILT session request.', { error, success: false, req })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
