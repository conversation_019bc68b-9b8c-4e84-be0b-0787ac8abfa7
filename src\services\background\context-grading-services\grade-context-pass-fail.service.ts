import mssql from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import LearningContextModel from '../../../models/learning-context.model.js'
import { getLearnerProgressForILT, getLearnerProgressForObject } from './utils.js'
import getLearningObjectsService from '../../mssql/learning-objects/get-multiple.service.js'
import { getContextPercentage } from './grade-percentage.service.js'
import { getContextExamScore } from './grade-exam.service.js'
import { getContextCompletion } from './grade-context-completion.service.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { LearningContextTypes } from '@tess-f/sql-tables/dist/lms/learning-context-type.js'
import { GradeTypes } from '@tess-f/sql-tables/dist/lms/grade-type.js'
import { CourseTypes } from '@tess-f/sql-tables/dist/lms/course-type.js'
import { UserCompletedLearningContext, UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import { getErrorMessage } from '@tess-f/backend-utils'

const completedStatusIDs = [LessonStatuses.passed, LessonStatuses.completed]
const log = logger.create('Service-Background.grade-context-pass-fail')

export default async function gradeContextPassFail (context: LearningContextModel, userID: string, minProgressAge: Date | undefined, maxProgressAge: Date): Promise<{ LessonStatusID: number, RawScore: number, CompletionDate: Date | null }> {
  const pool = mssql.getPool()

  if (context.fields.ContextTypeID === LearningContextTypes.Section) {
    // let's get the content for this context
    const learningObjects = await getLearningObjectsService(context.fields.ID)
    if (!context.fields.Contexts) {
      context.fields.Contexts = []
    }

    if (learningObjects.length <= 0 && context.fields.Contexts.length <= 0) {
      return {
        LessonStatusID: context.fields.GradeTypeID === GradeTypes.CompleteIncomplete ? LessonStatuses.completed : LessonStatuses.passed,
        RawScore: 100,
        CompletionDate: null
      } // no content so the user has passed
    }

    let completed = true // we will assume the user has completed unless we can prove they haven't
    let completedDate: Date | undefined
    let numberItemsAttempted = 0

    for (const obj of learningObjects) {
      let objectCompleted = false
      let attempted = false
      let objectCompletedDate: Date | undefined
      const progress = await getLearnerProgressForObject(pool.request(), obj.fields.ID!, userID, minProgressAge, maxProgressAge)
      if (progress.length <= 0) {
        // the user has not attempted this, the context is not completed/passed
        completed = false
        break
      }
      progress.forEach(p => {
        if (completedStatusIDs.includes(p.fields.LessonStatusID!)) {
          // this is completed/passed
          objectCompleted = true
          attempted = true
          // check the completion date
          if (!objectCompletedDate || objectCompletedDate > (p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!)) {
            objectCompletedDate = p.fields.CompletedDate ? p.fields.CompletedDate : p.fields.CreatedOn!
          }
        } else if (p.fields.LessonStatusID! > LessonStatuses.notAttempted) {
          attempted = true
        }
      })
      if (attempted) {
        numberItemsAttempted++
      }
      // is this object completed/passed?
      if (objectCompleted) {
        if (objectCompletedDate && (!completedDate || objectCompletedDate > completedDate)) {
          completedDate = objectCompletedDate
        }
      } else {
        completed = false
      }
    }

    // now we go through the child contexts
    for (const lc of context.fields.Contexts) {
      // check the grade type and get the completion
      let grade!: { LessonStatusID: number, RawScore: number, CompletionDate: Date | null }
      const child = new LearningContextModel(lc)
      if (!child.fields.GradeTypeID) {
        grade = await getContextCompletion(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.CompleteIncomplete || child.fields.GradeTypeID === GradeTypes.PassFail) {
        grade = await getContextPassFail(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.Percentage) {
        grade = await getContextPercentage(child, userID, minProgressAge, maxProgressAge)
      } else if (child.fields.GradeTypeID === GradeTypes.Exam) {
        grade = await getContextExamScore(child, userID, maxProgressAge)
      }

      if (completedStatusIDs.includes(grade.LessonStatusID)) {
        numberItemsAttempted++
        if (grade.CompletionDate && (!completedDate || grade.CompletionDate > completedDate)) {
          completedDate = grade.CompletionDate
        }
      } else if (grade.LessonStatusID > LessonStatuses.notAttempted) {
        numberItemsAttempted++
        completed = false
      } else {
        completed = false
      }
    }

    // we have looked through all the content, is this context complete?
    if (numberItemsAttempted === (learningObjects.length + context.fields.Contexts.length)) {
      // we have completed the context, lets save the status
      let status
      if (completed) {
        status = context.fields.GradeTypeID === GradeTypes.CompleteIncomplete ? LessonStatuses.completed : LessonStatuses.passed
      } else {
        status = context.fields.GradeTypeID === GradeTypes.CompleteIncomplete ? LessonStatuses.incomplete : LessonStatuses.fail
      }
      return {
        LessonStatusID: status,
        RawScore: completedStatusIDs.includes(status) ? 100 : 0,
        CompletionDate: completedDate ?? new Date()
      }
    } else {
      return {
        LessonStatusID: LessonStatuses.browsed,
        RawScore: numberItemsAttempted / (learningObjects.length + context.fields.Contexts.length),
        CompletionDate: null
      }
    }
  } else if (context.fields.ContextTypeID === LearningContextTypes.Course && context.fields.CourseTypeID === CourseTypes.InstructorLed) {
    // this is an ILT Course
    const progress = await getLearnerProgressForILT(pool.request(), context.fields.ID!, userID, minProgressAge, maxProgressAge)
    if (progress.length <= 0) {
      // the user has not attempted this
      return {
        LessonStatusID: LessonStatuses.notAttempted,
        RawScore: 0,
        CompletionDate: null
      }
    }

    for (const prog of progress) {
      // the progress is in order oldest to newest
      // the first one we find that satisfies our condition will be the
      // oldest record and thus our completion date
      if (completedStatusIDs.includes(prog.fields.LessonStatusID!)) {
        return {
          LessonStatusID: LessonStatuses.completed,
          RawScore: 100,
          CompletionDate: prog.fields.CompletedDate ?? prog.fields.CreatedOn!
        }
      }
    }

    // if we got this far we didn't complete the ILT
    return {
      LessonStatusID: LessonStatuses.browsed,
      RawScore: 0,
      CompletionDate: null
    }
  } else {
    // nothing should hit this point unless the data schema changes
    return {
      LessonStatusID: -1,
      RawScore: 0,
      CompletionDate: null
    }
  }
}

export async function getContextPassFail (context: LearningContextModel, userID: string, minProgressAge: Date | undefined, maxProgressAge: Date): Promise<{ LessonStatusID: number, RawScore: number, CompletionDate: Date | null }> {
  try {
    // find the latest score
    const request = mssql.getPool().request()
    request.input('contextID', context.fields.ID)
    request.input('userID', userID)
    request.input('to', maxProgressAge)

    const res = await request.query<UserCompletedLearningContext>(`
      SELECT TOP(1) *
      FROM [${UserCompletedLearningContextsTableName}]
      WHERE [${UserCompletedLearningContextFields.LearningContextID}] = @contextID
      AND [${UserCompletedLearningContextFields.UserID}] = @userID
      AND [${UserCompletedLearningContextFields.CompletedOn}] <= @to
      ORDER BY [${UserCompletedLearningContextFields.RawScore}] DESC, [${UserCompletedLearningContextFields.CompletedOn}] DESC
    `)

    if (res.recordset.length > 0) {
      return {
        LessonStatusID: res.recordset[0].LessonStatusID!,
        RawScore: res.recordset[0].RawScore!,
        CompletionDate: res.recordset[0].CompletedOn!
      }
    } else {
      return gradeContextPassFail(context, userID, minProgressAge, maxProgressAge)
    }
  } catch (error) {
    log('error', 'Unknown database error', { errorMessage: getErrorMessage(error), success: false })
    throw error
  }
}
