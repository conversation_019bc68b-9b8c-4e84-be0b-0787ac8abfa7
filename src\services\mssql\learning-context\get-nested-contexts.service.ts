import mssql, { DB_Errors } from '@lcs/mssql-utility'
import { LearningContextJson } from '@tess-f/lms/dist/common/learning-context.js'
import { GetNestedContexts } from './utils.service.js'

export default async function (contextID: string): Promise<LearningContextJson[]> {
  const pool = mssql.getPool()
  const result = await GetNestedContexts(pool.request(), contextID)
  if (result) {
    return result
  } else {
    throw new Error(DB_Errors.default.NOT_FOUND_IN_DB)
  }
}
