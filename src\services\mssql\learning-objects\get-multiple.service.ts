import mssql, { streamQuery } from '@lcs/mssql-utility'
import LearningObjectModel from '../../../models/learning-object.model.js'
import LearningObjectKeywordModel from '../../../models/learning-object-keyword.model.js'
import { Request } from 'mssql'
import { LearningObjectUserViewFields, LearningObjectUserViewsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-view.js'
import { LearningObject, LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningObjectKeyword, LearningObjectKeywordFields, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import { LearningObjectContextFields, LearningObjectContextsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-context.js'
import { LearningObjectRatingFields, LearningObjectRatingsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-rating.js'

export default async function (contextID?: string, visibility?: number, contextCount?: boolean, rating?: boolean): Promise<LearningObjectModel[]> {
  const pool = mssql.getPool()

  const learningObjects = await getLearningObjects(pool.request(), contextID, visibility)
  const ids: string[] = learningObjects.map(learningObject => learningObject.fields.ID!)

  const extras = await Promise.all([
    getKeywords(pool.request(), ids),
    contextCount ? getContextCounts(pool.request(), ids) : undefined,
    rating ? getAverageRatingMap(pool.request(), ids) : undefined,
    getViewsMap(pool.request(), ids),
    contextID ? getOrderIDMap(pool.request(), contextID) : undefined
  ])

  // add the extras to the learning objects
  learningObjects.forEach(obj => {
    obj.fields.Keywords = extras[0].filter(keyword => keyword.fields.LearningObjectID === obj.fields.ID).map(keyword => keyword.fields.Keyword!)
    if (extras[1]) {
      obj.fields.ContextCount = extras[1][obj.fields.ID!]
    }
    if (extras[2]) {
      const ratings = extras[2][obj.fields.ID!]
      if (ratings) {
        obj.fields.Rating = ratings.average
        obj.fields.RatingCount = ratings.count
      } else {
        obj.fields.Rating = 0
        obj.fields.RatingCount = 0
      }
    }
    obj.fields.Views = extras[3][obj.fields.ID!]
    if (extras[4]) {
      obj.fields.OrderID = extras[4][obj.fields.ID!]
    }
  })

  return learningObjects
}

async function getViewsMap (request: Request, ids: string[]): Promise<{ [key: string]: number }> {
  if (ids.length === 0) return {}

  const conds = ids.map((id, index) => {
    request.input('id_' + index, id)
    return '@id_' + index
  })

  let query = `SELECT COUNT([${LearningObjectUserViewFields.LearningObjectID}]) as Views, [${LearningObjectUserViewFields.LearningObjectID}] FROM [${LearningObjectUserViewsTableName}] `
  query += `WHERE [${LearningObjectUserViewFields.LearningObjectID}] IN (${conds.join(', ')}) `
  query += `GROUP BY [${LearningObjectUserViewFields.LearningObjectID}];`

  const res = await request.query<{ LearningObjectID: string, Views: number }>(query)

  const map: { [key: string]: number } = {}

  for (const id of ids) {
    const views = res.recordset.find(record => {
      return record.LearningObjectID === id
    })

    map[id] = views ? views.Views : 0
  }

  return map
}

async function getAverageRatingMap (request: Request, ids: string[]): Promise<{ [key: string]: { count: number, average: number } }> {
  if (ids.length === 0) return {}

  const conditions = ids.map((id, index) => {
    request.input('id_' + index, id)
    let condition = `[${LearningObjectRatingFields.LearningObjectID}] = @id_${index}`

    if (index < ids.length - 1) {
      condition += ' OR '
    }

    return condition
  })

  const query = `
    SELECT AVG([${LearningObjectRatingFields.Rating}]) as average, Count([${LearningObjectRatingFields.ID}]) as count, [${LearningObjectRatingFields.LearningObjectID}]
    FROM [${LearningObjectRatingsTableName}]
    WHERE ${conditions.join('')}
    GROUP BY [${LearningObjectRatingFields.LearningObjectID}]
  `

  const res = await request.query<{ average: number, count: number, LearningObjectID: string }>(query)
  const map: { [key: string]: { count: number, average: number } } = {}

  for (const rating of res.recordset) {
    map[rating.LearningObjectID] = {
      count: rating.count,
      average: rating.average
    }
  }

  return map
}

async function getLearningObjects (request: Request, contextID?: string, visibility?: number): Promise<LearningObjectModel[]> {
  let query = `SELECT [${LearningObjectsTableName}].* FROM [${LearningObjectsTableName}] `

  const conditions = []

  if (contextID) {
    request.input('contextID', contextID)
    query += `LEFT JOIN [${LearningObjectContextsTableName}] ON [${LearningObjectsTableName}].[${LearningObjectFields.ID}] = [${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningObjectID}] `
    conditions.push(`[${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningContextID}] = @contextID`)
  }

  if (visibility) {
    request.input('visibility', visibility)
    conditions.push(`[${LearningObjectsTableName}].[${LearningObjectFields.VisibilityID}] = @visibility`)
  }

  if (conditions.length > 0) {
    query += 'WHERE '

    for (let i = 0; i < conditions.length; i++) {
      query += conditions[i]
      if (i < conditions.length - 1) query += ' AND '
    }
  }

  const result = await streamQuery<LearningObject>(request, query)
  return result.map(record => new LearningObjectModel(undefined, record))
}

async function getKeywords (request: Request, ids: string[]): Promise<LearningObjectKeywordModel[]> {
  if (ids.length === 0) return []

  let query = `
    SELECT *
    FROM [${LearningObjectKeywordsTableName}]
    WHERE [${LearningObjectKeywordFields.LearningObjectID}] IN (
  `

  for (let i = 0; i < ids.length; i++) {
    request.input('id_' + i, ids[i])
    query += '@id_' + i
    if (i < ids.length - 1) query += ','
    else query += ');'
  }

  const result = await request.query<LearningObjectKeyword>(query)

  return result.recordset.map(record => new LearningObjectKeywordModel(record))
}

async function getContextCounts (request: Request, ids: string[]): Promise<{ [key: string]: number }> {
  if (ids.length === 0) return {}

  let query = `
    SELECT [${LearningObjectsTableName}].[${LearningObjectFields.ID}] id, COUNT([${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningObjectID}]) contextCount
    FROM [${LearningObjectsTableName}]
    LEFT JOIN [${LearningObjectContextsTableName}] ON [${LearningObjectsTableName}].[${LearningObjectFields.ID}] = [${LearningObjectContextsTableName}].[${LearningObjectContextFields.LearningObjectID}]
    WHERE [${LearningObjectsTableName}].[${LearningObjectFields.ID}] IN (
  `

  for (let i = 0; i < ids.length; i++) {
    request.input('id_' + i, ids[i])
    query += '@id_' + i
    if (i < ids.length - 1) query += ','
    else query += ')'
  }

  query += `GROUP BY [${LearningObjectsTableName}].[${LearningObjectFields.ID}];`

  const result = await request.query<{ id: string, contextCount: number }>(query)

  const map: { [key: string]: number } = {}

  for (const record of result.recordset) {
    map[record.id] = record.contextCount
  }

  return map
}

async function getOrderIDMap (request: Request, contextID: string): Promise<{ [key: string]: number }> {
  const query = `
  SELECT [${LearningObjectContextFields.LearningObjectID}], [${LearningObjectContextFields.OrderID}] 
  FROM [${LearningObjectContextsTableName}] 
  WHERE [${LearningObjectContextFields.LearningContextID}] = @contextID`
  request.input('contextID', contextID)

  const result = await request.query<{ LearningObjectID: string, OrderID: number }>(query)

  const map: { [key: string]: number } = {}

  for (const record of result.recordset) {
    map[record.LearningObjectID] = record.OrderID
  }

  return map
}
