import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LocationModel from '../../../models/location.model.js'

describe('HTTP create controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/locations/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LocationModel()))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                AddressLine1: 'a',
                City: 'a',
                State: 'a',
                Zip: 'a',
                Title: 'a',
                Country: 'a',
                AddressLine2: 'a',
                Building: 'a',
                Room: 'a'
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/locations/create.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LocationModel()))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                AddressLine1: false,
                City: false,
                State: false,
                Zip: false,
                Title: false,
                Country: false,
                AddressLine2: false,
                Building: false,
                Room: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('AddressLine1')
        expect(mocks.res._getData()).include('City')
        expect(mocks.res._getData()).include('State')
        expect(mocks.res._getData()).include('Zip')
        expect(mocks.res._getData()).include('Title')
        expect(mocks.res._getData()).include('Country')
        expect(mocks.res._getData()).include('AddressLine2')
        expect(mocks.res._getData()).include('Building')
        expect(mocks.res._getData()).include('Room')

    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./create.controller', {
            '../../../services/mssql/locations/create.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new LocationModel()))
            }
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                AddressLine1: 'a',
                City: 'a',
                State: 'a',
                Zip: 'a',
                Title: 'a',
                Country: 'a',
                AddressLine2: 'a',
                Building: 'a',
                Room: 'a'
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })
   

})