import logger from '@lcs/logger'
import { getInProgressUserIDs } from '../../../services/mssql/learning-objects/get-completion-user-ids.service.js'
import { Request, Response } from 'express'
import getUserInProgressDates from '../../../services/mssql/learning-objects/get-user-completion-dates.service.js'
import httpStatus from 'http-status'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { z } from 'zod'
import { zodDates, zodGUID, zodLimit, zodOffset } from '@tess-f/backend-utils/validators'

const log = logger.create('Controller-HTTP.get-in-progress-learning-object-users', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { offset, limit, search, groupIDs } = z.object({
      offset: zodOffset,
      limit: zodLimit,
      search: z.string().optional(),
      groupIDs: z.array(zodGUID).optional()
    }).parse(req.body)

    const { from, to } = z.object({
      from: zodDates.optional(),
      to: zodDates.optional()
    }).parse(req.query)

    const { id } = z.object({ id: zodGUID }).parse(req.params)

    const userIds = await getInProgressUserIDs(offset, limit, id, from, to, search, groupIDs)

    const users = await Promise.all(userIds.ids.map(async userId => {
      // map oon the started and last accessed date
      const dates = await getUserInProgressDates(id, userId, from, to)
      return {
        UserID: userId,
        StartedOn: dates.startedOn,
        LastAccessed: dates.LastAccessed
      }
    }))

    log('info', 'Successfully fetched in progress users for learning object', {
      search,
      groupIDs,
      from,
      to,
      objectID: id,
      count: users.length,
      totalRecords: userIds.totalRecords,
      success: true,
      req
    })

    res.json({
      totalRecords: userIds.totalRecords,
      users
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to get in progress users for learning object: input validation error', { error, success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    } else {
      log('error', 'Failed to get in progress users for learning object', { error, req, success: false })
      res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
