import logger from '@lcs/logger'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import Sinon from 'sinon'
import { v4 as uuid } from 'uuid'
import httpStatus from 'http-status'

describe('HTTP Controller: Get Assignment Details', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('should return a bad request status code when no user ID is provided', async () => {
    const mocks = httpMocks.createMocks()
    const getAssignmentDetails = await esmock('./get-assignment-details.controller.js')
    await getAssignmentDetails(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Required')
  })

  it('should return a bad request status code when user ID is invalid', async () => {
    const mocks = httpMocks.createMocks({ params: { id: 'test' } })
    const getAssignmentDetails = await esmock('./get-assignment-details.controller.js')
    await getAssignmentDetails(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Invalid')
  })

  it('should return a bad request status code when user ID is a number', async () => {
    const mocks = httpMocks.createMocks({ params: { id: 1 } })
    const getAssignmentDetails = await esmock('./get-assignment-details.controller.js')
    await getAssignmentDetails(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Expected string, received number')
  })

  it('returns ok status when request params are valid', async () => {
    const mocks = httpMocks.createMocks({ params: { id: uuid() } })
    const getAssignmentDetails = await esmock('./get-assignment-details.controller.js', {
      '../../../services/mssql/assignments/search-user-active-assignments.service.js': {
        default: Sinon.stub().returns(Promise.resolve({ Assignments: [], TotalRecords: 0 }))
      }
    })
    await getAssignmentDetails(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
  })
})
