import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { UserTableName, UserFields } from '@tess-f/sql-tables/dist/id-mgmt/user.js'
import { UserGroupFields, UserGroupTableName } from '@tess-f/sql-tables/dist/id-mgmt/user-group.js'
import { UserCompletedLearningContext, UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import UserCompletedLearningContextModel from '../../../models/user-completed-learning-context.model.js'

export default async function (contextID: string, offset = 0, limit = 10, search?: string, groupIDs?: string[], from?: Date, to?: Date): Promise<{totalRecords: number, records: UserCompletedLearningContextModel[]}> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('contextID', contextID)
  request.input('offset', offset)
  request.input('limit', limit)

  let query = `
    SELECT [${UserCompletedLearningContextsTableName}].*, [${UserTableName}].[${UserFields.FirstName}], [${UserTableName}].[${UserFields.LastName}], [TotalRecords] = COUNT(*) OVER()
    FROM [${UserCompletedLearningContextsTableName}]
    JOIN [${UserTableName}] ON [${UserTableName}].[${UserFields.ID}] =[${UserCompletedLearningContextsTableName}].[${UserCompletedLearningContextFields.UserID}]
    WHERE [${UserCompletedLearningContextFields.LearningContextID}] = @contextID
    AND [${UserCompletedLearningContextFields.Certificate}] IS NOT NULL
  `

  if (search) {
    query += `AND (${parseSearchTerms(request, search, [UserFields.FirstName, UserFields.LastName], 'any')}) `
  }

  if (groupIDs && groupIDs.length > 0) {
    const conds = groupIDs.map((id, index) => {
      request.input(`group_${index}`, id)
      return `@group_${index}`
    })
    query += `
      AND [${UserCompletedLearningContextFields.UserID}] IN (
        SELECT [${UserGroupFields.UserID}]
        FROM [${UserGroupTableName}]
        WHERE [${UserGroupFields.GroupID}] IN (
          ${conds.join(', ')}
        )
      )
    `
  }

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
    query += `AND [${UserCompletedLearningContextFields.CompletedOn}] BETWEEN @from AND @to`
  }

  query += `
    ORDER BY [${UserFields.FirstName}], [${UserFields.LastName}]
    OFFSET @offset ROWS
    FETCH FIRST @limit ROWS ONLY
  `

  const results = await request.query<UserCompletedLearningContext & {TotalRecords: number}>(query)

  return {
    totalRecords: results.recordset.length > 0 ? results.recordset[0].TotalRecords : 0,
    records: results.recordset.map(record => new UserCompletedLearningContextModel(record))
  }
}
