import mssql, { addRow } from '@lcs/mssql-utility'
import { SystemWidgetGroup } from '@tess-f/sql-tables/dist/lms/system-widget-group.js'
import SystemWidgetGroupModel from '../../../models/system-widget-group.model.js'

export default async function createSystemWidgetGroup (group: SystemWidgetGroupModel): Promise<SystemWidgetGroupModel> {
  const pool = mssql.getPool()
  const record = await addRow<SystemWidgetGroup>(pool.request(), group)
  return new SystemWidgetGroupModel(record)
}
