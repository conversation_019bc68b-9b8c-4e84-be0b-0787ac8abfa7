import mssql, { parseSearchTerms } from '@lcs/mssql-utility'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningObjectKeywordFields, LearningObjectKeywordsTableName } from '@tess-f/sql-tables/dist/lms/learning-object-keyword.js'
import { LearningObject, LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import LearningObjectModel from '../../../models/learning-object.model.js'

export default async function (userID: string, from?: Date, to?: Date, search?: string, keywords?: string[]): Promise<LearningObjectModel[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('userID', userID)
  let query = `
        SELECT *
        FROM [${LearningObjectsTableName}]
        WHERE [${LearningObjectFields.ID}] IN (
          SELECT [${LearnerProgressFields.LearningObjectID}]
          FROM [${LearnerProgressTableName}]
          WHERE [${LearnerProgressFields.UserID}] = @userID
          AND [${LearnerProgressFields.LessonStatusID}] < 4
          ${from && to ? `AND [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to` : ''}
        ) AND [${LearnerProgressFields.ID}] NOT IN (
          SELECT [${LearningObjectFields.ID}]
          FROM [${LearningObjectsTableName}]
          WHERE [${LearningObjectFields.ID}] IN (
            SELECT [${LearnerProgressFields.LearningObjectID}]
            FROM [${LearnerProgressTableName}]
            WHERE [${LearnerProgressFields.UserID}] = @userID
            AND [${LearnerProgressFields.LessonStatusID}] >= 4
            ${from && to ? `AND [${LearnerProgressFields.CreatedOn}] BETWEEN @from AND @to` : ''}
          )
        )
      `

  if (from && to) {
    request.input('from', from)
    request.input('to', to)
  }

  if (search) {
    query += `
          AND (${parseSearchTerms(request, search, [LearningObjectFields.Title, LearningObjectFields.Description], 'any')})
        `
  }

  if (keywords && keywords.length > 0) {
    query += `AND ( [${LearningObjectFields.ID}] IN ( SELECT [${LearningObjectKeywordFields.LearningObjectID}] FROM [${LearningObjectKeywordsTableName}] WHERE `
    keywords.forEach((key, index) => {
      request.input(`keyword_${index}`, key)
      query += `[${LearningObjectKeywordFields.Keyword}] = @keyword_${index} ${index + 1 < keywords.length ? 'OR ' : ''}`
    })
    query += '))'
  }

  const results = await request.query<LearningObject>(query)

  return results.recordset.map(record => new LearningObjectModel(record))
}
