import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'
import LearningObject from '../../../models/learning-object.model'
import { Request, Response } from 'express'

describe('HTTP Controller: get learning object overview', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => sinon.restore())

  it('returns bad request when the id parameter is invalid', async () => {
    const controller = await esmock('./get-learning-object-overview.controller.js')
    const mocks = httpMocks.createMocks({ params: { id: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request data:')
    expect(data).to.include('id: Invalid')
  })

  it('should get the report even when the query dates are invalid', async () => {
    const controller = await esmock('./get-learning-object-overview.controller.js', {
      '../../../services/mssql/learning-objects/get.service.js': { default: sinon.stub().resolves(new LearningObject({
        ID: uuid(),
        Rating: 1
      })) },
      '../../../services/mssql/learning-object-views/get-counts.service.js': { default: sinon.stub().resolves(0) },
      '../../../services/mssql/learning-objects/get-completion-counts.service.js': {
        getObjectCompletedCount: sinon.stub().resolves(0),
        getObjectInProgressCount: sinon.stub().resolves(0)
      },
      '../../../services/mssql/learning-object-views/get-view-history.service.js': {
        default: sinon.stub().resolves([])
      }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() }, query: { from: 'test', to: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
    expect(data.element).to.exist
    expect(data.ViewHistory).to.exist
    expect(data.ViewHistory).to.be.an('array')
    expect(data.ViewHistory.length).to.equal(0)
  })

  it('should get the report with the given dates', async () => {
    const controller = await esmock<(req: Request, res: Response) => void>('./get-learning-object-overview.controller.js', {
      '../../../services/mssql/learning-objects/get.service.js': { default: sinon.stub().resolves(new LearningObject({
        ID: uuid(),
        Rating: 1
      })) },
      '../../../services/mssql/learning-object-views/get-counts.service.js': { default: sinon.stub().resolves(0) },
      '../../../services/mssql/learning-objects/get-completion-counts.service.js': {
        getObjectCompletedCount: sinon.stub().resolves(0),
        getObjectInProgressCount: sinon.stub().resolves(0)
      },
      '../../../services/mssql/learning-object-views/get-view-history.service.js': {
        default: sinon.stub().resolves([])
      }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() }, query: { from: new Date().toISOString(), to: new Date().toISOString() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.exist
    expect(data.element).to.exist
    expect(data.ViewHistory).to.exist
    expect(data.ViewHistory).to.be.an('array')
    expect(data.ViewHistory.length).to.equal(0)
  })
})
