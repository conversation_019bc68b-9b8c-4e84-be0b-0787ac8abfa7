import mssql from '@lcs/mssql-utility'
import getInprogressObjects from './get-inprogress-objects.service.js'
import getInprogressCourses from './get-inprogress-context.service.js'
import getCompletedObjects from './get-completed-objects.service.js'
import getCompletedCourses from './get-completed-contexts.service.js'
import { Request } from 'mssql'
import getUsersOverdueAssignmentCount from '../assignments/get-user-overdue-assignment-count.service.js'
import getUsersActiveAssignmentCount from '../assignments/get-users-active-assignment-count.service.js'
import { LearningObjectUserBookmarkFields, LearningObjectUserBookmarksTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-bookmark.js'
import { LearningContextUserBookmarkFields, LearningContextUserBookmarksTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-bookmark.js'
import { LearningObjectUserFavoriteFields, LearningObjectUserFavoritesTableName } from '@tess-f/sql-tables/dist/lms/learning-object-user-favorite.js'
import { LearningContextUserFavoriteFields, LearningContextUserFavoritesTableName } from '@tess-f/sql-tables/dist/lms/learning-context-user-favorite.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { LearningContextSessionFields, LearningContextSessionsTableName } from '@tess-f/sql-tables/dist/lms/learning-context-session.js'
import { SessionEnrollmentFields, SessionEnrollmentsTableName } from '@tess-f/sql-tables/dist/lms/session-enrollment.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { UserCompletedLearningContextFields, UserCompletedLearningContextsTableName } from '@tess-f/sql-tables/dist/lms/user-completed-learning-context.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { LearningMetadata } from '@tess-f/lms/dist/common/learning-metadata.js'
import { UserContentCertificateFields, UserContentCertificatesViewName } from '@tess-f/sql-tables/dist/lms/user-content-certificate.js'

export default async function (userID: string, from?: Date, to?: Date): Promise<LearningMetadata> {
  const pool = mssql.getPool()
  const inprogressObjects = await getInprogressObjects(userID, from, to)
  const completedObjects = await getCompletedObjects(userID, from, to)
  const completedCourses = await getCompletedCourses(userID, from, to)
  const inprogressCourses = await getInprogressCourses(userID, from, to)

  return {
    InProgress: inprogressObjects.length + inprogressCourses.length,
    Completed: completedObjects.length + completedCourses.length,
    Assigned: await getUsersActiveAssignmentCount(userID),
    Overdue: await getUsersOverdueAssignmentCount(userID),
    Bookmarked: await getBookmarkCount(pool.request(), userID),
    Favorites: await getFavoriteCount(pool.request(), userID),
    Enrolled: await getEnrolledCount(pool.request(), userID),
    Certificates: await getCertificateCount(pool.request(), userID),
    AverageScore: await getAverageScore(pool.request(), userID)
  }
}

async function getBookmarkCount (request: Request, userID: string): Promise<number> {
  request.input('userID', userID)

  const objectCount = await request.query<{ TotalRecords: number }>(`
    SELECT COUNT(*) AS TotalRecords
    FROM [${LearningObjectUserBookmarksTableName}]
    WHERE [${LearningObjectUserBookmarkFields.UserID}] = @userID
  `)

  const contextCount = await request.query<{ TotalRecords: number }>(`
    SELECT COUNT(*) AS TotalRecords
    FROM [${LearningContextUserBookmarksTableName}]
    WHERE [${LearningContextUserBookmarkFields.UserID}] = @userID
  `)

  return objectCount.recordset[0].TotalRecords + contextCount.recordset[0].TotalRecords
}

async function getFavoriteCount (request: Request, userID: string): Promise<number> {
  request.input('userID', userID)

  const objectCount = await request.query<{ TotalRecords: number }>(`
    SELECT COUNT(*) AS TotalRecords
    FROM [${LearningObjectUserFavoritesTableName}]
    WHERE [${LearningObjectUserFavoriteFields.UserID}] = @userID
  `)

  const contextCount = await request.query<{ TotalRecords: number }>(`
    SELECT COUNT(*) AS TotalRecords
    FROM [${LearningContextUserFavoritesTableName}]
    WHERE [${LearningContextUserFavoriteFields.UserID}] = @userID
  `)

  return objectCount.recordset[0].TotalRecords + contextCount.recordset[0].TotalRecords
}

async function getEnrolledCount (request: Request, userID: string): Promise<number> {
  request.input('userID', userID)

  const enrolled = await request.query<{ TotalRecords: number }>(`
    SELECT COUNT(*) AS TotalRecords
    FROM [${LearningContextTableName}]
    WHERE [${LearningContextFields.ID}] IN (
      SELECT [${LearningContextSessionFields.LearningContextID}]
      FROM [${LearningContextSessionsTableName}]
      WHERE [${LearningContextSessionFields.ID}] IN (
        SELECT [${SessionEnrollmentFields.SessionID}]
        FROM [${SessionEnrollmentsTableName}]
        WHERE [${SessionEnrollmentFields.UserID}] = @userID
      ) AND [${LearningContextSessionFields.ID}] NOT IN (
        SELECT [${LearnerProgressFields.LearningContextSessionID}]
        FROM [${LearnerProgressTableName}]
        WHERE [${LearnerProgressFields.UserID}] = @userID
        AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.fail}
        AND [${LearnerProgressFields.LearningContextSessionID}] IS NOT NULL
      )
    )
  `)

  return enrolled.recordset[0].TotalRecords
}

async function getCertificateCount (request: Request, userID: string): Promise<number> {
  request.input('userID', userID)

  const certificateCount = await request.query<{ TotalRecords: number }>(`
    SELECT COUNT(*) AS TotalRecords
    FROM [${UserContentCertificatesViewName}]
    WHERE [${UserContentCertificateFields.UserID}] = @userID
  `)

  return certificateCount.recordset[0].TotalRecords
}

async function getAverageScore (request: Request, userID: string): Promise<number> {
  request.input('userID', userID)

  const result = await request.query<{ AverageScore: number }>(`
    SELECT [AverageScore] = COALESCE(AVG([${LearnerProgressFields.RawScore}]), 0)
    FROM (
      SELECT [${LearnerProgressFields.RawScore}]
      FROM [${LearnerProgressTableName}]
      WHERE [${LearnerProgressFields.UserID}] = @userID
      AND [${LearnerProgressFields.RawScore}] IS NOT NULL
      AND [${LearnerProgressFields.LessonStatusID}] >= ${LessonStatuses.fail}

      UNION ALL

      SELECT [${UserCompletedLearningContextFields.RawScore}]
      FROM [${UserCompletedLearningContextsTableName}]
      WHERE [${UserCompletedLearningContextFields.UserID}] = @userID
      AND [${UserCompletedLearningContextFields.RawScore}] IS NOT NULL
      AND [${UserCompletedLearningContextFields.LessonStatusID}] >= ${LessonStatuses.fail}
    ) AS CombinedScores
  `)

  return result.recordset[0].AverageScore
}
