import mssql from '@lcs/mssql-utility'
import logger from '@lcs/logger'
import { UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'

const log = logger.create('Service.Update-User-Assigned-Learning-Object-Status')

export default async function (UserID: string, LearningObjectID: string, StatusID: number, ProgressID: string): Promise<number> {
  const pool = mssql.getPool()
  const request = pool.request()
  request.input('statusID', StatusID)
  request.input('userID', UserID)
  request.input('objectID', LearningObjectID)
  request.input('progressID', ProgressID)

  const res = await request.query(`
    UPDATE [${UserAssignedLearningObjectsTableName}]
    SET [${UserAssignedLearningObjectFields.LessonStatusID}] = @statusID,
    [${UserAssignedLearningObjectFields.LearnerProgressID}] = @progressID,
    [${UserAssignedLearningObjectFields.Completed}] = ${StatusID >= LessonStatuses.passed ? 1 : 0}
    WHERE [${UserAssignedLearningObjectFields.UserID}] = @userID
    AND [${UserAssignedLearningObjectFields.LearningObjectID}] = @objectID
    AND [${UserAssignedLearningObjectFields.LessonStatusID}] < @statusID
  `)

  log('info', 'Successfully updated records', { userID: UserID, objectID: LearningObjectID, count: res.rowsAffected[0] })
  return res.rowsAffected[0]
}
