import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningObject from '../../../models/learning-object.model.js'

describe('HTTP update controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-objects/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObject({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                ID: uuid(),
                Title: 'test',
                Description: 'test',
                LearningObjectTypeID: 1,
                CreatedBy: uuid(),
                CreatedOn: new Date()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })



    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-objects/update.service.js': {
                default: Sinon.stub().returns(Promise.resolve(new LearningObject({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                ID: false,
                Title: false,
                Description: false,
                LearningObjectTypeID: false,
                CreatedBy: false,
                CreatedOn: false
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request data')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('Title')
        expect(mocks.res._getData()).include('Description')
        expect(mocks.res._getData()).include('LearningObjectTypeID')

    })


    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./update.controller', {
            '../../../services/mssql/learning-objects/update.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(new LearningObject({ ID: uuid() })))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            body: {
                ID: uuid(),
                Title: 'test',
                Description: 'test',
                LearningObjectTypeID: 1,
                CreatedBy: uuid(),
                CreatedOn: new Date()
            }

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })
 

})