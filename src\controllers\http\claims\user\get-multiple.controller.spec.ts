import logger from '@lcs/logger'
import httpStatus from 'http-status'
import sinon from 'sinon'
import { expect } from 'chai'
import httpMocks from 'node-mocks-http'
import esmock from 'esmock'
import { v4 as uuid } from 'uuid'

describe('HTTP Controller: Get Multiple Claims for User', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => sinon.restore())

  it('should return bad request when the id param is not a uuid', async () => {
    const controller = await esmock('./get-multiple.controller.js')
    const mocks = httpMocks.createMocks({ params: { id: 'test' } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.BAD_REQUEST)
    const data = mocks.res._getData()
    expect(data).to.include('Invalid request parameters,')
    expect(data).to.include('id: Invalid')
  })

  it('should return the claims for the user', async () => {
    const controller = await esmock('./get-multiple.controller.js', {
      '.../../../../services/mssql/claims/get-for-user.service.js': {
        default: sinon.stub().resolves(['test', 'test-2'])
      }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.OK)
    const data = mocks.res._getJSONData()
    expect(data).to.be.an('array')
    expect(data.length).to.equal(2)
  })

  it('should gracefully handle an error', async () => {
    const controller = await esmock('./get-multiple.controller.js', {
      '../../../../services/mssql/claims/get-for-user.service.js': {
        default: sinon.stub().rejects(new Error('unknown error'))
      }
    })
    const mocks = httpMocks.createMocks({ params: { id: uuid() } })
    await controller(mocks.req, mocks.res)
    expect(mocks.res._getStatusCode()).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
  })
})
