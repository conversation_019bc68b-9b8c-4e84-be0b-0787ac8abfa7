import mssql from '@lcs/mssql-utility'
import { UserAssignmentDatabaseRecord } from '../../../models/internals/user-assignment.model.js'
import { UserAssignedLearningObjectFields, UserAssignedLearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-learning-object.js'
import { UserAssignedMultiSessionCoursesFields, UserAssignedMultiSessionCoursesTableName } from '@tess-f/sql-tables/dist/lms/user-assigned-multi-session-course.js'
import { LearnerProgressFields, LearnerProgressTableName } from '@tess-f/sql-tables/dist/lms/learner-progress.js'
import { LearningContextFields, LearningContextTableName } from '@tess-f/sql-tables/dist/lms/learning-context.js'
import { GradeTypes } from '@tess-f/sql-tables/dist/lms/grade-type.js'
import { LessonStatuses } from '@tess-f/sql-tables/dist/lms/lesson-status.js'
import { LearningObjectFields, LearningObjectsTableName } from '@tess-f/sql-tables/dist/lms/learning-object.js'
import { LearningObjectTypes } from '@tess-f/sql-tables/dist/lms/learning-object-type.js'

export default async function (assignmentID: string, userID: string): Promise<UserAssignmentDatabaseRecord[]> {
  const pool = mssql.getPool()
  const request = pool.request()

  request.input('assignmentID', assignmentID)
  request.input('userID', userID)

  const response = await request.query<UserAssignmentDatabaseRecord>(`
    SELECT
      [${UserAssignedLearningObjectFields.DueDate}], [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.LearningObjectID}],
      NULL AS [${UserAssignedMultiSessionCoursesFields.LearningContextID}], [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.LessonStatusID}],
      [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.UserID}], [${UserAssignedLearningObjectFields.ForContextID}], [${UserAssignedLearningObjectFields.ForContextTitle}],
      [${UserAssignedLearningObjectFields.ForContextLabel}], [${UserAssignedLearningObjectFields.ForContextGradeTypeID}], [${UserAssignedLearningObjectFields.ForContextTypeID}],
      [${UserAssignedLearningObjectFields.ForContextRequiredContentCount}], [${UserAssignedLearningObjectFields.ForContextExamID}],
      [${UserAssignedLearningObjectFields.SubContextID}], [${UserAssignedLearningObjectFields.SubContextLabel}], [${UserAssignedLearningObjectFields.SubContextTitle}],
      [${UserAssignedLearningObjectFields.SubContextTypeID}], [${UserAssignedLearningObjectFields.SubContextGradeTypeID}], [${UserAssignedLearningObjectFields.SubContextRequiredContentCount}],
      [${UserAssignedLearningObjectFields.SubContextExamID}], [${UserAssignedLearningObjectFields.SubContextParentID}], [${UserAssignedLearningObjectFields.SubContextOrderID}],
      [${UserAssignedLearningObjectFields.ContentOrderID}], [${UserAssignedLearningObjectFields.Completed}], [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.LearningObjectTypeID}],
      COALESCE([${LearnerProgressTableName}].[${LearnerProgressFields.StartedDate}], [${LearnerProgressTableName}].[${LearnerProgressFields.CreatedOn}]) AS [${LearnerProgressFields.StartedDate}],
      COALESCE([${LearnerProgressTableName}].[${LearnerProgressFields.ModifiedOn}], [${LearnerProgressTableName}].[${LearnerProgressFields.CompletedDate}], [${LearnerProgressTableName}].[${LearnerProgressFields.CreatedOn}]) AS [LastAccessed],
      CASE
        WHEN ([${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.LearningObjectTypeID}] = ${LearningObjectTypes.Audio} OR [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.LearningObjectTypeID}] = ${LearningObjectTypes.Video}) AND [${LearnerProgressTableName}].[${LearnerProgressFields.RawScore}] IS NOT NULL THEN [${LearnerProgressTableName}].[${LearnerProgressFields.RawScore}]
        WHEN [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.LessonStatusID}] = ${LessonStatuses.notAttempted} THEN 0
        WHEN [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.LessonStatusID}] < ${LessonStatuses.fail} THEN 50
        WHEN [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.LessonStatusID}] >= ${LessonStatuses.fail} THEN 100
      END AS [ItemProgress],
      CASE
        WHEN [${LearnerProgressTableName}].[${LearnerProgressFields.RawScore}] IS NOT NULL THEN [${LearnerProgressTableName}].[${LearnerProgressFields.RawScore}]
        WHEN [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.LessonStatusID}] = ${LessonStatuses.completed} THEN 100
        ELSE 0
      END AS [ItemScore],
      'LearningObject' AS [EntityType],
      [${LearningObjectsTableName}].[${LearningObjectFields.Title}], [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.ForContextMinScore}], [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.ForContextMaxScore}], [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.SubContextMinScore}], [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.SubContextMaxScore}]
    FROM [${UserAssignedLearningObjectsTableName}]
      JOIN [${LearningObjectsTableName}] ON [${LearningObjectsTableName}].[${LearningObjectFields.ID}] = [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.LearningObjectID}]
      LEFT JOIN [${LearnerProgressTableName}] ON [${LearnerProgressTableName}].[${LearnerProgressFields.ID}] = [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.LearnerProgressID}]
    WHERE [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.AssignmentID}] = @assignmentID
    AND [${UserAssignedLearningObjectsTableName}].[${UserAssignedLearningObjectFields.UserID}] = @userID

    UNION ALL

    SELECT
      [${UserAssignedMultiSessionCoursesFields.DueDate}], NULL AS [${UserAssignedLearningObjectFields.LearningObjectID}], [${UserAssignedMultiSessionCoursesFields.LearningContextID}], [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.LessonStatusID}], [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.UserID}],
      [${UserAssignedMultiSessionCoursesFields.ForContextID}], [${UserAssignedMultiSessionCoursesFields.ForContextTitle}], [${UserAssignedMultiSessionCoursesFields.ForContextLabel}], [${UserAssignedMultiSessionCoursesFields.ForContextGradeTypeID}], [${UserAssignedMultiSessionCoursesFields.ForContextTypeID}], [${UserAssignedMultiSessionCoursesFields.ForContextRequiredContentCount}], [${UserAssignedMultiSessionCoursesFields.ForContextExamID}],
      [${UserAssignedMultiSessionCoursesFields.SubContextID}], [${UserAssignedMultiSessionCoursesFields.SubContextLabel}], [${UserAssignedMultiSessionCoursesFields.SubContextTitle}], [${UserAssignedMultiSessionCoursesFields.SubContextTypeID}], [${UserAssignedMultiSessionCoursesFields.SubContextGradeTypeID}], [${UserAssignedMultiSessionCoursesFields.SubContextRequiredContentCount}], [${UserAssignedMultiSessionCoursesFields.SubContextExamID}], [${UserAssignedMultiSessionCoursesFields.SubContextParentID}], [${UserAssignedMultiSessionCoursesFields.SubContextOrderID}],
      [${UserAssignedMultiSessionCoursesFields.ContentOrderID}], [${UserAssignedMultiSessionCoursesFields.Completed}], NULL AS [${UserAssignedLearningObjectFields.LearningObjectTypeID}],
      COALESCE([${LearnerProgressTableName}].[${LearnerProgressFields.StartedDate}], [${LearnerProgressTableName}].[${LearnerProgressFields.CreatedOn}]) AS [${LearnerProgressFields.StartedDate}],
      COALESCE([${LearnerProgressTableName}].[${LearnerProgressFields.ModifiedOn}], [${LearnerProgressTableName}].[${LearnerProgressFields.CompletedDate}], [${LearnerProgressTableName}].[${LearnerProgressFields.CreatedOn}]) AS [LastAccessed],
      CASE
        WHEN [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.LessonStatusID}] = ${LessonStatuses.notAttempted} THEN 0
        WHEN [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.LessonStatusID}] < ${LessonStatuses.fail} THEN 50
        WHEN [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.LessonStatusID}] >= ${LessonStatuses.fail} THEN 100
      END AS [ItemProgress],
      CASE
        WHEN [${LearningContextTableName}].[${LearningContextFields.GradeTypeID}] = ${GradeTypes.Percentage} AND [${LearnerProgressTableName}].[${LearnerProgressFields.RawScore}] IS NOT NULL THEN [${LearnerProgressTableName}].[${LearnerProgressFields.RawScore}]
        WHEN [${LearningContextTableName}].[${LearningContextFields.GradeTypeID}] = ${GradeTypes.Percentage} AND [${LearnerProgressTableName}].[${LearnerProgressFields.RawScore}] IS NULL THEN 0
        WHEN [${LearningContextTableName}].[${LearningContextFields.GradeTypeID}] <= ${GradeTypes.PassFail} AND [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.LessonStatusID}] <= ${LessonStatuses.fail} THEN 0
        WHEN [${LearningContextTableName}].[${LearningContextFields.GradeTypeID}] <= ${GradeTypes.PassFail} AND [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.LessonStatusID}] > ${LessonStatuses.fail} THEN 100
        ELSE 0
      END AS [ItemScore],
      'LearningContext' AS [EntityType],
      [${LearningContextTableName}].[${LearningContextFields.Title}], [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.ForContextMinScore}], [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.ForContextMaxScore}], [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.SubContextMinScore}], [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.SubContextMaxScore}]
    FROM [${UserAssignedMultiSessionCoursesTableName}]
      JOIN [${LearningContextTableName}] ON [${LearningContextTableName}].[${LearningContextFields.ID}] = [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.LearningContextID}]
      LEFT JOIN [${LearnerProgressTableName}] ON [${LearnerProgressTableName}].[${LearnerProgressFields.ID}] = [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.LearnerProgressID}]
    WHERE [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.AssignmentID}] = @assignmentID
    AND [${UserAssignedMultiSessionCoursesTableName}].[${UserAssignedMultiSessionCoursesFields.UserID}] = @userID
  `)

  return response.recordset
}
