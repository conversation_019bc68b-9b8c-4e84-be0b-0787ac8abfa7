import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'

describe('HTTP Get multiple controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get-multiple.controller', {
            '../../../services/mssql/learner-progress/get-multiple.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            }
        })

        const mocks = httpMocks.createMocks({
           
            params: {
                userID: uuid(),
                objectID: uuid()
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)
        
    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get-multiple.controller', {
            '../../../services/mssql/learner-progress/get-multiple.service.js': {
                default: Sinon.stub().returns(Promise.resolve([]))
            }
        })

        const mocks = httpMocks.createMocks({
           
            params: {
                userID: false,
                objectID: false
            }
        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request parameter data')
        expect(mocks.res._getData()).include('userID: Expected string, received boolean')
        expect(mocks.res._getData()).include('objectID: Expected string, received boolean')
        
    })
})