import mssql from '@lcs/mssql-utility'
import { CurrentAssignmentCountsView } from '@tess-f/sql-tables/dist/lms/current-assignment-counts-view.js'
import { OverdueAssignmentsViewName } from'@tess-f/sql-tables/dist/lms/overdue-assignments-view.js'
import { InProgressAssignmentsViewName } from '@tess-f/sql-tables/dist/lms/in-progress-assignments-view.js'
import { CompletedAssignmentsViewName } from '@tess-f/sql-tables/dist/lms/completed-assignments-view.js'
import { AssignmentsWithNoContentViewName } from '@tess-f/sql-tables/dist/lms/assignments-with-no-content-view.js'
import { AssignmentsTableName } from '@tess-f/sql-tables/dist/lms/assignment.js'

export default async function (): Promise<Required<CurrentAssignmentCountsView>> {
  const request = mssql.getPool().request()
  const overdue = await request.query<{ Count: number }>(`SELECT COUNT(*) AS [Count] FROM [${OverdueAssignmentsViewName}]`)
  const inProgress = await request.query<{ Count: number }>(`SELECT COUNT(*) AS [Count] FROM [${InProgressAssignmentsViewName}]`)
  const completed = await request.query<{ Count: number }>(`SELECT COUNT(*) AS [Count] FROM [${CompletedAssignmentsViewName}]`)
  const noContent = await request.query<{ Count: number }>(`SELECT COUNT(*) AS [Count] FROM [${AssignmentsWithNoContentViewName}]`)
  const all = await request.query<{ Count: number }>(`SELECT COUNT(*) AS [Count] FROM [${AssignmentsTableName}]`)
  const notStarted = all.recordset[0].Count - overdue.recordset[0].Count - completed.recordset[0].Count - inProgress.recordset[0].Count - noContent.recordset[0].Count

  return {
    Overdue: overdue.recordset[0].Count,
    InProgress: inProgress.recordset[0].Count,
    Completed: completed.recordset[0].Count,
    NoContent: noContent.recordset[0].Count,
    NotStarted: notStarted,
    Active: inProgress.recordset[0].Count + notStarted
  }
}
