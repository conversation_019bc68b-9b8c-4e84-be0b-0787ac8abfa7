import SkillLevel, { skillLevelSchema } from '../../../models/skill-level.model.js'
import createService from '../../../services/mssql/skill-levels/create.service.js'
import logger from '@lcs/logger'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
import { ZodError } from 'zod'

const log = logger.create('Controller-HTTP.create-skill-level', httpLogTransformer)

/**
 * @param req.body.skillLevel {SkillLevelModel} - Skill level model with required creation fields
 */
export default async function (req: Request, res: Response) {
  // STIG V-69375 HTTP headers
  // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-create-skill-level' label."

  try {
    const skillLevel = new SkillLevel(skillLevelSchema.parse(req.body))
    const created = (await createService(skillLevel)).fields

    // STIG V-69427 changing data (success)
    // STIGTEST "In the LMS application, ???. Note the time. Check the LMS API log for the 'http-create-skill-level' label and message indicating successful creation."
    log('info', 'Successfully created skill level', { id: created.ID, success: true, req })

    res.json(created)
  } catch (error) {
    // STIG V-69427 changing data (failure)
    if (error instanceof ZodError) {
      log('warn', 'Failed to create skill level: input validation error', { success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request data: '))
    }
    else{
    log('error', 'Failed to create skill level.', { error, success: false, req })
    res.sendStatus(httpStatus.INTERNAL_SERVER_ERROR)
  }
}
}
