import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'
import { v4 as uuid } from 'uuid'
import LearningObjectRatingModel from '../../../models/learning-object-rating.model.js'

describe('HTTP get controller', () => {
    before(() => logger.init({ level: 'error' }))
    afterEach(() => Sinon.restore())
    
    it('returns success if the request data is valid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-object-ratings/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(LearningObjectRatingModel))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                rating: 1,
                id: uuid(),
                obj: uuid(),
                user: uuid()
            }

          

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.OK)

    })

    it('returns an error if the request data is invalid', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-object-ratings/get.service.js': {
                default: Sinon.stub().returns(Promise.resolve(LearningObjectRatingModel))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                rating: false,
                id: false,
                obj: false,
                user: false
            }

          

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.BAD_REQUEST)
        expect(mocks.res._getData()).include('Invalid request query data:')
        expect(mocks.res._getData()).include('Expected string, received boolean')
        expect(mocks.res._getData()).include('id')
        expect(mocks.res._getData()).include('obj')

    })

    it('returns an internal server error if the request is rejected', async () => {
        const controller = await esmock('./get.controller', {
            '../../../services/mssql/learning-object-ratings/get.service.js': {
                default: Sinon.stub().rejects(Promise.resolve(LearningObjectRatingModel))
            }
            
        })

        const mocks = httpMocks.createMocks({
            
            session: {
                userId: uuid()
            },
            query: {
                rating: 1,
                id: uuid(),
                obj: uuid(),
                user: uuid()
            }

          

        })
        await controller(mocks.req, mocks.res)
        expect(mocks.res.statusCode).equal(httpStatus.INTERNAL_SERVER_ERROR)

    })


})