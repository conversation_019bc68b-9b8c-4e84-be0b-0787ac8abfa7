export interface AgentJson {
  objectType?: string
  name?: string
  member?: <PERSON><PERSON><PERSON>[]
  mbox?: string
  mbox_sha1sum?: string
  openid?: string
  account?: {
    homePage: string
    name: string
  }
}

export default interface Statement {
  id?: string
  actor: <PERSON><PERSON><PERSON>,
  verb: {
    id: string,
    display?: {
      [key: string]: string
    }
  },
  object: {
    objectType: 'Activity',
    id: string,
    definition?: {
      name?: {[key: string]: string},
      description?: {[key: string]: string},
      type?: string
    }
  },
  timestamp: string,
  context?: {
    registration?: string,
    extensions?: any,
    contextActivities?: {
      grouping?: {
        id: string
        objectType?: string
        definition?: {
          name?: { [key: string]: string },
          description?: { [key: string]: string },
          type?: string,
          moreInfo?: string,
          extensions?: { [key: string]: any }
        }
      }[],
      parent?: {
        id: string
        objectType?: string
        definition?: {
          name?: { [key: string]: string },
          description?: { [key: string]: string },
          type?: string,
          moreInfo?: string,
          extensions?: { [key: string]: any }
        }
      }[],
      category?: {
        id: string
        objectType?: string
        definition?: {
          name?: { [key: string]: string },
          description?: { [key: string]: string },
          type?: string,
          moreInfo?: string,
          extensions?: { [key: string]: any }
        }
      }[],
      other?: {
        id: string
        objectType?: string
        definition?: {
          name?: { [key: string]: string },
          description?: { [key: string]: string },
          type?: string,
          moreInfo?: string,
          extensions?: { [key: string]: any }
        }
      }[]
    }
  },
  result?: {
    score?: {
      scaled?: number,
      raw?: number,
      min?: number,
      max?: number
    },
    success?: boolean,
    completion?: boolean,
    response?: string,
    duration?: string,
    extensions?: any
  },
  authority: AgentJson
}
