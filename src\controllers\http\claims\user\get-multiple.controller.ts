import logger from '@lcs/logger'
import { Request, Response } from 'express'
import httpStatus from 'http-status'
const { INTERNAL_SERVER_ERROR } = httpStatus
import getClaimsForUser from '../../../../services/mssql/claims/get-for-user.service.js'
import { httpLogTransformer, zodErrorToMessage } from '@tess-f/backend-utils'
import { zodGUID } from '@tess-f/backend-utils/validators'
import { z } from 'zod'

const log = logger.create('Controller-HTTP.get-multi-user-claims', httpLogTransformer)

export default async function (req: Request, res: Response) {
  try {
    const { id } = z.object({ id: zodGUID }).parse(req.params)
    const claims = await getClaimsForUser(id)
    log('info', 'Successfully retrieved claims for user', { count: claims.length, success: true, req })
    res.json(claims)
  } catch (error) {
    if (error instanceof z.ZodError) {
      log('warn', 'Failed to retrieve user claims: input validation error', { success: false, req })
      res.status(httpStatus.BAD_REQUEST).send(zodErrorToMessage(error, 'Invalid request parameters,'))
    } else {
      log('error', 'Failed to retrieve user claims', { success: false, req, error })
      res.sendStatus(INTERNAL_SERVER_ERROR)
    }
  }
}
